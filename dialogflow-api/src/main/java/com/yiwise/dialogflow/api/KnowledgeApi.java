package com.yiwise.dialogflow.api;


import com.yiwise.dialogflow.api.dto.response.knowledge.SimpleKnowledge;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping({"/apiBot/v3/knowledge"})
public interface KnowledgeApi {

    @GetMapping("getListByDialogFlowId")
    List<SimpleKnowledge> getKnowledgeListByDialogFlowId(@RequestParam("dialogFlowId") Long dialogFlowId);

    @PostMapping("updateKnowledge")
    Boolean updateKnowledge(@RequestParam("tenantId") Long tenantId,
                         @RequestParam("userId") Long userId,
                         @RequestBody SimpleKnowledge simpleKnowledge);

    @GetMapping("getListByGroupName")
    List<SimpleKnowledge> getKnowledgeListByGroupName(@RequestParam("botId") Long botId,
                                                      @RequestParam("groupName") String groupName);
}