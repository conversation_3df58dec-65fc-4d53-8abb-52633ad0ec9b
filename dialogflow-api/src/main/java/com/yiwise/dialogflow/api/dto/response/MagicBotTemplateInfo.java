package com.yiwise.dialogflow.api.dto.response;

import com.yiwise.dialogflow.api.dto.response.variable.VariableInfoDTO;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import lombok.Data;

import java.util.List;

@Data
public class MagicBotTemplateInfo {

    /**
     * botId
     */
    Long botId;

    /**
     * bot名称
     */
    String name;

    /**
     * 话术id
     */
    Long dialogFlowId;

    /**
     * 意向等级标签组 id
     */
    Long intentLevelTagId;

    /**
     * 模板变量列表
     */
    List<VariableInfoDTO> templateVariableList;

    /**
     * 录音师 id, 如果为 null, 则可能未选择或不是真人录音
     */
    Long recordUserId;

    /**
     * 音频类型
     */
    AudioTypeEnum audioType;

    /**
     * 模板易呼版本, V1/V2
     */
    String easyCallVersion;

    /**
     * 自定义变量列表
     */
    List<VariableInfoDTO> usedVariableList;
}
