package com.yiwise.dialogflow.api.dto.response.knowledge;

import com.yiwise.dialogflow.api.dto.response.audio.AnswerAudioDetail;
import lombok.Data;

import java.util.List;

@Data
public class SimpleKnowledge {
    /**
     * 机器人id
     */
    Long botId;

    Long dialogFlowId;

    /**
     * 知识id
     */
    String id;

    /**
     * 知识名称
     */
    String name;

    /**
     * 知识标签
     */
    String label;

    /**
     * 答案列表
     */
    List<AnswerAudioDetail> answerList;
}
