package com.yiwise.dialogflow.api;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Set;

@RequestMapping({"/apiBot/v3/config"})
public interface BotConfigApi {

    /**
     * 根据botId获取客户关注点名称集合
     * @param dialogFlowId 话术id
     * @return 客户关注点名称集合, bot不存在时, 返回空集合
     */
    @GetMapping("getConcernNameSetByDialogFlowId")
    Set<String> getConcernNameSetByDialogFlowId(@RequestParam("dialogFlowId") Long dialogFlowId);

    /**
     * 根据话术id查询是否启用人工介入
     *
     * @param dialogFlowId 话术id
     * @return T/F
     */
    @GetMapping("checkEnableHumanInterventionByDialogFlowId")
    boolean checkEnableHumanInterventionByDialogFlowId(@RequestParam("dialogFlowId") Long dialogFlowId);
}
