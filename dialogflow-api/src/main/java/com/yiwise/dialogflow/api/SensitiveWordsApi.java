package com.yiwise.dialogflow.api;

import com.yiwise.dialogflow.api.dto.request.SensitiveWordDetectRequest;
import com.yiwise.dialogflow.api.dto.response.SensitiveWordsDetectResultBO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping({"/apiBot/v3/sensitiveWord"})
public interface SensitiveWordsApi {

    @PostMapping("batchDetect")
    List<SensitiveWordsDetectResultBO> batchDetect(@RequestBody SensitiveWordDetectRequest request);

    @GetMapping("detect")
    SensitiveWordsDetectResultBO detect(@RequestParam("text") String text);
}
