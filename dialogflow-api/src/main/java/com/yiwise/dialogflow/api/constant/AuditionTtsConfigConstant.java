package com.yiwise.dialogflow.api.constant;


import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public class AuditionTtsConfigConstant {

    private static final Map<String, Float> TTS_VOICE_SPEED_CONFIG = new HashMap<>(16);
    private static final Map<String, Float> TTS_VOICE_VOLUME_CONFIG = new HashMap<>(16);

    static {
        // 小红(精品版)
        TTS_VOICE_SPEED_CONFIG.put("WOMEN_XIAOLAN_PLUS", 5.7f);
        TTS_VOICE_VOLUME_CONFIG.put("WOMEN_XIAOLAN_PLUS", 25f);

        // 开心(精品版)
        TTS_VOICE_SPEED_CONFIG.put("WOMEN_XIAOMENG_PLUS", 6.2f);
        TTS_VOICE_VOLUME_CONFIG.put("WOMEN_XIAOMENG_PLUS", 30f);

        // 龙龙(精品版)
        TTS_VOICE_SPEED_CONFIG.put("MAN_LONG_PLUS", 6.1f);
        TTS_VOICE_VOLUME_CONFIG.put("MAN_LONG_PLUS", 30f);

        // 徐牧(精品版)
        TTS_VOICE_SPEED_CONFIG.put("MAN_XUMU_PLUS", 5.8f);
        TTS_VOICE_VOLUME_CONFIG.put("MAN_XUMU_PLUS", 25f);

        // 子明
        TTS_VOICE_SPEED_CONFIG.put("MAN_ZI_MING", 3.5f);
        TTS_VOICE_VOLUME_CONFIG.put("MAN_ZI_MING", 40f);

        // 甜糕
        TTS_VOICE_SPEED_CONFIG.put("WOMEN_TIAN_GAO", 3.5f);
        TTS_VOICE_VOLUME_CONFIG.put("WOMEN_TIAN_GAO", 40f);

        // 小红（max版）
        TTS_VOICE_SPEED_CONFIG.put("WOMEN_XIAO_HONG_MAX", 3.5f);
        TTS_VOICE_VOLUME_CONFIG.put("WOMEN_XIAO_HONG_MAX", 40f);

        // 倩倩（max版）
        TTS_VOICE_SPEED_CONFIG.put("WOMEN_QIANQIAN_MAX", 3.5f);
        TTS_VOICE_VOLUME_CONFIG.put("WOMEN_QIANQIAN_MAX", 40f);

        // 徐牧（max版）
        TTS_VOICE_SPEED_CONFIG.put("MAN_XUMU_MAX", 3.5f);
        TTS_VOICE_VOLUME_CONFIG.put("MAN_XUMU_MAX", 40f);

        // 静静(电商场景)
        TTS_VOICE_SPEED_CONFIG.put("WOMEN_JING", 5.6f);
        TTS_VOICE_VOLUME_CONFIG.put("WOMEN_JING", 12f);

        // 龙龙(llm)
        TTS_VOICE_SPEED_CONFIG.put("MAN_LONG_LLM", 4.2f);
        TTS_VOICE_VOLUME_CONFIG.put("MAN_LONG_LLM", 35f);

        // 龙龙(plus版)
        TTS_VOICE_SPEED_CONFIG.put("MAN_LONGLONG_LLM", 6.1f);
        TTS_VOICE_VOLUME_CONFIG.put("MAN_LONGLONG_LLM", 30f);

        // 开心(plus版)
        TTS_VOICE_SPEED_CONFIG.put("WOMEN_KAIXIN_LLM", 6.2f);
        TTS_VOICE_VOLUME_CONFIG.put("WOMEN_KAIXIN_LLM", 25f);

        // 小红(plus版-热情版)
        TTS_VOICE_SPEED_CONFIG.put("WOMEN_XIAOHONG_PLUS_REQING", 5.7f);
        TTS_VOICE_VOLUME_CONFIG.put("WOMEN_XIAOHONG_PLUS_REQING", 25f);

        // 徐牧(plus版)
        TTS_VOICE_SPEED_CONFIG.put("MAN_XUMU_LLM", 5.8f);
        TTS_VOICE_VOLUME_CONFIG.put("MAN_XUMU_LLM", 30f);

        // 甜糕(plus版)
        TTS_VOICE_SPEED_CONFIG.put("WOMEN_GAOTIAN_LLM", 5.8f);
        TTS_VOICE_VOLUME_CONFIG.put("WOMEN_GAOTIAN_LLM", 30f);

        // 临时1
        TTS_VOICE_SPEED_CONFIG.put("MAN_TMP_1", 5.7f);
        TTS_VOICE_VOLUME_CONFIG.put("MAN_TMP_1", 25f);
    }

    public static Optional<Float> getDefaultSpeed(String voiceName) {
        return Optional.ofNullable(TTS_VOICE_SPEED_CONFIG.get(voiceName));
    }

    public static Optional<Float> getDefaultVolume(String voiceName) {
        return Optional.ofNullable(TTS_VOICE_VOLUME_CONFIG.get(voiceName));
    }
}
