package com.yiwise.dialogflow.api.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * BOT类型
 * <AUTHOR>
 */
public enum V3BotTypeEnum implements CodeDescEnum {
    COMMON(0, "旗舰版话术"),
    MAGIC_TEMPLATE(1, "轻量化模板话术"),
    MAGIC(2, "轻量化话术"),
    ;

    @JsonValue
    private final Integer code;
    private final String desc;

    V3BotTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public static boolean isCommon(V3BotTypeEnum type) {
        return COMMON == type;
    }

    public static boolean isMagic(V3BotTypeEnum type) {
        return MAGIC == type;
    }

    public static boolean isMagicTemplate(V3BotTypeEnum type) {
        return MAGIC_TEMPLATE == type;
    }
}
