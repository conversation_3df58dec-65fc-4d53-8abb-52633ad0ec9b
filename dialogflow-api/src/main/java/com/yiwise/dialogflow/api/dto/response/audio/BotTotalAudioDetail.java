package com.yiwise.dialogflow.api.dto.response.audio;

import lombok.Data;

import java.util.List;

/**
 * 机器人所有的音频详情
 */
@Data
public class BotTotalAudioDetail {

    /**
     * botId
     */
    private Long botId;

    /**
     * 对应的dialogFlowId
     */
    private Long dialogFlowId;

    /**
     * bot名称
     */
    private String name;

    /**
     * 流程列表
     */
    private List<StepTotalAudioDetail> stepList;

    /**
     * 知识列表
     */
    private List<KnowledgeAudioDetail> knowledgeList;

    /**
     * 特殊语境列表
     */
    private List<SpecialAnswerConfigAudioDetail> specialAnswerConfigList;

}
