import com.yiwise.dialogflow.pattern.PatternEnhance;
import com.yiwise.dialogflow.pattern.PatternEnhanceCache;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;

public class PatternEnhanceTest {
    private static final String START_AND_DUP_END_REGEX = "\\^.*\\+\\$";

    public static void main(String[] args) {
        String path = "/Users/<USER>/Downloads/68830.log";
        String input = "不看房喂";
//        test(path, input);

        String regex = "^[^(不|没)]*(哦|是)[^(不|没)]*$";

        PatternEnhance patternEnhance = PatternEnhanceCache.getOrCreate(regex, Pattern.compile(regex), false);
        System.out.println(patternEnhance);
    }

    private static void test(String path, String input) {

        List<PatternEnhance> patternEnhanceList = new ArrayList<>();
        // 按行读取 regex, 并把结果写入到 outputPath
        try (BufferedReader br = new BufferedReader(new FileReader(path))) {
            while (true) {
                String line = br.readLine();
                if (line == null) {
                    break;
                }
                if (StringUtils.isBlank(line)) {
                    continue;
                }
                try {
                    PatternEnhance patternEnhance = PatternEnhanceCache.getOrCreate(line, Pattern.compile(line), false);
                    patternEnhanceList.add(patternEnhance);
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        for (int i = 0; i < 100; i++) {
            AtomicInteger count = new AtomicInteger();
            List<PatternEnhance> matchList = new ArrayList<>();
            long start = System.nanoTime();
            for (PatternEnhance patternEnhance : patternEnhanceList) {
                int beforeCount = count.get();
                patternEnhance.find(input, count);
                if (count.get() > beforeCount) {
                    matchList.add(patternEnhance);
                }
            }
            long end = System.nanoTime();

            System.out.println("use: " + (end - start));
        }
    }

    private static void batch_parse() {
        String path = "/Users/<USER>/Downloads/orderByRequireLengthList_1.log";
        String outputPath = "/Users/<USER>/Downloads/orderByRequireLengthList_1_out.log";
        // 按行读取 regex, 并把结果写入到 outputPath
        try (BufferedReader br = new BufferedReader(new FileReader(path)); BufferedWriter bw = new BufferedWriter(new FileWriter(outputPath))) {

            while (true) {
                String line = br.readLine();
                if (line == null) {
                    break;
                }
                try {
                    PatternEnhance patternEnhance = PatternEnhanceCache.getOrCreate(line, Pattern.compile(line), false);
                    bw.write(patternEnhance.toString());
                    bw.newLine();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
