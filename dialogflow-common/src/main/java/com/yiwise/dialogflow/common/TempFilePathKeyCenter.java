package com.yiwise.dialogflow.common;

/**
 *
 */
public class TempFilePathKeyCenter {

    public static String getBotUploadAudioDirPath(Long botId) {
        return ApplicationConstant.LOCAL_TMP_DIR + "UploadAudio/" + System.currentTimeMillis() + "/" + botId + "/";
    }

    public static String getOSSDownloadTempAudioFilePath(String fileName) {
        return ApplicationConstant.LOCAL_TMP_DIR + "DownloadAudio/" + System.currentTimeMillis() + "/" + fileName;
    }

    public static String getBotUploadAudioUnzipDirPath(Long botId) {
        return ApplicationConstant.LOCAL_TMP_DIR + "UploadAudioZip/" + System.currentTimeMillis() + "/" + botId + "/";

    }

    public static String getDownloadAudioFileDirPath(Long botId) {
        return ApplicationConstant.LOCAL_TMP_DIR + "DownloadAudio/" + System.currentTimeMillis() + "/" + String.format("%s_%s", botId, System.currentTimeMillis()) + "/";
    }

    public static String getDownloadAudioZipFileDirPath(Long botId) {
        return ApplicationConstant.LOCAL_TMP_DIR + "DownloadAudioZip/" + System.currentTimeMillis() + "/";
    }

    public static String getBotExportFilePath(Long botId, String fileName){
        return ApplicationConstant.LOCAL_TMP_DIR + "BotExport/" + System.currentTimeMillis() + "/" + botId + "/" + fileName;
    }

    public static String getBotBatchExportDir(){
        return ApplicationConstant.LOCAL_TMP_DIR + "BotBatchExport/" + System.currentTimeMillis() + "/";
    }

    public static String getBotOperationLogExportFilePath(Long botId, String fileName) {
        return ApplicationConstant.LOCAL_TMP_DIR + "OperationLogExport/" + System.currentTimeMillis() + "/" + botId + "/" + fileName;
    }

    /**
     * 用户上传的临时文件
     */
    public static String getUserUploadTempFilePath(String fileName) {
        return ApplicationConstant.LOCAL_TMP_DIR + "FileUpload/" + System.currentTimeMillis() + "/" + fileName;
    }

    public static String getBotExportFullResourceDirPath(Long botId) {
        return ApplicationConstant.LOCAL_TMP_DIR + "BotExportResource/" + System.currentTimeMillis() + "/" + botId + "/";
    }
    public static String getBotExportFullResourceZipDirPath() {
        return ApplicationConstant.LOCAL_TMP_DIR + "BotExportZip/" + System.currentTimeMillis() + "/";
    }

    public static String getBotImportFullResourceDirPath() {
        return ApplicationConstant.LOCAL_TMP_DIR + "BotImportResource/" + System.currentTimeMillis() + "/";
    }

    public static String getSilenceAudioFilePath(Long pauseMs) {
        return ApplicationConstant.LOCAL_TMP_DIR + "SilenceAudio/" +  System.currentTimeMillis() + "/" + pauseMs + ".wav";
    }

    public static String getBot8kAudioFilePath(Long botId) {
        return ApplicationConstant.LOCAL_TMP_DIR + "UploadAudio/"+ botId + "/" + System.currentTimeMillis() + ".wav";
    }

    public static String getBotGenerateExcelTemplateFilePath() {
        return ApplicationConstant.LOCAL_TMP_DIR + "UploadExcel/"+  System.currentTimeMillis() + ".xls";
    }

    public static String getBotQRCodeGenerateWorkspaceDir() {
        return ApplicationConstant.LOCAL_TMP_DIR + "qrCode/" + System.currentTimeMillis() + "/体验海报/";

    }

    public static String getAnalyzeTaskResultFilePath(Long taskId) {
        return ApplicationConstant.LOCAL_TMP_DIR + "AnalyzeTask/" + taskId + "/result.xlsx";
    }

    public static String getBotImportZipDir() {
        return ApplicationConstant.LOCAL_TMP_DIR + "BotImportZip/" + System.currentTimeMillis() + "/";
    }

    public static String getVarValueTtsReplaceImportFilePath() {
        return ApplicationConstant.LOCAL_TMP_DIR + "VarValueTtsReplaceImport/" + System.currentTimeMillis() + "/";
    }

}
