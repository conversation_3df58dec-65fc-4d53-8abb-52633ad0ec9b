package com.yiwise.dialogflow.pattern;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@FieldDefaults(level = AccessLevel.PROTECTED)
public class DefaultPatternEnhance implements PatternEnhance{

    /**
     * 正则表达式中的. .* .? .{1,2}, .{n} ^ $
     */
    private static final String SIMPLE_PLACEHOLDER_REGEX_SYMBOL = "((\\.\\*)|(\\^)|(\\$)|(\\.\\?)|(\\.\\{\\d+\\,\\d+\\})|(\\.\\{\\d\\})|(\\{\\d+\\,\\d+\\})|(\\.)|(\\*))|(\\+)";

    // 正则表达式中的简单分支条件 比如 [指定.*(专柜|柜台|店铺|门店)] 中的(专柜|柜台|店铺|门店)
    private static final String BRANCH_CONDITION_REGEX = "(\\([^\\(\\)\\|]+(\\|[^\\|\\)\\(]+)*\\))";

    private static final String CONDITION_REGEX = "(\\[\\^[^\\]\\[]+\\])|(\\[[^\\]\\[]+\\])";

    final String originalRegex;
    final Pattern pattern;

    final boolean isPinYinRegex;

    final int requireInputLength;

    /**
     * 是否可以用包含代替正则匹配
     */
    final boolean canUseContainsReplaceMatch;

    /**
     * 是否可以用相等代替正则匹配
     */
    final boolean canUseEqualsMatch = false;

    /**
     * 正则匹配前, 至少应该要包含的字符
     */
    String[] allContainsWords;

    String[] anyContainsWords;

    DefaultPatternEnhance(String originalRegex, Pattern pattern) {
        this(0L, originalRegex, pattern, false);
    }

    DefaultPatternEnhance(Long botId, String originalRegex, Pattern pattern, Boolean isPinyin) {
        this.pattern = pattern;
        this.originalRegex = originalRegex;
        this.isPinYinRegex = BooleanUtils.isTrue(isPinyin);
        if (isPinYinRegex) {
            this.requireInputLength = 1;
            this.canUseContainsReplaceMatch = false;
        } else {
            List<String> allContains = calculateRequireContainsWord(originalRegex);
            if (CollectionUtils.isNotEmpty(allContains)) {
                this.allContainsWords = allContains.toArray(new String[0]);
                this.requireInputLength = allContains.stream().mapToInt(String::length).sum();
                String first = allContains.get(0);
                this.canUseContainsReplaceMatch = allContains.size() == 1
                        && (originalRegex.equals(first) || String.format(".*%s.*", first).equals(originalRegex));
            } else {
                List<String> branchList = getBranchConditionRegex(originalRegex);
                List<String> anyContainsList = new ArrayList<>();
                for (String branchRegex : branchList) {
                    String[] anyWords = branchRegex.trim().substring(1, branchRegex.length() - 1).split("\\|");
                    anyContainsList.addAll(Arrays.asList(anyWords));
                }

                anyContainsList = anyContainsList.stream().distinct()
                        .filter(this::notContainsAnyRegexSymbol)
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(anyContainsList)) {
                    this.anyContainsWords = anyContainsList.stream()
                            .sorted(Comparator.comparingInt(String::length))
                            .toArray(String[]::new);
                    this.requireInputLength = anyContainsList.stream().mapToInt(String::length).min().orElse(0);
                    this.canUseContainsReplaceMatch = false;
                    this.allContainsWords = null;
                } else {
                    this.canUseContainsReplaceMatch = false;
                    this.allContainsWords = null;
                    this.requireInputLength = 0;
                    this.anyContainsWords = null;
                }
            }
        }
    }

    @Override
    public Optional<String> find(String input) {
        return find(input, new AtomicInteger());
    }

    @Override
    public Optional<String> find(String input, AtomicInteger count) {
        if (isPinYinRegex) {
            return Optional.empty();
        }
        if (input.length() < requireInputLength) {
            return Optional.empty();
        }
        if (canUseContainsReplaceMatch
                && allContainsWords != null
                && allContainsWords.length == 1
                && input.contains(allContainsWords[0])) {
            return Optional.of(allContainsWords[0]);
        }
        if (allContainsWords != null && allContainsWords.length > 0) {
            if (isAllContains(input)) {
                Matcher matcher = pattern.matcher(input);
                count.incrementAndGet();
                if (matcher.find()) {
                    return Optional.of(matcher.group());
                }
            }
        } else if (anyContainsWords != null && anyContainsWords.length > 0){
            if (isAnyContains(input)) {
                Matcher matcher = pattern.matcher(input);
                count.incrementAndGet();
                if (matcher.find()) {
                    return Optional.of(matcher.group());
                }
            }
        } else {
            Matcher matcher = pattern.matcher(input);
            count.incrementAndGet();
            if (matcher.find()) {
                return Optional.of(matcher.group());
            }
        }

        return Optional.empty();
    }

    private boolean isAllContains(String input) {
        if (allContainsWords == null || allContainsWords.length == 0) {
            return false;
        }
        for (String allContainsWord : allContainsWords) {
            if (!input.contains(allContainsWord)) {
                return false;
            }
        }
        return true;
    }

    private boolean isAnyContains(String input) {
        if (anyContainsWords == null) {
            return false;
        }
        for (String anyContainsWord : anyContainsWords) {
            if (input.contains(anyContainsWord)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Optional<String> findPinyin(String input) {
        return findPinyin(input, new AtomicInteger());
    }

    @Override
    public Optional<String> findPinyin(String input, AtomicInteger count) {
        if (!isPinYinRegex) {
            return Optional.empty();
        }
        if (input.length() < requireInputLength) {
            return Optional.empty();
        }
        Matcher matcher = pattern.matcher(input);
        count.incrementAndGet();
        if (matcher.find()) {
            return Optional.of(matcher.group());
        }
        return Optional.empty();
    }

    /**
     * 不包含正则表达式必须的一些特殊元符号
     * @param regex
     * @return
     */
    private boolean notContainsAnyRegexSymbol(String regex) {
        // 判断表达式中没有任何正则表达式中的字符, 即可
        String patternSymbol = ".*?[]{}(|)<>\\^$+";
        String[] symbols = patternSymbol.split("");
        return Stream.of(symbols).noneMatch(regex::contains);
    }

    private int calculateRegexRequireInputLength(String regex) {
        if (notContainsAnyRegexSymbol(regex)) {
            return regex.length();
        }
        // 稍简单一点的正则
        String regexWithoutDotStar = regex.replaceAll(SIMPLE_PLACEHOLDER_REGEX_SYMBOL, "");
        if (notContainsAnyRegexSymbol(regexWithoutDotStar)) {
            return regexWithoutDotStar.length();
        }

        // 包含分支表达式的少简单的正则
        String removeBranchCondition = removeConditionSymbol(regex);
        String removeHolderSymbol = removeBranchCondition.replaceAll(SIMPLE_PLACEHOLDER_REGEX_SYMBOL, "");
        if (notContainsAnyRegexSymbol(removeHolderSymbol)) {
            return removeHolderSymbol.length();
        }
        return 1;
    }

    private String removeConditionSymbol(String originalRegex) {
        return replaceBranchConditionSymbol(originalRegex, ".");
    }

    private String replaceBranchConditionSymbol(String originalRegex, String replacement) {
        String res = originalRegex;
        String preRes = res;
        do {
            preRes = res;
            res = res.replaceAll(BRANCH_CONDITION_REGEX, replacement);
            res = res.replaceAll(CONDITION_REGEX, replacement);
        } while (!preRes.equals(res));
        return res;
    }

    private List<String> getBranchConditionRegex(String originalRegex) {
        Pattern branchPattern = Pattern.compile(BRANCH_CONDITION_REGEX);
        List<String> result = new ArrayList<>();
        Matcher matcher = branchPattern.matcher(originalRegex);
        while (matcher.find()) {
            String matched = matcher.group();
            result.add(matched);
        }
        return result;
    }

    private boolean isSimplyRegex(String regex) {
        if (StringUtils.isBlank(regex)) {
            return false;
        }
        return notContainsAnyRegexSymbol(regex.replaceAll(SIMPLE_PLACEHOLDER_REGEX_SYMBOL, ""));
    }

    private List<String> calculateRequireContainsWord(String regex) {
        if (notContainsAnyRegexSymbol(regex)) {
            return Collections.singletonList(regex);
        }

        // 如果正则表达式仅包含[.*], 则去掉[.*]后的长度就是最小输入长度
        if (isSimplyRegex(regex)) {
            String[] str = regex.split(SIMPLE_PLACEHOLDER_REGEX_SYMBOL);
            // 返回最长的一个子串
            return Stream.of(str)
                    .filter(StringUtils::isNotBlank)
                    .sorted(Comparator.comparingInt(String::length).reversed())
                    .collect(Collectors.toList());
        }

        String removeBranchConditionSymbol = removeConditionSymbol(regex);
        if (isSimplyRegex(removeBranchConditionSymbol)) {
            String[] str = removeBranchConditionSymbol.split(SIMPLE_PLACEHOLDER_REGEX_SYMBOL);
            // 返回最长的一个子串
            return Stream.of(str)
                    .filter(regex::contains)
                    .filter(StringUtils::isNotBlank)
                    .sorted(Comparator.comparingInt(String::length).reversed())
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public String toString() {
        String template = "[originalRegex=%s, allContainsWords=%s, canUseContainsReplaceMatch=%s, anyContainsWords=%s, requireInputLength=%s, isPinYinRegex=%s]";
        return String.format(template, originalRegex, Arrays.toString(allContainsWords), canUseContainsReplaceMatch, Arrays.toString(anyContainsWords), requireInputLength, isPinYinRegex);
    }

}
