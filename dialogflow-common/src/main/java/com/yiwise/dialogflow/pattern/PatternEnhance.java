package com.yiwise.dialogflow.pattern;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;

public interface PatternEnhance {

    Optional<String> find(String input);

    Optional<String> find(String input, AtomicInteger count);

    Optional<String> findPinyin(String input);

    Optional<String> findPinyin(String input, AtomicInteger count);

    int getRequireInputLength();

    Pattern getPattern();

    String getOriginalRegex();

    boolean isCanUseContainsReplaceMatch();
}
