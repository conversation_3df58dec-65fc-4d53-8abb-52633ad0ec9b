package com.yiwise.dialogflow.pattern;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.Getter;

import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Pattern;

@Getter
public class PatternEnhanceCache {

    final String regex;

    final PatternEnhance patternEnhance;

    final AtomicLong hitCount = new AtomicLong(0);

    private PatternEnhanceCache(String regex, Pattern pattern, boolean isPinyin) {
        this.regex = regex;
        this.patternEnhance = new DefaultPatternEnhance(0L, regex, pattern, isPinyin);
    }

    public void hit() {
        hitCount.incrementAndGet();
    }

    private static final Cache<String, PatternEnhanceCache> CACHE = CacheBuilder.newBuilder()
            // 10万
            .maximumSize(10_0000)
            .expireAfterAccess(1, TimeUnit.DAYS)
            .build();

    public static PatternEnhance getOrCreate(String regex, Pattern pattern, boolean isPinyin) {
        PatternEnhanceCache patternCache = CACHE.getIfPresent(regex);
        if (patternCache != null) {
            patternCache.hit();
            return patternCache.getPatternEnhance();
        }

        PatternEnhanceCache cache = new PatternEnhanceCache(regex, pattern, isPinyin);
        CACHE.put(regex, cache);
        return cache.getPatternEnhance();
    }

    public static Map<String, Long> getCacheInfo() {
        return CACHE.asMap().entrySet().stream()
                .collect(java.util.stream.Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getHitCount().get()));
    }
}
