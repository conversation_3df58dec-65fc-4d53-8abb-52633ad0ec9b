package com.yiwise.dialogflow.reactor;

import com.yiwise.dialogflow.common.ApplicationConstant;
import org.reactivestreams.Subscriber;
import org.reactivestreams.Subscription;
import org.slf4j.MDC;
import reactor.core.CoreSubscriber;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxOperator;
import reactor.util.context.Context;

import java.util.Optional;

public class FluxAccumulator<IN, OUT> extends FluxOperator<IN, OUT> {

    private final Accumulator<IN, OUT> accumulator;

    FluxAccumulator(Flux<? extends IN> source, Accumulator<IN, OUT> accumulator) {
        super(source);
        this.accumulator = accumulator;
    }


    public static <IN, OUT> Flux<OUT> accumulator(Flux<? extends IN> source, Accumulator<IN, OUT> accumulator) {
        return onAssembly(new FluxAccumulator<>(source, accumulator));
    }

    @Override
    public void subscribe(CoreSubscriber<? super OUT> actual) {
        CoreSubscriber<IN> subscriber = new AccumulatorSubscriber(actual);
        source.subscribe(subscriber);
    }

    class AccumulatorSubscriber implements CoreSubscriber<IN> {

        final CoreSubscriber<? super OUT> actual;
        Subscription subscription;
        
        AccumulatorSubscriber(CoreSubscriber<? super OUT> actual) {
            this.actual = actual;
        }

        @Override
        public void onSubscribe(Subscription s) {
            copyToMdc(actual.currentContext());
            actual.onSubscribe(s);
            subscription = s;
        }

        @Override
        public void onNext(IN input) {
            copyToMdc(actual.currentContext());
            Optional<OUT> optional = accumulator.accumulate(input);
            if (optional.isPresent()) {
                actual.onNext(optional.get());
            } else {
                subscription.request(Long.MAX_VALUE);
            }
        }

        @Override
        public void onError(Throwable throwable) {
            copyToMdc(actual.currentContext());
            actual.onError(throwable);
        }

        @Override
        public void onComplete() {
            copyToMdc(actual.currentContext());
            accumulator.onComplete().ifPresent(actual::onNext);
            actual.onComplete();
        }
        
        @Override
        public Context currentContext() {
            return actual.currentContext();
        }
        
        private void copyToMdc(Context context) {
            if (context == null) {
                return;
            }
            if (!context.isEmpty()) {
                context.getOrEmpty(ApplicationConstant.MDC_LOG_ID)
                        .ifPresent(logId -> MDC.put(ApplicationConstant.MDC_LOG_ID, logId.toString()));
            }
        }
    }
}
