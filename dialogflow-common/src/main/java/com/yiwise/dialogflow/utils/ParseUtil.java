package com.yiwise.dialogflow.utils;

import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/21
 */
public class ParseUtil {

    private static final String ESCAPE_CHARACTER = "*.?+$^[](){}<>|\\/";
    private static final Pattern INVALID_KEYWORD_REGEX = Pattern.compile(".*[（）【】].*");

    private static final Pattern PINYIN_PATTERN = Pattern.compile("<([a-zA-Z1-4\\s]+)>");

    /**
     * 正则表达式有以下特殊字符。需要转义  * . ? + $ ^ [ ] ( ) { } | \ /
     */
    public static String regexEscape(String str) {
        StringBuilder sf = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            String ss = String.valueOf(str.charAt(i));
            if (ESCAPE_CHARACTER.contains(ss)) {
                ss = "\\" + ss;
            }
            sf.append(ss);
        }
        return sf.toString();
    }

    /**
     * 用于mongodb的regex搜索
     */
    public static String regex(String str) {
        return "^.*" + regexEscape(str) + ".*$";
    }

    /**
     * 用于es的regex搜索
     */
    public static String esRegex(String str) {
        return ".*" + regexEscape(str) + ".*";
    }

    /**
     * 校验正则表达式
     */
    public static void checkRegExValid(List<String> regexList) {
        if (CollectionUtils.isEmpty(regexList)) {
            return;
        }
        for (String regex : regexList) {
            try {
                Pattern.compile(regex);
            } catch (PatternSyntaxException e) {
                String detailMsg = "关键词: " + regex + ", 非法的正则表达式";
                throw new ComException(ComErrorCode.REQUEST_REGEX_ERROR, detailMsg, detailMsg).setData(regex);
            }
            if(INVALID_KEYWORD_REGEX.matcher(regex).matches()){
                String detailMsg = "关键词: " + regex + ", 包含中文括号";
                throw new ComException(ComErrorCode.REQUEST_REGEX_ERROR, detailMsg, detailMsg).setData(regex);
            }
        }
    }

    public static List<String> getInvalidRegexExp(List<String> regexList) {
        if (CollectionUtils.isEmpty(regexList)) {
            return Collections.emptyList();
        }

        return regexList.stream()
                .filter(ParseUtil::invalid)
                .collect(Collectors.toList());
    }


    public static boolean valid(String regex) {
        if (StringUtils.isBlank(regex)) {
            return false;
        }
        try {
            if (isInvalidPinyin(regex)) {
                return false;
            }
            Pattern pattern = Pattern.compile(regex);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean invalid(String regex) {
        return !valid(regex);
    }

    /**
     * 判断是否是拼音正则, 只要是<开头和>结尾就认为是拼音正则
     * @param regex 正则
     * @return 是否是拼音
     */
    public static boolean isPinyin(String regex) {
        if (StringUtils.isBlank(regex)) {
            return false;
        }
        regex = regex.trim();
        return regex.startsWith("<") && regex.endsWith(">");
    }

    /**
     * 判断是否是非合法的拼音正则
     * 如果以<开头和>结尾, 则需要匹配PINYIN_PATTERN
     * 否则, 不能匹配PINYIN_PATTERN
     * @param regex 正则
     * @return 是否是合法的拼音正则
     */
    public static boolean isInvalidPinyin(String regex) {
        return (isPinyin(regex) && !PINYIN_PATTERN.matcher(regex.trim()).find())
                || (!isPinyin(regex)) && PINYIN_PATTERN.matcher(regex).find();
    }

}
