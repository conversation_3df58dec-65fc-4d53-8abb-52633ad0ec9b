#!/bin/bash

# the service name
SERVICE_NAME="${MODULE_FINAL_NAME}"
JAR_NAME=$SERVICE_NAME\.jar
PID_FILE=$SERVICE_NAME\.pid
GC_LOG="${gcLog}"
ARMS_JAR="${armsJar}"
ARMS_LICENCE_KEY="${armsLicenceKey}"
ARMS_APP_NAME="${MODULE_ARTIFACT_ID}-${armsAppSuffix}"
ARMS_OPTIONS="${armsOptions}"
JAVA_MEM_OPTS=" -server -Xmx${Xmx} -Xms${Xms} -Xmn${Xmn} -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Dlog4j2.formatMsgNoLookups=true -XX:PermSize=256m
-XX:+UseConcMarkSweepGC -XX:+CMSParallelRemarkEnabled -XX:+UseCMSCompactAtFullCollection -XX:LargePageSizeInBytes=128m -XX:+UseFastAccessorMethods
-XX:+UseCMSInitiatingOccupancyOnly -XX:CMSInitiatingOccupancyFraction=70 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/${MODULE_FINAL_NAME}.dump "
DNS_OPTS="-Djava.net.preferIPv4Stack=true -Ddruid.mysql.usePingMethod=false"
DEBUG="${remoteDebugAddress}${jmxremotePort}"
SPRING_OPTS=" -Dspring.profiles.active=${profile}"
LOG_FILE=${MODULE_ARTIFACT_ID}.log
BOOT_SUCCESS_SIGN='spring boot start successful'
STOP_SIGN='spring boot exit successful'
JAVA_OPTS="$JAVA_MEM_OPTS $ARMS_OPTIONS $GC_LOG $DEBUG $DNS_OPTS $SPRING_OPTS"

# go to the service dir
cd `dirname $0`
SERVICE_DIR=$(dirname $(pwd))
cd ..

# start the service
function startService(){
    pwd
    CUR_PID=`jps -l |grep  "$SERVICE_NAME" | awk '{print $1}'`
    if [ -n "$CUR_PID" ]; then
        echo "ERROR : the $SERVICE_NAME already started, PID: $CUR_PID"
        exit 1
    else
        echo "starting the $SERVICE_NAME ..."

        nohup java $JAVA_OPTS -jar $JAR_NAME >nohup.out 2>&1 &
        echo $! > $SERVICE_DIR/$PID_FILE

        # check if the process started successfully
        COUNT=0
        while [ $COUNT -lt 1 ]; do
            COUNT=`jps -l | grep "$SERVICE_NAME" | awk '{print $1}' | wc -l`
            if [ $COUNT -gt 0 ]; then
                while [ ! -f "$SERVICE_DIR/logs/$LOG_FILE" ]; do
                  sleep 2
                done

                tailf $SERVICE_DIR/logs/$LOG_FILE | while read oneLineLog
                do
                  echo $oneLineLog
                   if [[ $oneLineLog =~ $BOOT_SUCCESS_SIGN ]]; then
                      echo 'start sign match'
                      tailf_pid=`ps -ef | grep tailf | grep "$SERVICE_DIR" | awk '{print $2}'`
                      if [  -n "$tailf_pid" ]; then
                          kill $tailf_pid
                      fi
                   fi
                done

                break
            fi
        done

        PID=`cat $SERVICE_DIR/$PID_FILE`
        echo  "$SERVICE_NAME start successfully, the process PID: $PID"
    fi
}
# stop the service
function stopService (){
    pwd
    PID_FILE_PATH=$SERVICE_DIR/$PID_FILE

		CUR_PID=""
    if [  ! -f "$PID_FILE_PATH" ]; then
        CUR_PID=`jps -l | grep  "$SERVICE_NAME" | awk '{print $1}'`
        if [  ! -n "$CUR_PID" ]; then
          echo  "$SERVICE_NAME process not exists ! "
          exit 0
        fi
        else
          CUR_PID=`cat $PID_FILE_PATH`
    fi

    echo "the process before stop, PID: $CUR_PID"
    kill $CUR_PID
    rm -rf $PID_FILE_PATH
    echo  "stop $SERVICE_NAME ..."

    STOPPED="0"
    while [ $STOPPED -eq "0" ]; do
        CUR_PID=`jps -l | grep "$SERVICE_NAME" | awk '{print $1}'`
        if [ "$CUR_PID" == "" ]; then
            STOPPED="1"
            echo "$SERVICE_NAME stop success!"
            break
        else
            # 检查日志是否关闭完
            tailf $SERVICE_DIR/logs/$LOG_FILE | while read oneLineLog
            do
                echo $oneLineLog
                if [[ $oneLineLog =~ $STOP_SIGN ]]; then
                    echo 'stop sign match'
                    tailf_pid=`ps -ef | grep tailf | grep "$SERVICE_DIR" | awk '{print $2}'`
                    if [  -n "$tailf_pid" ]; then
                        kill $tailf_pid
                    fi
                fi
                CUR_PID=`jps -l | grep "$SERVICE_NAME" | awk '{print $1}'`
                if [ "$CUR_PID" == "" ]; then
                    tailf_pid=`ps -ef | grep tailf | grep "$SERVICE_DIR" | awk '{print $2}'`
                    if [  -n "$tailf_pid" ]; then
                        kill $tailf_pid
                    fi
                fi
            done

            # warning
            echo  "still killing $SERVICE_NAME process, PID: $CUR_PID ..."
            sleep 10
        fi
    done
}

case "$1" in
    start)
	     (startService)
        ;;
    stop)
	    (stopService)
        ;;
    restart)
        (stopService)
        sleep 2
        (startService)
        ;;
    *)
	    ## help
	    echo "what do you want to do? input the command below."
        echo  "./startup.sh start $SERVICE_NAME start"
        echo  "./startup.sh stop $SERVICE_NAME stop"
        echo  "./startup.sh restart $SERVICE_NAME restart"
        ;;
esac
exit 0