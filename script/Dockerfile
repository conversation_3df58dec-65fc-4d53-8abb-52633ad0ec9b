# Version 0.1

# 基础镜像
FROM docker.yiwise.net/base/java-ffmpeg-base

ENV LC_ALL "zh_CN.UTF-8"

RUN yum install -y crontabs wget unzip libX11 libXext libXi libXrender libXtst alsa-lib && \
mkdir -p /home/<USER>/aicc-platform-dialogflow-web && \
curl -o /home/<USER>/ArmsAgent.zip "http://arms-apm-cn-hangzhou.oss-cn-hangzhou-internal.aliyuncs.com/ArmsAgent.zip" && \
curl -o /home/<USER>/zulu8.68.0.21-ca-jdk8.0.362-linux.x86_64.rpm  "https://cdn.azul.com/zulu/bin/zulu8.68.0.21-ca-jdk8.0.362-linux.x86_64.rpm" && \
rpm -i /home/<USER>/zulu8.68.0.21-ca-jdk8.0.362-linux.x86_64.rpm && \
unzip /home/<USER>/ArmsAgent.zip -d /home/<USER>/ && \
rm -f /home/<USER>/ArmsAgent.zip /home/<USER>/zulu8.68.0.21-ca-jdk8.0.362-linux.x86_64.rpm

WORKDIR /home/<USER>/aicc-platform-dialogflow-web/

# 安装服务
ADD ${MODULE_ARTIFACT_ID}-1.0.0-SNAPSHOT.tar.gz /home/<USER>/aicc-platform-dialogflow-web

RUN chmod a+x ${MODULE_ARTIFACT_ID}-1.0.0-SNAPSHOT/bin/dockerentry.sh

CMD ["sh", "-c", "${MODULE_ARTIFACT_ID}-1.0.0-SNAPSHOT/bin/dockerentry.sh"]