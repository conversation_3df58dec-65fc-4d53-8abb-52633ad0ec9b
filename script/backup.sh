#!/bin/bash
#sae中在容器销毁前执行
TIME_NOW=`date "+%Y-%m-%d%H%M%S"`
LOCAL_NAME=$(hostname)
# deploy server
function backup(){
  if [ -d "/home/<USER>/aicc-platform-dialogflow-web/$1" ];then
        if [ ! -d "/mnt/aicc-platform-dialogflow-web/$1/backup" ];then
            mkdir -p /mnt/aicc-platform-dialogflow-web/$1/backup
        fi
        mkdir -p /mnt/aicc-platform-dialogflow-web/$1/backup/${TIME_NOW}-${LOCAL_NAME}
        cp -r /home/<USER>/aicc-platform-dialogflow-web/$1 /mnt/aicc-platform-dialogflow-web/$1/backup/${TIME_NOW}-${LOCAL_NAME}
  fi
}

(backup $1)