<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yiwise.dialogflow</groupId>
        <artifactId>aicc-platform-dialogflow-web</artifactId>
        <version>2.24.4-RELEASE</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>dialogflow-api-web</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>


    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.yiwise.dialogflow</groupId>
            <artifactId>dialogflow-engine</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
                    <groupId>org.springframework.cloud</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yiwise.dialogflow</groupId>
            <artifactId>dialogflow-common-webmvc-config</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-circuitbreaker</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}-${project.version}</finalName>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>true</filtering>
            </testResource>
        </testResources>

        <plugins>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                            <mainClass>com.yiwise.dialogflow.DialogFlowApiWebBootstrap</mainClass>
                        </manifest>
                        <manifestEntries>
                            <Class-Path>conf/</Class-Path>
                        </manifestEntries>
                    </archive>
                    <includes>
                        <include>**/*.class</include>
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/conf</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/resources</directory>
                                    <filtering>false</filtering>
                                    <includes>
                                        <include>**/*.docx</include>
                                    </includes>
                                </resource>
                                <resource>
                                    <directory>src/main/resources</directory>
                                    <filtering>true</filtering>
                                    <excludes>
                                        <include>**/*.docx</include>
                                    </excludes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <appendAssemblyId>false</appendAssemblyId>
                    <descriptors>
                        <descriptor>src/assembly/assembly.xml</descriptor>
                    </descriptors>
                    <tarLongFileMode>gnu</tarLongFileMode>
                    <finalName>${project.artifactId}-1.0.0-SNAPSHOT</finalName>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.yiwise</groupId>
                <artifactId>json-variable-filter</artifactId>
                <executions>
                    <execution>
                        <id>custom-filter</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>json-variable-filter</goal>
                        </goals>
                        <configuration>
                            <fileResources>
                                <fileResource>
                                    <sourceFile>${basedir}/../script/startup.sh</sourceFile>
                                    <outputDirectory>${basedir}/target/bin</outputDirectory>
                                </fileResource>
                                <fileResource>
                                    <sourceFile>${basedir}/../script/mv_log_to_history.sh</sourceFile>
                                    <outputDirectory>${basedir}/target/bin</outputDirectory>
                                </fileResource>
                                <fileResource>
                                    <sourceFile>${basedir}/../script/mv_dialogflow_log.sh</sourceFile>
                                    <outputDirectory>${basedir}/target/bin</outputDirectory>
                                </fileResource>
                                <fileResource>
                                    <sourceFile>${basedir}/../script/dockerentry.sh</sourceFile>
                                    <outputDirectory>${basedir}/target/bin</outputDirectory>
                                </fileResource>
                                <fileResource>
                                    <sourceFile>${basedir}/../script/Dockerfile</sourceFile>
                                    <outputDirectory>${basedir}/target</outputDirectory>
                                </fileResource>
                            </fileResources>
                            <parameterFile>${basedir}/../script/build_parameter.json</parameterFile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--部署api module时跳过本module-->
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>