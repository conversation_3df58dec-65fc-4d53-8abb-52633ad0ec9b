package com.yiwise.dialogflow.config;

import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.helper.EnvironmentConstants;
import com.yiwise.dialogflow.interceptor.AuthFilter;
import com.yiwise.dialogflow.interceptor.LoginInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;

@Order(value = Ordered.HIGHEST_PRECEDENCE)
@Configuration
@EnableWebMvc
@Slf4j
public class WebConfig extends MvcConfig {

    @Bean
    public FilterRegistrationBean<AuthFilter> authenticationFilterRegistration() {
        FilterRegistrationBean<AuthFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new AuthFilter());
        registration.addUrlPatterns("/apiBot/v3/chatService/**"); // 拦截对话接口(异步)
        return registration;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {

    }
}
