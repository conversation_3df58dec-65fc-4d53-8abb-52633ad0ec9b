package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.api.SpecialAnswerConfigApi;
import com.yiwise.dialogflow.api.dto.response.specialAnswerConfig.SimpleSpecialAnswerConfig;
import com.yiwise.dialogflow.service.BotRefService;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.SpecialAnswerConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
public class SpecialAnswerConfigApiController implements SpecialAnswerConfigApi {
    @Resource
    private SpecialAnswerConfigService specialAnswerConfigService;

    @Resource
    private BotRefService botRefService;

    @Resource
    private BotService botService;

    @Override
    public List<SimpleSpecialAnswerConfig> getByDialogFlowId(Long dialogFlowId) {
        botService.checkBotExistAndThrowByDialogFlowId(dialogFlowId);
        return specialAnswerConfigService.getSimpleListByDialogFlowId(dialogFlowId);
    }

    @Override
    public Boolean update(Long tenantId, Long userId, SimpleSpecialAnswerConfig simpleSpecialAnswerConfig) {
        if (!botRefService.checkHasPermission(simpleSpecialAnswerConfig.getBotId(), tenantId)) {
            throw new ComException(ComErrorCode.FORBIDDEN, "无权限");
        }
        specialAnswerConfigService.updateBySimpleAnswerConfig(simpleSpecialAnswerConfig, userId);
        return true;
    }
}
