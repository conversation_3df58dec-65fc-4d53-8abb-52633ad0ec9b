package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.annotation.auth.InnerOnly;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.dialogflow.api.BotConfigApi;
import com.yiwise.dialogflow.service.BotService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Set;


@Slf4j
@RestController
public class BotConfigApiController implements BotConfigApi {

    @Resource
    private BotService botService;

    @InnerOnly
    @NoLogin
    @Override
    public Set<String> getConcernNameSetByDialogFlowId(Long dialogFlowId) {
        return botService.getConcernNameSetByDialogFlowId(dialogFlowId);
    }

    @InnerOnly
    @NoLogin
    @Override
    public boolean checkEnableHumanInterventionByDialogFlowId(Long dialogFlowId) {
        return botService.checkEnableHumanInterventionByDialogFlowId(dialogFlowId);
    }
}
