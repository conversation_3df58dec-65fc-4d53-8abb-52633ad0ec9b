package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.annotation.auth.InnerOnly;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.api.BotApi;
import com.yiwise.dialogflow.api.dto.request.*;
import com.yiwise.dialogflow.api.dto.response.*;
import com.yiwise.dialogflow.api.enums.V3BotTypeEnum;
import com.yiwise.dialogflow.service.BotRefService;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.OutsideResourceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 为第三方
 */
@Slf4j
@Validated
@RestController
public class BotApiController implements BotApi {

    @Resource
    private BotService botService;

    @Resource
    private BotRefService botRefService;

    @Resource
    private OutsideResourceService outsideResourceService;

    @InnerOnly
    @NoLogin
    @Override
    public List<SimpleBotInfo> getListByIdList(@RequestBody BotListRequest request) {
        return botService.querySimpleBotInfoList(request);
    }

    @Override
    public SimpleBotApprovalResult approval(BotApprovalRequest request) {
        if (!botRefService.checkHasPermissionByDialogFlowId(request.getDialogFlowId(), request.getTenantId())) {
            throw new ComException(ComErrorCode.FORBIDDEN, "无权限");
        }
        return botService.publishAndApproval(request);
    }

    @Override
    public Boolean checkHasPermissionByDialogFlowId(Long dialogFlowId, Long tenantId) {
        return botRefService.checkHasPermissionByDialogFlowId(dialogFlowId, tenantId);
    }

    @Override
    public Long copyBot(CopyBotRequest request) {
        return botService.copyBotAndSubmitIntentTrain(request);
    }

    @Override
    public void bindBot(BotBindRequest bindRequest) {
        if (CollectionUtils.isEmpty(bindRequest.getDialogFlowIds())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "dialogFlowIds不能为空");
        }
        if (Objects.isNull(bindRequest.getTenantId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "tenantId不能为空");
        }
        botService.bind(bindRequest.getDialogFlowIds(), bindRequest.getTenantId(), bindRequest.getUserId());
    }

    @Override
    public void unbind(BotBindRequest bindRequest) {
        if (CollectionUtils.isEmpty(bindRequest.getDialogFlowIds())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "dialogFlowIds不能为空");
        }
        botService.unbind(bindRequest.getDialogFlowIds(), bindRequest.getUserId());
    }

    @Override
    public Map<Long, Set<Long>> queryCallOutSmsTemplateIdSetByDialogFlowIdList(QueryCallOutUsedSmsTemplateIdRequest request) {
        return outsideResourceService.queryCallOutSmsTemplateIdSetByDialogFlowIdList(request.getDialogFlowIdList());
    }

    @Override
    public boolean checkBindStatusByDialogFlowId(Long tenantId, Long dialogFlowId) {
        return botRefService.checkHasPermissionByDialogFlowId(dialogFlowId, tenantId);
    }

    @Override
    public List<SimpleBotInfo> queryPublishedBotListByTenantId(Long tenantId, String searchWord) {
        return botService.queryPublishedBotList(tenantId, searchWord);
    }

    @Override
    public BotInfo getBotInfoByDialogFlowId(Long tenantId, Long dialogFlowId) {
        return botService.getByDialogFlowId(tenantId, dialogFlowId);
    }

    @Override
    public BotExportInfo exportBotInfo(Long tenantId, Long dialogFlowId) {
        return botService.exportBotInfo(tenantId, dialogFlowId);
    }

    @Override
    public List<BotInfo> getAllListByTenantId(Long tenantId) {
        return botService.getAllListByTenantId(tenantId, null);
    }

    @Override
    public List<BotInfo> getAllCommonBotListByTenantId(Long tenantId) {
        return botService.getAllListByTenantId(tenantId, V3BotTypeEnum.COMMON);
    }

    @Override
    public List<BotInfo> getDetailListByIdList(BotListRequest request) {
        return botService.getDetailListByIdList(request);
    }

    @Override
    public List<MagicBotTemplateInfo> getMagicBotTemplateByIdList(BotListRequest request) {
        return botService.getMagicBotTemplateByIdList(request);
    }

    @Override
    public MagicBotInfo createMagicBot(MagicBotCreateRequest request) {
        return botService.createMagicBot(request);
    }

    @Override
    public Boolean preCreateMagicBot(MagicBotCreateRequest request) {
        return botService.preCreateMagicBot(request);
    }

    @Override
    public Long getDialogFlowIdByBotId(Long botId) {
        return botRefService.getDialogFlowId(botId);
    }

    @Override
    public BotAttributeDTO getBotAttributeByDialogFlowId(Long dialogFlowId) {
        return botService.getBotAttributeByDialogFlowId(dialogFlowId);
    }

    @Override
    public PageResultObject<SimpleBotInfo> searchBot(BotSearchRequest request) {
        return botService.searchBot(request);
    }

    @Override
    public Boolean preCreateMagicActivityConfig(MagicActivityConfigCreateRequest request) {
        return botService.preCreateMagicActivityConfig(request);
    }

    @Override
    public String createMagicActivityConfig(MagicActivityConfigCreateRequest request) {
        return botService.createMagicActivityConfig(request);
    }

}
