package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.api.KnowledgeApi;
import com.yiwise.dialogflow.api.dto.response.knowledge.SimpleKnowledge;
import com.yiwise.dialogflow.service.BotRefService;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.KnowledgeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
public class KnowledgeApiController implements KnowledgeApi {

    @Resource
    private KnowledgeService knowledgeService;

    @Resource
    private BotRefService botRefService;

    @Resource
    private BotService botService;

    @Override
    public List<SimpleKnowledge> getKnowledgeListByDialogFlowId(Long dialogFlowId) {
        botService.checkBotExistAndThrowByDialogFlowId(dialogFlowId);
        return knowledgeService.getSimpleKnowledgeListByDialogFlowId(dialogFlowId);
    }

    @Override
    public Boolean updateKnowledge(Long tenantId, Long userId, SimpleKnowledge simpleKnowledge) {
        if (!botRefService.checkHasPermission(simpleKnowledge.getBotId(), tenantId)) {
            throw new ComException(ComErrorCode.FORBIDDEN, "无权限");
        }
        knowledgeService.updateBySimpleKnowledge(simpleKnowledge, userId);
        return true;
    }

    @Override
    public List<SimpleKnowledge> getKnowledgeListByGroupName(Long botId, String groupName) {
        return knowledgeService.queryByGroupName(botId, groupName);
    }
}
