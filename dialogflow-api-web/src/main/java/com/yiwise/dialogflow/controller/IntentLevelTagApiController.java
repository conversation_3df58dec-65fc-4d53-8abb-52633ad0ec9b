package com.yiwise.dialogflow.controller;

import com.yiwise.dialogflow.api.IntentLevelTagApi;
import com.yiwise.dialogflow.api.dto.request.IntentLevelTagListRequest;
import com.yiwise.dialogflow.api.dto.response.SimpleIntentLevelTag;
import com.yiwise.dialogflow.service.remote.IntentLevelTagDetailService;
import com.yiwise.dialogflow.service.remote.IntentLevelTagService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@RestController
public class IntentLevelTagApiController implements IntentLevelTagApi {

    @Resource
    private IntentLevelTagDetailService intentLevelTagDetailService;

    @Resource
    private IntentLevelTagService intentLevelTagService;

    @Override
    public SimpleIntentLevelTag getSimpleDetailInfo(Long intentLevelTagId) {
        SimpleIntentLevelTag simpleIntentLevelTag = new SimpleIntentLevelTag();
        simpleIntentLevelTag.setIntentLevelTagId(intentLevelTagId);
        String intentLevelTagName = intentLevelTagService.getIntentLevelTagName(intentLevelTagId);
        simpleIntentLevelTag.setIntentLevelTagName(intentLevelTagName);
        simpleIntentLevelTag.setDetailList(intentLevelTagDetailService.getIntentLevelTagDetailList(intentLevelTagId));
        return simpleIntentLevelTag;
    }

    @Override
    public SimpleIntentLevelTag getSimpleDetailInfoByDialogFlowId(Long dialogFlowId) {
        return intentLevelTagService.getSimpleDetailInfoByDialogFlowId(dialogFlowId);
    }

    @Override
    public List<SimpleIntentLevelTag> getSimpleDetailListByIntentLevelTagIdList(IntentLevelTagListRequest request) {
        if (CollectionUtils.isEmpty(request.getIntentLevelTagIdList())) {
            return Collections.emptyList();
        }
        // todo 批量查询
        return request.getIntentLevelTagIdList().stream()
                .map(this::getSimpleDetailInfo)
                .collect(Collectors.toList());
    }
}
