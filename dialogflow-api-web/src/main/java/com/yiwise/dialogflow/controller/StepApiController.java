package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.api.StepApi;
import com.yiwise.dialogflow.api.dto.response.step.SimpleNode;
import com.yiwise.dialogflow.api.dto.response.step.SimpleStep;
import com.yiwise.dialogflow.service.BotRefService;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.StepNodeService;
import com.yiwise.dialogflow.service.StepService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
public class StepApiController implements StepApi {

    @Resource
    private StepService stepService;

    @Resource
    private StepNodeService stepNodeService;

    @Resource
    private BotRefService botRefService;

    @Resource
    private BotService botService;

    @Override
    public List<SimpleStep> getStepListByDialogFlowId(Long dialogFlowId) {
        botService.checkBotExistAndThrowByDialogFlowId(dialogFlowId);
        return stepService.getSimpleStepListByDialogFlowId(dialogFlowId);
    }

    @Override
    public Boolean update(Long tenantId, Long userId, SimpleNode node) {
        if (!botRefService.checkHasPermission(node.getBotId(), tenantId)) {
            throw new ComException(ComErrorCode.FORBIDDEN, "无权限");
        }
        stepNodeService.updateBySimpleNode(node, userId);
        return true;
    }
}
