package com.yiwise.dialogflow.controller;

import com.yiwise.dialogflow.api.MagicActivityConfigApi;
import com.yiwise.dialogflow.api.dto.request.IdListDTO;
import com.yiwise.dialogflow.api.dto.response.activity.MagicActivityConfigDTO;
import com.yiwise.dialogflow.service.magic.MagicActivityConfigService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class MagicActivityConfigController implements MagicActivityConfigApi {

    @Resource
    private MagicActivityConfigService magicActivityConfigService;

    @Override
    public List<MagicActivityConfigDTO> queryByActivityIdList(IdListDTO idList) {
        return magicActivityConfigService.getByActivityIdList(idList.getIdList());
    }
}
