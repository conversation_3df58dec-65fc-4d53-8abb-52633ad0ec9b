package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.dialogflow.engine.service.PredictTestService;
import com.yiwise.dialogflow.entity.vo.PredictTestRequestVO;
import com.yiwise.dialogflow.entity.vo.PredictTestResultVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;

/**
 * 意图预测测试
 */
@RestController
@RequestMapping("apiBot/v3/predict")
public class PredictTestController {

    @Resource
    private PredictTestService predictTestService;

    /**
     * 对节点进行意图预测
     */
    @NoLogin
    @PostMapping("/test")
    public Mono<PredictTestResultVO> test(@RequestBody PredictTestRequestVO request) {
        return predictTestService.predictTest(request);
    }
}
