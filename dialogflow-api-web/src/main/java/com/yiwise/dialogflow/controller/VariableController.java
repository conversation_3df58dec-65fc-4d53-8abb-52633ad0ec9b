package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.annotation.auth.InnerOnly;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.dialogflow.api.VariableApi;
import com.yiwise.dialogflow.api.dto.response.variable.VariableInfoDTO;
import com.yiwise.dialogflow.service.VariableService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Validated
@RestController
public class VariableController implements VariableApi {

    @Resource
    private VariableService variableService;

    @NoLogin
    @InnerOnly
    @Override
    public Set<String> getUsedVariableNameSetByDialogFlowId(Long dialogFlowId) {
        return variableService.getUsedVariableNameSetByDialogFlowId(dialogFlowId, null);
    }

    @NoLogin
    @InnerOnly
    @Override
    public Set<String> getUsedVariableNameSetByDialogFlowIdAndCallJobId(Long dialogFlowId, Long callJobId) {
        return variableService.getUsedVariableNameSetByDialogFlowId(dialogFlowId, callJobId);
    }

    @NoLogin
    @InnerOnly
    @Override
    public List<VariableInfoDTO> getUsingVariableListByDialogFlowId(Long dialogFlowId) {
        return variableService.getUsedVariableListByDialogFlowId(dialogFlowId);
    }
    @NoLogin
    @InnerOnly
    @Override
    public List<VariableInfoDTO> getUsingTemplateVariableListByDialogFlowId(Long dialogFlowId) {
        return variableService.getUsingTemplateVariableListByDialogFlowId(dialogFlowId);
    }

    @Override
    public List<String> getAllDynamicVariableNameByDialogFlowId(Long dialogFlowId) {
        return variableService.listAllDynamicVariableNameByDialogFlowId(dialogFlowId);
    }

}
