package com.yiwise.dialogflow.controller;

import com.yiwise.dialogflow.api.DouyinCallbackApi;
import com.yiwise.dialogflow.api.dto.request.douyin.DouyinBotInfoQueryDTO;
import com.yiwise.dialogflow.api.dto.response.douyin.DouyinBotInfoDTO;
import com.yiwise.dialogflow.service.thirdpart.douyin.DouyinCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
public class DouyinCallbackController implements DouyinCallbackApi {

    @Resource
    private DouyinCallbackService douyinCallbackService;

    @Override
    public List<DouyinBotInfoDTO> queryByCondition(DouyinBotInfoQueryDTO query) {
        return douyinCallbackService.queryByCondition(query);
    }
}
