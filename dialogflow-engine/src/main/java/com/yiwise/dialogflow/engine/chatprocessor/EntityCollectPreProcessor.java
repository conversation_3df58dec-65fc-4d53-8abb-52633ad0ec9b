package com.yiwise.dialogflow.engine.chatprocessor;

import com.alibaba.fastjson2.JSON;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.engine.context.*;
import com.yiwise.dialogflow.engine.domain.EntityCollectContext;
import com.yiwise.dialogflow.engine.helper.SessionContextHelper;
import com.yiwise.dialogflow.engine.resource.*;
import com.yiwise.dialogflow.engine.utils.DebugLogUtils;
import com.yiwise.dialogflow.entity.bo.EntityValueBO;
import com.yiwise.dialogflow.entity.bo.entity.RuntimeEntityBO;
import com.yiwise.dialogflow.entity.enums.OriginInputCollectTypeEnum;
import com.yiwise.dialogflow.entity.po.OriginInputAssignConfigItem;
import com.yiwise.dialogflow.entity.po.VarAssignActionItemPO;
import com.yiwise.dialogflow.entity.po.VariablePO;
import com.yiwise.dialogflow.pattern.PatternEnhanceCache;
import com.yiwise.dialogflow.engine.service.EntityCollectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class EntityCollectPreProcessor extends AbstractChatPreProcessor {

    private static final EntityCollectService entityCollectService = AppContextUtils.getBean(EntityCollectService.class);

    private final RobotRuntimeResource resource;

    public EntityCollectPreProcessor(RobotRuntimeResource resource) {
        this.resource = resource;
    }

    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return "EntityCollectPreProcessor";
    }

    @Override
    protected void doProcess(SessionContext sessionContext, EventContext eventContext) {
        doProcessAsync(sessionContext, eventContext).block();
    }

    protected Mono<Void> doProcessAsync(SessionContext sessionContext, EventContext eventContext) {
        /*
         * 实体提取流程
         * 1. 从各个地方收集到本轮所有需要待提取的实体 id
         * 2. 执行实体提取
         * 3. 记录当前用户输入实体提取结果
         * 4. 对实体提取结果进行处理
         */
        AtomicReference<StepFlowContext> currentStepContextRef = new AtomicReference<>();

        // 获取当前节点待提取的实体列表
        return getWaitingCollectEntityIdSet(sessionContext, eventContext, currentStepContextRef)
                .flatMap(entityIdSet -> {
                    List<RuntimeEntityBO> entityList = entityIdSet.stream()
                            .map(resource.getEntityIdMap()::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                    if (CollectionUtils.isEmpty(entityList)) {
                        return Mono.empty();
                    }
                    log.info("需要提取的实体:{}", entityList.stream().map(RuntimeEntityBO::getName).collect(Collectors.joining(";")));
                    // 执行实体提取
                    EntityCollectContext entityCollectContext = EntityCollectContext.builder().sessionContext(sessionContext).eventContext(eventContext).robotRuntimeResource(resource).build();
                    Mono<List<EntityValueBO>> entityValueList = entityCollectService.collectAsync(eventContext.getUserInput(), entityList, entityCollectContext);

                    return entityValueList
                            .doOnNext(entityValue -> entityCollectValueConsumer(sessionContext, eventContext, entityValue, currentStepContextRef))
                            .flatMap(list -> Mono.empty());
                });
    }

    /**
     * 处理实体采集结果
     */
    private void entityCollectValueConsumer(SessionContext sessionContext,
                                            EventContext eventContext,
                                            List<EntityValueBO> entityValueList,
                                            AtomicReference<StepFlowContext> currentStepContextRef) {
        if (CollectionUtils.isEmpty(entityValueList)) {
            log.debug("实体提取结果为空");
            return;
        }
        log.info("实体提取结果:[{}]", JSON.toJSONString(entityValueList));
        eventContext.setEntityValueList(entityValueList);
        Map<String, List<EntityValueBO>> entityId2ValueListMap = MyCollectionUtils.listToMapList(entityValueList, EntityValueBO::getEntityId);

        // 记录实体提取结果
        recordCollectValue(sessionContext, eventContext, entityValueList);

        // 执行全局实体提取
        execGlobalAssign(sessionContext, eventContext, entityId2ValueListMap);

        // 执行当前节点的实体赋值
        if (Objects.nonNull(currentStepContextRef.get().getAssignConfig())) {
            NodeAssignConfigRuntime assignConfigRuntime = currentStepContextRef.get().getAssignConfig();
            execCurrentNodeAssign(sessionContext, eventContext, assignConfigRuntime, entityId2ValueListMap);
        }

        // 采集节点赋值
        if (CollectionUtils.isNotEmpty(currentStepContextRef.get().getCollectConfigList())) {
            for (NodeCollectConfigRuntime collectConfig : currentStepContextRef.get().getCollectConfigList()) {
                execCurrentNodeCollect(sessionContext, eventContext, collectConfig, entityId2ValueListMap);
            }
        }

    }

    private void execCurrentNodeCollect(SessionContext sessionContext,
                                        EventContext eventContext,
                                        NodeCollectConfigRuntime collectConfig,
                                        Map<String, List<EntityValueBO>> entityId2ValueListMap) {
        if (MapUtils.isEmpty(entityId2ValueListMap)
                || StringUtils.isBlank(collectConfig.getEntityId())
                || StringUtils.isBlank(collectConfig.getVariableId())) {
            return;
        }
        List<EntityValueBO> valueList = entityId2ValueListMap.get(collectConfig.getEntityId());
        if (CollectionUtils.isNotEmpty(valueList)) {
            String strValue = obtainDistinctValueIfNeeded(valueList, collectConfig.getVariableId());
            SessionContextHelper.variableAssign(resource, sessionContext, eventContext,
                    collectConfig.getVariableId(), strValue, collectConfig.getEntityId());
            DebugLogUtils.entityCollect(resource, eventContext, collectConfig.getEntityId(), valueList);
        }
    }

    private void recordCollectValue(SessionContext sessionContext, EventContext eventContext, List<EntityValueBO> entityValueList) {
        if (CollectionUtils.isEmpty(entityValueList)) {
            return;
        }
        UserInputCollectInfo userInputCollectInfo = new UserInputCollectInfo();
        userInputCollectInfo.setSequence(eventContext.getSeq());
        userInputCollectInfo.setUserInput(eventContext.getUserInput());
        userInputCollectInfo.setEntityValueList(entityValueList);
        sessionContext.getInputCollectInfoList().add(userInputCollectInfo);
    }

    private void execGlobalAssign(SessionContext sessionContext, EventContext eventContext, Map<String, List<EntityValueBO>> entityId2ValueListMap) {
        if (CollectionUtils.isNotEmpty(resource.getGlobalCollectList())) {
            for (GlobalCollectConfig globalCollectConfig : resource.getGlobalCollectList()) {
                List<EntityValueBO> valueList = entityId2ValueListMap.getOrDefault(globalCollectConfig.getEntityId(), Collections.emptyList());
                if (CollectionUtils.isNotEmpty(valueList) && globalCollectConfig.isEnableAssign()) {
                    String strValue = obtainDistinctValueIfNeeded(valueList, globalCollectConfig.getVariableId());
                    SessionContextHelper.variableAssign(resource, sessionContext, eventContext,
                            globalCollectConfig.getVariableId(), strValue, globalCollectConfig.getEntityId());
                    DebugLogUtils.globalEntityCollect(resource, eventContext, globalCollectConfig.getEntityId(), valueList);
                }
            }
        }
    }

    private String obtainDistinctValueIfNeeded(List<EntityValueBO> valueList, String varId) {
        Stream<String> stream = valueList.stream().map(EntityValueBO::getValue);
        Boolean needDistinct = Optional.ofNullable(varId)
                .map(resource.getVariableIdMap()::get)
                .map(VariablePO::getEnableDeduplicate)
                .map(BooleanUtils::isTrue)
                .orElse(false);
        if(needDistinct) {
            stream = stream.distinct();
        }
        return stream.collect(Collectors.joining(","));
    }

    /**
     * 执行当前节点的赋值
     */
    private void execCurrentNodeAssign(SessionContext sessionContext,
                                       EventContext eventContext,
                                       NodeAssignConfigRuntime assignConfigRuntime,
                                       Map<String, List<EntityValueBO>> entityId2ValueListMap) {

        if (Objects.nonNull(assignConfigRuntime.getEntityAssign()) && CollectionUtils.isNotEmpty(assignConfigRuntime.getEntityAssign().getAssignActionList())) {
            for (VarAssignActionItemPO assignAction : assignConfigRuntime.getEntityAssign().getAssignActionList()) {
                String entityId = assignAction.getEntityId();
                String variableId = assignAction.getVariableId();
                List<EntityValueBO> valueList = entityId2ValueListMap.get(entityId);
                if (CollectionUtils.isNotEmpty(valueList)) {
                    String strValue = obtainDistinctValueIfNeeded(valueList, variableId);
                    SessionContextHelper.variableAssign(resource, sessionContext, eventContext,
                            variableId, strValue, entityId);
                    DebugLogUtils.entityCollect(resource, eventContext, entityId, valueList);
                }
            }
        }

        if (Objects.nonNull(assignConfigRuntime.getOriginInputAssign()) && CollectionUtils.isNotEmpty(assignConfigRuntime.getOriginInputAssign().getAssignActionList())) {
            for (OriginInputAssignConfigItem assignAction : assignConfigRuntime.getOriginInputAssign().getAssignActionList()) {
                String entityId = assignAction.getEntityId();
                String variableId = assignAction.getVariableId();
                List<EntityValueBO> valueList = entityId2ValueListMap.get(entityId);
                if (CollectionUtils.isNotEmpty(valueList)) {
                    String strValue = obtainDistinctValueIfNeeded(valueList, variableId);
                    SessionContextHelper.variableAssign(resource, sessionContext, eventContext,
                            variableId, strValue, entityId);
                    DebugLogUtils.entityCollect(resource, eventContext, entityId, valueList);
                }
            }
        }
    }

    /**
     * 获取待提取的实体 id 集合
     */
    private Mono<Set<String>> getWaitingCollectEntityIdSet(SessionContext sessionContext,
                                                     EventContext eventContext,
                                                     AtomicReference<StepFlowContext> currentStepContextRef) {
        Set<String> entityIdSet = new HashSet<>();

        // 获取全局待提取的实体列表
        if (CollectionUtils.isNotEmpty(resource.getGlobalCollectList())) {
            entityIdSet.addAll(
                    resource.getGlobalCollectList().stream()
                            .filter(globalCollectConfig -> {
                                if (StringUtils.isNotBlank(globalCollectConfig.getSourceId())
                                        && sessionContext.getExecutedCollectNodeIdSet().contains(globalCollectConfig.getSourceId())) {
                                    log.info("节点:{} 对应的全局采集已经执行, 不再继续处理", globalCollectConfig.getSourceLabel());
                                    return false;
                                }
                                return true;
                            })
                            .map(GlobalCollectConfig::getEntityId)
                            .collect(Collectors.toList())
            );
        }

        SessionContextHelper.getCurrentActiveStepFlowContext(sessionContext)
                .ifPresent(stepFlowContext -> {
                    currentStepContextRef.set(stepFlowContext);
                    if (CollectionUtils.isNotEmpty(stepFlowContext.getCollectConfigList())) {
                        // 从采集节点中获取待提取的实体 id
                        entityIdSet.addAll(
                                stepFlowContext.getCollectConfigList().stream()
                                        .map(NodeCollectConfigRuntime::getEntityId)
                                        .collect(Collectors.toSet())
                        );
                    }
                });

        // 从当前流程中获取主流程中待提取的实体 id
        SessionContextHelper.getCurrentMainStepFlowContext(sessionContext)
                .ifPresent(stepFlowContext -> {
                    if (stepFlowContext.equals(currentStepContextRef.get())) {
                        return;
                    }
                    NodeAssignConfigRuntime assignConfig = stepFlowContext.getAssignConfig();
                    if (Objects.nonNull(assignConfig)
                            && Objects.nonNull(assignConfig.getEntityAssign())
                            && BooleanUtils.isTrue(assignConfig.getEntityAssign().getEnableCollectOnPullback())) {
                        entityIdSet.addAll(assignConfig.getEntityAssign().getDependentEntityIdSet());
                    }
                });

        Mono<Set<String>> mono = Mono.justOrEmpty(SessionContextHelper.getCurrentActiveStepFlowContext(sessionContext))
                        .flatMap(stepFlowContext -> {
                            NodeAssignConfigRuntime assignConfig = stepFlowContext.getAssignConfig();
                            if (Objects.nonNull(assignConfig)) {
                                return getEntityIdFromAssignConfig(sessionContext, eventContext, assignConfig);
                            }
                            return Mono.just(Collections.emptySet());
                        });

        return Mono.zip(mono, Mono.just(entityIdSet), (a, b) -> {
            Set<String> result = new HashSet<>();
            result.addAll(a);
            result.addAll(b);
            return result;
        });
    }

    /**
     * 从动态赋值配置中获取待提取的实体 id
     */
    private Mono<Set<String>> getEntityIdFromAssignConfig(SessionContext sessionContext, EventContext eventContext, NodeAssignConfigRuntime assignConfig) {
        Set<String> entityIdSet = new HashSet<>();
        if (Objects.nonNull(assignConfig.getEntityAssign())) {
            entityIdSet.addAll(assignConfig.getEntityAssign().getDependentEntityIdList());
        }
        if (Objects.isNull(assignConfig.getOriginInputAssign()) || CollectionUtils.isEmpty(assignConfig.getOriginInputAssign().getAssignActionList())) {
            return Mono.just(entityIdSet);
        }


        OriginInputAssignConfigItem assignConfigItem = assignConfig.getOriginInputAssign().getAssignActionList().get(0);

        if (!OriginInputCollectTypeEnum.isFiltered(assignConfigItem.getOriginInputCollectType())) {
            entityIdSet.add(assignConfigItem.getEntityId());
            return Mono.just(entityIdSet);
        }
        // 原话采集过滤
        boolean match = false;
        if (CollectionUtils.isNotEmpty(assignConfigItem.getFilteredRegexList())) {
            String matchRegex = assignConfigItem.getFilteredRegexList().stream().filter(p ->
                            PatternEnhanceCache.getOrCreate(p, PatternCache.compile(p), false).find(eventContext.getUserInput()).isPresent())
                    .collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(matchRegex)) {
                match = true;
                DebugLogUtils.commonDebugLog(eventContext, "命中过滤关键词：【" + matchRegex + "】");
            }
        }

        if (match) {
            return Mono.just(entityIdSet);
        }

        if (CollectionUtils.isEmpty(assignConfigItem.getFilteredIntentIdList())) {
            entityIdSet.add(assignConfigItem.getEntityId());
            return Mono.just(entityIdSet);
        }

        IntentPredictPreProcessor predictPreProcessor = new IntentPredictPreProcessor(resource);
        Mono<Set<String>> mono = predictPreProcessor.processAsync(sessionContext, eventContext)
                .then(Mono.fromSupplier(() -> {
                    boolean isMatch = false;
                    if (CollectionUtils.isNotEmpty(eventContext.getCandidateIntentIdList())) {
                        List<String> intersection = ListUtils.intersection(assignConfigItem.getFilteredIntentIdList(), eventContext.getCandidateIntentIdList());
                        if (CollectionUtils.isNotEmpty(intersection)) {
                            isMatch = true;
                            String matchIntentNames = intersection.stream().map(intentId -> resource.getIntentId2NameMap().get(intentId))
                                    .filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
                            DebugLogUtils.commonDebugLog(eventContext, "命中过滤意图：【" + matchIntentNames + "】");
                        }
                    }
                    if (!isMatch) {
                        return Collections.singleton(assignConfigItem.getEntityId());
                    }
                    return new HashSet<>();
                }));

        return Mono.zip(mono, Mono.just(entityIdSet), (a, b) -> {
            Set<String> result = new HashSet<>();
            result.addAll(a);
            result.addAll(b);
            return result;
        });
    }
}
