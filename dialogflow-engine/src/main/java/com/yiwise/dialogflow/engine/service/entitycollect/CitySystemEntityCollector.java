package com.yiwise.dialogflow.engine.service.entitycollect;

import com.yiwise.dialogflow.entity.bo.EntityValueBO;
import com.yiwise.dialogflow.entity.enums.SystemEntityCategoryEnum;
import com.yiwise.dialogflow.utils.LocationUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Flux;

import java.util.*;

@Slf4j
public class CitySystemEntityCollector implements SystemEntityCollector {

    @Override
    public void register(Map<SystemEntityCategoryEnum, SystemEntityCollector> registry) {
        registry.put(SystemEntityCategoryEnum.CITY, this);
        registry.put(SystemEntityCategoryEnum.PROVINCE, this);
        registry.put(SystemEntityCategoryEnum.COUNTRY, this);
    }

    @Override
    public Flux<EntityValueBO> asyncCollect(SystemEntityCategoryEnum category, String userInput, String userInputPinyin) {
        if (StringUtils.isBlank(userInput)) {
            return Flux.empty();
        }
        List<EntityValueBO> result = new ArrayList<>();
        List<String> list;
        switch (category) {
            case CITY:
                list = LocationUtil.CITY;
                break;
            case PROVINCE:
                list = LocationUtil.PROVINCE;
                break;
            case COUNTRY:
                list = LocationUtil.COUNTY;
                break;
            default:
                list = Collections.emptyList();
                break;
        }
        list.forEach(value -> {
            int indexOf = 0;
            while ((indexOf = userInput.indexOf(value, indexOf)) != -1) {
                EntityValueBO entityValue = new EntityValueBO();
                entityValue.setOriginValue(value);
                entityValue.setValue(value);
                entityValue.setStartOffset(indexOf);
                entityValue.setEndOffset(indexOf + value.length() - 1);
                entityValue.setSystemEntityCategory(category);
                result.add(entityValue);
                indexOf = indexOf + value.length();
            }
        });
        return Flux.fromIterable(result);
    }
}
