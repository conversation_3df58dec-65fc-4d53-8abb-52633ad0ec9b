package com.yiwise.dialogflow.engine.helper;

import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.action.ChatAction;
import com.yiwise.dialogflow.engine.share.action.HangupAction;
import com.yiwise.dialogflow.engine.share.action.JumpAction;
import com.yiwise.dialogflow.engine.share.action.WaitAction;
import com.yiwise.dialogflow.engine.share.enums.ActionScopeEnum;
import com.yiwise.dialogflow.engine.share.enums.ActionTypeEnum;
import com.yiwise.dialogflow.entity.enums.ActionCategoryEnum;
import com.yiwise.dialogflow.entity.po.intent.RuleActionParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class ActionHelper {

    public static HangupAction generateHangupAction(RobotRuntimeResource resource) {
        if (BooleanUtils.isTrue(resource.getHangupDelay())) {
            if (Objects.nonNull(resource.getHangupDelayMs())) {
                return new HangupAction(resource.getHangupDelay(), resource.getHangupDelayMs());
            } else {
                log.warn("数据异常, 开启延迟挂机, 但是延迟时间为空");
            }
        }
        return new HangupAction();
    }
    public static HangupAction generateHangupAction() {
        return new HangupAction();
    }

    public static List<JumpAction> getJumpActionList(List<ChatAction> actionList) {
        if (CollectionUtils.isEmpty(actionList)) {
            return Collections.emptyList();
        }
        return actionList.stream()
                .filter(action -> ActionScopeEnum.ENGINE.equals(action.getScope()))
                .filter(action -> ActionTypeEnum.JUMP.equals(action.getType()))
                .map(action -> (JumpAction)action)
                .collect(Collectors.toList());
    }
    public static List<WaitAction> getWaitActionList(List<ChatAction> actionList) {
        if (CollectionUtils.isEmpty(actionList)) {
            return Collections.emptyList();
        }
        return actionList.stream()
                .filter(action -> ActionScopeEnum.INTERACTION.equals(action.getScope()))
                .filter(action -> ActionTypeEnum.WAIT.equals(action.getType()))
                .map(action -> (WaitAction)action)
                .collect(Collectors.toList());
    }

    public static boolean containsAddWechatAction(List<RuleActionParam> actionList) {
        return CollectionUtils.isNotEmpty(actionList) &&
                actionList.stream().anyMatch(item -> ActionCategoryEnum.ADD_WECHAT.equals(item.getActionType()));
    }
}
