package com.yiwise.dialogflow.engine.utils;

import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.enums.SpecialChatModeEnum;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.resource.StepRuntime;
import com.yiwise.dialogflow.engine.share.request.UserSilenceEvent;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.engine.share.response.AnswerResult;
import com.yiwise.dialogflow.entity.bo.EntityValueBO;
import com.yiwise.dialogflow.entity.bo.algorithm.PredictResult;
import com.yiwise.dialogflow.entity.bo.entity.RuntimeEntityBO;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class DebugLogUtils {

    private static final Logger log = LoggerFactory.getLogger(DebugLogUtils.class);
    /**
     * 意向分支/问答知识预测详情
     */
    private static String predictDetail(PredictResult predictResult) {
        if (predictResult == null) {
            return "";
        }
        StringBuilder sb = new StringBuilder();

        if (PredictTypeEnum.COMPOSITE.equals(predictResult.getPredictType())
                && CollectionUtils.isNotEmpty(predictResult.getCompositeIntentConditionResults())) {
            sb.append("命中组合意图:").append(predictResult.getIntentName());
            sb.append(", 置信度:").append(formatConfidence(predictResult.getConfidence()));

            // 对每个条件的结果进行打印
            sb.append(", 命中条件: ");
            List<String> conditionList = predictResult.getCompositeIntentConditionResults()
                    .stream()
                    .map(subPredictResult -> {
                        if (PredictTypeEnum.ALGORITHM.equals(subPredictResult.getPredictType())) {
                            return subPredictResult.getIntentName() + ":" + formatConfidence(subPredictResult.getConfidence());
                        } else {
                            return subPredictResult.getIntentName() + ":" + subPredictResult.getKeyword();
                        }
                    }).collect(Collectors.toList());
            sb.append(StringUtils.join(conditionList, ", "));
            sb.append(predictIntentProperties(predictResult));
            return sb.toString();
        } else {
            sb.append("命中意图:").append(predictResult.getIntentName());
            if (PredictTypeEnum.ALGORITHM.equals(predictResult.getPredictType()) || PredictTypeEnum.COMPOSITE.equals(predictResult.getPredictType())) {
                if (IntentPriorityLevelEnum.NLU_INTENT.equals(predictResult.getIntentPriorityLevel())) {
                    sb.append(", 命中描述:").append(predictResult.getMatchText());
                } else {
                    sb.append(", 置信度:").append(formatConfidence(predictResult.getConfidence()));
                }
            }
            sb.append(predictIntentProperties(predictResult));
            if (PredictTypeEnum.REGEX.equals(predictResult.getPredictType())) {
                sb.append(", 关键词:").append(predictResult.getKeyword());
            }
            return sb.toString();
        }
    }

    private static String predictIntentProperties(PredictResult predictResult) {
        if (Objects.nonNull(predictResult.getSimpleIntentInfo()) && Objects.nonNull(predictResult.getSimpleIntentInfo().getIntentProperties())) {
            return ", 意图属性:" + predictResult.getSimpleIntentInfo().getIntentProperties().getDesc();
        } else {
            return ", 意图属性:--";
        }
    }

    private static String predictIntentName(PredictResult predictResult) {
        if (predictResult == null) {
            return "";
        }
        return "命中意图:" + predictResult.getIntentName();
    }

    private static String formatConfidence(Double confidence) {
        if (confidence == null) {
            return "--";
        }
        return String.format("%.4f", confidence);
    }

    public static void predictDetail(EventContext eventContext, PredictResult predictResult) {
        if (BooleanUtils.isTrue(eventContext.isRepeatAnswer())) {
            log.info("重复上一句, 不需要重复生成预测信息");
            return;
        }
        String sb = predictDetail(predictResult);
        if (StringUtils.isNotBlank(sb)) {
            eventContext.getDebugLog().add(sb);
        }
        sb = predictIntentName(predictResult);
        if (StringUtils.isNotBlank(sb)) {
            eventContext.getSimpleDebugLog().add(sb);
        }
    }

    public static void uninterruptedPredictDetail(EventContext eventContext, PredictResult predictResult) {
        String sb = predictDetail(predictResult);
        if (StringUtils.isNotBlank(sb)) {
            sb = "【" + sb.replace("命中意图", "预测意图") +"】";
            eventContext.getDebugLog().add(sb);
        }
        sb = predictIntentName(predictResult);
        if (StringUtils.isNotBlank(sb)) {
            sb = "【" + sb.replace("命中意图", "预测意图") +"】";
            eventContext.getSimpleDebugLog().add(sb);
        }
    }

    public static void defaultIntent(EventContext eventContext) {
        eventContext.getDebugLog().add("未命中意图，走兜底意图");
        eventContext.getSimpleDebugLog().add("未命中意图，走兜底意图");
    }

    private static String matchAnswer(AnswerResult answer) {
        if (Objects.isNull(answer.getLocate())) {
            return "";
        }
        String name = "";
        String typeName = "";
        String label = "";
        switch (answer.getLocate().getAnswerSource()) {
            case KNOWLEDGE:
                name = answer.getLocate().getKnowledgeName();
                typeName = "知识";
                label = "(" + answer.getLocate().getKnowledgeLabel() + ")";
                break;
            case STEP:
                name = answer.getLocate().getNodeLabel();
                typeName = "节点";
                break;
            case SPECIAL_ANSWER:
            default:
                name = answer.getLocate().getSpecialAnswerConfigName();
                typeName = "特殊语境";
                label = "(" + answer.getLocate().getSpecialAnswerConfigLabel() + ")";
                break;
        }
        return "命中" + typeName + ":" + name + label;
    }

    private static String simpleMatchAnswer(AnswerResult answer) {
        if (Objects.isNull(answer.getLocate())) {
            return "";
        }
        switch (answer.getLocate().getAnswerSource()) {
            case KNOWLEDGE:
            case STEP:
                return matchAnswer(answer);
            case SPECIAL_ANSWER:
            default:
                return answer.getLocate().getSpecialAnswerConfigName();
        }
    }

    /**
     * 打断且未命中任何意图
     * @param eventContext
     * @return
     */
    public static void interruptAndAiUnknown(EventContext eventContext) {
        if (eventContext.getSpecialChatModes().contains(SpecialChatModeEnum.UNINTERRUPTED)) {
            eventContext.getDebugLog().add("不可打断, 选择的知识/流程/特殊语境/节点分支未命中, 继续播报");
            eventContext.getSimpleDebugLog().add("不可打断, 选择的知识/流程/特殊语境/节点分支未命中, 继续播报");
        } else {
            eventContext.getDebugLog().add("打断成功，意图不明确，继续播报");
            eventContext.getSimpleDebugLog().add("打断成功，意图不明确，继续播报");
        }
    }

    public static void matchAnswer(EventContext eventContext, AnswerResult answer) {
        eventContext.getDebugLog().add(matchAnswer(answer));
        eventContext.getSimpleDebugLog().add(simpleMatchAnswer(answer));
    }

    public static void userSilence(EventContext eventcontext) {
        String log = "命中意图: 客户无应答";
        if ( eventcontext.getOriginEventParam() instanceof UserSilenceEvent) {
            UserSilenceEvent event = (UserSilenceEvent) eventcontext.getOriginEventParam();
            if (Objects.nonNull(event.getSilenceMs())) {
                log = "无应答时长: " + event.getSilenceMs() + "ms, 命中意图: 客户无应答";
            }
        }
        eventcontext.getDebugLog().add(log);
        eventcontext.getSimpleDebugLog().add(log);
    }

    public static void aiRepeatAnswer(EventContext eventcontext) {
        eventcontext.getDebugLog().add("命中特殊语境: AI重复上句语音");
        eventcontext.getSimpleDebugLog().add("AI重复上句语音");
    }

    public static void inaudibleRepeatAnswer(EventContext eventcontext) {
        eventcontext.getDebugLog().add("跳出特殊语境：听不清楚AI说话，重播进入该语境前话术。");
        eventcontext.getSimpleDebugLog().add("跳出特殊语境：听不清楚AI说话，重播进入该语境前话术。");
    }

    public static void matchNode(EventContext eventContext, DialogBaseNodePO node) {
        if (node == null) {
            return;
        }
        String debugLog = "当前节点编号:" + node.getLabel();
        eventContext.getDebugLog().add(debugLog);
    }

    public static void matchStepDetail(EventContext eventContext, StepRuntime stepRuntime) {
        if (stepRuntime == null) {
            return;
        }
        eventContext.getDebugLog().add(
                "命中" + stepRuntime.getType().getDesc() + "[" + stepRuntime.getLabel() + ":" + stepRuntime.getName() + "]"
        );
        eventContext.getSimpleDebugLog().add(
                "命中" + stepRuntime.getType().getDesc() + "[" +stepRuntime.getLabel() + ":" + stepRuntime.getName() + "]"
        );
    }

    public static void commonDebugLog(EventContext eventContext, String s) {
        commonDebugLog(eventContext, s, s);
    }

    public static void commonDebugLog(EventContext eventContext, String fullDebugLog, String simpleDebugLog) {
        eventContext.getDebugLog().add(fullDebugLog);
        eventContext.getSimpleDebugLog().add(simpleDebugLog);
    }

    public static void commonDebugLog(EventContext eventContext, String s, boolean addSimpleLog) {
        eventContext.getDebugLog().add(s);
        if (addSimpleLog) {
            eventContext.getSimpleDebugLog().add(s);
        }
    }

    public static void postAction(EventContext eventContext, KnowledgeAnswer answer, RobotRuntimeResource resource) {
        String postAction = "";
        if (Objects.isNull(answer.getPostAction())) {
            postAction = "NULL";
        } else {
            if (PostActionTypeEnum.SPECIFIED_STEP.equals(answer.getPostAction())) {
                String stepId = answer.getJumpStepId();
                StepRuntime step = resource.getStepIdMap().get(stepId);
                postAction = "跳转到:(" + ( Objects.isNull(step) ? "null" : step.getName()) + ")";
            } else if (PostActionTypeEnum.HANG_UP.equals(answer.getPostAction())) {
                postAction = answer.getPostAction().getDesc();
                if (BooleanUtils.isTrue(resource.getHangupDelay())) {
                    AnswerLocateBO locate = resource.getAnswerId2LocateMap().get(answer.getUniqueId());
                    if (Objects.nonNull(locate) && !StringUtils.equals(SpecialAnswerConfigPO.ASSISTANT, locate.getSpecialAnswerConfigName())) {
                        postAction = postAction + "(延迟" + resource.getHangupDelayMs() + "ms)";
                    }
                }
            } else {
                postAction = answer.getPostAction().getDesc();
            }
        }
        String log = "回答后操作：" + postAction;
        eventContext.getDebugLog().add(log);
    }

    public static void postJumpNodeHangupAction(EventContext eventContext, JumpTypeEnum jumpType, RobotRuntimeResource resource) {
        if (JumpTypeEnum.HANG_UP.equals(jumpType)) {
            String postAction = "回答后操作：挂机";
            if (BooleanUtils.isTrue(resource.getHangupDelay())) {
                postAction = postAction + "(延迟" + resource.getHangupDelayMs() + "ms)";
            }
            eventContext.getDebugLog().add(postAction);
        }
    }

    public static void matchAssistant(String debugLog, EventContext context) {
        context.getDebugLog().add(debugLog);
        context.getSimpleDebugLog().add("语音助手");
    }

    public static void globalEntityCollect(RobotRuntimeResource resource, EventContext eventContext, String entityId, List<EntityValueBO> valueList) {
        doEntityCollect(resource, eventContext, entityId, valueList, "全局采集");
    }

    public static void entityCollect(RobotRuntimeResource resource,
                                     EventContext eventContext,
                                     String entityId,
                                     List<EntityValueBO> valueList) {
        doEntityCollect(resource, eventContext, entityId, valueList, "实体提取");
    }

    public static void twoPhaseEntityCollect(RobotRuntimeResource resource,
                                             EventContext eventContext,
                                             String userInput,
                                             String entityId,
                                             List<EntityValueBO> valueList) {
        String prefix = "从输入【" + userInput + "】中提取到实体";
        doEntityCollect(resource, eventContext, entityId, valueList, prefix);
    }

    private static void doEntityCollect(RobotRuntimeResource resource,
                                        EventContext eventContext,
                                        String entityId,
                                        List<EntityValueBO> valueList,
                                        String prefix) {
        if (CollectionUtils.isEmpty(valueList)) {
            return;
        }
        String detail = valueList.stream().map(value -> {
            String entityValue = String.format("%s(%s)", value.getValue(), value.getOriginValue());
            if (StringUtils.isNotBlank(value.getMatchInfo())) {
                entityValue = String.format("%s(%s)", entityValue, value.getMatchInfo());
            }
            return entityValue;
        }).collect(Collectors.joining(", "));
        RuntimeEntityBO entity = resource.getEntityIdMap().get(entityId);
        String content = String.format("%s:  %s:【%s】", prefix, entity.getName(), detail);
        eventContext.getDebugLog().add(content);
        eventContext.getSimpleDebugLog().add(content);
    }

    public static void matchCollectSuccess(EventContext eventContext) {
        String log = "命中意图: 采集成功";
        eventContext.getDebugLog().add(log);
        eventContext.getSimpleDebugLog().add(log);
    }

    public static void matchCollectFailed(EventContext eventContext) {
        String log = "命中意图: 采集失败";
        eventContext.getDebugLog().add(log);
        eventContext.getSimpleDebugLog().add(log);
    }

    public static void matchBranch(EventContext eventContext, NodeConditionBranchPO matchBranch) {
        String log = String.format("命中分支: %s", matchBranch.getName());
        eventContext.getSimpleDebugLog().add(log);
        eventContext.getDebugLog().add(log);
    }

    public static void matchCandidateIntent(EventContext eventContext) {
        List<PredictResult> predictResultList = eventContext.getCandidatePredictResultList();
        String candidateIntentNames;
        if (CollectionUtils.isNotEmpty(predictResultList)) {
            candidateIntentNames = predictResultList.stream().map(PredictResult::getIntentName).distinct().collect(Collectors.joining(","));
        } else {
            candidateIntentNames = "";
        }
        String log = String.format("命中意图候选列表: 【%s】", candidateIntentNames);
        eventContext.getSimpleDebugLog().add(log);
        eventContext.getDebugLog().add(log);
    }
}
