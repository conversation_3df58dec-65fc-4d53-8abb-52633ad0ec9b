package com.yiwise.dialogflow.engine.service.entitycollect;

import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.MdcLogIdLifterTransformer;
import com.yiwise.dialogflow.entity.bo.EntityValueBO;
import com.yiwise.dialogflow.entity.enums.EntityTypeEnum;
import com.yiwise.dialogflow.entity.enums.SystemEntityCategoryEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;

@Slf4j
public class DatetimeSystemEntityCollector implements SystemEntityCollector {

    private static final WebClient webClient = AppContextUtils.getBean(WebClient.class);

    @Override
    public void register(Map<SystemEntityCategoryEnum, SystemEntityCollector> registry) {
        registry.put(SystemEntityCategoryEnum.FUTURE_TIME, this);
        registry.put(SystemEntityCategoryEnum.FUTURE_DATE, this);
        registry.put(SystemEntityCategoryEnum.FUTURE_DATETIME, this);
        registry.put(SystemEntityCategoryEnum.ORIGINAL_DATE, this);
        registry.put(SystemEntityCategoryEnum.PAST_DATE, this);
        registry.put(SystemEntityCategoryEnum.PAST_TIME, this);
        registry.put(SystemEntityCategoryEnum.PAST_DATETIME, this);
    }

    @Override
    public Flux<EntityValueBO> asyncCollect(SystemEntityCategoryEnum category, String userInput, String userInputPinyin) {
        return asyncCollect(Collections.singletonList(category), userInput, userInputPinyin);
    }

    @Override
    public Flux<EntityValueBO> asyncCollect(Collection<SystemEntityCategoryEnum> categoryList, String userInput, String userInputPinyin) {
        if (CollectionUtils.isEmpty(categoryList) || StringUtils.isBlank(userInput)) {
            return Flux.empty();
        }
        return extractTime(userInput, categoryList)
                .flatMapMany(Flux::fromIterable);
    }

    private Mono<List<EntityValueBO>> extractTime(String userInput, Collection<SystemEntityCategoryEnum> timeTypeList) {
        if (CollectionUtils.isEmpty(timeTypeList) || StringUtils.isBlank(userInput)) {
            return Mono.empty();
        }
        Map<String, String> param = new HashMap<>();
        param.put("text", userInput);
        String url = ApplicationConstant.ALGORITHM_DATETIME_ENTITY_URL;
        if (StringUtils.isBlank(url)) {
            log.warn("[LogHub_Warn]算法提取时间接口url为空, url={}", url);
            return Mono.empty();
        }
        log.info("url:{}, params={}", url, JsonUtils.object2String(param));
        return webClient.method(HttpMethod.POST)
                .uri(url)
                .body(BodyInserters.fromValue(JsonUtils.object2String(param)))
                .retrieve()
                .bodyToMono(String.class)
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .doOnNext(response -> {
                    log.info("请求算法提取时间:response={}", response);
                })
                .map(json -> parseResult(userInput, json, timeTypeList))
                .onErrorResume(e -> {
                    log.warn("[LogHub_Warn]调用算法提取时间接口失败, url={}, body={}, 失败原因={}", url, JsonUtils.object2String(param), e.getMessage());
                    return Mono.empty();
                });
    }

    private List<EntityValueBO> parseResult(String userInput, String json, Collection<SystemEntityCategoryEnum> timeTypeList) {
        if (StringUtils.isBlank(json) || CollectionUtils.isEmpty(timeTypeList)) {
            return Collections.emptyList();
        }
        DatetimeEntityResponse entityValue = JsonUtils.string2Object(json, DatetimeEntityResponse.class);
        if (Objects.isNull(entityValue) || CollectionUtils.isEmpty(entityValue.getTime())) {
            return Collections.emptyList();
        }
        List<EntityValueBO> result = new ArrayList<>();
        for (SystemEntityCategoryEnum timeType : timeTypeList) {
            List<EntityValueBO> tmpResult = new ArrayList<>();
            switch (timeType) {
                case FUTURE_DATETIME:
                    for (DatetimeEntityResultBO itemValue : entityValue.time) {
                        parse(itemValue.getRaw_time(), itemValue.getFuture_date_time(), userInput).ifPresent(tmpResult::add);
                    }
                    break;
                case FUTURE_DATE:
                    for (DatetimeEntityResultBO itemValue : entityValue.time) {
                        parse(itemValue.getRaw_time(), itemValue.getFuture_date(), userInput).ifPresent(tmpResult::add);
                    }
                    break;
                case FUTURE_TIME:
                    for (DatetimeEntityResultBO itemValue : entityValue.time) {
                        parse(itemValue.getRaw_time(), itemValue.getFuture_time(), userInput).ifPresent(tmpResult::add);
                    }
                    break;
                case ORIGINAL_DATE:
                    for (DatetimeEntityResultBO itemValue : entityValue.time) {
                        parse(itemValue.getRaw_time(), itemValue.getRaw_time(), userInput).ifPresent(tmpResult::add);
                    }
                    break;
                case PAST_DATE:
                    for (DatetimeEntityResultBO itemValue : entityValue.time) {
                        parse(itemValue.getRaw_time(), itemValue.getPast_date(), userInput).ifPresent(tmpResult::add);
                    }
                    break;
                case PAST_TIME:
                    for (DatetimeEntityResultBO itemValue : entityValue.time) {
                        parse(itemValue.getRaw_time(), itemValue.getPast_time(), userInput).ifPresent(tmpResult::add);
                    }
                    break;
                case PAST_DATETIME:
                    for (DatetimeEntityResultBO itemValue : entityValue.time) {
                        parse(itemValue.getRaw_time(), itemValue.getPast_date_time(), userInput).ifPresent(tmpResult::add);
                    }
                    break;
                default:
                    break;
            }
            if (CollectionUtils.isNotEmpty(tmpResult)) {
                result.addAll(tmpResult);
                for (EntityValueBO value : tmpResult) {
                    value.setEntityType(EntityTypeEnum.SYSTEM);
                    value.setSystemEntityCategory(timeType);
                }
            }

        }
        return result;
    }

    private Optional<EntityValueBO> parse(String originTime, String formatValue, String inputText) {
        if (StringUtils.isBlank(formatValue) || StringUtils.isBlank(originTime)) {
            return Optional.empty();
        }

        EntityValueBO entityValue = new EntityValueBO();
        entityValue.setOriginValue(originTime);
        entityValue.setValue(formatValue);
        int indexOf = inputText.indexOf(originTime);
        if (indexOf == -1) {
            entityValue.setStartOffset(0);
            entityValue.setEndOffset(inputText.length() - 1);
        } else {
            entityValue.setStartOffset(indexOf);
            entityValue.setEndOffset(indexOf + originTime.length() - 1);
        }
        entityValue.setEntityType(EntityTypeEnum.SYSTEM);
        return Optional.of(entityValue);
    }

    @Data
    public static class DatetimeEntityResponse {
        List<DatetimeEntityResultBO> time;
    }

    @Data
    public static class DatetimeEntityResultBO {
        String raw_time;
        String future_date_time;
        String past_date_time;
        String future_time;
        String past_time;
        String future_date;
        String past_date;
    }

}
