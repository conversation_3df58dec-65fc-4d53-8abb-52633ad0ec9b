package com.yiwise.dialogflow.engine.chatfilter;

import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.utils.ChatResponseGenerateUtils;

import java.util.Optional;

public class PauseAudioPlayFilter extends AbstractChatFilter{
    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return "PauseAudioPlayFilter";
    }

    @Override
    public Optional<ChatResponse> doFilter(SessionContext sessionContext, EventContext eventContext) {
        return Optional.of(ChatResponseGenerateUtils.generatePauseAudioResponse());
    }
}
