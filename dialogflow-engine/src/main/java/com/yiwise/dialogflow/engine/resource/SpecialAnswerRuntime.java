package com.yiwise.dialogflow.engine.resource;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SpecialAnswerRuntime extends SpecialAnswerConfigPO implements UninterruptedRuntime {
    ImmutableMap<String, KnowledgeAnswerRuntime> answerMap;
    ImmutableList<KnowledgeAnswerRuntime> answerRuntimeList;

    /**
     * 不可打断下, 可以响应的流程id
     */
    private ImmutableSet<String> uninterruptedOnlyReplyStepIdSet;

    /**
     * 不可打断下, 可以响应的问答知识id
     */
    private ImmutableSet<String> uninterruptedOnlyReplyKnowledgeIdSet;

    /**
     * 不可打断下, 可以响应的特殊语境名称
     */
    private ImmutableSet<String> uninterruptedOnlyReplySpecialAnswerIdSet;
}
