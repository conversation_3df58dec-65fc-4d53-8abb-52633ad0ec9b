package com.yiwise.dialogflow.engine.chatprocessor;

import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;

/**
 * <AUTHOR>
 */
public class NoopChatPreProcessor extends AbstractChatPreProcessor {

    @Override
    public void doProcess(SessionContext sessionContext, EventContext eventContext) {

    }

    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return "NoopChatPreProcessor";
    }
}
