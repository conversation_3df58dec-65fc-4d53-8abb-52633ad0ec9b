package com.yiwise.dialogflow.engine.chatmanager;

import com.aliyun.openservices.shade.com.alibaba.rocketmq.common.filter.impl.Op;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Sets;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.AnswerPredicate;
import com.yiwise.dialogflow.engine.CollectNodeSkipConditionPredicate;
import com.yiwise.dialogflow.engine.ConditionBranchPredicate;
import com.yiwise.dialogflow.engine.context.*;
import com.yiwise.dialogflow.engine.domain.NodeJumpInfo;
import com.yiwise.dialogflow.engine.enums.SpecialChatModeEnum;
import com.yiwise.dialogflow.engine.helper.ActionHelper;
import com.yiwise.dialogflow.engine.helper.SessionContextHelper;
import com.yiwise.dialogflow.engine.resource.*;
import com.yiwise.dialogflow.engine.share.action.ChatAction;
import com.yiwise.dialogflow.engine.share.action.WaitUserSayFinishAction;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.enums.RepeatAnswerPlayStrategyEnum;
import com.yiwise.dialogflow.engine.share.request.KeyCaptureSuccessEvent;
import com.yiwise.dialogflow.engine.share.request.UserSayFinishEvent;
import com.yiwise.dialogflow.engine.share.response.AnswerAudioPlayConfig;
import com.yiwise.dialogflow.engine.share.response.AnswerResult;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.utils.AnswerRenderUtils;
import com.yiwise.dialogflow.engine.utils.AnswerSelectUtils;
import com.yiwise.dialogflow.engine.utils.DebugLogUtils;
import com.yiwise.dialogflow.entity.bo.EntityValueBO;
import com.yiwise.dialogflow.entity.bo.algorithm.PredictResult;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.vo.SimpleIntentVO;
import com.yiwise.dialogflow.utils.AnswerConditionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class StepFlowChatManager extends AbstractStepFlowChatManager {


    private final ImmutableMap<String, NodeRuntime<?>> nodeMap;

    private final NodeRuntime<?> rootNode;

    // 尽量不要在engine中引用context, engine是会复用的
    private volatile StepFlowContext context;

    private volatile SessionContext sessionContext;

    private volatile EventContext eventContext;

    public StepFlowChatManager(RobotRuntimeResource resource, StepRuntime stepRuntime) {
        super(resource, stepRuntime);
        this.rootNode = step.getRootNode();
        this.nodeMap = step.getNodeMap();
    }

    @Override
    public void initContext(SessionContext sessionContext) {
        StepFlowContext stepFlowContext = new StepFlowContext();
        stepFlowContext.setCurrentNodeId(rootNode.getId());
        stepFlowContext.setStepId(stepId);
        stepFlowContext.setStepName(stepName);
        stepFlowContext.setKeyCaptureNodeRetryTimesMap(new HashMap<>());
        sessionContext.getStepFlowContextMap().put(stepId, stepFlowContext);
    }

    @Override
    public void resetContext(SessionContext sessionContext, EventContext eventContext) {
        this.sessionContext = sessionContext;
        this.context = getStepFlowContext(sessionContext);
        this.eventContext = eventContext;
    }

    @Override
    public void clearContext() {
        this.sessionContext = null;
        this.context = null;
        this.eventContext = null;
    }

    @Override
    public Optional<ChatResponse> doProcess() {
        StepFlowContext context = getStepFlowContext(sessionContext);
        Optional<ChatResponse> result = executeEvent(sessionContext, eventContext, context);

        // 更新状态
        updateContextState(sessionContext, eventContext, context, result.orElse(null));

        // 记录当前流程为最后活跃流程
        sessionContext.setLastActiveStepId(stepId);
        if (StepTypeEnum.MAIN.equals(step.getType())) {
            sessionContext.setLastActiveMainStepId(stepId);
        }
        return result;
    }

    /**
     * 拉回到原节点, 从在问答知识/特殊语境aiSayFinish的时候, 自动播放原流程中原节点信息
     */
    private Optional<ChatResponse> doPullback(SessionContext sessionContext,
                                              EventContext eventContext,
                                              StepFlowContext context) {
        NodeRuntime<?> currentNode = getCurrentNode(context);
        log.info("处理doPullback, 当前节点:{}:{}:{}", currentNode.getLabel(), currentNode.getName(), currentNode.getType());

        if (currentNode instanceof JumpNodeRuntime) {
            // 跳转节点直接执行后续的跳转动作
            return generateActionResponse(currentNode);
        }

        if (currentNode instanceof CollectNodeRuntime) {
            return pullbackOnCollectNode((CollectNodeRuntime) currentNode, eventContext);
        }

        // 回到按键采集节点,走采集失败流程
        if (currentNode instanceof KeyCaptureNodeRuntime) {
            return keyCaptureFailed((KeyCaptureNodeRuntime) currentNode);
        }

        ChatNodeRuntime chatNode = (ChatNodeRuntime) currentNode;

        // 判断是从其他独立流程回到原主流程的
        if (AnswerSourceEnum.STEP.equals(eventContext.getJumpSource())
                && BooleanUtils.isTrue(chatNode.origin.getEnableAssign())
                && chatNode.origin.enableEntityAssign()
                && BooleanUtils.isTrue(chatNode.origin.getEntityAssign().getEnableCollectOnPullback())) {

            NodeAssignConfigRuntime assignConfigRuntime = context.getAssignConfig();
            Set<String> entityIdSet = assignConfigRuntime.getDependEntityIdSet();
            List<UserInputCollectInfo> globalEntityCollectList = sessionContext.getInputCollectInfoList().stream()
                    .filter(item -> !Objects.isNull(item.getSequence()) && item.getSequence() >= assignConfigRuntime.getLastActiveSeq())
                    .filter(item -> CollectionUtils.isNotEmpty(item.getEntityValueList()))
                    .map(item -> {
                        UserInputCollectInfo userInputCollectInfo = new UserInputCollectInfo();
                        userInputCollectInfo.setSequence(item.getSequence());
                        userInputCollectInfo.setUserInput(item.getUserInput());
                        List<EntityValueBO> entityValues = item.getEntityValueList().stream()
                                        .filter(entityValue -> entityIdSet.contains(entityValue.getEntityId()))
                                .collect(Collectors.toList());
                        userInputCollectInfo.setEntityValueList(entityValues);
                        return userInputCollectInfo;
                    })
                    .filter(item -> CollectionUtils.isNotEmpty(item.getEntityValueList()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(globalEntityCollectList)) {
                List<EntityValueBO> allEntityValueList = new ArrayList<>();
                for (UserInputCollectInfo userInputCollectInfo : globalEntityCollectList) {
                    Map<String, List<EntityValueBO>> entityValueListMap = MyCollectionUtils.listToMapList(userInputCollectInfo.getEntityValueList(), EntityValueBO::getEntityId);
                    allEntityValueList.addAll(userInputCollectInfo.getEntityValueList());
                    entityValueListMap.forEach((entityId, entityValue) -> {
                        DebugLogUtils.twoPhaseEntityCollect(resource, eventContext, userInputCollectInfo.getUserInput(), entityId, entityValue);
                    });
                }

                execCollectSuccessAssign(allEntityValueList, chatNode.origin);

                // todo 如果未启用收集成功意图 如何处理?
                Optional<NodeRuntime<?>> nextNodeOpt = getNextNodeByIntentOrDefaultIntent(chatNode, ApplicationConstant.COLLECT_SUCCESS_INTENT_ID);
                if (nextNodeOpt.isPresent()) {
                    // todo debugLog
                    DebugLogUtils.matchCollectSuccess(eventContext);
                    return generateResponseByNode(nextNodeOpt.get());
                }
            }

            // 判断是否收集成功, 成功走成功分支, 否则, 切换重新收集
            return generateResponseByChatNode(chatNode, true);
        }

        // 判断是否需要重新播放
        if (nodeLastAnswerNeedReplay(chatNode)) {
            // 切换话术重新播放
            double progress = getCurrentAnswerPlayProgress(chatNode, ApplicationConstant.AUDIO_PLAY_FINISH_PROGRESS);
            DebugLogUtils.commonDebugLog(eventContext, "原对话流播放进度"+ String.format("%.2f", progress) + "%，未满足设置阈值，重新播报");
            return generateResponseByChatNode(chatNode);
        } else if (needSwitchAnswerReplay(chatNode)) {
            log.debug("当前节点开启了切换答案重播");
            return generateResponseByChatNode(chatNode, true);
        }

        // 走默认节点, 没有默认节点返回空
        Optional<NodeRuntime<?>> nextNodeOpt = getNextNodeByDefaultIntent(chatNode);
        if (nextNodeOpt.isPresent()) {
            double progress = getCurrentAnswerPlayProgress(chatNode, ApplicationConstant.AUDIO_PLAY_FINISH_PROGRESS);
            DebugLogUtils.commonDebugLog(eventContext, "原对话流播放进度"+ String.format("%.2f", progress) + "%，满足设置阈值，走兜底意图");
            return generateResponseByNode(nextNodeOpt.get());
        }

        // 返回等待用户应答的响应
        return generateWaitUserSayFinishResponse(chatNode);
    }

    /**
     * 在采集节点进行拉回
     */
    private Optional<ChatResponse> pullbackOnCollectNode(CollectNodeRuntime currentNode, EventContext eventContext) {

        // 执行跳过逻辑
        // 从跳转时的用户输入判断
        execSkipCondition(eventContext, currentNode);

        // 判断当前需要反问的问题 index
        // 判断当前节点上一次播放的答案是否仍然属于当前的问题
        // 如果属于当前的问题, 判断播放进度是否需要重播还是切换下一个答案
        Optional<Integer> questionIndex = calculateNextQuestionIndex(sessionContext, currentNode, collectNodeContext(context, currentNode.id));
        String preAnswerId = sessionContext.getNodeLastReplyAnswerIdMap().get(currentNode.id);

        if (questionIndex.isPresent()) {
            boolean changeAnswer = false;
            if (StringUtils.isNotBlank(preAnswerId)) {
                boolean isSameQuestion = currentNode.getEntityCollectConfigRuntimeList()
                        .get(questionIndex.get())
                        .getAnswerRuntimeList().stream()
                        .anyMatch(answer -> answer.getUniqueId().equals(preAnswerId));
                if (isSameQuestion) {
                    // 判断播放进度
                    double preAnswerPlayProgress = sessionContext.getAnswerProgressMap().getOrDefault(preAnswerId, 0.0);
                    double skipThreshold = 30;
                    if (BooleanUtils.isTrue(currentNode.origin.getEnableCustomReplay())
                            && Objects.nonNull(currentNode.origin.getCustomReplayThreshold())) {
                        skipThreshold = currentNode.origin.getCustomReplayThreshold();
                    }
                    boolean skipCurrentAnswer = preAnswerPlayProgress >= skipThreshold;
                    if (skipCurrentAnswer) {
                        changeAnswer = true;
                    }
                } else {
                    changeAnswer = true;
                }
            }
            return generateResponseByNode(currentNode, changeAnswer, false);
        }

        return collectNodeToNextNode(currentNode);
    }

    private CollectNodeContext collectNodeContext(StepFlowContext stepContext, String nodeId) {
        return stepContext.getCollectNodeContextMap().computeIfAbsent(nodeId, (x) -> new CollectNodeContext());
    }

    /**
     * 更新当前节点期望下一轮的意图信息
     * 比如下一轮只允许三个意图, 那么在dispatcher阶段就可以以此来判断选择合适的manager来进行处理
     * 跳转节点是没有期望的
     */
    private void updateContextState(SessionContext sessionContext,
                                    EventContext eventContext,
                                    StepFlowContext context,
                                    ChatResponse chatResponse) {
        NodeRuntime<?> node = getCurrentNode(context);

        // 设置动态变量赋值配置
        updateAssignConfig(eventContext, context, node.origin);

        updateCollectConfig(eventContext, context, node.origin);

        // 更新是否等待用户应答状态
        context.setWaitUserSayFinish(false);
        context.setWaitUserSayFinishNodeId(null);
        if (Objects.nonNull(chatResponse) && CollectionUtils.isNotEmpty(chatResponse.getActionList())) {
            for (ChatAction chatAction : chatResponse.getActionList()) {
                if (chatAction instanceof WaitUserSayFinishAction) {
                    context.setWaitUserSayFinish(true);
                    context.setWaitUserSayFinishNodeId(node.getId());
                    break;
                }
            }
        }

        // 更新强制拉回设置状态
        context.setEnablePullback(false);
        if (node instanceof ChatNodeRuntime) {
            ChatNodeRuntime collectNode = (ChatNodeRuntime) node;
            if (BooleanUtils.isTrue(collectNode.getOrigin().getEnablePullback())) {
                context.setEnablePullback(true);
            }
        }
    }

    private void updateCollectConfig(EventContext eventContext, StepFlowContext context, DialogBaseNodePO origin) {
        context.setCollectConfigList(null);
        if (origin instanceof DialogCollectNodePO) {
            DialogCollectNodePO collectNode = (DialogCollectNodePO) origin;
            List<NodeCollectConfigRuntime> list = collectNode.getEntityCollectList().stream()
                            .map(item -> MyBeanUtils.copy(item, NodeCollectConfigRuntime.class))
                    .collect(Collectors.toList());
            context.setCollectConfigList(list);
        }
    }

    private void updateAssignConfig(EventContext eventContext,
                                    StepFlowContext context,
                                    DialogBaseNodePO node) {
        context.setAssignConfig(null);
        if (BooleanUtils.isNotTrue(node.getEnableAssign())
                || !node.enableEntityOrOriginInputAssign()) {
            return;
        }
        if (node instanceof DialogChatNodePO) {
            // 只有对话节点才支持实体采集
            // 如果当前节点设置了采集成功分支 且 赋值类型是实体/原话采集 才会更新状态
            // 否则就仅需要再进入下一节点前执行赋值就可以了
            DialogChatNodePO chatNode = (DialogChatNodePO) node;
            boolean containsCollectSuccessIntent = chatNode.getIntentRelatedNodeMap().containsKey(ApplicationConstant.COLLECT_SUCCESS_INTENT_ID);
            NodeAssignConfigRuntime assignConfig = MyBeanUtils.copy(chatNode, NodeAssignConfigRuntime.class);
            assignConfig.setLastActiveSeq(eventContext.getSeq());
            assignConfig.setEnableCollectSuccessIntent(containsCollectSuccessIntent);
            context.setAssignConfig(assignConfig);
        }
    }


    private Optional<ChatResponse> executeEvent(SessionContext sessionContext,
                                                EventContext eventContext,
                                                StepFlowContext context) {
        Optional<ChatResponse> response = Optional.empty();
        switch (eventContext.getEvent()) {
            case ENTER:
                if (BooleanUtils.isTrue(eventContext.isPullbackToOrigin())) {
                    response = doPullback(sessionContext, eventContext, context);
                } else {
                    response = enter(context);
                }
                break;
            case AI_SAY_FINISH:
                response = aiSayFinish(context);
                break;
            case USER_SAY_FINISH:
                response = userSayFinish(context, eventContext);
                break;
            case USER_SILENCE:
                response = userSilence(context);
                break;
            case KEY_CAPTURE_FAILED:
            case KEY_CAPTURE_SUCCESS:
                response = keyCapture(sessionContext, eventContext, context);
                break;
            default:
                break;
        }

        return response;
    }

    private Optional<ChatResponse> keyCapture(SessionContext sessionContext, EventContext eventContext, StepFlowContext context) {
        NodeRuntime<?> keyCaptureNode = getCurrentNode(context);
        log.info("处理{}, 当前节点:{}:{}:{}", eventContext.getEvent(), keyCaptureNode.getLabel(), keyCaptureNode.getName(), keyCaptureNode.getType());

        if (!(keyCaptureNode instanceof KeyCaptureNodeRuntime)) {
            log.warn("当前节点不是按键采集节点, 无法处理{}, stepId={}, stepName={}, nodeId={}, nodeName={}",
                    eventContext.getEvent(), stepId, stepName, keyCaptureNode.getId(), keyCaptureNode.getName());
            return Optional.empty();
        }
        KeyCaptureNodeRuntime keyCaptureNodeRuntime = (KeyCaptureNodeRuntime) keyCaptureNode;

        if (ChatEventTypeEnum.KEY_CAPTURE_SUCCESS.equals(eventContext.getEvent())) {
            return keyCaptureSuccess(keyCaptureNodeRuntime, eventContext);
        }
        return keyCaptureFailed(keyCaptureNodeRuntime);
    }

    private Optional<ChatResponse> keyCaptureSuccess(KeyCaptureNodeRuntime keyCaptureNodeRuntime, EventContext eventContext) {
        String result = ((KeyCaptureSuccessEvent) eventContext.getOriginEventParam()).getResult();
        DebugLogUtils.commonDebugLog(eventContext, String.format("按键采集成功【%s】", result));
        // 动态变量赋值
        SessionContextHelper.variableAssign(resource, sessionContext, eventContext, keyCaptureNodeRuntime.getResultVarId(), result);
        DebugLogUtils.matchCollectSuccess(eventContext);
        eventContext.setMatchIntentId(ApplicationConstant.COLLECT_SUCCESS_INTENT_ID);
        eventContext.setPredictResult(generateKeyCaptureCollectSuccessPredictResult());
        Optional<NodeRuntime<?>> nextNodeOpt = getNextNodeByIntent(keyCaptureNodeRuntime, ApplicationConstant.COLLECT_SUCCESS_INTENT_ID);
        if (nextNodeOpt.isPresent()) {
            return generateResponseByNode(nextNodeOpt.get());
        } else {
            log.warn("对话逻辑执行错误, 采集成功分支不存在, stepId={}, stepName={}, nodeId={}, nodeName={}",
                    stepId, stepName, keyCaptureNodeRuntime.getId(), keyCaptureNodeRuntime.getName());
        }
        return Optional.empty();
    }

    private static PredictResult generateKeyCaptureCollectSuccessPredictResult() {
        PredictResult predictResult = new PredictResult();
        predictResult.setIntentId(ApplicationConstant.COLLECT_SUCCESS_INTENT_ID);
        predictResult.setIntentName(ApplicationConstant.COLLECT_SUCCESS_INTENT_NAME);
        predictResult.setSimpleIntentInfo(SimpleIntentVO.collectSuccess());
        predictResult.setConfidence(1.0);
        predictResult.setPredictType(PredictTypeEnum.REGEX);
        predictResult.setCompositeIntentConditionResults(Collections.emptyList());
        predictResult.setIntentRefType(IntentRefTypeEnum.NODE);
        return predictResult;
    }

    private static PredictResult generateKeyCaptureCollectFailedPredictResult() {
        PredictResult predictResult = new PredictResult();
        predictResult.setIntentId(ApplicationConstant.COLLECT_FAILED_INTENT_ID);
        predictResult.setIntentName(ApplicationConstant.COLLECT_FAILED_INTENT_NAME);
        predictResult.setSimpleIntentInfo(SimpleIntentVO.collectFailed());
        predictResult.setConfidence(1.0);
        predictResult.setPredictType(PredictTypeEnum.REGEX);
        predictResult.setCompositeIntentConditionResults(Collections.emptyList());
        predictResult.setIntentRefType(IntentRefTypeEnum.NODE);
        return predictResult;
    }

    private Integer incrementAndGetRetryTimes(KeyCaptureNodeRuntime keyCaptureNodeRuntime, StepFlowContext context) {
        Map<String, AtomicInteger> keyCaptureNodeRetryTimesMap = context.getKeyCaptureNodeRetryTimesMap();
        return keyCaptureNodeRetryTimesMap.computeIfAbsent(keyCaptureNodeRuntime.getId(), k -> new AtomicInteger(0)).incrementAndGet();
    }

    private Optional<ChatResponse> keyCaptureFailed(KeyCaptureNodeRuntime keyCaptureNodeRuntime) {
        Integer retryTimes;
        if ((retryTimes = incrementAndGetRetryTimes(keyCaptureNodeRuntime, context)) > keyCaptureNodeRuntime.getRetryTimes()) {
            DebugLogUtils.commonDebugLog(eventContext, "按键采集失败");
            DebugLogUtils.matchCollectFailed(eventContext);
            eventContext.setMatchIntentId(ApplicationConstant.COLLECT_FAILED_INTENT_ID);
            eventContext.setPredictResult(generateKeyCaptureCollectFailedPredictResult());
            Optional<NodeRuntime<?>> nextNodeOpt = getNextNodeByIntent(keyCaptureNodeRuntime, ApplicationConstant.COLLECT_FAILED_INTENT_ID);
            if (nextNodeOpt.isPresent()) {
                return generateResponseByNode(nextNodeOpt.get());
            } else {
                log.warn("对话逻辑执行错误, 采集失败分支不存在, stepId={}, stepName={}, nodeId={}, nodeName={}",
                        stepId, stepName, keyCaptureNodeRuntime.getId(), keyCaptureNodeRuntime.getName());
            }
            return Optional.empty();
        }

        DebugLogUtils.commonDebugLog(eventContext, String.format("按键采集失败，准备第%s次重试", retryTimes));
        return generateResponseByNode(keyCaptureNodeRuntime);
    }

    private Optional<ChatResponse> userSilence(StepFlowContext context) {
        // 如果用户用户无应答走到这, 可以理解为命中了用户无应答意图, 当然这种逻辑也可以在意图预测的地方进行处理,
        NodeRuntime<?> chatNode = getCurrentNode(context);
        log.info("处理userSilence, 当前节点:{}:{}:{}", chatNode.getLabel(), chatNode.getName(), chatNode.getType());

        if (!(chatNode instanceof ChatNodeRuntime)) {
            log.warn("当前节点不是对话节点, 无法处理用户无应答, stepId={}, stepName={}, nodeId={}, nodeName={}",
                    stepId, stepName, chatNode.getId(), chatNode.getName());
            return Optional.empty();
        }
        // 如果是调整节点, 则说明用户输入没有命中任何的问答知识, 这个时候只需要继续播放录音就可以了
        String matchIntentId = ApplicationConstant.USER_SILENCE_INTENT_ID;

        Optional<NodeRuntime<?>> nextNodeOpt = getNextNodeByIntent((ChatNodeRuntime) chatNode, matchIntentId);

        if (nextNodeOpt.isPresent()) {
            return generateResponseByNode(nextNodeOpt.get());
        } else {
            log.warn("对话逻辑执行错误, 用户无应答意图不存在对应的分支, stepId={}, stepName={}, nodeId={}, nodeName={}",
                    stepId, stepName, chatNode.getId(), chatNode.getName());
        }
        return Optional.empty();
    }

    /**
     * 判断这个节点最后的答案就是机器人最后的答案, 用来判断中间是否跳转过其他答案, 在续播的时候需要进行判断处理
     */
    private boolean nodeLastAnswerIsRobotLastAnswer(SessionContext sessionContext,
                                                    EventContext eventContext,
                                                    StepFlowContext context,
                                                    NodeRuntime<?> node) {
        Optional<NodeAnswerRuntime> currentAnswerOpt = getCurrentAnswer(node);
        if (currentAnswerOpt.isPresent() && StringUtils.isNotBlank(sessionContext.getLastAnswerId())) {
            return currentAnswerOpt.get().getUniqueId().equals(sessionContext.getLastAnswerId());
        }
        return false;
    }

    private Optional<ChatResponse> userSayFinish(StepFlowContext context, EventContext eventContext) {
        if (eventContext.isRepeatAnswer()) {
            return repeatAnswer(context);
        }
        NodeRuntime<?> currentNode = getCurrentNode(context);
        log.info("处理userSayFinish, 当前节点:{}:{}:{}", currentNode.getLabel(), currentNode.getName(), currentNode.getType());
        // 跳转节点
        if (currentNode instanceof JumpNodeRuntime) {
            return userSayFinishOnJumpNode((JumpNodeRuntime) currentNode);
        }

        // 信息采集节点
        if (currentNode instanceof CollectNodeRuntime) {
            return userSayFinishOnCollectNode(eventContext, (CollectNodeRuntime) currentNode);
        }

        return userSayFinishOnChatNode(eventContext, (ChatNodeRuntime) currentNode);
    }

    private Optional<ChatResponse> userSayFinishOnChatNode(EventContext eventContext, ChatNodeRuntime chatNode) {
        // 这里需要判断对话节点是否开启了重新播放, 如果开启了重新播放, 则不会再命中任何分支
        // 判断当前这个节点的答案播放进度是否需要重新播放
        // 如果重新播放的话, 则重新生成节点答案, 否则判断是否命中了

        String matchIntentId = eventContext.getMatchIntentId();
        if (StringUtils.isBlank(matchIntentId)) {
            matchIntentId = ApplicationConstant.DEFAULT_INTENT_ID;
        }
        // 判断是否开启等待ai 应答时长 功能, 如果开启, 则判断是否匹配目标分支
        if (BooleanUtils.isTrue(chatNode.origin.getEnableWaitUserSayFinish())) {
            UserSayFinishEvent userSayFinish = (UserSayFinishEvent) eventContext.getOriginEventParam();
            boolean matchWaitIntent = CollectionUtils.isEmpty(chatNode.origin.getTriggerWaitIntentIdList())
                    || chatNode.origin.getTriggerWaitIntentIdList().contains(matchIntentId);
            // 如果没有配置触发意图, 则默认执行等待逻辑
            if (matchWaitIntent && BooleanUtils.isNotTrue(userSayFinish.getWaitUserSayFinishDone())) {
                // 返回等待用户说完事件
                predictDebugLog(eventContext, matchIntentId);
                DebugLogUtils.commonDebugLog(eventContext, String.format("等待用户说完, AI应答延长时间: %sms", chatNode.origin.getWaitUserSayFinishMs()));
                return generateWaitUserSayFinishResponse(chatNode);
            }
        }

        // 先获取匹配的意图,
        // 如果没有匹配, 再次尝试默认意图
        String intentName = resource.getIntentId2NameMap().getOrDefault(matchIntentId, matchIntentId);
        if (checkHasNextNodeByIntentId(chatNode, matchIntentId)) {
            predictDebugLog(eventContext, matchIntentId);
            Optional<NodeRuntime<?>> nextNodeOpt = getNextNodeByIntent(chatNode, matchIntentId);
            if (nextNodeOpt.isPresent()) {
                return generateResponseByNode(nextNodeOpt.get());
            } else {
                log.warn("对话逻辑执行错误, 意图{}不存在对应的分支", intentName);
            }
        } else if (StringUtils.isNotBlank(matchIntentId)) {
            log.warn("对话逻辑执行错误, 意图{}不存在对应的分支", intentName);
        }
        return Optional.empty();
    }

    /**
     * 信息采集节点处理用户输入
     */
    private Optional<ChatResponse> userSayFinishOnCollectNode(EventContext eventContext, CollectNodeRuntime currentNode) {
        // 判断当前输入是否执行跳过某些问题逻辑
        execSkipCondition(eventContext, currentNode);

        Optional<ChatResponse> responseOptional = generateResponseByNode(currentNode);

        if (responseOptional.isPresent()) {
            return responseOptional;
        }

        return collectNodeToNextNode(currentNode);
    }

    private Optional<ChatResponse> collectNodeToNextNode(CollectNodeRuntime currentNode) {
        // 判断是否采集成功
        boolean collectSuccess = checkCollectSuccess(currentNode, sessionContext);
        Optional<NodeRuntime<?>> nextNodeOpt;
        if (collectSuccess && checkHasNextNodeByIntentId(currentNode, ApplicationConstant.COLLECT_SUCCESS_INTENT_ID)) {
            log.info("采集成功, 通过采集成功分支获取下一节点");
            DebugLogUtils.commonDebugLog(eventContext, "全部实体采集成功, 走采集成功");
            nextNodeOpt = getNextNodeByIntent(currentNode, ApplicationConstant.COLLECT_SUCCESS_INTENT_ID);
        } else {
            // 根据默认意图跳转到下一个节点
            List<String> entityNameList = getCollectFailEntityName(currentNode, sessionContext);
            DebugLogUtils.commonDebugLog(eventContext, String.format("实体[%s]采集失败, 走默认分支", String.join(", ", entityNameList)));
            nextNodeOpt = getNextNodeByDefaultIntent(currentNode);
        }
        if (nextNodeOpt.isPresent()) {
            return generateResponseByNode(nextNodeOpt.get());
        } else {
            log.warn("对话数据错误, 采集节点未配置默认意图, botId:{}, nodeLabel={}", currentNode.origin.getBotId(), currentNode.getLabel());
        }
        return Optional.empty();
    }

    private boolean checkCollectSuccess(CollectNodeRuntime currentNode, SessionContext sessionContext) {
        return CollectionUtils.isEmpty(getCollectFailEntityName(currentNode, sessionContext));
    }

    /**
     * 获取采集失败的实体名称列表
     */
    private List<String> getCollectFailEntityName(CollectNodeRuntime currentNode, SessionContext sessionContext) {
        return currentNode.getEntityCollectConfigRuntimeList().stream()
                .filter(item -> {
                    String varValue = sessionContext.getGlobalVariableValueMap().get(item.getVariableName());
                    return StringUtils.isBlank(varValue);
                })
                .map(EntityCollectConfigRuntime::getEntityName)
                .collect(Collectors.toList());
    }

    private void execSkipCondition(EventContext eventContext,
                                   CollectNodeRuntime currentNode) {
        if (BooleanUtils.isNotTrue(currentNode.getOrigin().getEnableSkipCondition())) {
            return;
        }

        List<CollectNodeSkipConditionPredicate> predicateList = new ArrayList<>();

        CollectNodeContext nodeContext = collectNodeContext(context, currentNode.id);
        int preSeq = nodeContext.getPreExecSkipConditionSeq();
        nodeContext.setPreExecSkipConditionSeq(eventContext.getSeq());

        for (UserInputPredictInfo predictInfo : sessionContext.getInputPredictInfoList()) {
            if (predictInfo.getSequence() > preSeq) {
                if (currentNode.id.equals(predictInfo.getActiveNodeId())
                        && CollectionUtils.isNotEmpty(predictInfo.getPredictIntentIdList())) {
                    CollectNodeSkipConditionPredicate predicate = new CollectNodeSkipConditionPredicate(resource, sessionContext, predictInfo.getPredictIntentIdList());
                    predicateList.add(predicate);
                }
            }
        }


        Map<CollectNodeSkipTypeEnum, CollectNodeSkipCondition> matchConditionMap = new HashMap<>();
        for (CollectNodeSkipCondition collectNodeSkipCondition : currentNode.getOrigin().getSkipConditionList()) {
            for (CollectNodeSkipConditionPredicate predicate : predicateList) {
                if (predicate.test(collectNodeSkipCondition)) {
                    matchConditionMap.put(collectNodeSkipCondition.getSkipType(), collectNodeSkipCondition);
                }
            }
        }

        if (MapUtils.isNotEmpty(matchConditionMap)) {
            if (Objects.isNull(context.getCollectNodeContextMap())) {
                context.setCollectNodeContextMap(new HashMap<>());
            }
            log.info("执行跳过逻辑: skipTypeSet:{}, {}", matchConditionMap.keySet(), context.getCollectNodeContextMap());
            if (matchConditionMap.containsKey(CollectNodeSkipTypeEnum.ALL_QUESTION)) {
                // 跳过所有答案
                int questionSize = currentNode.getEntityCollectConfigRuntimeList().size();
                context.getCollectNodeContextMap().put(currentNode.id, new CollectNodeContext(questionSize));

                CollectNodeSkipCondition condition = matchConditionMap.get(CollectNodeSkipTypeEnum.ALL_QUESTION);
                String skipRuleDesc = AnswerConditionUtil.condition2String(condition, resource.getIntentId2NameMap());
                DebugLogUtils.commonDebugLog(eventContext, String.format("命中条件%s", skipRuleDesc));
            } else {
                // 仅跳过当前答案
                CollectNodeContext collectNodeContext = context.getCollectNodeContextMap().computeIfAbsent(currentNode.id, (x) -> new CollectNodeContext());
                collectNodeContext.incIndex();

                CollectNodeSkipCondition condition = matchConditionMap.get(CollectNodeSkipTypeEnum.CURRENT_QUESTION);
                String skipRuleDesc = AnswerConditionUtil.condition2String(condition, resource.getIntentId2NameMap());
                DebugLogUtils.commonDebugLog(eventContext, String.format("命中条件%s", skipRuleDesc));
            }
        }
    }

    private void execAssignFromPreInput(EventContext eventContext, CollectNodeRuntime currentNode) {
        if (BooleanUtils.isNotTrue(currentNode.getOrigin().getEnableCollectWithPreInput())
                || CollectionUtils.isEmpty(sessionContext.getInputCollectInfoList())) {
            return;
        }
        CollectNodeContext nodeContext = collectNodeContext(context, currentNode.id);
        int preExecIndex = nodeContext.getPreExecGlobalAssignSeq();
        nodeContext.setPreExecGlobalAssignSeq(eventContext.getSeq());

        sessionContext.getInputCollectInfoList().stream()
                .filter(item -> item.getSequence() > preExecIndex)
                .filter(item -> CollectionUtils.isNotEmpty(item.getEntityValueList()))
                .forEach(item -> {
                    Map<String, List<EntityValueBO>> entityId2ValueListMap =
                            MyCollectionUtils.listToMapList(item.getEntityValueList(), EntityValueBO::getEntityId);
                    for (EntityCollectConfigRuntime entityConf : currentNode.getEntityCollectConfigRuntimeList()) {
                        // 执行赋值
                        List<EntityValueBO> valueList = entityId2ValueListMap.getOrDefault(entityConf.getEntityId(), Collections.emptyList());
                        if (CollectionUtils.isNotEmpty(valueList)) {
                            String strValue = valueList.stream()
                                    .map(EntityValueBO::getValue)
                                    .collect(Collectors.joining(","));
                            DebugLogUtils.twoPhaseEntityCollect(resource, eventContext, item.getUserInput(), entityConf.getEntityId(), valueList);
                            SessionContextHelper.variableAssign(resource, sessionContext, eventContext,
                                    entityConf.getVariableId(), strValue, entityConf.getEntityId());
                        }
                    }
                });
    }

    private Optional<Integer> calculateNextQuestionIndex(SessionContext sessionContext,
                                                         CollectNodeRuntime currentNode,
                                                         CollectNodeContext collectNodeContext) {
        for (int i = collectNodeContext.getQuestionIndex(); i < currentNode.getEntityCollectConfigRuntimeList().size(); i++) {
            CollectNodeEntityItemPO entityConfigItem = currentNode.getEntityCollectConfigRuntimeList().get(i);
            String variableId = entityConfigItem.getVariableId();
            VariablePO variable = resource.getVariableIdMap().get(variableId);
            if (Objects.nonNull(variable)) {
                String variableName = variable.getName();
                String varValue = sessionContext.getGlobalVariableValueMap().get(variableName);
                // 判断是否超过反问次数了
                int askCount = collectNodeContext.getQuestionAskCountMap().computeIfAbsent(i, (x) -> 0);
                if (StringUtils.isBlank(varValue) && askCount < entityConfigItem.getRepeatCount()) {
                    return Optional.of(i);
                }
            }
        }
        return Optional.empty();
    }

    private void predictDebugLog(EventContext eventContext, String intentId) {
        if (ApplicationConstant.DEFAULT_INTENT_ID.equals(intentId)) {
            DebugLogUtils.defaultIntent(eventContext);
        } else if (ApplicationConstant.COLLECT_SUCCESS_INTENT_ID.equals(intentId)) {
            DebugLogUtils.matchCollectSuccess(eventContext);
        } else {
            DebugLogUtils.predictDetail(eventContext, eventContext.getPredictResult());
        }
    }

    private Optional<ChatResponse> generateWaitUserSayFinishResponse(ChatNodeRuntime node) {
        log.info("当前节点[{}]开启了等待用户说完功能, 返回等待用户说完事件", node.getLabel());
        ChatResponse response = new ChatResponse();
        response.setAnswerLocate(node.getNodeLocate());
        response.setActionList(node.getWaitUserSayFinishActionList());
        return Optional.of(response);
    }

    /**
     * 采集成功赋值, 包含实体采集和原话采集
     */
    private void execCollectSuccessAssign(List<EntityValueBO> entityValueList,
                                          DialogChatNodePO node) {
        // 把实体提取到的值, 更新到对话状态(SessionContext)中
        Map<String, List<EntityValueBO>> entityValueMapList = MyCollectionUtils.listToMapList(entityValueList, EntityValueBO::getEntityId);

        if (node.enableEntityAssign()) {
            for (VarAssignActionItemPO assignAction : node.getEntityAssign().getAssignActionList()) {
                List<EntityValueBO> valueList = entityValueMapList.get(assignAction.getEntityId());
                if (CollectionUtils.isNotEmpty(valueList) && StringUtils.isNotBlank(assignAction.getVariableId())) {
                    String variableId = assignAction.getVariableId();
                    String value = valueList.stream()
                            .map(EntityValueBO::getValue)
                            .collect(Collectors.joining(","));
                    SessionContextHelper.variableAssign(resource, sessionContext, eventContext, variableId, value, assignAction.getEntityId());
                }
            }
        }
    }

    /**
     * 目前逻辑是无法走到这里来, 如果是答案未播放完成, 且未命中任何意图, 会被统一的打断并重播逻辑处理掉
     * 如果是已经播放完成了, 应该是触发跳转动作了也不会触发到这里, 只有那种临界值的情况, 后续观察下
     */
    private Optional<ChatResponse> userSayFinishOnJumpNode(JumpNodeRuntime jumpNode) {
        if (!checkNodeLastAnswerPlayFinish(jumpNode)) {
            // 判断是否当前正在播放这个答案
            if (nodeLastAnswerIsRobotLastAnswer(sessionContext, eventContext, context, jumpNode)) {
                log.info("跳转节点打断继续播放录音");
                DebugLogUtils.commonDebugLog(eventContext, "打断成功，意图不明确，继续播报");
                return generateContinuePlayAnswer(jumpNode);
            } else {
                log.info("已经播放过其他答案了, 需要切换答案");
                DebugLogUtils.commonDebugLog(eventContext, "未命中意图，跳转节点返回，再执行一遍", false);
                return generateResponseByNode(jumpNode);
            }
        }
        // 跳转节点播放完成后直接执行跳转动作
        log.info("当前节点是跳转节点, 且当前节点允许打断时, 则执行跳转动作");
        eventContext.getDebugLog().add("未命中任何意图, 执行跳转动作");
        return generateActionResponse(jumpNode);
    }

    private Optional<ChatResponse> repeatAnswer(StepFlowContext context) {
        NodeRuntime<?> currentNode = getCurrentNode(context);
        log.info("处理重复上一句, 当前节点:{}:{}:{}", currentNode.getLabel(), currentNode.getName(), currentNode.getType());
        return generateResponseByNode(currentNode, true);
    }


    /**
     * 是否在对话流的处理过程中, 回复了其他的答案
     * todo 这里方法的命名不够准确
     */
    private boolean isJumpBackFromOther(SessionContext sessionContext,
                                        EventContext eventContext,
                                        StepFlowContext context) {
        ActiveManagerInfo activeInfo = sessionContext.getActiveManagerInfo();
        if (activeInfo == null) {
            return false;
        }
        // 说明一直在这个流程, 不是从其他地方回来的
        return !ActiveTypeEnum.STEP.equals(activeInfo.getActiveType())
                || !stepId.equals(activeInfo.getOriginId());
    }

    /**
     * 判断是否需要重新播放当前的录音
     * 重新播放的条件是, 在该节点跳出到问答知识和特殊语境
     */
    private boolean nodeLastAnswerNeedReplay(ChatNodeRuntime currentChatNode) {
        // 判断是否从该节点跳出了
        // 就是判断最后activeManagerInfo是否是问答知识或者特殊语境即可
        int replyThreshold = currentChatNode.getCustomReplayThreshold();

        Optional<NodeAnswerRuntime> currentAnswerOpt = getCurrentAnswer(currentChatNode);
        if (!currentAnswerOpt.isPresent()) {
            // 除非是bug, 不然不可能没有当前节点的id
            log.warn("对话逻辑异常, 查询当前节点的当前答案异常, stepId={}, stepName={}, nodeId={}, nodeName={}",
                    stepId, stepName, currentChatNode.getId(), currentChatNode.getName());
            return false;
        }
        NodeAnswerRuntime answer = currentAnswerOpt.get();
        double progress = getAnswerPlayProgress(answer, ApplicationConstant.AUDIO_PLAY_FINISH_PROGRESS);
        boolean result = progress < replyThreshold;
        log.info("判断当前节点是否需要重新播放答案, nodeId={}, nodeName={}, 答案={}, 答案播放进度={}, 重复播放阈值={}, 结果={}",
                currentChatNode.getId(), currentChatNode.getName(), answer.getText(), progress, replyThreshold, result);
        return result;
    }

    // 是否需要切换答案重播
    private boolean needSwitchAnswerReplay(ChatNodeRuntime currentChatNode) {
        if (!currentChatNode.isEnableSwitchAnswerReplayOnExceedThreshold()) {
            return false;
        }

        // 判断是否有切换答案

        Optional<NodeAnswerRuntime> currentAnswerOpt = getCurrentAnswer(currentChatNode);
        if (!currentAnswerOpt.isPresent()) {
            return false;
        }

        Optional<NodeAnswerRuntime> nextAnswerOpt = getNextAvailableAnswer(currentChatNode);
        return nextAnswerOpt.filter(nodeAnswerRuntime -> !currentAnswerOpt.get().equals(nodeAnswerRuntime)).isPresent();
    }

    private double getCurrentAnswerPlayProgress(NodeRuntime<?> node, double defaultProgress) {
        Optional<NodeAnswerRuntime> currentAnswerOpt = getCurrentAnswer(node);
        if (!currentAnswerOpt.isPresent()) {
            // 除非是bug, 不然不可能没有当前节点的id
            log.warn("对话逻辑异常, 查询当前节点的当前答案异常, stepId={}, stepName={}, nodeId={}, nodeName={}",
                    stepId, stepName, node.getId(), node.getName());
            return defaultProgress;
        }
        return getAnswerPlayProgress(currentAnswerOpt.get(), defaultProgress);
    }

    private double getAnswerPlayProgress(NodeAnswerRuntime answer, double defaultProgress) {
        return sessionContext.getAnswerProgressMap().computeIfAbsent(answer.getUniqueId(), (id) -> {
            log.info("未获取到答案[id={}]的录音播放进度, 故返回默认值: {}", id, defaultProgress);
            return defaultProgress;
        });
    }

    private Optional<NodeRuntime<?>> getNextNodeByIntentOrDefaultIntent(ChatNodeRuntime chatNode, String intentId) {
        if (checkHasNextNodeByIntentId(chatNode, intentId)) {
            return getNextNodeByIntent(chatNode, intentId);
        }
        return getNextNodeByIntent(chatNode, ApplicationConstant.DEFAULT_INTENT_ID);
    }

    private Optional<NodeRuntime<?>> getNextNodeByIntent(ChatNodeRuntime chatNode, String intentId) {
        if (StringUtils.isBlank(intentId)) {
            return Optional.empty();
        }
        String nextNodeId = chatNode.origin.getIntentRelatedNodeMap().get(intentId);
        return loopFindNextNode(chatNode, intentId, nextNodeId);
    }

    private boolean checkHasNextNodeByIntentId(ChatNodeRuntime chatNode, String intentId) {
        if (StringUtils.isBlank(intentId)) {
            return false;
        }
        String nextNodeId = chatNode.origin.getIntentRelatedNodeMap().get(intentId);
        return nextNodeId != null && nodeMap.get(nextNodeId) != null;
    }

    private Optional<NodeRuntime<?>> loopFindNextNode(NodeRuntime<?> currentNode, String intentId, String nextNodeId) {
        return Optional.ofNullable(nodeMap.get(nextNodeId))
                .map(next -> {
                    eventContext.getPrevNodeInfoList().add(NodeJumpInfo.of(stepId, currentNode.origin, intentId, next.origin));
                    return next;
                })
                .map(this::getNextSkipJudgeNode)
                .map(this::getNextTrySkipCollect);
    }

    /**
     * 跳过判断节点, 获取下一个节点, 如果节点是判断节点, 则获取匹配的分支
     */
    private NodeRuntime<? extends DialogBaseNodePO> getNextSkipJudgeNode(NodeRuntime<? extends DialogBaseNodePO> node) {
        if (node.origin instanceof DialogJudgeNodePO) {
            DialogJudgeNodePO judgeNode = (DialogJudgeNodePO) node.origin;
            // 判断匹配的分支
            NodeConditionBranchPO matchBranch = getMatchBranch(judgeNode, sessionContext);
            if (Objects.isNull(matchBranch)) {
                log.warn("botId:{}, 节点:{}, 节点数据配置错误, 未成功获取匹配的分支", judgeNode.getBotId(), judgeNode.getLabel());
                return null;
            }
            // 执行分支动作
            if (BooleanUtils.isTrue(matchBranch.getEnableAction()) && CollectionUtils.isNotEmpty(matchBranch.getActionList())) {
                for (BranchActionPO branchAction : matchBranch.getActionList()) {
                    if (BranchActionCategoryEnum.isAssign(branchAction.getCategory())) {
                        String value = ConditionVarTypeEnum.isVar(branchAction.getVarType())
                                ? sessionContext.getGlobalVariableValueMap().getOrDefault(Optional.ofNullable(resource.getVariableIdMap().get(branchAction.getValue())).map(VariablePO::getName).orElse(null), "")
                                : branchAction.getValue();
                        // 赋值
                        SessionContextHelper.variableAssign(resource, sessionContext, eventContext, branchAction.getVarId(), value);
                    } else {
                        // 清空
                        SessionContextHelper.variableAssign(resource, sessionContext, eventContext, branchAction.getVarId(), "", null, true);
                    }
                }
            }
            log.info("节点:{}, 匹配的分支:{}", judgeNode.getLabel(), matchBranch.getName());
            String nextNodeId = judgeNode.getBranchRelatedNodeMap().get(matchBranch.getId());
            NodeRuntime<? extends DialogBaseNodePO> nextNode = nodeMap.get(nextNodeId);
            if (Objects.isNull(nextNode)) {
                log.warn("获取下一个节点失败, stepId={}, stepName={}, nodeId={}, nodeName={}, nextNodeId={}",
                        stepId, stepName, node.getId(), node.getName(), nextNodeId);
                return null;
            } else {
                DebugLogUtils.matchNode(eventContext, node.origin);
                DebugLogUtils.matchBranch(eventContext, matchBranch);
                DebugLogUtils.matchCandidateIntent(eventContext);
            }
//            // todo 这个意图id怎么设置?
//            eventContext.getPrevNodeInfoList().add(NodeJumpInfo.of(stepId, node.origin, matchBranch.getId(), nextNode.origin));
            return loopFindNextNode(node, matchBranch.getId(), nextNodeId).orElse(null);
        } else {
            return node;
        }
    }

    /**
     * 获取下一个节点是采集节点时, 判断下是否可以直接跳过
     */
    private NodeRuntime<? extends DialogBaseNodePO> getNextTrySkipCollect(NodeRuntime<? extends DialogBaseNodePO> node) {
        if (node instanceof  CollectNodeRuntime) {
            CollectNodeRuntime collectNode = (CollectNodeRuntime) node;

            // 执行赋值
            execAssignFromPreInput(eventContext, collectNode);

            // 计算下一个待提问的问题下标
            CollectNodeContext collectNodeContext = context.getCollectNodeContextMap().computeIfAbsent(collectNode.id, (x) -> new CollectNodeContext());
            Optional<Integer> nextQuestionIndex = calculateNextQuestionIndex(sessionContext, collectNode, collectNodeContext);
            boolean skip = !nextQuestionIndex.isPresent();

            if (!skip) {
                return node;
            }
            DebugLogUtils.matchNode(eventContext, node.origin);
            log.debug("节点待采集的实体已全部采集完成, 跳过当前节点, nodeId={}, nodeName={}", node.getId(), node.getName());
            if (checkHasNextNodeByIntentId(collectNode, ApplicationConstant.COLLECT_SUCCESS_INTENT_ID)) {
                DebugLogUtils.commonDebugLog(eventContext, "节点待采集的实体已全部采集完成, 执行采集成功分支");
                return getNextNodeByIntent(collectNode, ApplicationConstant.COLLECT_SUCCESS_INTENT_ID).orElse(null);
            } else {
                DebugLogUtils.commonDebugLog(eventContext, "节点待采集的实体已全部采集完成, 执行兜底分支");
                return getNextNodeByDefaultIntent(collectNode).orElse(null);
            }
        } else {
            return node;
        }
    }

    private NodeConditionBranchPO getMatchBranch(DialogJudgeNodePO node, SessionContext sessionContext) {
        ConditionBranchPredicate predicate = new ConditionBranchPredicate(resource, sessionContext, eventContext);
        // 对branch进行排序, 默认分支放在最后
        List<NodeConditionBranchPO> branchList = new ArrayList<>();
        for (NodeConditionBranchPO b : node.getBranchList()) {
            if (BooleanUtils.isNotTrue(b.getIsDefault())) {
                branchList.add(b);
            }
        }
        for (NodeConditionBranchPO b : node.getBranchList()) {
            if (BooleanUtils.isTrue(b.getIsDefault())) {
                branchList.add(b);
            }
        }
        Optional<NodeConditionBranchPO> firstMatchOpt = branchList.stream()
                .filter(predicate)
                .findFirst();
        return firstMatchOpt.orElse(null);
    }

    private Optional<NodeRuntime<?>> getNextNodeByDefaultIntent(ChatNodeRuntime chatNode) {
        return getNextNodeByIntent(chatNode, ApplicationConstant.DEFAULT_INTENT_ID);
    }

    private Optional<ChatResponse> aiSayFinish(StepFlowContext context) {
        NodeRuntime<?> currentNode = getCurrentNode(context);
        if (Objects.isNull(currentNode)) {
            log.warn("获取当前节点失败, stepId={}, stepName={}, nodeId={}", stepId, stepName, context.getCurrentNodeId());
            return Optional.empty();
        }
        log.info("处理aiSayFinish, 当前节点:{}:{}:{}", currentNode.getLabel(), currentNode.getName(), currentNode.getType());

        if (context.isWaitUserSayFinish()
                && currentNode.id.equals(context.getWaitUserSayFinishNodeId())) {
            log.info("当前在等待用户说完, 返回等待用户说完事件");
            return generateWaitUserSayFinishResponse((ChatNodeRuntime) currentNode);
        }

        // 信息查询节点ai播报完成
        if (currentNode instanceof QueryNodeRuntime) {
            // 如果还没拿到接口响应，轮播当前节点答案
            if (SessionContextHelper.isWaitingResponse(sessionContext, currentNode.getId())) {
                log.info("当前正在等待http响应，轮播话术");
                return generateResponseByNode(currentNode, true);
            }
            // 拿到结果直接走兜底分支
            Optional<NodeRuntime<?>> nextNodeOpt = getNextNodeByIntent((QueryNodeRuntime) currentNode, ApplicationConstant.DEFAULT_INTENT_ID);
            if (nextNodeOpt.isPresent()) {
                return generateResponseByNode(nextNodeOpt.get());
            } else {
                log.warn("对话逻辑执行错误, 信息查询节点没有默认分支, stepId={}, stepName={}, nodeId={}, nodeName={}",
                        stepId, stepName, currentNode.getId(), currentNode.getName());
            }
        }

        return generateActionResponse(currentNode);
    }

    private Optional<ChatResponse> generateActionResponse(NodeRuntime<?> node) {
        ChatResponse response = new ChatResponse();
        response.setAnswerLocate(node.getNodeLocate());
        response.setActionList(node.getActionList());
        if (node.origin instanceof DialogJumpNodePO) {
            DialogJumpNodePO jumpNode = (DialogJumpNodePO) node.origin;
            DebugLogUtils.postJumpNodeHangupAction(eventContext, jumpNode.getJumpType(), resource);
        }
        return Optional.of(response);
    }

    private Optional<ChatResponse> enter(StepFlowContext context) {
        // 有两种路径会调用的这表, 一种是通过流程间的跳转/意图触发, 一种是回到原主动流程
        // 判断是否是重复命中当前流程
        NodeRuntime<?> currentNode = getCurrentNode(context);
        if (Objects.isNull(currentNode)) {
            log.warn("获取当前节点失败, stepId={}, stepName={}, nodeId={}", stepId, stepName, context.getCurrentNodeId());
            return Optional.empty();
        }
        log.info("处理enter, 当前节点:{}:{}:{}", currentNode.getLabel(), currentNode.getName(), currentNode.getType());
        if (currentNode instanceof CollectNodeRuntime) {
            currentNode = getNextTrySkipCollect(currentNode);
        }
        if (isRepeatEnter()) {
            log.info("重复命中当前流程, 执行重复命中流程处理");
            return execReenter(currentNode);
        }
        return generateResponseByNode(currentNode);
    }

    private Optional<ChatResponse> execReenter(NodeRuntime<?> currentNode) {
        log.info("重复命中当前流程, 当前节点:{}:{}:{}", currentNode.getLabel(), currentNode.getName(), currentNode.getType());
        // 重复当前的对话流
        // 判断当前节点是否在根节点
        if (checkIsRootNode(currentNode)) {
            log.info("当前节点仍是根节点, 重新生成答案");
            if (checkNodeLastAnswerPlayFinish(currentNode)) {
                log.info("当前节点的答案已经播放完毕, 重新生成答案");
                return generateResponseByNode(currentNode);
            } else {
                return generateContinuePlayAnswer(currentNode);
            }
        } else {
            // 已经在其他节点了
            // 判断节点类型, 如果是对话节点, 则走默认分支, 如果是跳转节点,则走跳转逻辑
            if (currentNode instanceof ChatNodeRuntime) {
                // 走默认节点, 没有默认节点就返回空
                Optional<NodeRuntime<?>> nextDefaultNode = getNextNodeByDefaultIntent((ChatNodeRuntime) currentNode);
                if (nextDefaultNode.isPresent()) {
                    log.info("当前节点非根节点且为对话节点, 走默认兜底意图");
                } else {
                    log.info("当前节点非根节点且为对话节点, 且不存在默认意图, 故返回空答案");
                }
                return nextDefaultNode.flatMap(this::generateResponseByNode);
            } else {
                // 执行跳转
                log.info("当前节点非根节点且为跳转节点, 执行跳转逻辑");
                return generateActionResponse(currentNode);
            }
        }
    }

    /**
     * 当前节点的最后要播放的答案是否播放完成
     */
    private boolean checkNodeLastAnswerPlayFinish(NodeRuntime<?> node) {
        double playProgress = getCurrentAnswerPlayProgress(node, 0d);
        return playProgress >= ApplicationConstant.AUDIO_PLAY_FINISH_PROGRESS;
    }

    private boolean checkIsRootNode(NodeRuntime<?> node) {
        return rootNode.equals(node) || rootNode.getId().equals(node.getId());
    }

    private boolean isRepeatEnter() {
        return stepId.equals(sessionContext.getLastActiveStepId());
    }

    private NodeRuntime<?> getCurrentNode(StepFlowContext context) {
        NodeRuntime<?> nodeRuntime = nodeMap.get(context.getCurrentNodeId());
        if (Objects.isNull(nodeRuntime)) {
            log.warn("获取当前节点失败, stepId={}, stepName={}, nodeId={}", stepId, stepName, context.getCurrentNodeId());
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "获取当前节点失败");
        }
        return nodeRuntime;
    }

    private void updateCurrentNode(String nodeId) {
        context.setCurrentNodeId(nodeId);
    }

    private Optional<ChatResponse> generateResponseByNode(NodeRuntime<?> node) {
        return generateResponseByNode(node, true);
    }

    private Optional<ChatResponse> generateResponseByNode(NodeRuntime<?> node, boolean changeAnswer) {
        return generateResponseByNode(node, changeAnswer, false);
    }

    private Optional<ChatResponse> generateContinuePlayAnswer(NodeRuntime<?> node) {
        log.info("生成继续播放答案");
        return generateResponseByNode(node, false, true);
    }

    private Optional<ChatResponse> generateResponseByNode(NodeRuntime<?> node, boolean changeAnswer, boolean resume) {
        if (Objects.isNull(node)) {
            return Optional.empty();
        }

        // 执行常量赋值(如果有的话)
        executeConstantAssign(node);

        updateCurrentNode(node.getId());
        Optional<ChatResponse> response;
        switch (node.getType()) {
            case COLLECT:
                response = generateResponseByCollectNode((CollectNodeRuntime) node, changeAnswer);
                break;
            case CHAT:
            case QUERY:
            case KEY_CAPTURE:
                response = generateResponseByChatNode((ChatNodeRuntime) node, changeAnswer);
                break;
            case JUMP:
                response = generateResponseByJumpNode((JumpNodeRuntime) node, changeAnswer);
                break;
            default:
                throw new ComException(ComErrorCode.UNKNOWN_ERROR, "对话数据错误, 未处理的节点类型");
        }
        response.ifPresent(resp -> {
            if (resume) {
                AnswerAudioPlayConfig config = new AnswerAudioPlayConfig();
                config.setRepeatPlayStrategy(RepeatAnswerPlayStrategyEnum.RESUME);
                resp.setAnswerAudioPlayConfig(config);
            }
            // 查询节点增加异步查询http接口动作
            if (node instanceof QueryNodeRuntime && !SessionContextHelper.isWaitingResponse(sessionContext, node.getId())) {
                resp.getActionList().add(SessionContextHelper.generateHttpRequestAction(((QueryNodeRuntime) node).getHttpRequestInfo(), sessionContext, node.getId(), resource));
                SessionContextHelper.enterWaitingList(sessionContext, node.getId());
            }
        });
        // 当前节点设置了外呼中加微的话,在eventContext中添加一个需要加微的标记
        if (ActionHelper.containsAddWechatAction(node.origin.getActionList())) {
            eventContext.setNeedAddWechat(true);
        }
        return response;
    }

    private Optional<ChatResponse> generateResponseByCollectNode(CollectNodeRuntime collectNode, boolean changeAnswer) {
        // 用来忽略全局提取该节点实体逻辑
        sessionContext.getExecutedCollectNodeIdSet().add(collectNode.getId());

        // 计算下一个待提问的问题下标
        CollectNodeContext collectNodeContext = context.getCollectNodeContextMap().computeIfAbsent(collectNode.id, (x) -> new CollectNodeContext());
        Optional<Integer> nextQuestionIndex = calculateNextQuestionIndex(sessionContext, collectNode, collectNodeContext);
        if (!nextQuestionIndex.isPresent() && !eventContext.isRepeatAnswer()) {
            return Optional.empty();
        }

        if (eventContext.isRepeatAnswer()) {
            // 重复播放当前问题
            nextQuestionIndex = Optional.of(collectNodeContext.getQuestionIndex());
        }

        //获取可用答案
        int index = nextQuestionIndex.get();
        // 更新问题状态
        collectNodeContext.setQuestionIndex(index);
        Integer count = collectNodeContext.getQuestionAskCountMap().compute(index, (k, v) -> v == null ? 1 : v + 1);

        EntityCollectConfigRuntime entityConfig = collectNode.getEntityCollectConfigRuntimeList().get(index);
        DebugLogUtils.matchNode(eventContext, collectNode.origin);
        DebugLogUtils.commonDebugLog(eventContext, String.format("对实体[%s]进行第%s次引导", entityConfig.getEntityName(), count));

        Optional<NodeAnswerRuntime> answerOpt = changeAnswer ?
                getNextAvailableAnswer(collectNode.id, entityConfig.getAnswerRuntimeList()) :
                getCurrentAnswer(collectNode);
        if (!answerOpt.isPresent()) {
            // 数据校验异常, 正常不应该出现没有答案的情况
            log.warn("对话数据异常, 获取对话节点答案为空, botId={}, stepName={}, nodeLabel={}", sessionContext.getBotId(), stepName, collectNode.getLabel());
            return Optional.empty();
        }

        return generateAnswerResponse(collectNode, answerOpt.get());
    }

    private void executeConstantAssign(NodeRuntime<?> node) {
        if (BooleanUtils.isNotTrue(node.origin.getEnableAssign())
                || !node.origin.enableConstantAssign()) {
            return;
        }
        for (VarAssignActionItemPO assignAction : node.origin.getConstantAssign().getAssignActionList()) {
            SessionContextHelper.variableAssign(resource, sessionContext, eventContext, assignAction.getVariableId(), assignAction.getConstantValue());
        }
    }

    /**
     * 处理对话节点, 就是根据节点准备和包装答案
     * @param chatNode 对话节点
     * @return 响应内容
     */
    private Optional<ChatResponse> generateResponseByChatNode(ChatNodeRuntime chatNode) {
       return generateResponseByChatNode(chatNode, true);
    }

    private Optional<ChatResponse> generateResponseByChatNode(ChatNodeRuntime chatNode, boolean changeAnswer) {
        //获取可用答案
        DebugLogUtils.matchNode(eventContext, chatNode.origin);
        Optional<NodeAnswerRuntime> answerOpt = changeAnswer ? getNextAvailableAnswer(chatNode) : getCurrentAnswer(chatNode);
        if (!answerOpt.isPresent()) {
            log.info("当前节点没有可用答案, 获取可用的答案, 而不是返回空");
            answerOpt = getNextAvailableAnswer(chatNode);
        }
        if (!answerOpt.isPresent()) {
            // 数据校验异常, 正常不应该出现没有答案的情况
            log.warn("对话数据异常, 获取对话节点答案为空, botId={}, stepId={}, stepName={}, nodeId={}, nodeName={}", sessionContext.getBotId(), stepId, stepName, chatNode.getId(), chatNode.getName());
            return Optional.empty();
        }

        return generateAnswerResponse(chatNode, answerOpt.get());
    }

    private Optional<ChatResponse> generateAnswerResponse(NodeRuntime<?> node, NodeAnswerRuntime answer) {
        log.info("生成对话节点答案, stepName={},  nodeLabel={}, answerTemplate={}",stepName, node.getLabel(), answer.getText());
        AnswerResult answerResult = renderAnswer(node.origin, answer);
        // 是否允许打断等
        ChatResponse response = new ChatResponse();
        response.setAnswer(answerResult);
        // 更新节点最后的答案
        updateNodeLastAnswer(node.getId(), answer.getUniqueId());
        return Optional.of(response);
    }

    private Optional<NodeAnswerRuntime> getNextAvailableAnswer(String nodeId, List<NodeAnswerRuntime> answerList) {
        String preAnswerId = sessionContext.getNodeLastReplyAnswerIdMap().get(nodeId);
        NodeRuntime<?> nodeRuntime = nodeMap.get(nodeId);
        return AnswerSelectUtils.getNextAvailableId(preAnswerId, answerList, new AnswerPredicate(resource, sessionContext))
                .map(nodeRuntime.getAnswerMap()::get);
    }

    /**
     * 记录节点最后播放的答案id
     */
    private void updateNodeLastAnswer(String nodeId, String answerId) {
        sessionContext.getNodeLastReplyAnswerIdMap().put(nodeId, answerId);
    }

    private Optional<NodeAnswerRuntime> getNextAvailableAnswer(NodeRuntime<?> node) {
        return getNextAvailableAnswer(node.getId(), node.getAnswerList());
    }

    private Optional<NodeAnswerRuntime> getCurrentAnswer(NodeRuntime<?> node) {
        String nodeId = node.getId();
        String nodeCurrentAnswerId = sessionContext.getNodeLastReplyAnswerIdMap().get(nodeId);
        NodeRuntime<?> nodeRuntime = nodeMap.get(nodeId);
        ImmutableMap<String, NodeAnswerRuntime> answerRuntimeMap = nodeRuntime.getAnswerMap();
        return Optional.ofNullable(answerRuntimeMap.get(nodeCurrentAnswerId));
    }

    /**
     * 按条件组对答案进行过滤
     */
    private List<NodeAnswer> filterAnswer(List<NodeAnswer> answerList) {
        return answerList;
    }

    private Optional<ChatResponse> generateResponseByJumpNode(JumpNodeRuntime jumpNode, boolean changeAnswer) {
        DebugLogUtils.matchNode(eventContext, jumpNode.origin);
        Optional<NodeAnswerRuntime> answerOptional = changeAnswer ? getNextAvailableAnswer(jumpNode) : getCurrentAnswer(jumpNode);
        return answerOptional
                .map(nodeAnswerRuntime -> generateAnswerResponse(jumpNode, nodeAnswerRuntime))
                .orElseGet(() -> generateActionResponse(jumpNode));
    }

    private AnswerResult renderAnswer(DialogBaseNodePO node, NodeAnswerRuntime answer) {
        return AnswerRenderUtils.render(node, answer, sessionContext, resource);
    }

    @Override
    public String getName() {
        return step.getType() + ":" + step.getLabel() + ":"+ step.getName();
    }


    public String getStepName() {
        return step.getName();
    }

    @Override
    public List<ChatManagerTriggerCondition> getTriggerConditions(SessionContext sessionContext, EventContext context) {
        // 命中流程中的意图, 默认意图, 用户无应答
        List<ChatManagerTriggerCondition> result = new ArrayList<>();
        StepFlowContext stepFlowContext = getStepFlowContext(sessionContext);
        result.add(matchBySelectIntent(stepFlowContext));
        matchByUninterruptedReplyBranchIntentId(sessionContext, stepFlowContext).ifPresent(result::add);
        result.add(matchKeyCaptureSuccessOrFailed());
        result.add(triggerByUserSilence(sessionContext, context));
        matchCollectSuccess(sessionContext, context).ifPresent(result::add);
        triggerByDefaultIntent(stepFlowContext).ifPresent(result::add);
        triggerByJumpNode(stepFlowContext).ifPresent(result::add);
        currentEntityCollectSuccess(stepFlowContext, context).ifPresent(result::add);
        collectNodeSkipCondition(stepFlowContext, context).ifPresent(result::add);
        return result;
    }

    // 采集节点当前采集成功
    private Optional<ChatManagerTriggerCondition> currentEntityCollectSuccess(StepFlowContext stepFlowContext,
                                                                       EventContext eventContext) {
        // 当前节点
        NodeRuntime<?> currentNode = getCurrentNode(stepFlowContext);
        if (!(currentNode instanceof CollectNodeRuntime)) {
            return Optional.empty();
        }

        // 没有提取到, 直接返回空
        if (CollectionUtils.isEmpty(eventContext.getEntityValueList())) {
            return Optional.empty();
        }

        CollectNodeRuntime collectNode = (CollectNodeRuntime) currentNode;
        // 获取当前正在反问的实体
        CollectNodeContext collectNodeContext = collectNodeContext(stepFlowContext, collectNode.id);
        int index = collectNodeContext.getQuestionIndex();
        EntityCollectConfigRuntime currentEntityConfig = collectNode.getEntityCollectConfigRuntimeList().get(index);
        // 当前实体是否已经采集成功

        List<EntityValueBO> valueList = eventContext.getEntityValueList().stream()
                .filter(item -> currentEntityConfig.getEntityId().equals(item.getEntityId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(valueList)) {
            return Optional.empty();
        }

        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.COLLECT_NODE_CURRENT_ENTITY_SUCCESS);
        return Optional.of(condition);
    }

    // 采集节点当前采集成功
    private Optional<ChatManagerTriggerCondition> collectNodeSkipCondition(StepFlowContext stepFlowContext,
                                                                       EventContext eventContext) {
        // 当前节点
        NodeRuntime<?> currentNode = getCurrentNode(stepFlowContext);
        if (!(currentNode instanceof CollectNodeRuntime)) {
            return Optional.empty();
        }

        CollectNodeRuntime collectNode = (CollectNodeRuntime) currentNode;

        if (BooleanUtils.isNotTrue(collectNode.getOrigin().getEnableSkipCondition())
                || CollectionUtils.isEmpty(collectNode.getOrigin().getSkipConditionList())
                || CollectionUtils.isEmpty(eventContext.getCandidateIntentIdList())) {
            return Optional.empty();
        }

        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.COLLECT_NODE_SKIP_CONDITION);
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
        condition.setDynamicPredicate((sc, ec) -> {
            CollectNodeSkipConditionPredicate predicate = new CollectNodeSkipConditionPredicate(resource, sc, ec.getCandidateIntentIdList());
            return collectNode.getOrigin().getSkipConditionList().stream().anyMatch(predicate);
        });
        return Optional.of(condition);
    }

    private ChatManagerTriggerCondition matchBySelectIntent(StepFlowContext stepFlowContext) {
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.STEP_MATCH_NEXT_NODE);
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
        condition.setIntentIdSet(getCurrentNode(stepFlowContext).getNextExpectIntentIdNameMap().keySet());
        condition.setIntentRefType(IntentRefTypeEnum.NODE);
        condition.setMustNotMatchModes(Sets.newHashSet(SpecialChatModeEnum.DELAY_HANGUP, SpecialChatModeEnum.INAUDIBLE_REPEAT, SpecialChatModeEnum.UNINTERRUPTED));
        return condition;
    }

    private Optional<ChatManagerTriggerCondition> matchByUninterruptedReplyBranchIntentId(SessionContext sessionContext,
                                                                                          StepFlowContext stepFlowContext) {
        // 判断当前是否打断该流程
        if (!isInterruptStepFlow(sessionContext)) {
            return Optional.empty();
        }

        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.STEP_MATCH_NEXT_NODE);
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
        condition.setIntentIdSet(getCurrentNode(stepFlowContext).getUninterruptedOnlyReplyBranchIntentIdSet());
        condition.setIntentRefType(IntentRefTypeEnum.NODE);
        condition.setMustMatchModes(Collections.singleton(SpecialChatModeEnum.UNINTERRUPTED));
        condition.setMustNotMatchModes(Sets.newHashSet(SpecialChatModeEnum.DELAY_HANGUP, SpecialChatModeEnum.INAUDIBLE_REPEAT));
        return Optional.of(condition);
    }

    private boolean isInterruptStepFlow(SessionContext sessionContext) {
        return Objects.nonNull(sessionContext.getActiveManagerInfo())
                && step.getName().equals(sessionContext.getActiveManagerInfo().getOriginName());
    }

    private ChatManagerTriggerCondition matchKeyCaptureSuccessOrFailed() {
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.KEY_CAPTURE);
        condition.setChatEventSet(Sets.newHashSet(ChatEventTypeEnum.KEY_CAPTURE_SUCCESS, ChatEventTypeEnum.KEY_CAPTURE_FAILED));
        condition.setMustNotMatchModes(Sets.newHashSet(SpecialChatModeEnum.DELAY_HANGUP, SpecialChatModeEnum.INAUDIBLE_REPEAT, SpecialChatModeEnum.UNINTERRUPTED));
        condition.setMustMatchModes(Collections.singleton(SpecialChatModeEnum.KEY_CAPTURE));
        return condition;
    }

    private Optional<ChatManagerTriggerCondition> matchCollectSuccess(SessionContext sessionContext, EventContext context) {
        Set<String> expectIntentIdSet = SessionContextHelper.currentNodeExpectIntentIdSet(sessionContext, resource);
        if (!expectIntentIdSet.contains(ApplicationConstant.COLLECT_SUCCESS_INTENT_ID)) {
            return Optional.empty();
        }
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.NODE_MATCH_COLLECT_SUCCESS);
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
        condition.setIntentIdSet(Collections.singleton(ApplicationConstant.COLLECT_SUCCESS_INTENT_ID));
        condition.setIntentRefType(IntentRefTypeEnum.NODE);
        condition.setMustNotMatchModes(Sets.newHashSet(SpecialChatModeEnum.DELAY_HANGUP, SpecialChatModeEnum.INAUDIBLE_REPEAT, SpecialChatModeEnum.UNINTERRUPTED));
        return Optional.of(condition);
    }

    private Optional<ChatManagerTriggerCondition> triggerByDefaultIntent(StepFlowContext context) {
        NodeRuntime<?> currentNode = getCurrentNode(context);
        if (currentNode instanceof ChatNodeRuntime) {
            ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.STEP_NODE_DEFAULT_INTENT);
            condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
            condition.setDynamicPredicate((sc, ec) -> {
                return SessionContextHelper.currentNodeExpectIntentIdSet(sc, resource)
                        .contains(ApplicationConstant.DEFAULT_INTENT_ID);
            });
            condition.getMustNotMatchModes().add(SpecialChatModeEnum.UNINTERRUPTED);
            return Optional.of(condition);
        } else {
            return Optional.empty();
        }
    }

    private Optional<ChatManagerTriggerCondition> triggerByJumpNode(StepFlowContext context) {
        NodeRuntime<?> currentNode = getCurrentNode(context);
        if (currentNode instanceof JumpNodeRuntime) {
            ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.STEP_NODE_DEFAULT_INTENT);
            condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
            condition.getMustNotMatchModes().add(SpecialChatModeEnum.UNINTERRUPTED);
            return Optional.of(condition);
        } else {
            return Optional.empty();
        }
    }

    private ChatManagerTriggerCondition triggerByUserSilence(SessionContext sessionContext, EventContext context) {
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.STEP_NODE_USER_SILENCE_INTENT);
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SILENCE));
        condition.setDynamicPredicate((sc, ec) -> {
            return SessionContextHelper.currentNodeExpectIntentIdSet(sc, resource)
                    .contains(ApplicationConstant.USER_SILENCE_INTENT_ID);
        });
        condition.getMustNotMatchModes().add(SpecialChatModeEnum.UNINTERRUPTED);
        return condition;
    }
}
