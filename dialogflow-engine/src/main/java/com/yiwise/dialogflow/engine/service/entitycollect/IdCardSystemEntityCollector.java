package com.yiwise.dialogflow.engine.service.entitycollect;

import com.yiwise.dialogflow.entity.bo.EntityValueBO;
import com.yiwise.dialogflow.entity.enums.SystemEntityCategoryEnum;
import com.yiwise.dialogflow.utils.NumberUtils;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 身份证使用正则收集了, 之前版本是用算法, 这个在语音对话过程中, 应该极低概率使用
 * 而且就收集18位数字就可以了
 */
@Slf4j
public class IdCardSystemEntityCollector implements SystemEntityCollector {

    private static final Pattern pattern = Pattern.compile("^[1-9][0-9]{5}(19|20)[0-9]{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)[0-9]{3}([0-9]|(X|x))");

    @Override
    public void register(Map<SystemEntityCategoryEnum, SystemEntityCollector> registry) {
        registry.put(SystemEntityCategoryEnum.ID_CARD, this);
    }

    @Override
    public Flux<EntityValueBO> asyncCollect(SystemEntityCategoryEnum category, String userInput, String userInputPinyin) {
        if (userInput.length() < 18) {
            return Flux.empty();
        }
        Matcher matcher = pattern.matcher(userInput);
        List<EntityValueBO> list = new ArrayList<>();
        while (matcher.find()) {
            String value = matcher.group();
            String number = NumberUtils.str2Arab(value);
            EntityValueBO bo = new EntityValueBO();
            bo.setOriginValue(value);
            bo.setValue(number);
            bo.setStartOffset(matcher.start());
            bo.setEndOffset(matcher.end() - 1);
            bo.setSystemEntityCategory(category);
            bo.setInputText(userInput);
            list.add(bo);
        }
        return Flux.fromIterable(list);
    }
}
