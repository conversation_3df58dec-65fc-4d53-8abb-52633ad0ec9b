package com.yiwise.dialogflow.engine.service.entitycollect;

import com.fasterxml.jackson.databind.JsonNode;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.MdcLogIdLifterTransformer;
import com.yiwise.dialogflow.entity.bo.EntityValueBO;
import com.yiwise.dialogflow.entity.enums.SystemEntityCategoryEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;

@Slf4j
public class AddressSystemEntityCollector implements SystemEntityCollector {

    private static final WebClient webClient = AppContextUtils.getBean(WebClient.class);

    @Override
    public void register(Map<SystemEntityCategoryEnum, SystemEntityCollector> registry) {
        registry.put(SystemEntityCategoryEnum.ADDRESS, this);
        registry.put(SystemEntityCategoryEnum.PERSON, this);
        registry.put(SystemEntityCategoryEnum.ORGANIZATION, this);
    }

    @Override
    public Flux<EntityValueBO> asyncCollect(SystemEntityCategoryEnum category, String userInput, String userInputPinyin) {
        if (StringUtils.isBlank(userInput)) {
            return Flux.empty();
        }
        String type = "LOC";
        if (category == SystemEntityCategoryEnum.PERSON) {
            type = "PER";
        } else if (category == SystemEntityCategoryEnum.ORGANIZATION) {
            type = "ORG";
        }
        return requestAlgorithm(userInput, type)
                .flatMapIterable(entityValues -> entityValues)
                .doOnNext(entityValue -> {
                    entityValue.setSystemEntityCategory(category);
                });
    }

    private Mono<List<EntityValueBO>> requestAlgorithm(String userInput, String type) {
        Map<String, String> param = new HashMap<>();
        param.put("text", userInput);
        param.put("type", type);
        String url = ApplicationConstant.ALGORITHM_ADDRESS_ENTITY_URL;
        if (StringUtils.isBlank(url)) {
            log.warn("[LogHub_Warn]算法提取地址接口地址为空, url={}", url);
            return Mono.empty();
        }
        log.info("url:{}, params={}", url, JsonUtils.object2PrettyString(param));
        return webClient.method(HttpMethod.POST)
                .uri(url)
                .body(BodyInserters.fromValue(JsonUtils.object2String(param)))
                .retrieve()
                .bodyToMono(String.class)
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .doOnNext(response -> {
                    log.info("请求算法提取地址:response={}", response);
                })
                .map(json -> parseResult(userInput, json, type))
                .onErrorResume(e -> {
                    log.warn("[LogHub_Warn]调用算法提取地址接口失败, url={}, body={}, 失败原因={}", url, JsonUtils.object2String(param), e.getMessage());
                    return Mono.empty();
                });
    }

    private List<EntityValueBO> parseResult(String userInput, String json, String type) {
        JsonNode node = JsonUtils.string2JsonNode(json);
        if (node != null && node.get(type) != null) {
            JsonNode loc = node.get(type);
            if (loc.get(0) != null) {
                String value = loc.get(0).asText();
                EntityValueBO entityValue = new EntityValueBO();
                entityValue.setOriginValue(value);
                entityValue.setValue(value);

                int indexOf = userInput.indexOf(value);
                if (indexOf == -1) {
                    entityValue.setStartOffset(0);
                    entityValue.setEndOffset(value.length() - 1);
                } else {
                    entityValue.setStartOffset(indexOf);
                    entityValue.setEndOffset(indexOf + value.length() - 1);
                }
                return Collections.singletonList(entityValue);
            }
        }
        return Collections.emptyList();
    }

}
