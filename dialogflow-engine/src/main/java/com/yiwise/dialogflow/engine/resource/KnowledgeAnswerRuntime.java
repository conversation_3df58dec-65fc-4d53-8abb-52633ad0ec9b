package com.yiwise.dialogflow.engine.resource;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.yiwise.dialogflow.engine.share.action.*;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.enums.JumpTargetEnum;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.entity.po.KnowledgeAnswer;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.yiwise.dialogflow.engine.helper.ActionHelper.generateHangupAction;

@Slf4j
@Getter
public class KnowledgeAnswerRuntime extends BaseAnswerRuntime<KnowledgeAnswer> implements UninterruptedRuntime {

    final ImmutableList<ChatAction> actionList;

    final ImmutableSet<String> uninterruptedReplyKnowledgeIdSet;
    final ImmutableSet<String> uninterruptedReplyStepIdSet;
    final ImmutableSet<String> uninterruptedReplySpecialAnswerIdSet;

    public KnowledgeAnswerRuntime(KnowledgeAnswer knowledgeAnswer, RobotRuntimeResource resource, AnswerLocateBO locate) {
        super(knowledgeAnswer, resource, locate);

        List<ChatAction> actionList = new ArrayList<>();
        String sourceId = AnswerSourceEnum.KNOWLEDGE.equals(locate.getAnswerSource()) ?
                locate.getKnowledgeId() : locate.getSpecialAnswerConfigId();
        switch (knowledgeAnswer.getPostAction()) {
            case SPECIFIED_STEP:
                JumpAction specialStepJump = new JumpAction(locate.getAnswerSource(), sourceId);
                specialStepJump.setJumpTarget(JumpTargetEnum.SPECIFIED_STEP);
                specialStepJump.setJumpStepId(knowledgeAnswer.getJumpStepId());
                actionList.add(specialStepJump);
                break;
            case ORIGINAL_STEP:
                JumpAction originStepJump = new JumpAction(locate.getAnswerSource(), sourceId);
                originStepJump.setJumpTarget(JumpTargetEnum.ORIGINAL_STEP);
                actionList.add(originStepJump);
                break;
            case HANG_UP:
                actionList.add(generateHangupAction());
                break;
            case WAIT:
                actionList.add(new WaitAction(resource.getBotConfig().getUserSilenceMs()));
                break;
            case HUMAN_SERVICE:
                actionList.add(new SwitchToHumanServiceAction());
                break;
            default:
                log.warn("未处理的post action: {}", knowledgeAnswer.getPostAction());
                break;
        }
        this.actionList = ImmutableList.copyOf(actionList);
        this.uninterruptedReplyStepIdSet = convertList(knowledgeAnswer.getUninterruptedReplyStepIdList());
        this.uninterruptedReplyKnowledgeIdSet = convertList(knowledgeAnswer.getUninterruptedReplyKnowledgeIdList());
        this.uninterruptedReplySpecialAnswerIdSet = convertList(knowledgeAnswer.getUninterruptedReplySpecialAnswerIdList());
    }

    private ImmutableSet<String> convertList(List<String> list) {
        if (Objects.isNull(list)) {
            return ImmutableSet.of();
        }
        return ImmutableSet.copyOf(list);
    }

    @Override
    public ImmutableSet<String> getUninterruptedOnlyReplyStepIdSet() {
        return uninterruptedReplyStepIdSet;
    }

    @Override
    public ImmutableSet<String> getUninterruptedOnlyReplyKnowledgeIdSet() {
        return uninterruptedReplyKnowledgeIdSet;
    }

    @Override
    public ImmutableSet<String> getUninterruptedOnlyReplySpecialAnswerIdSet() {
        return uninterruptedReplySpecialAnswerIdSet;
    }
}
