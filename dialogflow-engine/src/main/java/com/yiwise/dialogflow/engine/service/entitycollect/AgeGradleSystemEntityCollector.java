package com.yiwise.dialogflow.engine.service.entitycollect;

import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.MdcLogIdLifterTransformer;
import com.yiwise.dialogflow.entity.bo.EntityValueBO;
import com.yiwise.dialogflow.entity.enums.EntityTypeEnum;
import com.yiwise.dialogflow.entity.enums.SystemEntityCategoryEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.Serializable;
import java.util.*;

@Slf4j
public class AgeGradleSystemEntityCollector implements SystemEntityCollector {
    private static final WebClient webClient = AppContextUtils.getBean(WebClient.class);

    @Override
    public void register(Map<SystemEntityCategoryEnum, SystemEntityCollector> registry) {
        registry.put(SystemEntityCategoryEnum.AGE, this);
        registry.put(SystemEntityCategoryEnum.GRADE, this);
    }

    @Override
    public Flux<EntityValueBO> asyncCollect(SystemEntityCategoryEnum category, String userInput, String userInputPinyin) {
        return asyncCollect(Collections.singletonList(category), userInput, userInputPinyin);
    }

    @Override
    public Flux<EntityValueBO> asyncCollect(Collection<SystemEntityCategoryEnum> categoryList, String userInput, String userInputPinyin) {
        if (CollectionUtils.isEmpty(categoryList) || StringUtils.isBlank(userInput)) {
            return Flux.empty();
        }
        return asyncCollect(userInput, categoryList).flatMapMany(Flux::fromIterable);
    }

    private Mono<List<EntityValueBO>> asyncCollect(String userInput, Collection<SystemEntityCategoryEnum> categoryList) {
        if (CollectionUtils.isEmpty(categoryList) || StringUtils.isBlank(userInput)) {
            return Mono.empty();
        }
        Map<String, String> param = new HashMap<>(2);
        param.put("model", "recipient");
        param.put("texts", userInput);
        String url = ApplicationConstant.ALGORITHM_AGE_GRADE_ENTITY_URL;
        if (StringUtils.isBlank(url)) {
            log.warn("[LogHub_Warn]算法提取年龄、年级接口url为空, url={}", url);
            return Mono.empty();
        }
        log.info("url:{}, params={}", url, JsonUtils.object2String(param));
        return webClient.method(HttpMethod.POST)
                .uri(url)
                .body(BodyInserters.fromValue(JsonUtils.object2String(param)))
                .retrieve()
                .bodyToMono(String.class)
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .doOnNext(response -> log.info("请求算法提取年龄、年级:response={}", response))
                .map(json -> parseResult(userInput, json, categoryList))
                .onErrorResume(e -> {
                    log.warn("[LogHub_Warn]调用算法提取年龄、年级接口失败, url={}, body={}, 失败原因={}", url, JsonUtils.object2String(param), e.getMessage());
                    return Mono.empty();
                });
    }

    private List<EntityValueBO> parseResult(String userInput, String json, Collection<SystemEntityCategoryEnum> categoryList) {
        if (StringUtils.isBlank(json)) {
            return Collections.emptyList();
        }
        AlgorithmEntityResponse entityValue = JsonUtils.string2Object(json, AlgorithmEntityResponse.class);
        if (Objects.isNull(entityValue) || CollectionUtils.isEmpty(entityValue.getEntities())) {
            return Collections.emptyList();
        }
        List<EntityValueBO> result = new ArrayList<>();
        for (SystemEntityCategoryEnum category : categoryList) {
            List<EntityValueBO> tmpResult = new ArrayList<>();
            switch (category) {
                case AGE:
                    for (Map<String, List<EntityDetail>> entity : entityValue.getEntities()) {
                        parse(entity, "年龄").ifPresent(tmpResult::addAll);
                    }
                    break;
                case GRADE:
                    for (Map<String, List<EntityDetail>> entity : entityValue.getEntities()) {
                        parse(entity, "学年").ifPresent(tmpResult::addAll);
                    }
                    break;
                default:
                    break;
            }
            if (CollectionUtils.isNotEmpty(tmpResult)) {
                result.addAll(tmpResult);
                for (EntityValueBO value : tmpResult) {
                    value.setEntityType(EntityTypeEnum.SYSTEM);
                    value.setInputText(userInput);
                    value.setSystemEntityCategory(category);
                }
            }
        }
        return result;
    }

    private Optional<List<EntityValueBO>> parse(Map<String, List<EntityDetail>> entityMap, String key) {
        if (!entityMap.containsKey(key)) {
            return Optional.empty();
        }

        List<EntityDetail> entityList = entityMap.get(key);
        if (CollectionUtils.isEmpty(entityList)) {
            return Optional.empty();
        }
        List<EntityValueBO> reslutList = new ArrayList<>();
        for (EntityDetail entity : entityList) {
            parse(entity).ifPresent(reslutList::add);
        }
        return Optional.of(reslutList);
    }

    private Optional<EntityValueBO> parse(EntityDetail entity) {
        if (Objects.isNull(entity) || Objects.isNull(entity.getStart()) || Objects.isNull(entity.getEnd()) || StringUtils.isBlank(entity.getText()) || StringUtils.isBlank(entity.getSimplified())) {
            return Optional.empty();
        }

        EntityValueBO entityValue = new EntityValueBO();
        entityValue.setOriginValue(entity.getText());
        entityValue.setValue(entity.getSimplified());
        entityValue.setStartOffset(entity.getStart());
        entityValue.setEndOffset(entity.getEnd());
        return Optional.of(entityValue);
    }

    @Data
    private static class AlgorithmEntityResponse implements Serializable {

        private List<Map<String, List<EntityDetail>>> entities;
    }

    @Data
    private static class EntityDetail implements Serializable {

        private Integer start;

        private Integer end;

        private String simplified;

        private String text;
    }
}
