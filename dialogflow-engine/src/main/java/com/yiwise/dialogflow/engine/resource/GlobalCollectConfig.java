package com.yiwise.dialogflow.engine.resource;

import lombok.Data;

/**
 * 全局实体提取配置
 */
@Data
public class GlobalCollectConfig {
    /**
     * 实体id
     */
    String entityId;

    /**
     * 动态变量 id, 采集成功后会执行赋值
     */
    String variableId;

    /**
     * 是否需要赋值, enableAssign 为 true 时, 且variableId不为空, 则将用户输入提取到的实体进行赋值
     * 加这个字段主要是实现采集节点中的读取已有对话内容, 该配置下进行全局采集, 但是不进行动态变量赋值
     */
    boolean enableAssign;

    /**
     * 来源
     */
    String sourceLabel;

    /**
     * 来源 id
     */
    String sourceId;
}