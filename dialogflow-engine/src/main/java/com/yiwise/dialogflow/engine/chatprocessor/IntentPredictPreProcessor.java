package com.yiwise.dialogflow.engine.chatprocessor;

import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.context.*;
import com.yiwise.dialogflow.engine.helper.IntentPredictHelper;
import com.yiwise.dialogflow.engine.helper.SessionContextHelper;
import com.yiwise.dialogflow.engine.resource.NodeAssignConfigRuntime;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.entity.bo.EntityValueBO;
import com.yiwise.dialogflow.entity.bo.algorithm.PredictResult;
import com.yiwise.dialogflow.entity.enums.PredictTypeEnum;
import com.yiwise.dialogflow.entity.vo.SimpleIntentVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 执行意图预测逻辑, 预测结果写入eventContext中
 */
@Slf4j
public class IntentPredictPreProcessor extends AbstractChatPreProcessor {

    protected RobotRuntimeResource resource;

    public IntentPredictPreProcessor(RobotRuntimeResource resource) {
        this.resource = resource;
    }

    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return "意图预测预处理";
    }

    /**
     * 意图判断逻辑：
     * - 先获取算法预测结果（如果算法开关为开的话）
     * - 算法高于阈值上限，直接取算法结果
     * - 算法低于阈值上限高于阈值下限，取算法与正则的交集中置信度最高的，若无交集，走正则逻辑
     * - 算法低于阈值下限，取正则预测结果
     * - 正则结果先根据关键词长度和业务知识/一般知识进行排序
     * - 组合意图优先级高于单个意图
     */
    @Override
    public void doProcess(SessionContext sessionContext, EventContext eventContext) {
        doProcessAsync(sessionContext, eventContext).block();
    }

    protected void doAfterIntentPredict(List<PredictResult> candidatePredictResultList, EventContext eventContext, SessionContext sessionContext) {
        if (checkCollectSuccess(sessionContext, eventContext)) {
            PredictResult collectSuccessPredictResult = generateCollectSuccessPredictResult();
            candidatePredictResultList = ListUtils.union(Collections.singletonList(collectSuccessPredictResult), candidatePredictResultList);
        }
        eventContext.setCandidatePredictResultList(candidatePredictResultList);
        eventContext.setCandidateIntentIdList(MyCollectionUtils.listToConvertList(candidatePredictResultList, PredictResult::getIntentId));
        eventContext.setReusePreStepPredictResult(true);

        // 记录所有的候选结果
        List<String> intentIdList = candidatePredictResultList.stream()
                .map(PredictResult::getIntentId)
                .distinct()
                .collect(Collectors.toList());
        UserInputPredictInfo predictInfo = new UserInputPredictInfo();
        predictInfo.setUserInput(eventContext.getUserInput());
        predictInfo.setSequence(eventContext.getSeq());
        predictInfo.setPredictIntentIdList(intentIdList);
        SessionContextHelper.getCurrentActiveStepFlowContext(sessionContext)
                        .ifPresent(stepFlowContext -> predictInfo.setActiveNodeId(stepFlowContext.getCurrentNodeId()));
        sessionContext.getInputPredictInfoList().add(predictInfo);
    }

    /**
     * 检查当前节点是否启用了采集成功分支, 且实体收集成功
     */
    protected boolean checkCollectSuccess(SessionContext sessionContext, EventContext eventContext) {
        if (CollectionUtils.isEmpty(eventContext.getEntityValueList())) {
            return false;
        }
        Optional<StepFlowContext> currentStepFlowContextOptional = SessionContextHelper.getCurrentActiveStepFlowContext(sessionContext);
        if (!currentStepFlowContextOptional.isPresent() || Objects.isNull(currentStepFlowContextOptional.get().getAssignConfig())) {
            return false;
        }
        NodeAssignConfigRuntime assignConfig = currentStepFlowContextOptional.get().getAssignConfig();
        if (BooleanUtils.isNotTrue(assignConfig.getEnableCollectSuccessIntent())) {
            return false;
        }

        Set<String> entityIdSet = assignConfig.getDependEntityIdSet();
        List<EntityValueBO> expectEntityValueList = eventContext.getEntityValueList().stream()
                .filter(item -> entityIdSet.contains(item.getEntityId()))
                .collect(Collectors.toList());
        return !CollectionUtils.isEmpty(expectEntityValueList);
    }

    private static PredictResult generateCollectSuccessPredictResult() {
        PredictResult predictResult = new PredictResult();
        predictResult.setIntentId(ApplicationConstant.COLLECT_SUCCESS_INTENT_ID);
        predictResult.setIntentName(ApplicationConstant.COLLECT_SUCCESS_INTENT_NAME);
        predictResult.setSimpleIntentInfo(SimpleIntentVO.collectSuccess());
        predictResult.setConfidence(1.0);
        predictResult.setPredictType(PredictTypeEnum.REGEX);
        predictResult.setCompositeIntentConditionResults(Collections.emptyList());
        return predictResult;
    }

    @Override
    protected Mono<Void> doProcessAsync(SessionContext sessionContext, EventContext eventContext) {
        if (eventContext.isReusePreStepPredictResult()) {
            log.info("复用前面步骤的预测结果, candidatePredictResultList:{}, candidateIntentIdList:{}", eventContext.getCandidatePredictResultList(), eventContext.getCandidateIntentIdList());
            doAfterIntentPredict(eventContext.getCandidatePredictResultList(), eventContext, sessionContext);
            return Mono.empty();
        }

        return IntentPredictHelper.predictAsync(eventContext.getUserInput(), resource.getIntentPredictRequiredResource(), SessionContextHelper.getLastAiSay(sessionContext, resource), sessionContext.getGlobalVariableValueMap())
                .doOnSuccess(result -> {
                    eventContext.setAlgorithmPredictIntent(result.getIntentResultDetail());
                    doAfterIntentPredict(result.getCandidatePredictResultList(), eventContext, sessionContext);
                }).then();
    }

}
