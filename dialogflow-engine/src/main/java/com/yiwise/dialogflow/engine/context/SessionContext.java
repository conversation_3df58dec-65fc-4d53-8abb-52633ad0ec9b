package com.yiwise.dialogflow.engine.context;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yiwise.dialogflow.engine.enums.SpecialChatModeEnum;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.*;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SessionContext {
    String sessionId;

    Long botId;

    Integer version;

    RobotSnapshotUsageTargetEnum usageTarget;

    Map<String, StepFlowContext> stepFlowContextMap = new HashMap<>();

    RepeatAnswerContext repeatAnswerContext;

    KnowledgeContext knowledgeContext;

    UserSilenceContext userSilenceContext;

    AiUnknownContext aiUnknownContext;

    HangupDelayContext hangupDelayContext;

    InaudibleContext inaudibleContext;

    AssistantContext assistantContext;

    LLMContext llmContext;

    Integer lastSequence;

    String lastAnswerId;

    Map<String, String> globalVariableValueMap = new HashMap<>();

    /**
     * 设置为重复的流程id集合, 问答知识在命中的时候是会设置为某些流程为重复了的
     */
    Set<String> repeatStepIdSet = new HashSet<>();

    /**
     * 记录整个主动流程的进度, 如果当前是主流程, 则更新这个值,
     * 和lastActiveStep的区别是有些场景需要回到原主动流程的时候, 是要根据这个值来回到原主动流程的
     */
    String lastActiveMainStepId;

    /**
     * 最后活跃的流程id, 只要是处于流程回复时, 都更新这个值
     */
    String lastActiveStepId;

    /**
     * 当前处于活跃的manager信息, 分为流程, 问答知识, 特殊语境等
     * 每次chatManager处理后都更新这个值, 在aiSayFinish等事件到了的时候, 由上一轮处理的manager继续处理
     * 这个值的更新是在处理完事件之后更新的
     */
    ActiveManagerInfo activeManagerInfo;

    /**
     * 记录答案播放进度
     */
    Map<String, Double> answerProgressMap = new HashMap<>();

    /**
     * 对话所处的特殊模式
     * 所谓特殊模式, 是进入特殊状态后, 会影响意图的判断逻辑, 所以单独提出来方便统一判断
     */
    Set<SpecialChatModeEnum> specialChatModes = new HashSet<>();

    /**
     * 记录节点录音播放最大进度，key:节点id；value:录音播放最大进度
     */
    Map<String, Double> nodeAnswerMaxProgressMap = new HashMap<>();

    /**
     * 记录知识录音播放最大进度，key:知识id；value:录音播放最大进度
     */
    Map<String, Double> knowledgeAnswerMaxProgressMap = new HashMap<>();

    /**
     * 有效的对话事件序列, 用于断句补齐时, 过滤掉被回退的对话log
     */
    Set<Integer> validSequenceSet = new HashSet<>();

    AnswerInterruptConfig lastAnswerInterruptConfig;

    /**
     * 节点答案下标, 用于重复命中节点时的轮询, value为最近一次的答案id
     * 这里只所以存的是id, 而不是下标是方便后续对答案进行按条件过滤时的处理
     * VM-8234 需求产品要求全局记录节点的答案轮询状态
     */
    private Map<String, String> nodeLastReplyAnswerIdMap = new HashMap<>();

    /**
     * 变量赋值历史
     */
    private Map<String, List<String>> variableAssignHistoryMap = new HashMap<>();

    /**
     * 实体收集结果, key: 实体id, value: 实体值列表
     */
    private Map<String, List<String>> entityCollectMap = new HashMap<>();

    /**
     * 正在等待响应的查询节点id集合
     */
    private Set<String> waitingResNodeIdSet = new HashSet<>();

    /**
     * 用户输入采集结果
     */
    private List<UserInputCollectInfo> inputCollectInfoList = new ArrayList<>();

    /**
     * 用户输入预测结果
     * 此字段用于在采集节点中, 判断用户输入是否命中跳过条件
     */
    private List<UserInputPredictInfo> inputPredictInfoList = new ArrayList<>();

    /**
     * 进入到此集合中的节点 id, 其对应的全局采集不在执行
     */
    private Set<String> executedCollectNodeIdSet = new HashSet<>();

    /**
     * 地址实体识别最近一次上下文信息(算法需要)
     */
    private String lastAddressAdvancedContext;
}
