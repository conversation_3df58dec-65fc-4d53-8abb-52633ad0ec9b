package com.yiwise.dialogflow.engine.helper;

import com.google.common.collect.*;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.resource.*;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.entity.enums.IntentTypeEnum;
import com.yiwise.dialogflow.api.enums.V3BotTypeEnum;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.RobotSnapshotPO;
import com.yiwise.dialogflow.entity.po.intent.*;
import com.yiwise.dialogflow.pattern.PatternEnhance;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.intent.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/4
 */
@Slf4j
public class IntentPredictRequiredResourceLoader {

    private static final Pattern PINYIN_PATTERN = Pattern.compile("<[^>]*>");

    private static final IntentConfigService intentConfigService = AppContextUtils.getBean(IntentConfigService.class);
    private static final IntentService intentService = AppContextUtils.getBean(IntentService.class);
    private static final IntentCorpusService intentCorpusService = AppContextUtils.getBean(IntentCorpusService.class);
    private static final BotService botService = AppContextUtils.getBean(BotService.class);
    public static IntentPredictRequiredResource loadFromDb(Long botId, RobotSnapshotUsageTargetEnum usageTarget) {
        IntentConfigPO intentConfig = intentConfigService.detail(botId);
        List<IntentPO> intentList = intentService.getAllByBotId(botId);
        List<IntentCorpusPO> intentCorpusList = intentCorpusService.findByBotId(botId);
        BotPO bot = botService.getById(botId);
        return prepareIntentPredictRequiredResource(botId, bot.getType(), bot.getMagicTemplateId(), usageTarget, intentConfig, intentList, intentCorpusList);
    }

    public static IntentPredictRequiredResource loadFromSnapshot(RobotSnapshotPO snapshot, RobotSnapshotUsageTargetEnum usageTarget) {
        return prepareIntentPredictRequiredResource(snapshot.getBotId(), snapshot.getBot().getType(), snapshot.getBot().getMagicTemplateId(), usageTarget, snapshot.getIntentConfig(), snapshot.getIntentList(), snapshot.getIntentCorpusList());
    }

    private static IntentPredictRequiredResource prepareIntentPredictRequiredResource(Long botId,
                                                                                      V3BotTypeEnum botType,
                                                                                      Long magicTemplateId,
                                                                                      RobotSnapshotUsageTargetEnum usageTarget,
                                                                                      IntentConfigPO intentConfig,
                                                                                      List<IntentPO> intentList,
                                                                                      List<IntentCorpusPO> intentCorpusList) {
        Map<String, String> intentName2IdMap = intentList.stream().collect(Collectors.toMap(IntentPO::getName, IntentPO::getId));
        Map<String, IntentPO> compositeIntentMap = intentList.stream()
                .filter(intent -> IntentTypeEnum.COMPOSITE.equals(intent.getIntentType()))
                .collect(Collectors.toMap(IntentPO::getId, v -> v));
        intentList = intentList.stream().filter(intent -> !IntentTypeEnum.COMPOSITE.equals(intent.getIntentType())).collect(Collectors.toList());

        List<String> intentIdList = intentList.stream().map(IntentPO::getId).collect(Collectors.toList());

        List<IntentPatternEnhance> keywordList = Lists.newArrayList();
        List<IntentPatternEnhance> pinyinKeywordList = Lists.newArrayList();

        Map<String, IntentPO> intentMap = MyCollectionUtils.listToMap(intentList, IntentPO::getId);
        Map<String, IntentCorpusPO> intentCorpusMap = MyCollectionUtils.listToMap(intentCorpusList, IntentCorpusPO::getIntentId);

        intentIdList.stream().filter(intentCorpusMap::containsKey).forEach(intentId -> {
            IntentCorpusPO intentCorpus = intentCorpusMap.get(intentId);
            IntentPO intent = intentMap.get(intentId);
            enhanceIntentPatterns(keywordList, pinyinKeywordList, intentCorpus, intent);
        });

        List<IntentPatternEnhance> orderByRequireLengthList = new ArrayList<>(keywordList.size());
        for (int i = 0; i < keywordList.size(); i++) {
            IntentPatternEnhance intentPatternEnhance = keywordList.get(i);
            orderByRequireLengthList.add(IntentPatternEnhance.copy(intentPatternEnhance, i));
        }
        orderByRequireLengthList.sort(Comparator.comparingInt(IntentPatternEnhance::getRequireInputLength).reversed());

        if (ApplicationConstant.enableDebug) {
            log.info("orderByRequireLengthList:{}", orderByRequireLengthList);
        }

        Long intentModelTemplateBotId = botId;
        if (V3BotTypeEnum.isMagic(botType) && Objects.nonNull(magicTemplateId)) {
            intentModelTemplateBotId = magicTemplateId;
        }
        Map<String, List<String>> intentNameDescListMap = MyCollectionUtils.listToConvertMap(
                intentCorpusList.stream().filter(corpus -> CollectionUtils.isNotEmpty(corpus.getDescList())).collect(Collectors.toList()),
                IntentCorpusPO::getName, IntentCorpusPO::getDescList);
        return IntentPredictRequiredResource.builder()
                .botId(botId)
                .botType(botType)
                .intentModelTemplateBotId(intentModelTemplateBotId)
                .usageTarget(usageTarget)
                .enableCompositeIntent(true)
                .intentConfig(intentConfig)
                .keywordList(ImmutableList.copyOf(keywordList))
                .pinyinKeywordList(ImmutableList.copyOf(pinyinKeywordList))
                .compositeIntentMap(ImmutableMap.copyOf(compositeIntentMap))
                .singIntentMap(ImmutableMap.copyOf(intentMap))
                .orderByRequireLengthKeywordList(ImmutableList.copyOf(orderByRequireLengthList))
                .requireInputLengthIndex(createRequireInputLengthIndexMap(orderByRequireLengthList))
                .intentName2IdMap(ImmutableMap.copyOf(intentName2IdMap))
                .intentNameDescListMap(ImmutableMap.copyOf(intentNameDescListMap))
                .build();
    }

    private static int[] createRequireInputLengthIndexMap(List<? extends PatternEnhance> intentPatternEnhances) {
        int maxLength = 100;
        int[] result = new int[maxLength];
        for (int i = 0; i < intentPatternEnhances.size(); i++) {
            PatternEnhance enhance = intentPatternEnhances.get(i);
            if (enhance.getRequireInputLength() < maxLength) {
                int index = result[enhance.getRequireInputLength()];
                if (index == 0) {
                    result[enhance.getRequireInputLength()] = i;
                }
            }
        }
        log.info("requireInputLengthIndex:{}", result);
        return result;
    }

    private static void enhanceIntentPatterns(List<IntentPatternEnhance> keywordList,
                                              List<IntentPatternEnhance> pinyinKeywordList,
                                              IntentCorpusPO intentCorpus,
                                              IntentPO intent) {
        if (Objects.isNull(intentCorpus) || Objects.isNull(intent)) {
            return;
        }

        processRegexList(keywordList, pinyinKeywordList, intentCorpus.getRegexList(), intent);
        processRegexList(keywordList, pinyinKeywordList, intentCorpus.getBuildInRegexList(), intent);
    }

    private static void processRegexList(List<IntentPatternEnhance> keywordList,
                                         List<IntentPatternEnhance> pinyinKeywordList,
                                         List<String> regexList,
                                         IntentPO intent) {
        if (CollectionUtils.isEmpty(regexList)) {
            return;
        }

        regexList.forEach(regex -> processSingleRegex(keywordList, pinyinKeywordList, regex, intent));
    }

    private static void processSingleRegex(List<IntentPatternEnhance> keywordList,
                                           List<IntentPatternEnhance> pinyinKeywordList,
                                           String regex,
                                           IntentPO intent) {
        Pattern pattern = PatternCache.compile(String.format("(%s)", regex));
        keywordList.add(new IntentPatternEnhance(regex, pattern, intent, false));

        if (PINYIN_PATTERN.matcher(regex).find()) {
            String content = regex.substring(1, regex.length() - 1).replaceAll("\\s", " ").trim();
            Pattern pinyinPattern = Pattern.compile("(" + content + ")", Pattern.CASE_INSENSITIVE);
            pinyinKeywordList.add(new IntentPatternEnhance(content, pinyinPattern, intent, true));
        }
    }

}
