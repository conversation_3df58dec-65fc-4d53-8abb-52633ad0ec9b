package com.yiwise.dialogflow.engine.chatfilter;

import com.yiwise.dialogflow.engine.ChatComponent;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import reactor.core.publisher.Mono;

import java.util.Optional;

/**
 * 语气词过滤器
 * <AUTHOR>
 */
public interface ChatFilter extends ChatComponent {

    Optional<ChatResponse> filter(SessionContext sessionContext, EventContext eventContext);

    default Mono<ChatResponse> filterAsync(SessionContext sessionContext, EventContext eventContext) {
        return Mono.justOrEmpty(filter(sessionContext, eventContext));
    }
}
