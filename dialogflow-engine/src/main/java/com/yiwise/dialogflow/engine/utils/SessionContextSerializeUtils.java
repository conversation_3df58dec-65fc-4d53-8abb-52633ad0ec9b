package com.yiwise.dialogflow.engine.utils;

import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.dialogflow.engine.context.SessionContext;

public class SessionContextSerializeUtils {

    public static String serialize(SessionContext sessionContext) {
        return JsonUtils.object2String(sessionContext);
    }

    public static SessionContext deserialize(String sessionContextStr) {
        return JsonUtils.string2Object(sessionContextStr, SessionContext.class);
    }
}
