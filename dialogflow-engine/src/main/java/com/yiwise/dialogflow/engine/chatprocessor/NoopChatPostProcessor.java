package com.yiwise.dialogflow.engine.chatprocessor;

import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;

import java.util.Optional;

/**
 * <AUTHOR>
 */
public class NoopChatPostProcessor extends AbstractPostProcessor {

    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return "NoopChatPostProcessor";
    }

    @Override
    public Optional<ChatResponse> doProcess(SessionContext sessionContext, EventContext eventContext, ChatResponse chatResponse) {
        return Optional.empty();
    }
}
