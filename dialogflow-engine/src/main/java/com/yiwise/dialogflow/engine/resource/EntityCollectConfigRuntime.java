package com.yiwise.dialogflow.engine.resource;

import com.google.common.collect.ImmutableList;
import com.yiwise.dialogflow.entity.bo.entity.RuntimeEntityBO;
import com.yiwise.dialogflow.entity.po.BaseEntityPO;
import com.yiwise.dialogflow.entity.po.CollectNodeEntityItemPO;
import com.yiwise.dialogflow.entity.po.VariablePO;
import lombok.Getter;
import org.springframework.beans.BeanUtils;

@Getter
public class EntityCollectConfigRuntime extends CollectNodeEntityItemPO {
    /**
     * 答案列表
     */
    ImmutableList<NodeAnswerRuntime> answerRuntimeList;

    private final String entityName;

    private final String variableName;

    public EntityCollectConfigRuntime(CollectNodeEntityItemPO origin,
                                      ImmutableList<NodeAnswerRuntime> answerRuntimeList,
                                      RobotRuntimeResource resource) {
        this.answerRuntimeList = answerRuntimeList;
        BeanUtils.copyProperties(origin, this);
        RuntimeEntityBO entity = resource.getEntityIdMap().get(getEntityId());
        this.entityName = entity == null ? "未知" : entity.getName();
        VariablePO variable = resource.getVariableIdMap().get(getVariableId());
        this.variableName = variable == null ? "未知" : variable.getName();
    }
}
