package com.yiwise.dialogflow.engine.chatmanager;

import com.yiwise.dialogflow.engine.ChatComponent;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Optional;

/**
 * 对话逻辑的处理过程, 比如问答知识的处理, 画布节点的对话管理等, 特殊语境的处理
 * 就是真正生成对话答案的地方
 * <AUTHOR>
 */
public interface ChatManager extends ChatComponent {
    Logger logger = LoggerFactory.getLogger(ChatManager.class);

    Optional<ChatResponse> process(SessionContext sessionContext, EventContext context);

    default Flux<ChatResponse> processLLMEvent(SessionContext sessionContext, EventContext context) {
        logger.warn("[LogHub_Warn]对话处理异常, {} 未实现 processLLMEvent", getName());
        return Flux.empty();
    }

    List<ChatManagerTriggerCondition> getTriggerConditions(SessionContext sessionContext, EventContext context);
}
