package com.yiwise.dialogflow.engine.utils;

import com.yiwise.base.common.text.TextPlaceholderElement;
import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.*;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderElement;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.engine.share.model.KeyCaptureConfig;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.engine.share.response.AnswerResult;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.utils.AnswerLocateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class AnswerRenderUtils {

    public static AnswerResult render(String answerId, SessionContext sessionContext, RobotRuntimeResource resource) {
        AnswerLocateBO locate = resource.getAnswerId2LocateMap().get(answerId);
        if (Objects.isNull(locate)) {
            log.warn("数据/对话 异常, 答案对应的locate为空, answerId={}", answerId);
            return null;
        }
        switch (locate.getAnswerSource()) {
            case STEP:
                NodeRuntime<?> node = resource.getNodeIdMap().get(locate.getNodeId());
                return render(node.origin, node.getAnswerMap().get(answerId), sessionContext, resource);
            case KNOWLEDGE:
                KnowledgeRuntime knowledge = resource.getKnowledgeIdMap().get(locate.getKnowledgeId());
                KnowledgeAnswerRuntime knowledgeAnswer = knowledge.getAnswerMap().get(answerId);
                return render(knowledgeAnswer, sessionContext, resource);
            case SPECIAL_ANSWER:
                SpecialAnswerRuntime specialAnswerRuntime = resource.getSpecialAnswerIdMap().get(locate.getSpecialAnswerConfigId());
                KnowledgeAnswerRuntime specialAnswer = specialAnswerRuntime.getAnswerMap().get(answerId);
                return render(specialAnswer, sessionContext, resource);
            default:
                break;
        }
        return null;
    }

    public static AnswerResult render(DialogBaseNodePO node,
                                      NodeAnswerRuntime answer,
                                      SessionContext sessionContext,
                                      RobotRuntimeResource resource) {
        AnswerResult answerResult = AnswerRenderUtils.render(answer, sessionContext, resource);
        // 是否允许打断等
        answerResult.setUninterrupted(BooleanUtils.isTrue(node.getEnableUninterrupted()));
        answerResult.setCustomInterruptThreshold(Objects.isNull(node.getCustomInterruptThreshold()) ? 100 : node.getCustomInterruptThreshold());

        if (BooleanUtils.isTrue(node.getEnableUninterrupted())
                && (Objects.isNull(node.getCustomInterruptThreshold()) || (Objects.nonNull(node.getCustomInterruptThreshold())
                && node.getCustomInterruptThreshold() > 0))) {
            if (CollectionUtils.isNotEmpty(node.getUninterruptedReplyStepIdList())
                    || CollectionUtils.isNotEmpty(node.getUninterruptedReplyKnowledgeIdList())
                    || CollectionUtils.isNotEmpty(node.getUninterruptedReplySpecialAnswerIdList())
                    || CollectionUtils.isNotEmpty(node.getUninterruptedReplyBranchIntentIdList())) {
                answerResult.setNeedTryReplyOnUninterrupted(true);
            }
        }
        if (node instanceof DialogKeyCaptureNodePO) {
            DialogKeyCaptureNodePO keyCaptureNode = (DialogKeyCaptureNodePO) node;
            answerResult.setKeyCaptureConfig(new KeyCaptureConfig(keyCaptureNode.getTimeout(), keyCaptureNode.getCommitMode(), keyCaptureNode.getEnableReplay()));
        }
        return answerResult;
    }

    public static AnswerResult render(BaseAnswerRuntime<?> answerRuntime,
                                      SessionContext sessionContext,
                                      RobotRuntimeResource resource) {
        AnswerResult answerResult = new AnswerResult();
        answerResult.setId(answerRuntime.getUniqueId());
        answerResult.setTemplate(answerRuntime.getText());
        answerResult.setLabel(answerRuntime.getLabel());

        List<AnswerPlaceholderElement> answerPlaceholderElementList = generateAnswerElement(answerRuntime, sessionContext.getGlobalVariableValueMap());
        answerResult.setAnswerElementList(answerPlaceholderElementList);
        answerResult.setRealAnswer(
                answerPlaceholderElementList.stream()
                        .map(AnswerPlaceholderElement::getRealValue)
                        .collect(Collectors.joining())
        );
        answerResult.setLocate(answerRuntime.getLocate());
        answerResult.setAnswerSource(answerRuntime.getLocate().getAnswerSource());

        if (Objects.isNull(answerResult.getLocate())) {
            log.warn("数据/对话 异常, 答案对应的locate为空, answerId={}, answerText={}", answerRuntime.getUniqueId(), answerRuntime.getText());
        }
        return answerResult;
    }

    private static List<AnswerPlaceholderElement> generateAnswerElement(BaseAnswerRuntime<?> answerRuntime,
                                                                        Map<String, String> properties) {
        return answerRuntime.getAnswerAudioElements()
                        .stream()
                        .map(holder -> {
                            String realValue;
                            if (TextPlaceholderTypeEnum.TEXT.equals(holder.getType())) {
                                realValue = holder.getValue();
                            } else if (TextPlaceholderTypeEnum.PLACE_HOLDER.equals(holder.getType())) {
                                realValue = properties.getOrDefault(holder.getValue(), "");
                            } else if (TextPlaceholderTypeEnum.TTS_SENTENCE.equals(holder.getType())) {
                                // 整句合成
                                realValue = replacePlaceholder(holder.getValue(), properties);
                            } else {
                                realValue = "";
                            }
                            AnswerPlaceholderElement element = new AnswerPlaceholderElement();
                            element.setType(holder.getType());
                            element.setValue(holder.getValue());
                            element.setUrl(holder.getUrl());
                            element.setRealValue(realValue);
                            return element;
                        }).collect(Collectors.toList());
    }

    public static String replacePlaceholder(String sentence, Map<String, String> properties) {
        AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(sentence, false);
        StringBuilder sb = new StringBuilder();
        for (TextPlaceholderElement element : splitter.getTextPlaceholderList()) {
            if (TextPlaceholderTypeEnum.TEXT.equals(element.getType())) {
                sb.append(element.getValue());
            } else {
                sb.append(properties.getOrDefault(element.getValue(), ""));
            }
        }
        return sb.toString();
    }
}
