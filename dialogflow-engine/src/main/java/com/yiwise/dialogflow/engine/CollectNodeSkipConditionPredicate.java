package com.yiwise.dialogflow.engine;

import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.entity.po.BaseConditionGroup;
import com.yiwise.dialogflow.entity.po.CollectNodeSkipCondition;

import java.util.List;
import java.util.function.Predicate;

public class CollectNodeSkipConditionPredicate extends AbstractConditionPredicate<BaseConditionGroup> implements Predicate<CollectNodeSkipCondition> {

    public CollectNodeSkipConditionPredicate(RobotRuntimeResource robotRuntimeResource,
                                             SessionContext sessionContext,
                                             List<String> intentIdList) {
        super(() -> defaultVarIdValueFunc.apply(robotRuntimeResource, sessionContext),
                () -> intentIdList);
    }

    @Override
    public boolean test(CollectNodeSkipCondition conditionGroup) {
        return super.testOneCondition(conditionGroup);
    }
}
