package com.yiwise.dialogflow.engine.context;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KnowledgeContext {

    String currentKnowledgeId;

    /**
     * key: knowledgeId, value: answerId
     */
    Map<String, String> knowledgeAnswerIdMap = new HashMap<>();
}
