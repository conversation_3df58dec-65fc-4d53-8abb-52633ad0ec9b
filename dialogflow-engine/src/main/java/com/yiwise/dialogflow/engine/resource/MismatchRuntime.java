package com.yiwise.dialogflow.engine.resource;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.yiwise.dialogflow.entity.po.Mismatch;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;
import java.util.stream.Collectors;

public interface MismatchRuntime {

    /**
     * 不关联知识/流程开关
     */
    boolean isMismatchKnowledgeAndStep();

    /**
     * 不关联全部问答知识
     */
    boolean isMismatchAllKnowledge();

    /**
     * 不关联全部流程
     */
    boolean isMismatchAllStep();

    /**
     * 不关联知识对应的意图Id-Name
     */
    ImmutableMap<String, String> getExcludeKnowledgeIntentIdNameMap();

    /**
     * 不关联流程对应的意图Id-Name
     */
    ImmutableMap<String, String> getExcludeStepIntentIdNameMap();

    /**
     * 不关联的特殊语境名称列表
     */
    ImmutableList<String> getExcludeSpecialAnswerConfigNameList();

    static ImmutableMap<String, String> processExcludeKnowledgeIntentIdNameMap(Mismatch mismatch, RobotRuntimeResource resource) {
        if (BooleanUtils.isTrue(mismatch.getMismatchKnowledgeAndStep())) {
            if (BooleanUtils.isNotTrue(mismatch.getMismatchAllKnowledge()) && CollectionUtils.isNotEmpty(mismatch.getMismatchKnowledgeIdList())) {
                Map<String, String> excludeKnowledgeIntentIdNameMap = new HashMap<>();
                Set<String> excludeKnowledgeIntentIdSet = mismatch.getMismatchKnowledgeIdList().stream()
                        .map(resource.getKnowledgeTriggerIntentIdSetMap()::get)
                        .filter(Objects::nonNull)
                        .flatMap(Collection::stream)
                        .collect(Collectors.toSet());
                excludeKnowledgeIntentIdSet.forEach(intentId -> excludeKnowledgeIntentIdNameMap.put(intentId, resource.getIntentId2NameMap().getOrDefault(intentId, intentId)));
                return ImmutableMap.copyOf(excludeKnowledgeIntentIdNameMap);
            }
        }
        return ImmutableMap.of();
    }

    static ImmutableMap<String, String> processExcludeStepIntentIdNameMap(Mismatch node, RobotRuntimeResource resource) {
        if (BooleanUtils.isTrue(node.getMismatchKnowledgeAndStep())) {
            if (BooleanUtils.isNotTrue(node.getMismatchAllStep()) && CollectionUtils.isNotEmpty(node.getMismatchStepIdList())) {
                Map<String, String> excludeStepIntentIdNameMap = new HashMap<>();
                Set<String> excludeStepIntentIdSet = node.getMismatchStepIdList().stream()
                        .map(resource.getStepTriggerIntentIdSetMap()::get)
                        .filter(Objects::nonNull)
                        .flatMap(Collection::stream)
                        .collect(Collectors.toSet());
                excludeStepIntentIdSet.forEach(intentId -> excludeStepIntentIdNameMap.put(intentId, resource.getIntentId2NameMap().getOrDefault(intentId, intentId)));
                return ImmutableMap.copyOf(excludeStepIntentIdNameMap);
            }
        }
        return ImmutableMap.of();
    }

    static ImmutableSet<String> processExcludeStepIdSet(Mismatch node, RobotRuntimeResource resource) {
        if (BooleanUtils.isTrue(node.getMismatchKnowledgeAndStep())) {
            if (BooleanUtils.isNotTrue(node.getMismatchAllStep()) && CollectionUtils.isNotEmpty(node.getMismatchStepIdList())) {
                return ImmutableSet.copyOf(node.getMismatchStepIdList());
            } else if (BooleanUtils.isTrue(node.getMismatchAllStep())) {
                return ImmutableSet.copyOf(resource.getStepIdMap().keySet());
            }
        }
        return ImmutableSet.of();
    }

    static ImmutableList<String> processExcludeSpecialAnswerConfigNameList(Mismatch node, RobotRuntimeResource resource) {
        if (BooleanUtils.isTrue(node.getMismatchKnowledgeAndStep())) {
            if (BooleanUtils.isTrue(node.getMismatchAllSpecialAnswerConfig())) {
                return ImmutableList.copyOf(SpecialAnswerConfigPO.INTENT_TRIGGER_SPECIAL_ANSWER_CONFIG_NAME_LIST);
            } else if (CollectionUtils.isNotEmpty(node.getMismatchSpecialAnswerConfigIdList())) {
                return ImmutableList.copyOf(resource.getSpecialAnswerIdMap().entrySet().stream()
                        .filter(entry -> node.getMismatchSpecialAnswerConfigIdList().contains(entry.getKey()))
                        .map(entry -> entry.getValue().getName()).collect(Collectors.toList()));
            }
        }
        return ImmutableList.of();
    }

}

