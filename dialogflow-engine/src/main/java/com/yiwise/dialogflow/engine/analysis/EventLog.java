package com.yiwise.dialogflow.engine.analysis;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.dialogflow.engine.ReceptionInfo;
import com.yiwise.dialogflow.engine.chatmanager.ChatManagerPriorityEnum;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.domain.NodeJumpInfo;
import com.yiwise.dialogflow.engine.share.action.ChatAction;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.request.EventParam;
import com.yiwise.dialogflow.engine.share.request.LLMRequestEvent;
import com.yiwise.dialogflow.engine.share.response.AnswerAudioPlayConfig;
import com.yiwise.dialogflow.engine.share.response.AnswerResult;
import com.yiwise.dialogflow.entity.bo.algorithm.PredictResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.annotation.Nullable;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventLog {

    ReceptionInfo receptionInfo;

    String answerId;

    String realAnswerText;

    String answerTemplate;

    NonNullAnswerLocateBO answerLocate;

    @Nullable
    SimplePredictResult predictResult;

    PredictResult uninterruptedPredictResult;

    ChatEventTypeEnum event;

    EventParam originParam;

    String userInput;

    String domain;

    String attribute;

    Boolean uninterrupted;

    Integer customInterruptThreshold;

    ChatManagerPriorityEnum selectedConditionType;

    List<NodeJumpInfo> prevNodeInfoList;

    Integer sequence;

    List<String> debugLog;

    AnswerAudioPlayConfig answerAudioPlayConfig;

    List<ChatAction> actionList;

    public void recordAnswerResultInfo(AnswerResult answerResult) {
        if (Objects.isNull(answerResult)) {
            return;
        }
        this.answerLocate = answerResult.getLocate() == null ? null : MyBeanUtils.copy(answerResult.getLocate(), NonNullAnswerLocateBO.class);
        this.realAnswerText = answerResult.getRealAnswer();
        this.answerTemplate = answerResult.getTemplate();
        this.answerId = answerResult.getId();
    }

    public void recordEventContextInfo(EventContext eventContext) {
        if (Objects.isNull(eventContext)) {
            return;
        }
        if (Objects.nonNull(eventContext.getPredictResult()) && Objects.nonNull(eventContext.getPredictResult().getSimpleIntentInfo())) {
            SimplePredictResult simplePredictResult = new SimplePredictResult();
            simplePredictResult.setIntentName(eventContext.getPredictResult().getSimpleIntentInfo().getName());
            simplePredictResult.setPredictType(eventContext.getPredictResult().getPredictType());
            simplePredictResult.setIntentId(eventContext.getPredictResult().getSimpleIntentInfo().getId());
            this.predictResult = simplePredictResult;
        }
        if (Objects.nonNull(eventContext.getUninterruptedPredictResult()) && Objects.nonNull(eventContext.getUninterruptedPredictResult().getSimpleIntentInfo())) {
            this.uninterruptedPredictResult = eventContext.getUninterruptedPredictResult();
        }
        this.originParam = eventContext.getOriginEventParam();
        this.event = eventContext.getEvent();
        this.userInput = eventContext.getUserInput();
        this.selectedConditionType = eventContext.getSelectedConditionType();
        this.prevNodeInfoList = eventContext.getPrevNodeInfoList();
        this.debugLog = eventContext.getDebugLog();
    }
}
