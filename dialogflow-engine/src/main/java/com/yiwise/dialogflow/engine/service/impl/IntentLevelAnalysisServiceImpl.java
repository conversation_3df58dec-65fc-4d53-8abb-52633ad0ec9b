package com.yiwise.dialogflow.engine.service.impl;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.ImmutableList;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.MdcLogIdLifterTransformer;
import com.yiwise.dialogflow.engine.ReceptionInfo;
import com.yiwise.dialogflow.engine.analysis.*;
import com.yiwise.dialogflow.engine.chatmanager.ChatManagerPriorityEnum;
import com.yiwise.dialogflow.engine.chatmanager.PredictResultComparator;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.domain.NodeJumpInfo;
import com.yiwise.dialogflow.engine.helper.IntentPredictHelper;
import com.yiwise.dialogflow.engine.resource.KnowledgeRuntime;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.resource.StepRuntime;
import com.yiwise.dialogflow.engine.service.IntentLevelAnalysisService;
import com.yiwise.dialogflow.engine.service.IntentLevelPredictService;
import com.yiwise.dialogflow.engine.share.CallDataInfo;
import com.yiwise.dialogflow.engine.share.IntentLevelAnalysisResult;
import com.yiwise.dialogflow.engine.share.IntentLevelRuleMatchInfo;
import com.yiwise.dialogflow.engine.share.IntentRuleActionResult;
import com.yiwise.dialogflow.engine.share.enums.*;
import com.yiwise.dialogflow.engine.share.response.*;
import com.yiwise.dialogflow.engine.utils.LlmIntentRuleAnalyzeUtils;
import com.yiwise.dialogflow.entity.bo.algorithm.PredictResult;
import com.yiwise.dialogflow.engine.utils.QuestionAnswerAlignUtils;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.po.intent.IntentRuleActionPO;
import com.yiwise.dialogflow.entity.po.intent.IntentRuleConditionPO;
import com.yiwise.dialogflow.entity.po.intent.IntentRulePO;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.pattern.PatternEnhance;
import com.yiwise.dialogflow.utils.IntentRuleContentRenderUtils;
import javaslang.Tuple;
import javaslang.Tuple2;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class IntentLevelAnalysisServiceImpl implements IntentLevelAnalysisService {

    @Resource
    private IntentLevelPredictService intentLevelPredictService;

    @Override
    public Mono<IntentLevelAnalysisResult> analysisAsync(RobotRuntimeResource resource, SessionContext sessionContext, SessionInfo sessionInfo, CallDataInfo callDataInfo, OriginChatData originChatData) {
        // 设置通话数据字段, 比如对话轮次等等
        IntentLevelAnalysisResult result = new IntentLevelAnalysisResult();

        return QuestionAnswerAlignUtils.align(sessionContext, callDataInfo.getCallRecordId(), originChatData, resource)
                .then(Mono.defer(() -> Mono.just(prepareOriginResult(resource, sessionContext, callDataInfo, originChatData))))
                .flatMap(
                        originResult ->
                                wrapAlgorithmPredictResultAsync(callDataInfo.getCallRecordId(), resource, originResult, sessionContext, originChatData)
                                        .then(Mono.defer(() -> wrapLlmLabelResult(originChatData, resource, originResult)))
                                        .then(Mono.defer(() -> calculateLastDeclineAndLastDefinitive(originChatData, resource, originResult)))
                                        .then(Mono.defer(() -> Mono.just(calculateAnalysisUseRule(resource, sessionContext, callDataInfo, originChatData, result, originResult))))
                );
    }

    private Mono<Void> wrapLlmLabelResult(OriginChatData originChatData, RobotRuntimeResource resource, OriginResult result) {
        if (BooleanUtils.isNotTrue(resource.getContainsLlmLabelRule())) {
            return Mono.empty();
        }
        return LlmIntentRuleAnalyzeUtils.analyzeLlmLabel(originChatData, resource)
                .doOnNext(tuple3 -> {
                    result.setHitLlmLabelId(tuple3._1);
                    result.setHitBuiltInTagList(tuple3._2);
                    result.setHitCustomTagList(tuple3._3);
                })
                .then();
    }

    private Mono<Void> calculateLastDeclineAndLastDefinitive(OriginChatData originChatData, RobotRuntimeResource resource, OriginResult result) {
        List<EventLog> logList = originChatData.getLogList();
        if (CollectionUtils.isEmpty(logList)) {
            return Mono.empty();
        }
        if (!containsAlgorithmPredictCondition(resource, DialogFlowConditionTypeEnum.CUSTOMER_FINALLY_REJECT)
                && !containsAlgorithmPredictCondition(resource, DialogFlowConditionTypeEnum.CUSTOMER_FINALLY_DEFINITIVE)) {
            return Mono.empty();
        }
        int size = logList.size();
        List<String> userSayList = new ArrayList<>();
        for (int i = size - 1; i >= 0; i--) {
            EventLog eventLog = logList.get(i);
            if (CollectionUtils.isEmpty(userSayList)) {
                if (!ChatEventTypeEnum.USER_SAY_FINISH.equals(eventLog.getEvent())) {
                    continue;
                }
                userSayList.add(eventLog.getUserInput());
            } else {
                if (!ChatEventTypeEnum.USER_SAY_FINISH.equals(eventLog.getEvent())
                        || ChatManagerPriorityEnum.STEP_NODE_DEFAULT_INTENT.equals(eventLog.getSelectedConditionType())
                        || Objects.nonNull(eventLog.getPredictResult())) {
                    break;
                }
                userSayList.add(eventLog.getUserInput());
            }
        }
        log.info("用户最后说话={}", userSayList);
        if (CollectionUtils.isEmpty(userSayList)) {
            return Mono.empty();
        }
        return Flux.fromIterable(userSayList)
                .flatMap(userSay -> IntentPredictHelper.predictAsync(userSay, resource.getIntentPredictRequiredResource(), ""))
                .map(pr -> {
                    if (Objects.nonNull(pr) && CollectionUtils.isNotEmpty(pr.getCandidatePredictResultList())) {
                        return pr.getCandidatePredictResultList().stream().sorted(new PredictResultComparator())
                                .map(PredictResult::getIntentId).findFirst().orElse("");
                    }
                    return "";
                }).filter(StringUtils::isNotBlank).collectList()
                .doOnNext(intentIdList -> {
                    List<IntentPO> intentList = intentIdList.stream().map(resource.getIntentMap()::get).filter(Objects::nonNull).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(intentList)) {
                        log.info("用户最后说话命中意图={}", MyCollectionUtils.listToConvertList(intentList, IntentPO::getName));
                        List<IntentPropertiesEnum> intentPropertyList = intentList.stream().map(IntentPO::getIntentProperties).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(intentPropertyList)) {
                            result.setLastDefinitive(
                                    intentPropertyList.stream().anyMatch(IntentPropertiesEnum.DEFINITE::equals)
                                    &&
                                    intentPropertyList.stream().noneMatch(IntentPropertiesEnum.DECLINE::equals)
                            );
                            result.setLastDecline(intentPropertyList.stream().anyMatch(IntentPropertiesEnum.DECLINE::equals));
                        }
                    } else {
                        log.info("用户最后说话命中意图={}", Collections.emptyList());
                    }
                }).then();
    }


    private IntentLevelAnalysisResult calculateAnalysisUseRule(RobotRuntimeResource resource,
                                                               SessionContext sessionContext,
                                                               CallDataInfo callDataInfo,
                                                               OriginChatData originChatData,
                                                               IntentLevelAnalysisResult result,
                                                               OriginResult originResult) {
        result.setAiRounds(originResult.getAiRounds());
        result.setDeclineCount(originResult.getDeclineCount());
        result.setDefiniteCount(originResult.getDefiniteCount());
        result.setBusinessQuestionCount(originResult.getBusinessCount());
        result.setCustomerAttribute(Collections.emptySet());
        result.setCustomerFocus(originResult.getCustomerFocus());
        result.setExtraInfoMapId(Collections.emptyMap());


        // 主要逻辑是:
        // 1. 先判断是否有问答知识或者节点设置了意向等级的, 如果设置了, 则直接返回这种结果(虽然觉得这种逻辑很不对)
        // 2. 对计算出各种维度的结果, 比如主流程进度, 命中问答知识, 拒绝次数等
        // 3. 再使用原始数据依次遍历所有的意向规则, 只要有一个满足条件即返回
        // 4. 通话状态等等内置的意向规则判断

        Optional<Tuple2<AnswerLocateBO, Integer>> highestCode = findHighestIntentLevelFromAnswer(resource, originResult);

        // 节点里面设置了意向等级的, 优先级是最高的
        if (highestCode.isPresent()) {
            log.info("通过节点或问答知识设置了意向等级, 意向等级={}, 节点={}", highestCode.get()._2, highestCode.get()._1);
            Integer intentLevelCode = highestCode.get()._2;
            AnswerLocateBO answerLocate = highestCode.get()._1;
            IntentLevelRuleMatchInfo matchInfo = new IntentLevelRuleMatchInfo();
            switch (answerLocate.getAnswerSource()) {
                case STEP:
                    matchInfo.setStepId(answerLocate.getStepId());
                    matchInfo.setNodeId(answerLocate.getNodeId());
                    break;
                case KNOWLEDGE:
                    matchInfo.setKnowledgeId(answerLocate.getKnowledgeId());
                    break;
                case SPECIAL_ANSWER:
                    matchInfo.setSpecialAnswerConfigId(answerLocate.getSpecialAnswerConfigId());
                    break;
                default:
                    break;
            }
            result.setIntentLevelRuleMatchInfo(matchInfo);
            result.setIntentLevel(intentLevelCode);
            result.setBasis(generateRuleDescription(answerLocate, resource.getIntentLevelRuleResource().getIntentLevelCode2NameMap(), intentLevelCode));
            originResult.setIntentLevel(intentLevelCode);
            matchIntentAction(resource, sessionContext, callDataInfo, originResult, result);
            return result;
        }

        // 其次是按照规则一次向下匹配, 返回匹配的第一个规则
        List<IntentRulePO> customerRuleList = resource.getIntentLevelRuleResource().getIntentRuleList().stream()
                .filter(item -> Objects.isNull(item.getSpecialIntentRuleType()))
                .collect(Collectors.toList());
        List<IntentRulePO> builtInRule = resource.getIntentLevelRuleResource().getIntentRuleList().stream()
                .filter(item -> Objects.nonNull(item.getSpecialIntentRuleType()))
                .collect(Collectors.toList());

        Optional<IntentRulePO> firstMatchedRuleOpt = findFirstMatchedRule(customerRuleList, originResult, callDataInfo);
        // 匹配自定义规则
        if (firstMatchedRuleOpt.isPresent()) {
            IntentRulePO rule = firstMatchedRuleOpt.get();
            String ruleDesc = generateRuleDescription(rule, originResult, resource);
            Integer intentLevelCode = rule.getIntentLevelTagDetailCode();
            String basis = String.format("命中自定义规则%d, 条件:[%s], 意向等级:%s", rule.getMatchOrder(), ruleDesc, getIntentLevelDetailName(resource.getIntentLevelRuleResource().getIntentLevelCode2NameMap(), intentLevelCode));
            result.setBasis(basis);
            result.setIntentLevel(rule.getIntentLevelTagDetailCode());
            result.setMatchedIntentRuleId(rule.getId());
            IntentLevelRuleMatchInfo matchInfo = new IntentLevelRuleMatchInfo();
            matchInfo.setRuleId(rule.getId());
            result.setIntentLevelRuleMatchInfo(matchInfo);
            originResult.setIntentLevel(intentLevelCode);
            matchIntentAction(resource, sessionContext, callDataInfo, originResult, result);
            return result;
        }

        // 没有命中自定义规则, 只能通过通话状态判断了
        Optional<IntentRulePO> matchedDialStatsRuleOpt = builtInRule.stream()
                .filter(this::isBuiltInDialStatsRule)
                .filter(rule -> matchedRule(rule, originResult, callDataInfo))
                .findFirst();

        // 匹配对话状态规则
        if (matchedDialStatsRuleOpt.isPresent()) {
            IntentRulePO rule = matchedDialStatsRuleOpt.get();
            result.setBasis(generateRuleDescription(rule, originResult, resource));
            result.setIntentLevel(rule.getIntentLevelTagDetailCode());
            result.setMatchedIntentRuleId(rule.getId());
            IntentLevelRuleMatchInfo matchInfo = new IntentLevelRuleMatchInfo();
            matchInfo.setRuleId(rule.getId());
            result.setIntentLevelRuleMatchInfo(matchInfo);
            originResult.setIntentLevel(rule.getIntentLevelTagDetailCode());
            matchIntentAction(resource, sessionContext, callDataInfo, originResult, result);
            return result;
        }

        // 最后一个默认的规则
        Optional<IntentRulePO> othersRuleOpt = builtInRule.stream()
                .filter(this::isOthersRule)
                .findFirst();

        if (othersRuleOpt.isPresent()) {
            IntentRulePO rule = othersRuleOpt.get();
            result.setBasis("除以上情况外的其他情况");
            result.setIntentLevel(rule.getIntentLevelTagDetailCode());
            result.setMatchedIntentRuleId(rule.getId());
            IntentLevelRuleMatchInfo matchInfo = new IntentLevelRuleMatchInfo();
            matchInfo.setRuleId(rule.getId());
            result.setIntentLevelRuleMatchInfo(matchInfo);
            originResult.setIntentLevel(rule.getIntentLevelTagDetailCode());
            matchIntentAction(resource, sessionContext, callDataInfo, originResult, result);
            return result;
        }

        log.warn("意向等级分析异常, 不存在默认的意向等级规则");
        result.setIntentLevel(3);
        result.setBasis("除以上情况外的其他情况");
        originResult.setIntentLevel(result.getIntentLevel());
        matchIntentAction(resource, sessionContext, callDataInfo, originResult, result);
        return result;
    }

    private void matchIntentAction(RobotRuntimeResource resource, SessionContext sessionContext,
                                   CallDataInfo callDataInfo, OriginResult originResult,
                                   IntentLevelAnalysisResult result) {
        List<AddWhiteListBO> addWhiteListGroupIdList = new ArrayList<>();
        Set<Long> smsIdSet = new HashSet<>();
        Set<Long> whiteList = new HashSet<>();
        Set<Long> tagIdSet = new HashSet<>();
        //动作匹配
        List<IntentRuleActionResult> intentActionRuleResultList = matchAction(resource, sessionContext, callDataInfo, originResult);
        if (CollectionUtils.isNotEmpty(intentActionRuleResultList)) {
            log.info("intentActionRuleResultList={}", intentActionRuleResultList);
        }

        // 去重并包装为交互层需要的结果
        // 黑名单的加入理由使用节点里面设置的, 不需要使用规则的
        for (IntentRuleActionResult intentActionRuleResult : intentActionRuleResultList) {
            smsIdSet.addAll(intentActionRuleResult.getSmsTemplateIdList());
            tagIdSet.addAll(intentActionRuleResult.getCustomerTagIdList());
            if (CollectionUtils.isNotEmpty(intentActionRuleResult.getWhiteGroupIdList())) {
                intentActionRuleResult.getWhiteGroupIdList().forEach(whiteGroupId -> {
                    if (whiteList.contains(whiteGroupId)) {
                        return;
                    }
                    whiteList.add(whiteGroupId);
                    AddWhiteListBO item = new AddWhiteListBO();
                    item.setWhiteListGroupId(whiteGroupId);
                    item.setDetail(intentActionRuleResult.getReason());
                    addWhiteListGroupIdList.add(item);
                });
            }
        }
        result.setSmsTemplateIds(smsIdSet);
        result.setAddWhiteListGroupIdList(addWhiteListGroupIdList);
        result.setCustomerLevelTagDetailIdSet(tagIdSet);
        result.setIntentRuleActionResultList(intentActionRuleResultList);
    }

    private List<IntentRuleActionResult> matchAction(RobotRuntimeResource resource, SessionContext sessionContext,
                                                     CallDataInfo callDataInfo, OriginResult originResult) {
        List<IntentRuleActionResult> result = new ArrayList<>();
        //知识/节点命中黑名单处理
        // 1. 处理问答知识/节点/特殊语境中设置了的动作
        result.addAll(matchActionByNode(resource, originResult));
        result.addAll(matchActionByKnowledge(resource, originResult));
        result.addAll(matchActionBySpecialAnswerConfig(resource, originResult));

        // 2. 处理命中规则配置的动作
        List<IntentRuleActionPO> matchRuleActionList = findMatchedRuleActionList(resource.getIntentActionResource().getIntentRuleActionList(),
                sessionContext, originResult, callDataInfo);
        if (CollectionUtils.isNotEmpty(matchRuleActionList)) {
            log.debug("命中动作规则结果:{}", JSON.toJSONString(matchRuleActionList));
        }
        matchRuleActionList.forEach(ruleAction -> {
            IntentRuleActionResult actionResult = new IntentRuleActionResult();
            actionResult.setRuleId(ruleAction.getId());
            String reason = generateRuleDescription(ruleAction, originResult, resource);
            actionResult.setReason(reason);
            actionResult.setCustomerTagIdList(new ArrayList<>());
            actionResult.setSmsTemplateIdList(new ArrayList<>());
            actionResult.setWhiteGroupIdList(new ArrayList<>());
            actionResult.setAddWechat(false);
            result.add(actionResult);
            ruleAction.getActionList().forEach(action -> {
                if (CollectionUtils.isEmpty(action.getSourceIdList())) {
                    return;
                }
                List<Long> idList = action.getSourceIdList().stream().map(IdNamePair::getId).collect(Collectors.toList());
                switch (action.getActionType()) {
                    case SEND_SMS:
                        actionResult.getSmsTemplateIdList().addAll(idList);
                        break;
                    case WHITE_LIST:
                        actionResult.getWhiteGroupIdList().addAll(idList);
                        break;
                    case ADD_TAG:
                        actionResult.getCustomerTagIdList().addAll(idList);
                        break;
                }
            });
        });
        return result;
    }

    private List<IntentRuleActionResult> matchActionByNode(RobotRuntimeResource resource, OriginResult originResult) {
        List<IntentRuleActionResult> resultList = matchActionByResource(originResult.getNodeId2StepMap().keySet(),
                resource.getIntentActionResource().getNodeId2SmsIdMap(),
                resource.getIntentActionResource().getNodeId2tagIdMap(),
                resource.getIntentActionResource().getNodeId2WhiteGroupIdMap(),
                resource.getIntentActionResource().getAddWechatNodeIdList(),
                (nodeId) -> {
                    DialogBaseNodePO node = resource.getNodeIdMap().get(nodeId).origin;
                    return String.format("命中节点: %s(%s)", node.getLabel(), node.getName());
                }, IntentRuleActionResult::setNodeId);

        // 设置stepId
        for (IntentRuleActionResult item : resultList) {
            item.setStepId(originResult.getNodeId2StepMap().get(item.getNodeId()));
        }
        return resultList;
    }

    private List<IntentRuleActionResult> matchActionByKnowledge(RobotRuntimeResource resource, OriginResult originResult) {
        return matchActionByResource(originResult.getKnowledgeCountMap().keySet(),
                resource.getIntentActionResource().getKnowledgeId2SmsIdMap(),
                resource.getIntentActionResource().getKnowledgeId2tagIdMap(),
                resource.getIntentActionResource().getKnowledgeId2WhiteGroupIdMap(),
                resource.getIntentActionResource().getAddWechatKnowledgeIdList(),
                (knowledgeId) -> {
                    KnowledgePO knowledge = resource.getKnowledgeIdMap().get(knowledgeId);
                    return String.format("命中知识: %s(%s)", knowledge.getLabel(), knowledge.getName());
                }, IntentRuleActionResult::setKnowledgeId);
    }

    private List<IntentRuleActionResult> matchActionBySpecialAnswerConfig(RobotRuntimeResource resource, OriginResult originResult) {
        return matchActionByResource(originResult.getSpecialAnswerCountMap().keySet(),
                resource.getIntentActionResource().getSpecialAnswerId2SmsIdMap(),
                resource.getIntentActionResource().getSpecialAnswerId2TagIdMap(),
                resource.getIntentActionResource().getSpecialAnswerId2WhiteGroupIdMap(),
                Collections.emptyList(),
                (specialAnswerId) -> {
                    SpecialAnswerConfigPO specialAnswer = resource.getSpecialAnswerIdMap().get(specialAnswerId);
                    return String.format("命中特殊语境: %s(%s)", specialAnswer.getLabel(), specialAnswer.getName());
                }, IntentRuleActionResult::setSpecialAnswerConfigId);
    }

    private List<IntentRuleActionResult> matchActionByResource(Collection<String> matchResourceIdList,
                                                               Map<String, Set<Long>> smsIdSetMap,
                                                               Map<String, Set<Long>> tagIdSetMap,
                                                               Map<String, Set<Long>> whiteGroupIdSetMap,
                                                               List<String> addWechatResourceIdList,
                                                               Function<String, String> reasonGenerator,
                                                               BiConsumer<IntentRuleActionResult, String> onMatch) {
        if (CollectionUtils.isEmpty(matchResourceIdList)) {
            return new ArrayList<>();
        }
        List<IntentRuleActionResult> result = new ArrayList<>();
        matchResourceIdList.forEach(itemId -> {
            IntentRuleActionResult actionResult = new IntentRuleActionResult();
            actionResult.setSmsTemplateIdList(new ArrayList<>());
            actionResult.setCustomerTagIdList(new ArrayList<>());
            actionResult.setWhiteGroupIdList(new ArrayList<>());
            actionResult.getCustomerTagIdList().addAll(tagIdSetMap.getOrDefault(itemId, Collections.emptySet()));
            actionResult.getSmsTemplateIdList().addAll(smsIdSetMap.getOrDefault(itemId, Collections.emptySet()));
            actionResult.getWhiteGroupIdList().addAll(whiteGroupIdSetMap.getOrDefault(itemId, Collections.emptySet()));
            actionResult.setAddWechat(CollectionUtils.isNotEmpty(addWechatResourceIdList) && addWechatResourceIdList.contains(itemId));
            boolean isMatch = CollectionUtils.isNotEmpty(actionResult.getCustomerTagIdList())
                    || CollectionUtils.isNotEmpty(actionResult.getSmsTemplateIdList())
                    || CollectionUtils.isNotEmpty(actionResult.getWhiteGroupIdList())
                    || actionResult.getAddWechat();
            if (isMatch) {
                actionResult.setReason(reasonGenerator.apply(itemId));
                onMatch.accept(actionResult, itemId);
                result.add(actionResult);
            }
        });
        return result;
    }

    private List<IntentRuleActionPO> findMatchedRuleActionList(ImmutableList<IntentRuleActionPO> intentRuleActionList,
                                                               SessionContext sessionContext,
                                                               OriginResult originResult,
                                                               CallDataInfo callDataInfo) {
        if (CollectionUtils.isEmpty(intentRuleActionList)) {
            return new ArrayList<>();
        }
        return intentRuleActionList.stream()
                .filter(rule -> DialStatusEnum.ANSWERED.getCode() == callDataInfo.getDialStatus()
                        || ruleContainsDialStatusCondition(rule))
                .filter(rule -> matchedRule(rule, originResult, callDataInfo))
                .collect(Collectors.toList());
    }

    private String getIntentLevelDetailName(Map<Integer, String> intentLevelCode2NameMap, Integer intentLevelCode) {
        return intentLevelCode2NameMap.getOrDefault(intentLevelCode, String.valueOf(intentLevelCode));
    }

    private boolean isBuiltInDialStatsRule(IntentRulePO rule) {
        return SpecialIntentRuleTypeEnum.DIAl_STATUS_FAIL.equals(rule.getSpecialIntentRuleType())
                || SpecialIntentRuleTypeEnum.DIAL_STATUS_PHONE_ERROR.equals(rule.getSpecialIntentRuleType());
    }

    private boolean isOthersRule(IntentRulePO rule) {
        return SpecialIntentRuleTypeEnum.OTHERS.equals(rule.getSpecialIntentRuleType());
    }

    private String generateRuleDescription(IntentRulePO rule, OriginResult originResult, RobotRuntimeResource resource) {
        return IntentRuleContentRenderUtils.renderIntentRuleConditionContent(rule.getConditionList(), resource.getResourceId2NameBO());
    }

    private OriginResult prepareOriginResult(RobotRuntimeResource resource,
                                             SessionContext sessionContext,
                                             CallDataInfo callDataInfo,
                                             OriginChatData originChatData) {
        OriginResult result = new OriginResult();
        List<String> userSayContentList = getUserSayContentList(callDataInfo, originChatData);
        result.setMainStepCompleteProgress(calculateMainStepProgress(resource, callDataInfo, originChatData));
        result.setCompleteAllStep(result.getMainStepCompleteProgress() >= 100);

        // 客户关注点
        result.setCustomerFocus(calculateCustomerFocus(resource, originChatData));

        //处理最后一次答案播放进度
        handleLastAnswerProgress(callDataInfo, resource, sessionContext);

        // 肯定次数, 拒绝次数, 连续肯定次数, 连续拒绝次数, 最后肯定, 最后拒绝
        calculateIntentProperties(result, resource, callDataInfo, originChatData);

        // 问答知识命中列表, 命中次数, 独立流程命中次数
        calculateBusinessCount(result, resource, callDataInfo, originChatData);

        result.setAiRounds(calculateAiRounds(originChatData));
        result.setEffectiveChatRounds(calculateEffectiveChatRounds(originChatData));
        result.setChatDuration(callDataInfo.getChatDuration());
        result.setActualChatDuration(calculateActualChatDuration(callDataInfo));

        result.setUserSayContentList(userSayContentList);
        result.setUserSayCount(CollectionUtils.size(userSayContentList));
        result.setUserSayWordCount(calculateUserSayCount(originChatData));

        result.setFastHangup(BooleanUtils.isTrue(callDataInfo.getFastHangup()));
        result.setHangupByUser(BooleanUtils.isTrue(callDataInfo.getUserHangup()));
        result.setLastUserSayContent(CollectionUtils.isEmpty(userSayContentList) ? "" : userSayContentList.get(userSayContentList.size() - 1));
        result.setLastAiSayContent(calculateLastAiSayContent(originChatData));
        result.setUserHangupKnowledgeId(result.isHangupByUser() ? getLastReceptionKnowledgeId(originChatData).orElse(null) : null);
        result.setUserHangupNodeId(result.isHangupByUser() ? getLastReceptionNodeId(originChatData).orElse(null) : null);
        result.setAiUnknownCount((int) calculateAiUnknownCount(originChatData));
        result.setAiUnknownPercent(calculatePercent(result.getAiUnknownCount(), result.getAiRounds()));
        result.setNodeId2StepMap(getReceptionNodeIdList(originChatData));
        result.setSpecialAnswerCountMap(getReceptionSpecialAnswerIdList(originChatData));
        result.setNodeAnswerMaxProgressMap(sessionContext.getNodeAnswerMaxProgressMap());
        result.setKnowledgeAnswerMaxProgressMap(sessionContext.getKnowledgeAnswerMaxProgressMap());
        result.setStepCountMap(getReceptionStepMap(resource, originChatData));
        result.setEntityCollectMap(sessionContext.getEntityCollectMap());
        result.setUserLastSay(calculateUserLastSay(originChatData));

        Tuple2<List<String>, List<String>> tuple2 = calculateContinuousKnowledgeAndSpecialAnswerConfig(originChatData);
        result.setContinuousKnowledgeIdList(tuple2._1);
        result.setContinuousSpecialAnswerConfigIdList(tuple2._2);

        Map<String, String> varIdValueMap = new HashMap<>();
        resource.getVariableIdMap().forEach((varId, var) -> {
            String varValue = sessionContext.getGlobalVariableValueMap().getOrDefault(var.getName(), "");
            if (StringUtils.isNotBlank(varValue)) {
                varIdValueMap.put(varId, varValue);
            }
        });
        result.setVariableIdValueMap(varIdValueMap);

        log.info("对通话数据进行初步计算汇总结果={}", JSON.toJSONString(result));
        return result;
    }

    private Long calculateActualChatDuration(CallDataInfo callDataInfo) {
        if (Objects.nonNull(callDataInfo.getEngineResetTimestamp()) && Objects.nonNull(callDataInfo.getEndTime())) {
            return (callDataInfo.getEndTime() - callDataInfo.getEngineResetTimestamp()) / 1000;
        }
        return callDataInfo.getChatDuration();
    }

    private String calculateLastAiSayContent(OriginChatData originChatData) {
        List<EventLog> logList = originChatData.getLogList();
        if (CollectionUtils.isEmpty(logList)) {
            return null;
        }
        for (int i = logList.size() - 1; i >= 0; i--) {
            EventLog eventLog = logList.get(i);
            if (Objects.nonNull(eventLog.getRealAnswerText())) {
                return eventLog.getRealAnswerText();
            }
        }
        return null;
    }

    private Boolean calculateUserLastSay(OriginChatData originChatData) {
        List<EventLog> logList = originChatData.getLogList();
        if (CollectionUtils.isEmpty(logList)) {
            return false;
        }
        for (int i = logList.size() - 1; i >= 0; i--) {
            EventLog eventLog = logList.get(i);
            boolean aiSay = Objects.nonNull(eventLog.getAnswerLocate()) &&
                    Objects.nonNull(eventLog.getAnswerAudioPlayConfig()) && RepeatAnswerPlayStrategyEnum.REPLAY.equals(eventLog.getAnswerAudioPlayConfig().getRepeatPlayStrategy());
            if (aiSay) {
                return false;
            }
            if (ChatEventTypeEnum.USER_SAY_FINISH.equals(eventLog.getEvent())) {
                if (CollectionUtils.isNotEmpty(eventLog.getActionList()) && eventLog.getActionList().stream().anyMatch(action -> ActionTypeEnum.IGNORE_INPUT.equals(action.getType()))) {
                    continue;
                }
                return true;
            }
        }
        return false;
    }

    private Map<String, Integer> getReceptionStepMap(RobotRuntimeResource resource, OriginChatData originChatData) {
        Map<String, Integer> result = new HashMap<>();
        originChatData.getLogList()
                .stream()
                .filter(item -> ChatManagerPriorityEnum.INTENT_TRIGGER_STEP.equals(item.getSelectedConditionType()))
                .map(EventLog::getReceptionInfo)
                .filter(Objects::nonNull)
                .filter(item -> AnswerSourceEnum.STEP.equals(item.getType()))
                .map(ReceptionInfo::getTargetId)
                .forEach(stepId -> {
                    result.compute(stepId, (k, v) -> Objects.isNull(v) ? 1 : v + 1);
                });
        return result;
    }

    private boolean containsAlgorithmPredictCondition(RobotRuntimeResource resource, DialogFlowConditionTypeEnum targetType) {
        return resource.getIntentLevelRuleResource().getIntentRuleList().stream()
                .filter(item -> Objects.isNull(item.getSpecialIntentRuleType()))
                .filter(rule -> CollectionUtils.isNotEmpty(rule.getConditionList()))
                .anyMatch(rule -> {
                    for (IntentRuleConditionPO condition : rule.getConditionList()) {
                        if (targetType.equals(condition.getType())) {
                            return true;
                        }
                    }
                    return false;
                });
    }

    private Mono<Void> wrapAlgorithmPredictResultAsync(Long callRecordId,
                                                       RobotRuntimeResource resource,
                                                       OriginResult result,
                                                       SessionContext sessionContext,
                                                       OriginChatData chatData) {

        boolean hasActivityCondition = containsAlgorithmPredictCondition(resource, DialogFlowConditionTypeEnum.ALGORITHM_ACTIVITY_INTENT_LEVEL);
        boolean hasPrivateDomainCondition = containsAlgorithmPredictCondition(resource, DialogFlowConditionTypeEnum.ALGORITHM_PRIVATE_DOMAIN_INTENT_LEVEL);
        boolean hasPassivePrivateDomainCondition = containsAlgorithmPredictCondition(resource, DialogFlowConditionTypeEnum.ALGORITHM_PRIVATE_DOMAIN_INTENT_LEVEL_PASSIVE);
        boolean hasEducationActivityCondition = containsAlgorithmPredictCondition(resource, DialogFlowConditionTypeEnum.ALGORITHM_EDUCATION_ACTIVITY_INTENT_LEVEL);

        if (!hasActivityCondition && !hasPrivateDomainCondition && !hasPassivePrivateDomainCondition && !hasEducationActivityCondition) {
            return Mono.empty();
        }

        Map<String, Object> predictParam = buildTalkContent(sessionContext, resource, chatData);

        if (Objects.isNull(predictParam)) {
            return Mono.empty();
        }

        // 活动通知,主动/被动加微每次请求单独设置唯一id
        Map<String,Object> activityIntentPredictParam = new HashMap<>(predictParam);
        activityIntentPredictParam.put("id", UUID.randomUUID().toString());
        activityIntentPredictParam.put("phone_id", callRecordId);
        Mono<Tuple2<Integer, String>> activityIntentLevelResult = hasActivityCondition ?
                intentLevelPredictService.predictAsync(activityIntentPredictParam, ApplicationConstant.ACTIVITY_INTENT_ALGORITHM_URL) : Mono.empty();

        Map<String,Object> educationActivityIntentPredictParam = new HashMap<>(predictParam);
        educationActivityIntentPredictParam.put("id", UUID.randomUUID().toString());
        educationActivityIntentPredictParam.put("phone_id", callRecordId);
        Mono<Tuple2<Integer, String>> educationActivityIntentLevelResult = hasEducationActivityCondition ?
                intentLevelPredictService.predictAsync(educationActivityIntentPredictParam, ApplicationConstant.EDUCATION_ACTIVITY_INTENT_ALGORITHM_URL) : Mono.empty();

        // 主动/被动加微请求的接口地址是同一个,通过add_wechat_method来区分
        Map<String,Object> privateDomainIntentPredictParam = new HashMap<>(predictParam);
        privateDomainIntentPredictParam.put("id", UUID.randomUUID().toString());
        privateDomainIntentPredictParam.put("add_wechat_method", "主动加微");
        privateDomainIntentPredictParam.put("phone_id", callRecordId);
        Mono<Tuple2<Integer, String>> privateDomainIntentLevelResult = hasPrivateDomainCondition ?
                intentLevelPredictService.predictAsync(privateDomainIntentPredictParam, ApplicationConstant.PRIVATE_DOMAIN_INTENT_ALGORITHM_URL) : Mono.empty();

        Map<String,Object> passivePrivateDomainIntentPredictParam = new HashMap<>(predictParam);
        passivePrivateDomainIntentPredictParam.put("id", UUID.randomUUID().toString());
        passivePrivateDomainIntentPredictParam.put("add_wechat_method", "被动加微");
        passivePrivateDomainIntentPredictParam.put("phone_id", callRecordId);
        Mono<Tuple2<Integer, String>> passivePrivateDomainIntentLevelResult = hasPassivePrivateDomainCondition ?
                intentLevelPredictService.predictAsync(passivePrivateDomainIntentPredictParam, ApplicationConstant.PRIVATE_DOMAIN_INTENT_ALGORITHM_URL) : Mono.empty();

        final Tuple2<Integer, String> emptyResult = new Tuple2<>(-1, "");

        return Mono.zip(
                activityIntentLevelResult.switchIfEmpty(Mono.just(emptyResult)),
                privateDomainIntentLevelResult.switchIfEmpty(Mono.just(emptyResult)),
                passivePrivateDomainIntentLevelResult.switchIfEmpty(Mono.just(emptyResult)),
                educationActivityIntentLevelResult.switchIfEmpty(Mono.just(emptyResult))
                )
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .doOnNext(tuple -> {
                    if (Objects.isNull(tuple)) {
                        return;
                    }
                    if (!emptyResult.equals(tuple.getT1())) {
                        result.setActivityIntentLevelResult(tuple.getT1());
                    }
                    if (!emptyResult.equals(tuple.getT2())) {
                        result.setPrivateDomainIntentLevelResult(tuple.getT2());
                    }
                    if (!emptyResult.equals(tuple.getT3())) {
                        result.setPassivePrivateDomainIntentLevelResult(tuple.getT3());
                    }
                    if (!emptyResult.equals(tuple.getT4())) {
                        result.setEducationActivityIntentLevelResult(tuple.getT4());
                    }
                }).then();
    }

    private Map<String, Object> buildTalkContent(SessionContext sessionContext, RobotRuntimeResource resource, OriginChatData chatData) {
        Map<String, Object> result = new HashMap<>();
        List<RoleTalkContent> elementContentList = new ArrayList<>();
        Map<String, Double> answerProgressMap = sessionContext.getAnswerProgressMap();
        if (CollectionUtils.isNotEmpty(chatData.getLogList())) {
            chatData.getLogList().forEach(eventLog -> {
                if (StringUtils.isNotBlank(eventLog.getUserInput()) && StringUtils.isNotBlank(eventLog.getRealAnswerText())) {
                    String userInput = eventLog.getUserInput();
                    RoleTalkContent roleTalkContent = new RoleTalkContent();
                    roleTalkContent.setRole("客户");
                    roleTalkContent.setText(userInput);
                    roleTalkContent.setAlgorithm_intent(eventLog.getDomain() != null ? eventLog.getDomain() : "");
                    roleTalkContent.setAlgorithm_intent_attribute(eventLog.getAttribute() != null ? eventLog.getAttribute() : "");
                    roleTalkContent.setUse_intent("");
                    if (Objects.nonNull(eventLog.getPredictResult())) {
                        roleTalkContent.setUse_intent(eventLog.getPredictResult().getIntentName());
                    }
                    roleTalkContent.setComplete_situation(-1d);
                    elementContentList.add(roleTalkContent);
                }
                if (StringUtils.isNotBlank(eventLog.getRealAnswerText())) {
                    String aiInput = eventLog.getRealAnswerText();
                    RoleTalkContent roleTalkContent = new RoleTalkContent();
                    roleTalkContent.setRole("客服");
                    roleTalkContent.setText(aiInput);
                    roleTalkContent.setUse_intent("");
                    roleTalkContent.setAlgorithm_intent("");
                    roleTalkContent.setAlgorithm_intent_attribute("");
                    Double progress = answerProgressMap.get(eventLog.getAnswerId());
                    if (progress == null) {
                        roleTalkContent.setComplete_situation(0d);
                    } else {
                        roleTalkContent.setComplete_situation(progress);
                    }
                    boolean allowBreak = BooleanUtils.isNotTrue(eventLog.getUninterrupted());
                    roleTalkContent.setAllow_break(allowBreak);
                    if (!allowBreak) {
                        roleTalkContent.setAllow_break_rate(eventLog.getCustomInterruptThreshold());
                    }
                    NonNullAnswerLocateBO answerLocate = eventLog.getAnswerLocate();
                    if (Objects.nonNull(answerLocate)) {
                        String nodeId = answerLocate.getNodeId();
                        roleTalkContent.setAlgorithm_intent(resource.getNodeIdAlgorithmLabelMap().get(nodeId));
                    }
                    elementContentList.add(roleTalkContent);
                }
            });
        }
        if (CollectionUtils.isEmpty(elementContentList)) {
            return null;
        }
        result.put("dialog", elementContentList);
        return result;
    }

    private void handleLastAnswerProgress(CallDataInfo callDataInfo, RobotRuntimeResource resource, SessionContext sessionContext) {
        if (!sessionContext.getAnswerProgressMap().containsKey(callDataInfo.getLastAnswerId())) {
            sessionContext.getAnswerProgressMap().put(callDataInfo.getLastAnswerId(), (double) callDataInfo.getLastAnswerPlayProgress());
        }
        if (resource.getAnswerId2LocateMap().containsKey(callDataInfo.getLastAnswerId())) {
            AnswerLocateBO answerLocateBO = resource.getAnswerId2LocateMap().get(callDataInfo.getLastAnswerId());
            String nodeId = answerLocateBO.getNodeId();
            String knowledgeId = answerLocateBO.getKnowledgeId();
            if (Objects.nonNull(nodeId)) {
                Map<String, Double> nodeAnswerMaxProgressMap = sessionContext.getNodeAnswerMaxProgressMap();
                if (nodeAnswerMaxProgressMap.containsKey(nodeId)) {
                    if (callDataInfo.getLastAnswerPlayProgress() > nodeAnswerMaxProgressMap.get(nodeId)) {
                        log.info("更新节点答案最大播放进度，nodeId={}, preProgress={}, progress={}", nodeId, nodeAnswerMaxProgressMap.get(nodeId), callDataInfo.getLastAnswerPlayProgress());
                        nodeAnswerMaxProgressMap.put(nodeId, (double) callDataInfo.getLastAnswerPlayProgress());
                    }
                } else {
                    log.info("设置节点答案最大播放进度，nodeId={},progress={}", nodeId, callDataInfo.getLastAnswerPlayProgress());
                    nodeAnswerMaxProgressMap.put(nodeId, (double) callDataInfo.getLastAnswerPlayProgress());
                }
            }
            if (Objects.nonNull(knowledgeId)) {
                Map<String, Double> knowledgeAnswerMaxProgressMap = sessionContext.getKnowledgeAnswerMaxProgressMap();
                if (knowledgeAnswerMaxProgressMap.containsKey(knowledgeId)) {
                    if (callDataInfo.getLastAnswerPlayProgress() > knowledgeAnswerMaxProgressMap.get(knowledgeId)) {
                        log.info("更新知识答案最大播放进度，knowledgeId={}, preProgress={}, progress={}", knowledgeId, knowledgeAnswerMaxProgressMap.get(knowledgeId), callDataInfo.getLastAnswerPlayProgress());
                        knowledgeAnswerMaxProgressMap.put(knowledgeId, (double) callDataInfo.getLastAnswerPlayProgress());
                    }
                } else {
                    log.info("设置知识答案最大播放进度，knowledgeId={},progress={}", knowledgeId, callDataInfo.getLastAnswerPlayProgress());
                    knowledgeAnswerMaxProgressMap.put(knowledgeId, (double) callDataInfo.getLastAnswerPlayProgress());
                }
            }
        }
    }

    private Map<String, Integer> getReceptionSpecialAnswerIdList(OriginChatData originChatData) {
        Map<String, Integer> countMap = new HashMap<>();
        List<String> list = originChatData.getLogList().stream()
                .filter(item -> ChatEventTypeEnum.USER_SAY_FINISH.equals(item.getEvent()))
                .map(EventLog::getReceptionInfo)
                .filter(Objects::nonNull)
                .filter(item -> AnswerSourceEnum.SPECIAL_ANSWER.equals(item.getType()))
                .map(ReceptionInfo::getTargetId)
                .collect(Collectors.toList());
        for (String id : list) {
            int count = countMap.getOrDefault(id, 0);
            countMap.put(id, count + 1);
        }

        // 针对用户无应答特殊语境 特殊处理下, 现在不确定当时为什么过滤事件为userSayFinish事件
        originChatData.getLogList().stream()
                        .filter(item -> ChatEventTypeEnum.USER_SILENCE.equals(item.getEvent()))
                        .map(EventLog::getReceptionInfo)
                        .filter(Objects::nonNull)
                        .filter(item -> AnswerSourceEnum.SPECIAL_ANSWER.equals(item.getType()))
                        .map(ReceptionInfo::getTargetId)
                        .forEach(id -> {
                            int count = countMap.getOrDefault(id, 0);
                            countMap.put(id, count + 1);
                        });

        return countMap;
    }

    // 独立流程命中次数, 命中问答知识列表, 命中
    private void calculateBusinessCount(OriginResult result, RobotRuntimeResource resource, CallDataInfo callDataInfo, OriginChatData originChatData) {
        Map<String, Integer> knowledgeHitCountMap = new HashMap<>();
        List<String> independentIdList = new ArrayList<>();

        Set<String> independentStepIdSet = resource.getStepIdMap().values().stream()
                .filter(step -> StepTypeEnum.INDEPENDENT.equals(step.getType()))
                .map(StepPO::getId).collect(Collectors.toSet());

        // 处理连续次数
        Set<String> businessKnowledgeIdSet = resource.getBusinessKnowledgeIdSet();
        Set<String> businessStepIdSet = resource.getBusinessStepIdSet();
        int businessCount = 0;
        int continuousBusinessCount = 0;
        int maxContinuousBusinessCount = 0;
        List<AnswerLocateBO> receptionList = originChatData.getLogList().stream()
                .filter(item -> !ChatManagerPriorityEnum.AI_UNKNOWN_AND_INTERRUPT.equals(item.getSelectedConditionType()))
                .map(EventLog::getAnswerLocate)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 计算问答知识命中次数
        for (AnswerLocateBO answerLocate : receptionList) {
            if (AnswerSourceEnum.KNOWLEDGE.equals(answerLocate.getAnswerSource())
                    && StringUtils.isNotBlank(answerLocate.getKnowledgeId())) {
                int count = knowledgeHitCountMap.getOrDefault(answerLocate.getKnowledgeId(), 0);
                knowledgeHitCountMap.put(answerLocate.getKnowledgeId(), count + 1);
            }
        }
        // 计算独立流程命中次数
        String previousStepId = null;
        for (AnswerLocateBO answerLocate : receptionList) {
            if ((AnswerSourceEnum.STEP.equals(answerLocate.getAnswerSource())
                    || AnswerSourceEnum.LLM_STEP.equals(answerLocate.getAnswerSource()))
                    && StringUtils.isNotBlank(answerLocate.getStepId())) {
                String currentStepId = answerLocate.getStepId();
                if (!currentStepId.equals(previousStepId) && independentStepIdSet.contains(currentStepId)) {
                    // 切换了流程
                    independentIdList.add(currentStepId);
                }
                previousStepId = currentStepId;
            }
        }

        // 把流程的命中进行聚合
        List<String> matchIdList = new ArrayList<>();
        previousStepId = null;
        for (AnswerLocateBO answerLocate : receptionList) {
            switch (answerLocate.getAnswerSource()) {
                case LLM_STEP:
                case STEP:
                    if (StringUtils.isNotBlank(answerLocate.getStepId()) && !answerLocate.getStepId().equals(previousStepId)) {
                        matchIdList.add(answerLocate.getStepId());
                    }
                    previousStepId = answerLocate.getStepId();
                    break;
                case KNOWLEDGE:
                    if (StringUtils.isNotBlank(answerLocate.getKnowledgeId())) {
                        matchIdList.add(answerLocate.getKnowledgeId());
                    }
                    break;
                case SPECIAL_ANSWER:
                default:
                    if (StringUtils.isNotBlank(answerLocate.getSpecialAnswerConfigId())) {
                        matchIdList.add(answerLocate.getSpecialAnswerConfigId());
                    }
                    break;
            }
        }

        for (String sourceId : matchIdList) {
            // 这里如果是从问答知识回到原主动流程, 则
            if (businessKnowledgeIdSet.contains(sourceId) || businessStepIdSet.contains(sourceId)) {
                businessCount++;
                continuousBusinessCount++;
                maxContinuousBusinessCount = Math.max(maxContinuousBusinessCount, continuousBusinessCount);
            } else {
                continuousBusinessCount = 0;
            }
        }

        result.setBusinessCount(businessCount);
        result.setContinuousBusinessCount(continuousBusinessCount);
        result.setIndependentStepCount(CollectionUtils.size(independentIdList));
        result.setKnowledgeCountMap(knowledgeHitCountMap);
    }

    private int calculatePercent(int count, int total) {
        return 100 * count / (Math.max(1, total));
    }

    private List<String> getUserSayContentList(CallDataInfo callDataInfo, OriginChatData originChatData) {
        return originChatData.getLogList().stream()
                .filter(item -> ChatEventTypeEnum.USER_SAY_FINISH.equals(item.getEvent()))
                .map(EventLog::getUserInput)
                .collect(Collectors.toList());
    }

    private Integer calculateUserSayCount(OriginChatData originChatData) {
        return originChatData.getLogList().stream()
                .filter(item -> ChatEventTypeEnum.USER_SAY_FINISH.equals(item.getEvent())
                        && CollectionUtils.isNotEmpty(item.getDebugLog())
                        && item.getDebugLog().stream().noneMatch(log -> StringUtils.contains(log, "噪音过滤")))
                .map(EventLog::getUserInput)
                .mapToInt(StringUtils::length).sum();
    }

    private long calculateAiUnknownCount(OriginChatData originChatData) {
        return originChatData.getLogList().stream()
                .map(EventLog::getAnswerLocate)
                .filter(Objects::nonNull)
                .filter(item -> AnswerSourceEnum.SPECIAL_ANSWER.equals(item.getAnswerSource())
                        && SpecialAnswerConfigPO.AI_UNKNOWN.equals(item.getSpecialAnswerConfigName()))
                .count();
    }

    private long calculateSpecialCount(OriginChatData originChatData) {
        return originChatData.getLogList().stream()
                .map(EventLog::getReceptionInfo)
                .filter(Objects::nonNull)
                .filter(item -> AnswerSourceEnum.SPECIAL_ANSWER.equals(item.getType()))
                .count();
    }

    /**
     * 这里需要处理跳转节点答案为空的情况
     */
    private Map<String, String> getReceptionNodeIdList(OriginChatData originChatData) {
        Map<String, String> result = new HashMap<>();
        originChatData.getLogList().stream()
                .map(EventLog::getAnswerLocate)
                .filter(Objects::nonNull)
                .filter(item -> AnswerSourceEnum.STEP.equals(item.getAnswerSource()))
                .forEach(item -> result.put(item.getNodeId(), item.getStepId()));

        // 上面是根据答案收集命中过的所有节点, 但是跳转节点可能是答案为空的情况, 这里需要补充进来(这只是临时方案)
        originChatData.getLogList().stream()
                .map(EventLog::getPrevNodeInfoList)
                .filter(CollectionUtils::isNotEmpty)
                .forEach(list -> {
                    for (NodeJumpInfo node2NodeInfo : list) {
                        if (StringUtils.isNotBlank(node2NodeInfo.getToNodeId()) && StringUtils.isNotBlank(node2NodeInfo.getStepId())) {
                            if (!result.containsKey(node2NodeInfo.getToNodeId())) {
                                result.put(node2NodeInfo.getToNodeId(), node2NodeInfo.getStepId());
                            }
                        }
                    }
                });
        return result;
    }

    /**
     * 获取最后接待的节点id, 即最后一个答案的来源是对话流
     */
    private Optional<String> getLastReceptionNodeId(OriginChatData originChatData) {
        return getLastAnswerLocate(originChatData)
                .filter(item -> AnswerSourceEnum.STEP.equals(item.getAnswerSource()))
                .map(AnswerLocateBO::getNodeId);
    }

    private Optional<String> getLastReceptionKnowledgeId(OriginChatData originChatData) {
        return getLastAnswerLocate(originChatData)
                .filter(item -> AnswerSourceEnum.KNOWLEDGE.equals(item.getAnswerSource()))
                .map(AnswerLocateBO::getKnowledgeId);
    }

    private Optional<? extends AnswerLocateBO> getLastAnswerLocate(OriginChatData originChatData) {
        List<EventLog> logList = new ArrayList<>(originChatData.getLogList());
        Collections.reverse(logList);
        return logList.stream()
                .map(EventLog::getAnswerLocate)
                .filter(Objects::nonNull)
                .findFirst();
    }


    /**
     * 计算意图属性相关字段, 比如肯定次数, 拒绝次数, 连续肯定次数, 连续拒绝次数
     */
    private void calculateIntentProperties(OriginResult result,
                                           RobotRuntimeResource resource,
                                           CallDataInfo callDataInfo,
                                           OriginChatData originChatData) {

        int decline = 0;
        int definitive = 0;
        int continuousDecline = 0;
        int continuousDefinitive = 0;
        int maxContinuousDecline = 0;
        int maxContinuousDefinitive = 0;

        List<String> hitIntentOrderList = new ArrayList<>();

        List<EventLog> eventLogList = originChatData.getLogList().stream()
                .filter(item -> ChatEventTypeEnum.USER_SAY_FINISH.equals(item.getEvent()))
                .collect(Collectors.toList());

        for (EventLog eventLog : eventLogList) {
            IntentPO intent;
            if (Objects.nonNull(eventLog.getPredictResult()) && Objects.nonNull((intent = resource.getIntentMap().get(eventLog.getPredictResult().getIntentId())))) {
                hitIntentOrderList.add(intent.getId());
                if (IntentPropertiesEnum.DECLINE.equals(intent.getIntentProperties())) {
                    decline++;
                    continuousDecline++;
                    continuousDefinitive = 0;
                } else if (IntentPropertiesEnum.DEFINITE.equals(intent.getIntentProperties())) {
                    definitive++;
                    continuousDefinitive++;
                    continuousDecline = 0;
                } else {
                    continuousDecline = 0;
                    continuousDefinitive = 0;
                }
                maxContinuousDecline = Math.max(maxContinuousDecline, continuousDecline);
                maxContinuousDefinitive = Math.max(maxContinuousDefinitive, continuousDefinitive);
            } else {
                continuousDecline = 0;
                continuousDefinitive = 0;
            }
            if (Objects.nonNull(eventLog.getUninterruptedPredictResult()) && Objects.nonNull((intent = resource.getIntentMap().get(eventLog.getUninterruptedPredictResult().getIntentId())))) {
                hitIntentOrderList.add(intent.getId());
            }
        }

        result.setDefiniteCount(definitive);
        result.setDeclineCount(decline);
        result.setContinuousDeclineCount(maxContinuousDecline);
        result.setContinuousDefiniteCount(maxContinuousDefinitive);
        result.setIntentIdSet(new HashSet<>(hitIntentOrderList));
        result.setHitIntentOrderList(hitIntentOrderList);
    }

    private Tuple2<List<String>, List<String>> calculateContinuousKnowledgeAndSpecialAnswerConfig(OriginChatData originChatData) {
        List<EventLog> logList = originChatData.getLogList().stream()
                .filter(item -> Objects.nonNull(item.getReceptionInfo()) && Objects.nonNull(item.getAnswerLocate()))
                .collect(Collectors.toList());
        List<String> continuousKnowledgeIdList = new ArrayList<>();
        List<String> continuousSpecialAnswerConfigIdList = new ArrayList<>();
        for (EventLog log : logList) {
            ReceptionInfo receptionInfo = log.getReceptionInfo();

            if (AnswerSourceEnum.KNOWLEDGE.equals(receptionInfo.getType())) {
                continuousKnowledgeIdList.add(receptionInfo.getTargetId());
            } else {
                continuousKnowledgeIdList.add(null);
            }

            if (AnswerSourceEnum.SPECIAL_ANSWER.equals(receptionInfo.getType())) {
                continuousSpecialAnswerConfigIdList.add(receptionInfo.getTargetId());
            } else {
                continuousSpecialAnswerConfigIdList.add(null);
            }
        }
        return Tuple.of(continuousKnowledgeIdList, continuousSpecialAnswerConfigIdList);
    }

    /**
     * 统计问答知识触发次数
     *
     * @return key: 问答知识id, value: 问答知识触发次数
     */
    private Map<String, Integer> calculateBusinessKnowledgeCount(RobotRuntimeResource resource,
                                                                 CallDataInfo callDataInfo,
                                                                 OriginChatData originChatData) {
        Map<String, Integer> result = new HashMap<>();

        originChatData.getLogList().stream()
                .map(EventLog::getAnswerLocate)
                .filter(Objects::nonNull)
                .filter(item -> AnswerSourceEnum.KNOWLEDGE.equals(item.getAnswerSource()))
                .forEach(item -> {
                    String knowledgeId = item.getKnowledgeId();
                    Integer count = result.getOrDefault(knowledgeId, -1);
                    result.put(knowledgeId, count + 1);
                });
        return result;
    }

    /**
     * 计算主流程进度
     */
    private int calculateMainStepProgress(RobotRuntimeResource resource, CallDataInfo callDataInfo, OriginChatData originChatData) {
        // 从答案中的流程信息获取就可以了
        int mainStepSize = CollectionUtils.size(resource.getMainStepList());
        List<String> mainStepIdList = resource.getMainStepList().stream().map(StepPO::getId).collect(Collectors.toList());

        int maxIndex = originChatData.getLogList().stream()
                .map(EventLog::getAnswerLocate)
                .filter(Objects::nonNull)
                .filter(item -> AnswerSourceEnum.STEP.equals(item.getAnswerSource())
                        ||  AnswerSourceEnum.LLM_STEP.equals(item.getAnswerSource()))
                .mapToInt(item -> mainStepIdList.lastIndexOf(item.getStepId()))
                .max()
                .orElse(-1);

        return 100 * (maxIndex + 1) / mainStepSize;
    }

    /**
     * 计算客户关注点
     */
    private Set<String> calculateCustomerFocus(RobotRuntimeResource resource, OriginChatData originChatData) {
        return originChatData.getLogList().stream().map(EventLog::getReceptionInfo)
                .filter(Objects::nonNull).map(s -> resource.getCustomerFocusTable().get(s.getType(), s.getTargetId()))
                .filter(Objects::nonNull).collect(Collectors.toSet());
    }

    /**
     * 计算ai轮次, 忽略掉客户无应答的处理
     */
    private int calculateAiRounds(OriginChatData originChatData) {
        // 就是播了多少个答案
        // 其中, 大模型生成的答案不计算在内
        return (int) originChatData.getLogList().stream()
                // 过滤掉继续播放的答案
                .filter(item -> !ChatManagerPriorityEnum.AI_UNKNOWN_AND_INTERRUPT.equals(item.getSelectedConditionType()))
                .filter(item -> !ChatEventTypeEnum.LLM_REQUEST.equals(item.getEvent()))
                .map(EventLog::getAnswerLocate)
                .filter(Objects::nonNull)
                .count();
    }

    private int calculateEffectiveChatRounds(OriginChatData originChatData) {
        int count = 0;
        boolean isNewRound = true;
        for (EventLog log : originChatData.getLogList()) {
            if (ChatEventTypeEnum.USER_SAY_FINISH.equals(log.getEvent())) {
                if (CollectionUtils.isNotEmpty(log.getActionList()) && log.getActionList().stream().anyMatch(action -> ActionTypeEnum.IGNORE_INPUT.equals(action.getType()))) {
                    continue;
                }
                if (isNewRound) {
                    count++;
                    isNewRound = false;
                }
            }
            if (Objects.nonNull(log.getAnswerLocate())) {
                isNewRound = Objects.nonNull(log.getAnswerAudioPlayConfig()) && RepeatAnswerPlayStrategyEnum.REPLAY.equals(log.getAnswerAudioPlayConfig().getRepeatPlayStrategy());
            }
        }
        return count;
    }

    private boolean isUserSilenceAnswer(AnswerResult answer) {
        return Objects.nonNull(answer)
                && Objects.nonNull(answer.getLocate())
                && SpecialAnswerConfigPO.USER_SILENCE.equals(answer.getLocate().getSpecialAnswerConfigName());
    }

    private Optional<IntentRulePO> findFirstMatchedRule(List<IntentRulePO> ruleList,
                                                        OriginResult originResult,
                                                        CallDataInfo callDataInfo) {
        if (CollectionUtils.isEmpty(ruleList)) {
            log.info("ruleList is empty ruleList.size: {}, callDataInfo.dialStatus: {}",
                    CollectionUtils.size(ruleList), callDataInfo.getDialStatus());
            return Optional.empty();
        }
        return ruleList.stream()
                .filter(rule -> DialStatusEnum.ANSWERED.getCode() == callDataInfo.getDialStatus()
                        || ruleContainsDialStatusCondition(rule))
                .filter(rule -> matchedRule(rule, originResult, callDataInfo))
                .findFirst();
    }

    private boolean ruleContainsDialStatusCondition(IntentRulePO rule) {
        if (Objects.isNull(rule) || CollectionUtils.isEmpty(rule.getConditionList())) {
            return false;
        }
        return rule.getConditionList().stream()
                .anyMatch(condition -> DialogFlowConditionTypeEnum.DIALOG_STATUS.equals(condition.getType()));
    }

    private boolean matchedRule(IntentRulePO intentRule, OriginResult originResult, CallDataInfo callDataInfo) {
        if (Objects.isNull(intentRule) || CollectionUtils.isEmpty(intentRule.getConditionList())) {
            return false;
        }

        try {
            boolean success = true;
            List<IntentRuleConditionPO> customerInitiativeConditionList = new ArrayList<>();
            Set<String> knowledgeIdSet = new HashSet<>();
            List<DialogFlowExtraRuleConditionNodePO> nodeList = new ArrayList<>();
            for (IntentRuleConditionPO condition : intentRule.getConditionList()) {
                // 客户主动挂机节点判断最后再处理
                if (DialogFlowConditionTypeEnum.CUSTOMER_HANGUP_ON_SPECIAL_NODE.equals(condition.getType())) {
                    customerInitiativeConditionList.add(condition);
                } else {
                    // 匹配失败 立即返回
                    if (!matchedCondition(intentRule.getId(), condition, originResult, callDataInfo)) {
                        success = false;
                        break;
                    }
                    if (isCustomerInitiativeDependCondition(condition.getType())) {
                        if (!DialogFlowConditionTypeEnum.TRIGGER_ROBOT_KNOWLEDGE.equals(condition.getType())
                                && CollectionUtils.isNotEmpty(condition.getNodeList())) {
                            nodeList.addAll(condition.getNodeList());
                        }
                        if (DialogFlowConditionTypeEnum.TRIGGER_ROBOT_KNOWLEDGE.equals(condition.getType())
                                && CollectionUtils.isNotEmpty(condition.getRobotKnowledgeIdList())) {
                            knowledgeIdSet.addAll(condition.getRobotKnowledgeIdList());
                        }
                    }
                }
            }

            if (success && CollectionUtils.isNotEmpty(customerInitiativeConditionList)) {
                for (IntentRuleConditionPO condition : customerInitiativeConditionList) {
                    if (!checkCustomerInitiativeHangupCondition(condition, originResult, nodeList, knowledgeIdSet)) {
                        return false;
                    }
                }
            }
            return success;
        } catch (Exception e) {
            log.warn("条件判断异常, rule={}", intentRule.getId(), e);
        }
        return false;
    }


    private boolean checkCustomerInitiativeHangupCondition(IntentRuleConditionPO condition,
                                                           OriginResult originalResultBO,
                                                           List<DialogFlowExtraRuleConditionNodePO> nodeList,
                                                           Set<String> knowledgeIdSet) {

        log.info("checkCustomerInitiativeHangupCondition, nodeList={}, knowledgeIdSet={}", nodeList, knowledgeIdSet);
        if (!DialogFlowConditionTypeEnum.CUSTOMER_HANGUP_ON_SPECIAL_NODE.equals(condition.getType())) {
            return false;
        }

        Predicate<OriginResult> predicate = (originResult) -> {
            // 判断挂机的节点在这些节点中, 或者问答知识在这些里面
            if (StringUtils.isNotBlank(originalResultBO.getUserHangupKnowledgeId())
                    && CollectionUtils.isNotEmpty(knowledgeIdSet)
                    && knowledgeIdSet.contains(originalResultBO.getUserHangupKnowledgeId())) {
                return true;
            }

            if (Objects.nonNull(originalResultBO.getUserHangupNodeId())
                    && CollectionUtils.isNotEmpty(nodeList)) {
                String nodeId = originalResultBO.getUserHangupNodeId();
                for (DialogFlowExtraRuleConditionNodePO nodeInfo : nodeList) {
                    if (nodeInfo.getNodeIndex().equals(nodeId)) {
                        return true;
                    }
                }
            }
            return false;
        };

        if (DialogFlowConditionOperationTypeEnum.TRUE.equals(condition.getOperation())) {
            return predicate.test(originalResultBO);
        }
        return !predicate.test(originalResultBO);
    }

    private boolean isCustomerInitiativeDependCondition(DialogFlowConditionTypeEnum conditionType) {
        return DialogFlowConditionTypeEnum.TRIGGER_ROBOT_KNOWLEDGE.equals(conditionType)
                || DialogFlowConditionTypeEnum.TRIGGER_PROCESS_NODE.equals(conditionType);
    }

    private boolean matchedCondition(String ruleId, IntentRuleConditionPO condition, OriginResult originResult, CallDataInfo callDataInfo) {
        if (Objects.isNull(condition)) {
            log.warn("数据异常, condition为空, ruleId={}", ruleId);
            return false;
        }
        if (Objects.isNull(condition.getType())) {
            log.warn("数据异常, condition.type为空, ruleId={}", ruleId);
        }

        DialogFlowConditionOperationTypeEnum operation = condition.getOperation();
        Integer number = condition.getNumber();
        switch (condition.getType()) {
            case MAIN_STEP_FINISH_PERCENTAGE:
                return matchedExpression(originResult.getMainStepCompleteProgress(), operation, number);
            case DIALOG_ROUND_COUNT:
                return matchedExpression(originResult.getAiRounds(), operation, number);
            case EFFECTIVE_CHAT_ROUNDS:
                return matchedExpression(originResult.getEffectiveChatRounds(), operation, number);
            case DIALOG_STATUS:
                return matchedExpression(callDataInfo.getDialStatus(), operation, condition.getDialStatusList());
            case DIALOG_DURATION:
                int duration = 0;
                if (Objects.nonNull(originResult.getChatDuration())) {
                    duration = originResult.getChatDuration().intValue();
                }
                return matchedExpression(duration, operation, number);
            case ACTUAL_DIALOG_DURATION:
                int actualDuration = 0;
                if (Objects.nonNull(originResult.getActualChatDuration())) {
                    actualDuration = originResult.getActualChatDuration().intValue();
                }
                return matchedExpression(actualDuration, operation, number);
            case CUSTOMER_SAY_WORD_COUNT:
                return matchedExpression(originResult.userSayWordCount, operation, number);
            case BUSINESS_KNOWLEDGE_TRIGGER_COUNT:
                return matchedExpression(originResult.getBusinessCount(), operation, number, originResult.getContinuousBusinessCount());
            case DECLINE_TRIGGER_COUNT:
                return matchedExpression(originResult.getDeclineCount(), operation, number, originResult.getContinuousDeclineCount());
            case DEFINITIVE_TRIGGER_COUNT:
                return matchedExpression(originResult.getDefiniteCount(), operation, number, originResult.getContinuousDefiniteCount());
            case TRIGGER_PROCESS_NODE:
                return matchedNodeExpression(new ArrayList<>(originResult.getNodeId2StepMap().keySet()), condition.getNodeList());
            case CUSTOMER_HANGUP_ON_SPECIAL_NODE:
                return false;
            case TRIGGER_ROBOT_KNOWLEDGE:
                return matchedKnowledgeExpression(originResult.getContinuousKnowledgeIdList(), originResult.getKnowledgeCountMap(), condition.getRobotKnowledgeIdList(), operation, number);
            case TRIGGER_DEPENDENCE_DIALOGFLOW:
                return matchedStepExpression(originResult.getStepCountMap(), condition.getStepList(), condition.getOperation(), condition.getNumber());
            case CUSTOMER_FINALLY_REJECT:
                return matchedExpression(operation, originResult.isLastDecline());
            case CUSTOMER_FINALLY_DEFINITIVE:
                return matchedExpression(operation, originResult.isLastDefinitive());
            case AI_UNKNOWN_PERCENT:
                return matchedExpression(originResult.getAiUnknownPercent(), operation, number);
            case TRIGGER_SPECIAL_CONTEXT:
                return matchedSpecialAnswerExpression(originResult.getContinuousSpecialAnswerConfigIdList(), originResult.getSpecialAnswerCountMap(), condition.getSpecialAnswerId(), operation, number);
            case FAST_HANGUP:
                return matchedExpression(operation, originResult.isFastHangup());
            case CUSTOMER_HANGUP:
                return matchedExpression(operation, originResult.isHangupByUser());
            case DIALOG_CONTENT:
                return matchedExpression(originResult.getUserSayContentList(), operation, condition.getKeywordPatterns());
            case LAST_CUSTOMER_CONTENT:
                if (StringUtils.isBlank(originResult.getLastUserSayContent())) {
                    return DialogFlowConditionOperationTypeEnum.NOT_CONTAIN.equals(operation);
                }
                return matchedExpression(Collections.singletonList(originResult.lastUserSayContent), operation, condition.getKeywordPatterns());
            case LAST_AI_CONTENT:
                if (StringUtils.isBlank(originResult.getLastAiSayContent())) {
                    return DialogFlowConditionOperationTypeEnum.NOT_CONTAIN.equals(operation);
                }
                return matchedExpression(Collections.singletonList(originResult.lastAiSayContent), operation, condition.getKeywordPatterns());
            case STEP_NODE_FINISH_PERCENTAGE:
                return matchedExpression(originResult.getNodeAnswerMaxProgressMap(), operation, number, condition);
            case KNOWLEDGE_FINISH_PERCENTAGE:
                return matchedExpression(originResult.getKnowledgeAnswerMaxProgressMap(), operation, number, condition);
            case ALGORITHM_PRIVATE_DOMAIN_INTENT_LEVEL:
                if (Objects.isNull(originResult.getPrivateDomainIntentLevelResult())) {
                    return false;
                }
                return originResult.getPrivateDomainIntentLevelResult()._1.equals(number);
            case ALGORITHM_PRIVATE_DOMAIN_INTENT_LEVEL_PASSIVE:
                if (Objects.isNull(originResult.getPassivePrivateDomainIntentLevelResult())) {
                    return false;
                }
                return originResult.getPassivePrivateDomainIntentLevelResult()._1.equals(number);
            case ALGORITHM_ACTIVITY_INTENT_LEVEL:
                if (Objects.isNull(originResult.getActivityIntentLevelResult())) {
                    return false;
                }
                return originResult.getActivityIntentLevelResult()._1.equals(number);
            case ALGORITHM_EDUCATION_ACTIVITY_INTENT_LEVEL:
                log.info("匹配算法意向标签(教育活动通知),接口返回结果{},配置信息{}", originResult.getEducationActivityIntentLevelResult(), number);
                if (Objects.isNull(originResult.getEducationActivityIntentLevelResult())) {
                    return false;
                }
                return originResult.getEducationActivityIntentLevelResult()._1.equals(number);
            case INTENT_TAG:
                if (Objects.isNull(originResult.getIntentLevel()) || CollectionUtils.isEmpty(condition.getIntentLevelIdList())) {
                    return false;
                }
                return condition.getIntentLevelIdList().contains(originResult.getIntentLevel());
            case ENTITY_COLLECT:
                return matchedEntityCollectCondition(originResult, condition);
            case HIT_INTENT:
                return matchedHintIntentCondition(originResult, condition);
            case DYNAMIC_VARIABLE:
                return matchedDynamicVariableCondition(originResult, condition);
            case LLM_LABEL:
                return StringUtils.equals(originResult.getHitLlmLabelId(), condition.getLlmLabelId());
            case LLM_BUILT_IN_TAG:
                return CollectionUtils.isNotEmpty(CollectionUtils.intersection(originResult.getHitBuiltInTagList(), condition.getDescList()));
            case LLM_CUSTOM_TAG:
                return CollectionUtils.isNotEmpty(CollectionUtils.intersection(originResult.getHitCustomTagList(), condition.getDescList()));
            case USER_LAST_SAY:
                return matchedExpression(condition.getOperation(), BooleanUtils.isTrue(originResult.getUserLastSay()) );
            default:
                log.warn("意向等级条件配置错误, 不存在的类型:{}", condition.getType().getDesc());
                return false;
        }
    }

    private boolean matchedDynamicVariableCondition(OriginResult originResult, IntentRuleConditionPO condition) {
        if (CollectionUtils.isEmpty(condition.getVariableIdList())) {
            log.warn("variableIdList为空, condition={}, return false", condition);
            return false;
        }

        Predicate<String> failedPredicate = (variableId) -> {
            String varValue = originResult.getVariableIdValueMap().get(variableId);
            return StringUtils.isBlank(varValue);
        };

        Predicate<String> successPredicate = (variableId) -> {
            String varValue = originResult.getVariableIdValueMap().get(variableId);
            return StringUtils.isNotBlank(varValue);
        };

        switch (condition.getOperation()) {
            case ANY_NULL:
                return condition.getVariableIdList().stream().anyMatch(failedPredicate);
            case ALL_NULL:
                return condition.getVariableIdList().stream().allMatch(failedPredicate);
            case ANY_NOT_NULL:
                return condition.getVariableIdList().stream().anyMatch(successPredicate);
            case ALL_NOT_NULL:
                return condition.getVariableIdList().stream().allMatch(successPredicate);
            default:
                log.warn("条件配置错误, {} 不支持的操作:{}", condition.getType().getDesc(), condition.getOperation().getDesc());
                return false;
        }
    }

    private boolean matchedHintIntentCondition(OriginResult originResult, IntentRuleConditionPO condition) {
        if (CollectionUtils.isEmpty(condition.getIntentIdList())) {
            log.warn("意图集合为空, condition={}, return false", condition);
            return false;
        }

        Predicate<String> failedPredicate = (intentId) -> !originResult.getIntentIdSet().contains(intentId);
        Predicate<String> successPredicate = (intentId) -> originResult.getIntentIdSet().contains(intentId);

        switch (condition.getOperation()) {
            case ANY_MISS:
                return condition.getIntentIdList().stream().anyMatch(failedPredicate);
            case ALL_MISS:
                return condition.getIntentIdList().stream().allMatch(failedPredicate);
            case ANY_HIT:
                if (DialogFlowConditionOperationTypeEnum.GREATER_OR_EQUAL.equals(condition.getSubOperation())) {
                    Map<String, Long> intentIdFrequencMap = originResult.getHitIntentOrderList().stream().collect(Collectors.groupingBy(s -> s, Collectors.counting()));
                    return condition.getIntentIdList().stream().mapToLong(intentId -> intentIdFrequencMap.getOrDefault(intentId, 0L)).sum() >= condition.getNumber();
                }
                if (DialogFlowConditionOperationTypeEnum.LESS_OR_EQUAL.equals(condition.getSubOperation())) {
                    Map<String, Long> intentIdFrequencMap = originResult.getHitIntentOrderList().stream().collect(Collectors.groupingBy(s -> s, Collectors.counting()));
                    return condition.getIntentIdList().stream().mapToLong(intentId -> intentIdFrequencMap.getOrDefault(intentId, 0L)).sum() <= condition.getNumber();
                }
                if (DialogFlowConditionOperationTypeEnum.CONSECUTIVE.equals(condition.getSubOperation())) {
                    return matchContinuousRule(originResult.getHitIntentOrderList(), condition.getIntentIdList(), condition.getNumber());
                }
                return false;
            case ALL_HIT:
                return condition.getIntentIdList().stream().allMatch(successPredicate);
            default:
                log.warn("意向等级条件配置错误, {} 不支持的操作:{}", condition.getType().getDesc(), condition.getOperation().getDesc());
                return false;
        }
    }

    public static Map<String, Integer> calculateMaxConsecutiveCounts(List<String> list) {
        Map<String, Integer> maxConsecutiveCounts = new HashMap<>();
        if (list == null || list.isEmpty()) {
            return maxConsecutiveCounts;
        }

        String currentElement = list.get(0);
        int currentCount = 1;
        maxConsecutiveCounts.put(currentElement, currentCount);

        for (int i = 1; i < list.size(); i++) {
            if (StringUtils.equals(list.get(i), currentElement)) {
                currentCount++;
                if (currentCount > maxConsecutiveCounts.get(currentElement)) {
                    maxConsecutiveCounts.put(currentElement, currentCount);
                }
            } else {
                currentElement = list.get(i);
                currentCount = 1;
                if (!maxConsecutiveCounts.containsKey(currentElement)) {
                    maxConsecutiveCounts.put(currentElement, currentCount);
                }
            }
        }
        return maxConsecutiveCounts;
    }

    private boolean matchedEntityCollectCondition(OriginResult originResult, IntentRuleConditionPO condition) {
        if (CollectionUtils.isEmpty(condition.getEntityIdList())) {
            log.warn("实体集合为空, condition={}, return false", condition);
            return false;
        }

        Predicate<String> failedPredicate = (entityId) -> {
            List<String> entityValueList = originResult.getEntityCollectMap().get(entityId);
            return CollectionUtils.isEmpty(entityValueList);
        };

        Predicate<String> successPredicate = (entityId) -> {
            List<String> entityValueList = originResult.getEntityCollectMap().get(entityId);
            return CollectionUtils.isNotEmpty(entityValueList);
        };

        switch (condition.getOperation()) {
            case ANY_FAILURE:
                return condition.getEntityIdList().stream().anyMatch(failedPredicate);
            case ALL_FAILURE:
                return condition.getEntityIdList().stream().allMatch(failedPredicate);
            case ANY_SUCCESS:
                return condition.getEntityIdList().stream().anyMatch(successPredicate);
            case ALL_SUCCESS:
                return condition.getEntityIdList().stream().allMatch(successPredicate);
            default:
                log.warn("意向等级条件配置错误, {} 不支持的操作:{}", condition.getType().getDesc(), condition.getOperation().getDesc());
                return false;
        }
    }

    private boolean matchedStepExpression(Map<String, Integer> stepMap, List<String> stepList, DialogFlowConditionOperationTypeEnum operation,
                                          Integer number) {
        int count = 0;
        for (String s : stepList) {
            if (stepMap.containsKey(s)) {
                count += stepMap.getOrDefault(s, 0);
            }
        }
        return matchedExpression(count, operation, number);
    }

    private boolean matchedExpression(Map<String, Double> answerMaxProgressMap, DialogFlowConditionOperationTypeEnum operation,
                                      Integer number, IntentRuleConditionPO condition) {
        if (answerMaxProgressMap.isEmpty()) {
            return false;
        }
        if (DialogFlowConditionTypeEnum.STEP_NODE_FINISH_PERCENTAGE.equals(condition.getType())) {
            return condition.getNodeList().stream().anyMatch(node -> {
                if (answerMaxProgressMap.containsKey(node.getNodeIndex())) {
                    return matchedExpression(answerMaxProgressMap.get(node.getNodeIndex()).intValue(), operation, number);
                }
                return false;
            });
        } else {
            return condition.getRobotKnowledgeIdList().stream().anyMatch(knowledgeId -> {
                if (answerMaxProgressMap.containsKey(knowledgeId)) {
                    return matchedExpression(answerMaxProgressMap.get(knowledgeId).intValue(), operation, number);
                }
                return false;
            });
        }
    }

    private boolean matchedExpression(Integer leftValue, DialogFlowConditionOperationTypeEnum operation, Integer rightValue) {
        return matchedExpression(leftValue, operation, rightValue, 0);
    }

    private boolean matchedExpression(List<String> contentList, DialogFlowConditionOperationTypeEnum operation, List<PatternEnhance> patternList) {
        // 转换正则表达式
        if (CollectionUtils.isEmpty(patternList)) {
            return false;
        }

        switch (operation) {
            case CONTAIN_ANY:
                for (String text : contentList) {
                    for (PatternEnhance patternEnhance : patternList) {
                        if (isMatches(text, patternEnhance)) {
                            return true;
                        }
                    }
                }
                return false;
            case CONTAIN_ALL:
                for (PatternEnhance patternEnhance : patternList) {
                    boolean anyMatch = contentList.stream().anyMatch(text -> isMatches(text, patternEnhance));
                    if (!anyMatch) {
                        return false;
                    }
                }
                return true;
            case NOT_CONTAIN:
                for (String text : contentList) {
                    for (PatternEnhance patternEnhance : patternList) {
                        if (isMatches(text, patternEnhance)) {
                            return false;
                        }
                    }
                }
                return true;
            default:
                log.error("[LogHub_Warn]操作类型不匹配, 字符串集合包含关系操作类型不包含" + operation.name());
                return false;
        }
    }

    private boolean isMatches(String text, PatternEnhance patternEnhance) {
        return patternEnhance.find(text).isPresent();
    }

    private boolean matchedExpression(DialogFlowConditionOperationTypeEnum operation, boolean yes) {
        return DialogFlowConditionOperationTypeEnum.TRUE.equals(operation) == yes;
    }

    private boolean matchedNodeExpression(List<String> nodeIdList, List<DialogFlowExtraRuleConditionNodePO> expectList) {
        if (CollectionUtils.isEmpty(expectList)) {
            return false;
        }
        if (CollectionUtils.isEmpty(nodeIdList)) {
            return false;
        }
        Set<String> nodeIdSet = new HashSet<>(nodeIdList);
        return expectList.stream()
                .map(DialogFlowExtraRuleConditionNodePO::getNodeIndex)
                .anyMatch(nodeIdSet::contains);
    }

    private boolean matchedKnowledgeExpression(List<String> continuousKnowledgeIdList, Map<String, Integer> knowledgeTriggerCountMap, List<String> expectList, DialogFlowConditionOperationTypeEnum operation, Integer rightValue) {
        if (Objects.isNull(expectList)
                || Objects.isNull(operation)
                || Objects.isNull(rightValue)) {
            return false;
        }
        if (DialogFlowConditionOperationTypeEnum.CONSECUTIVE.equals(operation)) {
            return matchContinuousRule(continuousKnowledgeIdList, expectList, rightValue);
        }
        int totalCount = 0;
        for (String s : expectList) {
            if (knowledgeTriggerCountMap.containsKey(s)) {
                totalCount += knowledgeTriggerCountMap.getOrDefault(s, 0);
            }
        }
        return matchedExpression(totalCount, operation, rightValue);
    }

    private boolean matchedSpecialAnswerExpression(List<String> continuousSpecialAnswerConfigIdList, Map<String, Integer> specialAnswerCountMap, String specialAnswerId,
                                                   DialogFlowConditionOperationTypeEnum operation, Integer rightValue) {
        if (StringUtils.isBlank(specialAnswerId)
                || Objects.isNull(operation)
                || Objects.isNull(rightValue)) {
            return false;
        }
        if (DialogFlowConditionOperationTypeEnum.CONSECUTIVE.equals(operation)) {
            return matchContinuousRule(continuousSpecialAnswerConfigIdList, Collections.singletonList(specialAnswerId), rightValue);
        }
        int count = specialAnswerCountMap.getOrDefault(specialAnswerId, 0);
        return matchedExpression(count, operation, rightValue);
    }

    private boolean matchContinuousRule(List<String> srcIdList, List<String> expectIdList, Integer times) {
        if (CollectionUtils.isEmpty(srcIdList) || CollectionUtils.isEmpty(expectIdList)) {
            return false;
        }
        Map<String, Integer> maxConsecutiveCounts = calculateMaxConsecutiveCounts(srcIdList);
        return expectIdList.stream().anyMatch(expectId -> maxConsecutiveCounts.getOrDefault(expectId, 0) >= times);
    }

    private boolean matchedExpression(Integer leftValue, DialogFlowConditionOperationTypeEnum operation, List<DialStatusEnum> dialStatusList) {
        List<Integer> dialStatusCodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dialStatusList)) {
            dialStatusCodeList = dialStatusList.stream().map(DialStatusEnum::getCode).collect(Collectors.toList());
        }
        if (Objects.nonNull(dialStatusList)) {
            switch (operation) {
                case CONTAIN_ANY:
                    return dialStatusCodeList.contains(leftValue);
                case NOT_CONTAIN:
                    return !dialStatusCodeList.contains(leftValue);
                default:
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("操作类型不匹配, 通话状态集合包含关系操作类型不包含:%s", operation.name()));
            }
        }
        return false;
    }

    private boolean matchedExpression(Integer leftValue, DialogFlowConditionOperationTypeEnum operation, Integer rightValue, Integer continueLeftValue) {
        switch (operation) {
            case GREATER_OR_EQUAL:
                return leftValue >= rightValue;
            case LESS_OR_EQUAL:
                return leftValue <= rightValue;
            case EQUAL:
                return leftValue.equals(rightValue);
            case CONSECUTIVE:
                return continueLeftValue >= rightValue;
            default:
                log.error("[LogHub_Warn]操作类型不匹配, 整数比较操作类型不包含" + operation.name());
                return false;
        }
    }

    private String generateRuleDescription(AnswerLocateBO locate, Map<Integer, String> intentLevelCode2NameMap, Integer code) {
        String type = locate.getAnswerSource().getDesc();
        String name = "未知";
        String label = "未知";
        switch (locate.getAnswerSource()) {
            case KNOWLEDGE:
                name = locate.getKnowledgeName();
                label = locate.getKnowledgeLabel();
                break;
            case STEP:
                type = "节点";
                name = locate.getNodeName();
                label = locate.getNodeLabel();
                break;
            case SPECIAL_ANSWER:
                name = locate.getSpecialAnswerConfigName();
                label = locate.getSpecialAnswerConfigLabel();
                break;
        }

        String codeName = getIntentLevelDetailName(intentLevelCode2NameMap, code);
        return String.format("命中%s：%s(%s), 该%s设置了意向等级为：%s", type, label, name, type, codeName);
    }

    /**
     * 获取设置了最高等级的答案信息
     * 最高意味着code最小
     */
    private Optional<Tuple2<AnswerLocateBO, Integer>> findHighestIntentLevelFromAnswer(RobotRuntimeResource resource, OriginResult originResult) {
        int intentLevelDetailCode = Integer.MAX_VALUE;
        AnswerLocateBO result = null;

        // 判断节点
        if (MapUtils.isNotEmpty(originResult.getNodeId2StepMap())) {
            Map<String, Integer> nodeId2IntentLevelIdMap = resource.getIntentLevelRuleResource().getNodeId2IntentLevelCodeMap();
            for (String nodeId : originResult.getNodeId2StepMap().keySet()) {
                Integer level = nodeId2IntentLevelIdMap.get(nodeId);
                if (Objects.nonNull(level)) {
                    if (level < intentLevelDetailCode) {
                        intentLevelDetailCode = level;
                        result = generateByNodeId(resource, nodeId);
                    }
                }
            }
        }

        // 处理特殊语境
        if (MapUtils.isNotEmpty(originResult.getSpecialAnswerCountMap())) {
            Map<String, Integer> specialAnswerId2IntentLevelIdMap = resource.getIntentLevelRuleResource().getSpecialAnswerId2IntentLevelCodeMap();
            for (String specialAnswerId : originResult.getSpecialAnswerCountMap().keySet()) {
                Integer level = specialAnswerId2IntentLevelIdMap.get(specialAnswerId);
                if (Objects.nonNull(level)) {
                    if (level < intentLevelDetailCode) {
                        intentLevelDetailCode = level;
                        result = generateBySpecialAnswerId(resource, specialAnswerId);
                    }
                }
            }
        }

        if (MapUtils.isNotEmpty(originResult.getKnowledgeCountMap())) {
            Map<String, Integer> knowledgeId2IntentLevelIdMap = resource.getIntentLevelRuleResource().getKnowledgeId2IntentLevelCodeMap();
            for (String knowledgeId : originResult.getKnowledgeCountMap().keySet()) {
                Integer level = knowledgeId2IntentLevelIdMap.get(knowledgeId);
                if (Objects.nonNull(level)) {
                    if (level < intentLevelDetailCode) {
                        intentLevelDetailCode = level;
                        result = generateByKnowledgeId(resource, knowledgeId);
                    }
                }
            }
        }

        if (Objects.isNull(result)) {
            return Optional.empty();
        }
        return Optional.of(Tuple.of(result, intentLevelDetailCode));
    }

    private AnswerLocateBO generateByNodeId(RobotRuntimeResource resource, String nodeId) {
        AnswerLocateBO result = new AnswerLocateBO();
        result.setAnswerSource(AnswerSourceEnum.STEP);
        result.setNodeId(nodeId);

        DialogBaseNodePO node = resource.getNodeIdMap().get(nodeId).origin;
        result.setNodeName(node.getName());
        result.setNodeLabel(node.getLabel());
        result.setStepId(node.getStepId());

        StepRuntime step = resource.getStepIdMap().get(node.getStepId());
        result.setStepName(step.getName());
        result.setStepLabel(step.getLabel());
        return result;
    }

    private AnswerLocateBO generateByKnowledgeId(RobotRuntimeResource resource, String knowledgeId) {
        AnswerLocateBO result = new AnswerLocateBO();
        result.setAnswerSource(AnswerSourceEnum.KNOWLEDGE);
        result.setKnowledgeId(knowledgeId);
        KnowledgeRuntime knowledge = resource.getKnowledgeIdMap().get(knowledgeId);
        result.setKnowledgeName(knowledge.getName());
        result.setKnowledgeLabel(knowledge.getLabel());
        return result;
    }

    private AnswerLocateBO generateBySpecialAnswerId(RobotRuntimeResource resource, String specialAnswerId) {
        AnswerLocateBO result = new AnswerLocateBO();
        result.setAnswerSource(AnswerSourceEnum.SPECIAL_ANSWER);
        result.setSpecialAnswerConfigId(specialAnswerId);
        SpecialAnswerConfigPO specialAnswer = resource.getSpecialAnswerIdMap().get(specialAnswerId);
        result.setSpecialAnswerConfigName(specialAnswer.getName());
        result.setSpecialAnswerConfigLabel(specialAnswer.getLabel());
        return result;
    }

    @Data
    static class OriginResult {
        /**
         * 主流程完成进度
         */
        int mainStepCompleteProgress;

        /**
         * 触发业务问题/业务流程总次数
         */
        int businessCount;

        /**
         * 连续命中业务次数
         */
        int continuousBusinessCount;
        /**
         * 触发拒绝次数
         */
        int declineCount;
        /**
         * 触发肯定次数
         */
        int definiteCount;

        /**
         * ai对话轮次
         */
        int aiRounds;

        /**
         * 有效对话轮次
         */
        int effectiveChatRounds;

        /**
         * 通话时长
         */
        Long chatDuration;

        /**
         * 实际通话时长
         */
        Long actualChatDuration;

        /**
         * 是否完成所有流程
         */
        boolean completeAllStep;
        /**
         * 是否最后拒绝
         */
        boolean lastDecline;
        /**
         * 是否最后肯定
         */
        boolean lastDefinitive;

        /**
         * 用户输入内容
         */
        List<String> userSayContentList;
        /**
         * 用户输入次数
         */
        int userSayCount;
        /**
         * 用户输入字数
         */
        int userSayWordCount;
        /**
         * 连续拒绝次数
         */
        int continuousDeclineCount;
        /**
         * 连续肯定次数
         */
        int continuousDefiniteCount;

        /**
         * 是否快速挂断
         */
        boolean fastHangup;
        /**
         * 是否用户挂断
         */
        boolean hangupByUser;
        /**
         * 用户最后说话内容
         */
        String lastUserSayContent;
        /**
         * Ai最后说话内容
         */
        String lastAiSayContent;
        /**
         * 客户挂断时的问答知识id
         */
        String userHangupKnowledgeId;
        /**
         * 客户挂断时的节点id
         */
        String userHangupNodeId;
        /**
         * 命中ai无法应答 次数
         */
        int aiUnknownCount;
        /**
         * 命中ai无法应答百分比
         */
        int aiUnknownPercent;
        /**
         * 命中过的节点id, key: 节点id, value: 流程id
         */
        Map<String, String> nodeId2StepMap = new HashMap<>();
        /**
         * 触发独立流程次数
         */
        int independentStepCount;
        /**
         * 问答知识命中次数, key: 问答知识id, value: 命中次数
         */
        Map<String, Integer> knowledgeCountMap = new HashMap<>();
        /**
         * 特殊语境命中次数, key: 特殊语境id, value: 命中次数
         */
        Map<String, Integer> specialAnswerCountMap = new HashMap<>();
        /**
         * 记录节点录音播放最大进度，key:节点id；value:录音播放最大进度
         */
        Map<String, Double> nodeAnswerMaxProgressMap = new HashMap<>();

        /**
         * 记录知识录音播放最大进度，key:知识id；value:录音播放最大进度
         */
        Map<String, Double> knowledgeAnswerMaxProgressMap = new HashMap<>();

        //算法判断意向等级结果（主动加微）
        private Tuple2<Integer, String> privateDomainIntentLevelResult;

        //算法判断意向等级结果（被动加微）
        private Tuple2<Integer, String> passivePrivateDomainIntentLevelResult;

        //算法判断意向等级结果（活动通知）
        private Tuple2<Integer, String> activityIntentLevelResult;

        //算法判断意向等级结果（教育活动通知）
        private Tuple2<Integer, String> educationActivityIntentLevelResult;

        /**
         * 客户关注点
         */
        private Set<String> customerFocus;

        /**
         * 意向标签
         */
        private Integer intentLevel;

        /**
         * 流程命中map,key-流程id，value-命中次数
         */
        private Map<String, Integer> stepCountMap = new HashMap<>();

        private Map<String, List<String>> entityCollectMap = new HashMap<>();

        /**
         * 通话中, 用户输入命中的意图 id 集合
         */
        private Set<String> intentIdSet = new HashSet<>();

        private Map<String, String> variableIdValueMap = new HashMap<>();

        /**
         * 通话中, 用户输入命中的意图 id 顺序集合
         */
        private List<String> hitIntentOrderList = new ArrayList<>();

        /**
         * 命中的大模型分类id
         */
        private String hitLlmLabelId;

        /**
         * 命中的内置标签列表
         */
        private List<String> hitBuiltInTagList = new ArrayList<>();

        /**
         * 命中的自定义标签列表
         */
        private List<String> hitCustomTagList = new ArrayList<>();
        /**
         * 连续命中的问答知识id列表
         */
        private List<String> continuousKnowledgeIdList = new ArrayList<>();

        /**
         * 连续命中的特殊语境id列表
         */
        private List<String> continuousSpecialAnswerConfigIdList = new ArrayList<>();

        /**
         * 用户最后说话
         */
        private Boolean userLastSay;
    }

}
