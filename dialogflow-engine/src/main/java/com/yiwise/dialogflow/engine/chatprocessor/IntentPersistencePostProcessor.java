package com.yiwise.dialogflow.engine.chatprocessor;

import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.entity.po.SessionDetailIntentInfoPO;
import com.yiwise.dialogflow.service.SessionDetailIntentInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/12/13
 */
@Slf4j
public class IntentPersistencePostProcessor extends AbstractPostProcessor {

    protected static final SessionDetailIntentInfoService sessionDetailIntentInfoService = AppContextUtils.getBean(SessionDetailIntentInfoService.class);

    protected RobotRuntimeResource resource;

    public IntentPersistencePostProcessor(RobotRuntimeResource resource) {
        this.resource = resource;
    }

    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return "意图预测结果持久化后处理";
    }

    @Override
    public Optional<ChatResponse> doProcess(SessionContext sessionContext, EventContext eventContext, ChatResponse chatResponse) {
        if (ChatEventTypeEnum.USER_SAY_FINISH.equals(eventContext.getEvent()) && StringUtils.isNotEmpty(eventContext.getUserInput())) {
            SessionDetailIntentInfoPO infoPO = new SessionDetailIntentInfoPO();
            infoPO.setBotId(resource.getBotId());
            infoPO.setSessionId(sessionContext.getSessionId());
            infoPO.setSeq(eventContext.getSeq());
            infoPO.setUserInput(eventContext.getUserInput());
            // TODO 断句补齐的问题，暂时截断防止报错
            if (StringUtils.isNotEmpty(infoPO.getUserInput()) && infoPO.getUserInput().length() > 150) {
                log.info("SessionDetailIntentInfoPO截断用户输入");
                infoPO.setUserInput(StringUtils.substring(infoPO.getUserInput(), 0, 150));
            }
            infoPO.setIntentId(eventContext.getMatchIntentId());
            infoPO.setIntentName(resource.getIntentId2NameMap().getOrDefault(infoPO.getIntentId(), null));
            sessionDetailIntentInfoService.asyncSave(infoPO);
        }
        return Optional.of(chatResponse);
    }
}
