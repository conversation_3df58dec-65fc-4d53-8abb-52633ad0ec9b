package com.yiwise.dialogflow.engine.context;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yiwise.dialogflow.engine.resource.NodeAssignConfigRuntime;
import com.yiwise.dialogflow.engine.resource.NodeCollectConfigRuntime;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StepFlowContext {
    private boolean isLLMStep;

    private String stepId;

    private String stepName;

    private String currentNodeId;

    /**
     * 当前节点动态变量赋值配置(仅实体赋值和原话赋值才会有值, 常量赋值不需多轮直接本地就处理了)
     */
    private NodeAssignConfigRuntime assignConfig;

    private boolean isWaitUserSayFinish;

    private String waitUserSayFinishNodeId;

    /**
     * 节点实体采集配置, 信息采集节点会设置此值
     */
    private List<NodeCollectConfigRuntime> collectConfigList;

    /**
     * 采集节点问题下标, 当命中采集节点跳过条件时, 会跳过当前问题,
     *
     */
    private Map<String, CollectNodeContext> collectNodeContextMap = new HashMap<>();

    /**
     * 是否需要强制拉回
     */
    private boolean enablePullback;

    private Map<String, AtomicInteger> keyCaptureNodeRetryTimesMap;


    /**
     * 大模型流程上一轮回复的答案 id
     */
    private String preLLMAnswerId;

    /**
     * 大模型流程上一轮回复的引导话术 id
     */
    private String preLLMGuideAnswerId;

    /**
     * 大模型流程上一轮回复的无应答答案 id
     */
    private String preLLMUnknownAnswerId;


    /**
     * 是否启用了大模型生成答案
     */
    private Boolean isRequestGenerate;

    /**
     * 是否开始生成
     */
    private Boolean isBeginGenerate;

    /**
     * 是否是重复上一句, 如果是重复上一句, 则不需要请求之前的接口, 直接按顺序返回算法生成的结果
     */
    private Boolean isRepeatMode;

    /**
     * 是否打断大模型对话
     */
    private Boolean interruptLLMAnswer;

    /**
     * 是否生成完成
     */
    private Boolean isGenerateComplete;

    /**
     * 算法生成的答案内容
     */
    private List<String> lastGenerateAnswerContent;

    /**
     * 大模型任务是否完成
     */
    private Boolean isLlmTaskFinished;

    /**
     * 大模型对话接口返回的 trackInfo 字段, 需要更新到这里, 下一次请求的时候, 再通过 request.trackInfo 带过去
     */
    private String trackInfo;

}
