package com.yiwise.dialogflow.engine.chatmanager.specialanswer;

import com.yiwise.dialogflow.engine.chatmanager.ChatManagerPriorityEnum;
import com.yiwise.dialogflow.engine.chatmanager.ChatManagerTriggerCondition;
import com.yiwise.dialogflow.engine.context.*;
import com.yiwise.dialogflow.engine.enums.SpecialChatModeEnum;
import com.yiwise.dialogflow.engine.helper.ActionHelper;
import com.yiwise.dialogflow.engine.helper.CustomFastHangupHelper;
import com.yiwise.dialogflow.engine.helper.SessionContextHelper;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.action.HangupAction;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.utils.DebugLogUtils;
import com.yiwise.dialogflow.entity.enums.IntentRefTypeEnum;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

/**
 * 语音助手对话管理器
 * <AUTHOR>
 */
@Slf4j
public class AssistantChatManager extends AbstractCommonSpecialAnswerChatManager {

    public AssistantChatManager(RobotRuntimeResource resource) {
        super(resource, resource.getSpecialAnswerNameMap().get(SpecialAnswerConfigPO.ASSISTANT));
    }

    @Override
    public void initContext(SessionContext sessionContext) {
        sessionContext.setAssistantContext(new AssistantContext());
    }

    @Override
    protected CommonSpecialAnswerContext getSpecialAnswerContext(SessionContext sessionContext) {
        return sessionContext.getAssistantContext();
    }

    @Override
    public String getName() {
        return "语音助手";
    }

    /**
     * 语音助手答案播放后, 立即挂断
     */
    @Override
    protected HangupAction generateHangupAction() {
        return ActionHelper.generateHangupAction();
    }

    @Override
    public List<ChatManagerTriggerCondition> getTriggerConditions(SessionContext sessionContext, EventContext context) {
        if (!enable) {
            return Collections.emptyList();
        }
        List<ChatManagerTriggerCondition> triggerConditions = new ArrayList<>();
        triggerConditions.add(triggerByFastHangupEvent(sessionContext, context));
        triggerByIntent(sessionContext, context).ifPresent(triggerConditions::add);
        triggerByFastHangupKeyword(sessionContext, context).ifPresent(triggerConditions::add);
        return triggerConditions;
    }

    private ChatManagerTriggerCondition triggerByFastHangupEvent(SessionContext sessionContext, EventContext context) {
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.FAST_HANGUP);
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.FAST_HANGUP));
        return condition;
    }

    private Optional<ChatManagerTriggerCondition> triggerByIntent(SessionContext sessionContext, EventContext context) {
        if (CollectionUtils.isEmpty(specialAnswerConfig.getTriggerIntentIdList())) {
            return Optional.empty();
        }
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.INTENT_TRIGGER_SPECIAL_ANSWER);
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
        condition.setIntentIdSet(new HashSet<>(specialAnswerConfig.getTriggerIntentIdList()));
        condition.setIntentRefType(IntentRefTypeEnum.SPECIAL_ANSWER);
        condition.getMustNotMatchModes().add(SpecialChatModeEnum.DELAY_HANGUP);
        if (context.getSpecialChatModes().contains(SpecialChatModeEnum.UNINTERRUPTED)
                && context.getCanInterruptSpecialAnswerIdSet().contains(specialAnswerConfig.getId())) {
            // 当前处于不可打断, 判断当前节点是否允许响应该问答知识
            log.info("当前处于不可打断, 但当前节点允许响应特殊语境:{}", specialAnswerConfig.getName());
        } else {
            condition.getMustNotMatchModes().add(SpecialChatModeEnum.UNINTERRUPTED);
        }
        condition.getMustNotMatchModes().add(SpecialChatModeEnum.KEY_CAPTURE);
        condition.setDynamicPredicate((sc, ec)
                -> !SessionContextHelper.getCurrentStepExcludeSpecialAnswerConfigNameList(sc, resource).contains(SpecialAnswerConfigPO.ASSISTANT));
        return Optional.of(condition);
    }

    private Optional<ChatManagerTriggerCondition> triggerByFastHangupKeyword(SessionContext sessionContext, EventContext context) {
        ChatManagerTriggerCondition condition = new ChatManagerTriggerCondition(ChatManagerPriorityEnum.FAST_HANGUP);
        condition.setChatEventSet(Collections.singleton(ChatEventTypeEnum.USER_SAY_FINISH));
        condition.setDynamicPredicate((sc, ec) -> {
            String userInput = ec.getUserInput();
            Optional<String> matchKeyword = CustomFastHangupHelper.needFastHangUp(userInput);
            matchKeyword.ifPresent(keyword -> {
                condition.setOnSelected((sc1, ec1) -> {
                    DebugLogUtils.matchAssistant(String.format("命中语音助手关键词判断逻辑，关键词: %s", keyword), context);
                });
            });
            return matchKeyword.isPresent();
        });
        if (context.getCanInterruptSpecialAnswerIdSet().contains(specialAnswerConfig.getId())) {
            // 当前处于不可打断, 判断当前节点是否允许响应该问答知识
            log.info("当前处于不可打断, 但当前节点允许响应特殊语境:{}", specialAnswerConfig.getName());
        } else {
            condition.getMustNotMatchModes().add(SpecialChatModeEnum.UNINTERRUPTED);
        }
        return Optional.of(condition);
    }

}
