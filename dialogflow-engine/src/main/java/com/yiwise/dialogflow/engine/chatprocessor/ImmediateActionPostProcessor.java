package com.yiwise.dialogflow.engine.chatprocessor;

import com.yiwise.dialogflow.engine.context.ActiveTypeEnum;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.helper.SessionContextHelper;
import com.yiwise.dialogflow.engine.resource.KnowledgeRuntime;
import com.yiwise.dialogflow.engine.resource.NodeRuntime;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.resource.SpecialAnswerRuntime;
import com.yiwise.dialogflow.engine.share.action.ChatAction;
import com.yiwise.dialogflow.engine.share.action.HumanInterventionAction;
import com.yiwise.dialogflow.engine.share.action.SendSmsAction;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.entity.enums.ActionCategoryEnum;
import com.yiwise.dialogflow.entity.po.intent.RuleActionParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 收集对话过程中, 需要立即执行的动作
 * 例如节点, 问答知识等动作配置了立即执行时, 则在此进行收集, 通过 response 返回给客户端
 * 在这里处理而不是在节点处理, 是因为空的跳转节点设置了立即执行, 然后跳转到其他流程, 需要处理这种跳转信息, 所以这里统一处理掉
 * 还有就是不需要在节点, 流程中进行处理
 */
public class ImmediateActionPostProcessor extends AbstractPostProcessor {

    final RobotRuntimeResource resource;

    public ImmediateActionPostProcessor(RobotRuntimeResource resource) {
        this.resource = resource;
    }

    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return "立即执行动作收集";
    }

    @Override
    protected Optional<ChatResponse> doProcess(SessionContext sessionContext, EventContext eventContext, ChatResponse chatResponse) {
        Set<String> nodeIdSet = new HashSet<>();

        // 判断当前节点
        if (ActiveTypeEnum.STEP.equals(sessionContext.getActiveManagerInfo().getActiveType())) {
            SessionContextHelper.getCurrentActiveStepFlowContext(sessionContext).ifPresent(stepContext -> {
                if (StringUtils.isNotBlank(stepContext.getCurrentNodeId())) {
                    nodeIdSet.add(stepContext.getCurrentNodeId());
                }
            });
        }

        // 是否经过多次跳转
        if (CollectionUtils.isNotEmpty(eventContext.getPrevNodeInfoList())) {
            eventContext.getPrevNodeInfoList().forEach(nodeJumpInfo -> {
                if (Objects.nonNull(nodeJumpInfo.getToNodeId())) {
                    nodeIdSet.add(nodeJumpInfo.getToNodeId());
                }
            });
        }
        List<ChatAction> immediateActionList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(nodeIdSet)) {

            for (String nodeId : nodeIdSet) {
                NodeRuntime<?> nodeRuntime = resource.getNodeIdMap().get(nodeId);
                // todo 立即执行的动作, 在加载 resource 时处理
                if (Objects.nonNull(nodeRuntime)
                        && BooleanUtils.isTrue(nodeRuntime.origin.getIsEnableAction())
                        && CollectionUtils.isNotEmpty(nodeRuntime.origin.getActionList())) {
                    for (RuleActionParam action : nodeRuntime.origin.getActionList()) {
                        if (BooleanUtils.isTrue(action.getImmediateExecute())
                                && ActionCategoryEnum.SEND_SMS.equals(action.getActionType())
                                && CollectionUtils.isNotEmpty(action.getSourceIdList())) {
                            List<Long> smsTemplateIdList = new ArrayList<>();
                            action.getSourceIdList().forEach(idNamePair -> smsTemplateIdList.add(idNamePair.getId()));
                            immediateActionList.add(new SendSmsAction(smsTemplateIdList));
                        }
                    }
                }
                if (Objects.nonNull(nodeRuntime) && BooleanUtils.isTrue(nodeRuntime.origin.getEnableHumanIntervention())) {
                    immediateActionList.add(new HumanInterventionAction());
                }
            }
        }

        // 处理知识中的立即发短信
        if (ActiveTypeEnum.KNOWLEDGE.equals(sessionContext.getActiveManagerInfo().getActiveType())) {
            String knowledgeId = sessionContext.getActiveManagerInfo().getOriginId();
            KnowledgeRuntime knowledge = resource.getKnowledgeIdMap().get(knowledgeId);
            if (Objects.nonNull(knowledge)
                    && BooleanUtils.isTrue(knowledge.getIsEnableAction())
                    && CollectionUtils.isNotEmpty(knowledge.getActionList())) {
                for (RuleActionParam action : knowledge.getActionList()) {
                    if (BooleanUtils.isTrue(action.getImmediateExecute())
                            && ActionCategoryEnum.SEND_SMS.equals(action.getActionType())
                            && CollectionUtils.isNotEmpty(action.getSourceIdList())) {
                        List<Long> smsTemplateIdList = new ArrayList<>();
                        action.getSourceIdList().forEach(idNamePair -> smsTemplateIdList.add(idNamePair.getId()));
                        immediateActionList.add(new SendSmsAction(smsTemplateIdList));
                    }
                }
            }
            if (Objects.nonNull(knowledge) && BooleanUtils.isTrue(knowledge.getEnableHumanIntervention())) {
                immediateActionList.add(new HumanInterventionAction());
            }
        }

        // 处理特殊语境中的立即发短信
        if (ActiveTypeEnum. SPECIAL_ANSWER.equals(sessionContext.getActiveManagerInfo().getActiveType())) {
            String specialAnswerConfigId = sessionContext.getActiveManagerInfo().getOriginId();
            SpecialAnswerRuntime specialAnswerRuntime = resource.getSpecialAnswerIdMap().get(specialAnswerConfigId);
            if (Objects.nonNull(specialAnswerRuntime)
                    && BooleanUtils.isTrue(specialAnswerRuntime.getIsEnableAction())
                    && CollectionUtils.isNotEmpty(specialAnswerRuntime.getActionList())) {
                for (RuleActionParam action : specialAnswerRuntime.getActionList()) {
                    if (BooleanUtils.isTrue(action.getImmediateExecute())
                            && ActionCategoryEnum.SEND_SMS.equals(action.getActionType())
                            && CollectionUtils.isNotEmpty(action.getSourceIdList())) {
                        List<Long> smsTemplateIdList = new ArrayList<>();
                        action.getSourceIdList().forEach(idNamePair -> smsTemplateIdList.add(idNamePair.getId()));
                        immediateActionList.add(new SendSmsAction(smsTemplateIdList));
                    }
                }
            }
            if (Objects.nonNull(specialAnswerRuntime) && BooleanUtils.isTrue(specialAnswerRuntime.getEnableHumanIntervention())) {
                immediateActionList.add(new HumanInterventionAction());
            }
        }

        if (CollectionUtils.isNotEmpty(immediateActionList)) {
            List<ChatAction> actionList = new ArrayList<>();
            actionList.addAll(chatResponse.getActionList());
            actionList.addAll(immediateActionList);
            chatResponse.setActionList(actionList);
        }

        return Optional.ofNullable(chatResponse);
    }
}
