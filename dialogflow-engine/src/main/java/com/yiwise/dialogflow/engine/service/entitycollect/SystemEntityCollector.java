package com.yiwise.dialogflow.engine.service.entitycollect;

import com.yiwise.dialogflow.engine.domain.EntityCollectContext;
import com.yiwise.dialogflow.entity.bo.EntityValueBO;
import com.yiwise.dialogflow.entity.enums.SystemEntityCategoryEnum;
import reactor.core.publisher.Flux;

import java.util.Collection;
import java.util.Map;

public interface SystemEntityCollector {

    void register(Map<SystemEntityCategoryEnum, SystemEntityCollector> registry);

    /**
     * 异步收集系统实体接口, 一个系统实体由一个类实现
     */
    Flux<EntityValueBO> asyncCollect(SystemEntityCategoryEnum category, String userInput, String userInputPinyin);

    /**
     * 用于请求算法的接口, 避免重复请求, 比如系统时间的提取, 不管是什么类型的时间, 只需要请求一次接口就可以了
     */
    default Flux<EntityValueBO> asyncCollect(Collection<SystemEntityCategoryEnum> categoryList, String userInput, String userInputPinyin) {
        return Flux.fromIterable(categoryList)
                .flatMap(category -> asyncCollect(category, userInput, userInputPinyin));
    }

    /**
     * 用于请求算法的接口, 避免重复请求, 比如系统时间的提取, 不管是什么类型的时间, 只需要请求一次接口就可以了
     */
    default Flux<EntityValueBO> asyncCollect(Collection<SystemEntityCategoryEnum> categoryList, String userInput, String userInputPinyin, EntityCollectContext entityCollectContext) {
        return asyncCollect(categoryList, userInput, userInputPinyin);
    }
}
