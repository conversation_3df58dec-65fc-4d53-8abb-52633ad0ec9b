package com.yiwise.dialogflow.engine.resource;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.OssKeyCenter;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.engine.share.common.PauseSeparatorElement;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import com.yiwise.dialogflow.entity.po.BaseAnswerContent;
import com.yiwise.dialogflow.utils.AnswerTextUtils;
import lombok.Getter;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
public class BaseAnswerRuntime<T extends BaseAnswerContent> {
    final String uniqueId;
    final String label;
    final String text;
    final ImmutableList<TextAudioPlaceholderElement> answerAudioElements;
    final AnswerLocateBO locate;
    public final T origin;

    public BaseAnswerRuntime(T baseAnswerContent, RobotRuntimeResource resource, AnswerLocateBO locate) {
        this.origin = baseAnswerContent;
        this.uniqueId = baseAnswerContent.getUniqueId();
        this.label = baseAnswerContent.getLabel();
        this.text = baseAnswerContent.getText();
        this.locate = locate;

        if (Objects.isNull(resource.getAudioType()) || Objects.isNull(resource.getAnswerText2AudioUrlMap())) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "执行顺序错误, 请先执行AudioType的初始化");
        }
        boolean mergeWholeVariableSentence = AudioTypeEnum.MIXTURE.equals(resource.getAudioType());;
        ImmutableMap<String, String> text2AudioMap = resource.getAnswerText2AudioUrlMap();

        List<TextAudioPlaceholderElement> answerPlaceholderElementList = new AnswerPlaceholderSplitter(text, mergeWholeVariableSentence)
                .getTextPlaceholderList().stream().map(placeholder -> {
                    String url = null;
                    if (TextPlaceholderTypeEnum.TEXT.equals(placeholder.getType())) {
                        url = text2AudioMap.get(AnswerTextUtils.removeAnswerPrefixAndSuffixSymbols(placeholder.getValue()));
                    } else if (TextPlaceholderTypeEnum.SEPARATOR.equals(placeholder.getType()) && placeholder instanceof PauseSeparatorElement) {
                        url = OssKeyCenter.getSilenceAudioOssKey(((PauseSeparatorElement) placeholder).getPauseMs());
                    }
                    TextAudioPlaceholderElement element = new TextAudioPlaceholderElement();
                    element.setType(placeholder.getType());
                    element.setValue(placeholder.getValue());
                    element.setUrl(url);
                    return element;
                }).collect(Collectors.toList());
        answerAudioElements = ImmutableList.copyOf(answerPlaceholderElementList);
    }
}
