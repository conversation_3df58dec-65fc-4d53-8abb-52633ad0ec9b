package com.yiwise.dialogflow.engine.service.impl;

import com.yiwise.dialogflow.engine.helper.IntentPredictHelper;
import com.yiwise.dialogflow.engine.helper.IntentPredictRequiredResourceLoader;
import com.yiwise.dialogflow.engine.resource.IntentPredictRequiredResource;
import com.yiwise.dialogflow.engine.service.IntentPredictService;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.entity.bo.algorithm.PredictResult;
import com.yiwise.dialogflow.entity.vo.SimplePredictResultVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/9/4
 */
@Service
public class IntentPredictServiceImpl implements IntentPredictService {

    @Override
    public Mono<List<SimplePredictResultVO>> predictWithoutAlgorithm(Long botId, String userInput) {
        // 加载意图预测必要资源
        IntentPredictRequiredResource intentPredictRequiredResource = IntentPredictRequiredResourceLoader.loadFromDb(botId, RobotSnapshotUsageTargetEnum.TEXT_TEST);

        // 不进行算法预测
        intentPredictRequiredResource.getIntentConfig().setEnableAlgorithm(false);
        // 不进行组合意图预测
        intentPredictRequiredResource.setEnableCompositeIntent(false);

        return IntentPredictHelper.predictAsync(userInput, intentPredictRequiredResource, "")
                .map(result -> {
                    List<PredictResult> candidatePredictResultList = result.getCandidatePredictResultList();
                    Map<String, SimplePredictResultVO> resultMap = new HashMap<>(candidatePredictResultList.size());
                    
                    // 将同一意图的多个预测结果进行聚合
                    for (PredictResult pr : candidatePredictResultList) {
                        String intentId = pr.getIntentId();

                        SimplePredictResultVO vo = resultMap.computeIfAbsent(intentId, k -> {
                            SimplePredictResultVO simplePredictResultVO = new SimplePredictResultVO();
                            simplePredictResultVO.setIntentId(intentId);
                            simplePredictResultVO.setIntentName(pr.getIntentName());
                            simplePredictResultVO.setIntentProperties(pr.getSimpleIntentInfo().getIntentProperties());
                            simplePredictResultVO.setMatchKeywordList(new ArrayList<>());
                            return simplePredictResultVO;
                        });

                        if (StringUtils.isNotBlank(pr.getKeyword())) {
                            vo.getMatchKeywordList().add(pr.getKeyword());
                        }
                    }

                    return new ArrayList<>(resultMap.values());
                });
    }

    @Override
    public Mono<List<SimplePredictResultVO>> predict(Long botId, String userInput, Double lowerThreshold, Double upperThreshold) {
        // 加载意图预测必要资源
        IntentPredictRequiredResource intentPredictRequiredResource = IntentPredictRequiredResourceLoader.loadFromDb(botId, RobotSnapshotUsageTargetEnum.TEXT_TEST);

        // 不进行算法预测
        if (Objects.nonNull(lowerThreshold)) {
            intentPredictRequiredResource.getIntentConfig().setAlgorithmLowerThreshold(lowerThreshold);
        }
        if (Objects.nonNull(upperThreshold)) {
            intentPredictRequiredResource.getIntentConfig().setAlgorithmUpperThreshold(upperThreshold);
        }

        return IntentPredictHelper.predictAsync(userInput, intentPredictRequiredResource, "")
                .map(result -> {
                    List<PredictResult> candidatePredictResultList = result.getCandidatePredictResultList();
                    Map<String, SimplePredictResultVO> resultMap = new HashMap<>(candidatePredictResultList.size());

                    // 将同一意图的多个预测结果进行聚合
                    for (PredictResult pr : candidatePredictResultList) {
                        String intentId = pr.getIntentId();
                        String key = intentId + pr.getPredictType();
                        // 算法和正则分开聚合
                        SimplePredictResultVO vo = resultMap.computeIfAbsent(key, k -> {
                            SimplePredictResultVO simplePredictResultVO = new SimplePredictResultVO();
                            simplePredictResultVO.setIntentId(intentId);
                            simplePredictResultVO.setIntentName(pr.getIntentName());
                            simplePredictResultVO.setIntentProperties(pr.getSimpleIntentInfo().getIntentProperties());
                            simplePredictResultVO.setMatchKeywordList(new ArrayList<>());
                            simplePredictResultVO.setIntentType(pr.getSimpleIntentInfo().getIntentType());
                            simplePredictResultVO.setCorpusType(pr.getSimpleIntentInfo().getCorpusType());
                            return simplePredictResultVO;
                        });
                        if (StringUtils.isNotBlank(pr.getKeyword())) {
                            vo.getMatchKeywordList().add(pr.getKeyword());
                        }
                        if (Objects.nonNull(pr.getConfidence())) {
                            vo.setConfidence(pr.getConfidence());
                        }
                    }
                    return new ArrayList<>(resultMap.values());
                });
    }
}
