package com.yiwise.dialogflow.engine.utils;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.MdcLogIdLifterTransformer;
import com.yiwise.dialogflow.engine.analysis.*;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.helper.IntentPredictHelper;
import com.yiwise.dialogflow.engine.helper.SessionContextHelper;
import com.yiwise.dialogflow.engine.resource.*;
import com.yiwise.dialogflow.entity.bo.EntityValueBO;
import com.yiwise.dialogflow.entity.bo.algorithm.PredictResult;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.pattern.PatternEnhanceCache;
import com.yiwise.dialogflow.engine.service.EntityCollectService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class QuestionAnswerAlignUtils {

    private static final Integer PERSON = 1;
    private static final Integer ROBOT = 2;

    private static final WebClient webClient = AppContextUtils.getBean(WebClient.class);
    private static final EntityCollectService entityCollectService = AppContextUtils.getBean(EntityCollectService.class);

    public static Mono<Void> align(SessionContext sessionContext, Long callRecordId, OriginChatData callDataInfo, RobotRuntimeResource resource) {
        ImmutableMap<String, NodeRuntime<?>> nodeMap = resource.getNodeIdMap();
        if (MapUtils.isEmpty(nodeMap)) {
            return Mono.empty();
        }

        List<String> enableAutoAlignDynamicVarIdList = resource.getVariableIdMap().values().stream()
                .filter(v -> VariableTypeEnum.isDynamicVariable(v.getType()) && BooleanUtils.isTrue(v.getEnableAutoAlign()))
                .map(VariablePO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(enableAutoAlignDynamicVarIdList)) {
            log.info("不存在启用自动对齐的变量");
            return Mono.empty();
        }

        List<EventLog> logList = callDataInfo.getLogList().stream()
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(EventLog::getSequence))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(logList)) {
            return Mono.empty();
        }

        Req req = new Req();
        req.setCallRecordId(String.valueOf(callRecordId));
        List<ReqData> dataList = new ArrayList<>();
        Map<String, Set<String>> nodeOfInterestMap = Maps.newHashMap();
        req.setData(dataList);
        req.setNodeOfInterest(nodeOfInterestMap);
        Map<String, DialogBaseNodePO> assignConfigMap = Maps.newHashMap();

        for (EventLog eventLog : logList) {
            if (StringUtils.isNotBlank(eventLog.getUserInput())) {
                ReqData data = new ReqData();
                data.setContent(eventLog.getUserInput());
                data.setPerson(PERSON);
                if (CollectionUtils.isNotEmpty(eventLog.getDebugLog())) {
                    data.setLog(String.join(";", eventLog.getDebugLog()));
                }
                dataList.add(data);
            }

            if (StringUtils.isNotBlank(eventLog.getRealAnswerText())) {
                ReqData data = new ReqData();
                data.setContent(eventLog.getRealAnswerText());
                data.setPerson(ROBOT);
                data.setLog("");
                dataList.add(data);

                NonNullAnswerLocateBO answerLocate = eventLog.getAnswerLocate();
                if (Objects.nonNull(answerLocate) && Objects.nonNull(answerLocate.getNodeId()) && nodeMap.containsKey(answerLocate.getNodeId())) {
                    NodeRuntime<?> nodeRuntime = nodeMap.get(answerLocate.getNodeId());
                    if (NodeTypeEnum.CHAT.equals(nodeRuntime.getType()) && nodeRuntime instanceof ChatNodeRuntime) {
                        ChatNodeRuntime chatNodeRuntime = (ChatNodeRuntime) nodeRuntime;
                        DialogChatNodePO chatNode = chatNodeRuntime.getOrigin();
                        if (BooleanUtils.isTrue(chatNode.getEnableAssign())) {
                            Set<String> variableIdSet = new HashSet<>();
                            if (Objects.nonNull(chatNode.getEntityAssign())) {
                                variableIdSet.addAll(chatNode.getEntityAssign().getDependVariableIdList());
                            }
                            if (Objects.nonNull(chatNode.getOriginInputAssign())) {
                                variableIdSet.addAll(chatNode.getOriginInputAssign().getDependVariableIdList());
                            }

                            if (CollectionUtils.isNotEmpty(variableIdSet) && variableIdSet.stream().anyMatch(enableAutoAlignDynamicVarIdList::contains)) {
                                nodeOfInterestMap.computeIfAbsent(chatNode.getId(), k -> new HashSet<>()).add(eventLog.getRealAnswerText());
                                assignConfigMap.put(chatNode.getId(), chatNode);
                            }
                        }
                    }
                }
            }
        }

        if (MapUtils.isEmpty(nodeOfInterestMap)) {
            log.info("未触发需要问答对齐的对话节点");
            return Mono.empty();
        }

        String url = ApplicationConstant.QA_ALIGN_URL;
        String body = JsonUtils.object2String(req);
        log.info("请求问答对齐接口,url={},body={}", url, body);
        return webClient.method(HttpMethod.POST)
                .uri(url)
                .body(BodyInserters.fromValue(body))
                .retrieve()
                .bodyToMono(String.class)
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .flatMap(response -> {
                    log.info("请求问答对齐接口结果为:response={}", response);
                    Res res = JsonUtils.string2Object(response, Res.class);
                    return handleRes(res, assignConfigMap, enableAutoAlignDynamicVarIdList, sessionContext, resource);
                })
                .onErrorResume(e -> {
                    log.warn("[LogHub_Warn]请求问答对齐接口失败, url={}, body={}", url, body, e);
                    return Mono.empty();
                });
    }

    private static Mono<Void> handleRes(Res res, Map<String, DialogBaseNodePO> assignConfigMap, List<String> enableAutoAlignDynamicVarIdList,
                                        SessionContext sessionContext, RobotRuntimeResource resource) {
        if (Objects.isNull(res) || !res.isSuccess() || MapUtils.isEmpty(res.getResult())) {
            return Mono.empty();
        }

        return Flux.fromIterable(res.getResult().entrySet())
                .flatMap(entry -> {
                            String nodeId = entry.getKey();
                            String userInput = entry.getValue();
                            return acquireEntityIdSet(nodeId, userInput, assignConfigMap, enableAutoAlignDynamicVarIdList, resource)
                                    .filter(CollectionUtils::isNotEmpty)
                                    .map(entityIdSet -> entityIdSet.stream().map(resource.getEntityIdMap()::get).filter(Objects::nonNull).collect(Collectors.toList()))
                                    .filter(CollectionUtils::isNotEmpty)
                                    .flatMap(entityList -> entityCollectService.collectAsync(userInput, entityList))
                                    .doOnNext(entityValue -> {
                                        log.info("实体提取结果:[{}]", JsonUtils.object2String(entityValue));
                                        if (CollectionUtils.isEmpty(entityValue)) {
                                            return;
                                        }
                                        Map<String, List<EntityValueBO>> entityId2ValueListMap = MyCollectionUtils.listToMapList(entityValue, EntityValueBO::getEntityId);
                                        DialogBaseNodePO node = assignConfigMap.get(nodeId);

                                        if (node.enableEntityAssign()) {
                                            for (VarAssignActionItemPO item : node.getEntityAssign().getAssignActionList()) {
                                                String entityId = item.getEntityId();
                                                String variableId = item.getVariableId();
                                                if (!enableAutoAlignDynamicVarIdList.contains(variableId)) {
                                                    continue;
                                                }
                                                List<EntityValueBO> valueList = entityId2ValueListMap.get(entityId);
                                                if (CollectionUtils.isNotEmpty(valueList)) {
                                                    String strValue = valueList.stream().map(EntityValueBO::getValue).collect(Collectors.joining(","));
                                                    String curValue = sessionContext.getGlobalVariableValueMap().get(getVariableName(variableId, resource));
                                                    log.info("算法对齐结果={}, 原结果={}", strValue, curValue);
                                                    if (!StringUtils.equals(curValue, strValue)) {
                                                        SessionContextHelper.variableAssign(resource, sessionContext, new EventContext(), variableId, strValue, entityId);
                                                    }
                                                }
                                            }
                                        }

                                        if (node.enableOriginInputAssign()) {
                                            for (OriginInputAssignConfigItem item : node.getOriginInputAssign().getAssignActionList()) {
                                                String entityId = item.getEntityId();
                                                String variableId = item.getVariableId();
                                                if (!enableAutoAlignDynamicVarIdList.contains(variableId)) {
                                                    continue;
                                                }
                                                List<EntityValueBO> valueList = entityId2ValueListMap.get(entityId);
                                                if (CollectionUtils.isNotEmpty(valueList)) {
                                                    String strValue = valueList.stream().map(EntityValueBO::getValue).collect(Collectors.joining(","));
                                                    String curValue = sessionContext.getGlobalVariableValueMap().get(getVariableName(variableId, resource));
                                                    log.info("算法对齐结果={}, 原结果={}", strValue, curValue);
                                                    if (!StringUtils.equals(curValue, strValue)) {
                                                        SessionContextHelper.variableAssign(resource, sessionContext, new EventContext(), variableId, strValue, entityId);
                                                    }
                                                }
                                            }
                                        }
                                    });
                        }
                ).then();
    }

    private static String getVariableName(String variableId, RobotRuntimeResource resource) {
        return Optional.ofNullable(resource.getVariableIdMap().get(variableId))
                .map(VariablePO::getName).orElse(null);
    }

    private static Mono<Set<String>> acquireEntityIdSet(String nodeId, String userInput,
                                                        Map<String, DialogBaseNodePO> assignConfigMap,
                                                        List<String> enableAutoAlignDynamicVarIdList,
                                                        RobotRuntimeResource resource) {
        if (!assignConfigMap.containsKey(nodeId) || StringUtils.isBlank(userInput)) {
            return Mono.empty();
        }
        DialogBaseNodePO node = assignConfigMap.get(nodeId);
        if (node.enableOriginInputAssign()) {
            for (OriginInputAssignConfigItem assignConfigItem : node.getOriginInputAssign().getAssignActionList()) {
                if (OriginInputCollectTypeEnum.isFiltered(assignConfigItem.getOriginInputCollectType())) {
                    if (CollectionUtils.isNotEmpty(assignConfigItem.getFilteredRegexList())) {
                        if (assignConfigItem.getFilteredRegexList().stream().anyMatch(p ->
                                PatternEnhanceCache.getOrCreate(p, PatternCache.compile(p), false).find(userInput).isPresent())) {
                            return Mono.empty();
                        }
                    }
                    if (CollectionUtils.isNotEmpty(assignConfigItem.getFilteredIntentIdList())) {
                        return IntentPredictHelper.predictAsync(userInput, resource.getIntentPredictRequiredResource(), "")
                                .flatMap(result -> {
                                    if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getCandidatePredictResultList())) {
                                        return Mono.empty();
                                    }
                                    List<String> intentIdList = result.getCandidatePredictResultList().stream().map(PredictResult::getIntentId).distinct().collect(Collectors.toList());
                                    List<String> intersection = ListUtils.intersection(assignConfigItem.getFilteredIntentIdList(), intentIdList);
                                    if (CollectionUtils.isEmpty(intersection)) {
                                        return Mono.just(acquireEntityIdSet(assignConfigItem, enableAutoAlignDynamicVarIdList));
                                    }
                                    return Mono.empty();
                                });
                    }
                }
            }
        }

        return Mono.just(acquireEntityIdSet(node.getEntityAssign(), enableAutoAlignDynamicVarIdList));
    }

    private static Set<String> acquireEntityIdSet(EntityAssignConfigPO assignConfig, List<String> enableAutoAlignDynamicVarIdList) {
        if (Objects.nonNull(assignConfig) && CollectionUtils.isNotEmpty(assignConfig.getAssignActionList())) {
            return assignConfig.getAssignActionList().stream()
                    .filter(action -> enableAutoAlignDynamicVarIdList.contains(action.getVariableId()))
                    .map(VarAssignActionItemPO::getEntityId).collect(Collectors.toSet());
        }
        return Collections.emptySet();
    }

    private static Set<String> acquireEntityIdSet(OriginInputAssignConfigItem item, List<String> enableAutoAlignDynamicVarIdList) {
        if (enableAutoAlignDynamicVarIdList.contains(item.getVariableId())) {
            return Collections.singleton(item.getEntityId());
        }
        return Collections.emptySet();
    }

    @Data
    static class Req implements Serializable {

        String callRecordId;

        List<ReqData> data;

        Map<String, Set<String>> nodeOfInterest;
    }

    @Data
    static class ReqData implements Serializable {

        String content;

        Integer person;

        String log;
    }

    @Data
    static class Res implements Serializable {

        Integer code;

        Map<String, String> result;

        String info;

        public boolean isSuccess() {
            return code != null && code.equals(0);
        }
    }
}
