package com.yiwise.dialogflow.engine.service;

import javaslang.Tuple2;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.Optional;

/**
 * 调用算法接口对意向等级进行预测
 */
public interface IntentLevelPredictService {

    Optional<Tuple2<Integer, String>> predict(Map<String, Object> callDetailDTO, String predictServerUrl);

    Mono<Tuple2<Integer, String>> predictAsync(Map<String, Object> callDetailDTO, String predictServerUrl);
}
