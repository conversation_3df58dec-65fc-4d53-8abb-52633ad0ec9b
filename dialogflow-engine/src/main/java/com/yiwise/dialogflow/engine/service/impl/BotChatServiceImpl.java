package com.yiwise.dialogflow.engine.service.impl;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.ImmutableMap;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.api.enums.V3BotTypeEnum;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.ChatEngine;
import com.yiwise.dialogflow.engine.ChatEnginePool;
import com.yiwise.dialogflow.engine.analysis.EventLog;
import com.yiwise.dialogflow.engine.analysis.OriginChatData;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.helper.LLMRequestHelper;
import com.yiwise.dialogflow.engine.resource.*;
import com.yiwise.dialogflow.engine.service.*;
import com.yiwise.dialogflow.engine.share.*;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.engine.share.request.ChatRequest;
import com.yiwise.dialogflow.engine.share.request.HttpRequest;
import com.yiwise.dialogflow.engine.share.request.UserSayFinishEvent;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.share.response.SessionInfo;
import com.yiwise.dialogflow.engine.utils.EventLogSerializeUtils;
import com.yiwise.dialogflow.engine.utils.SessionContextSerializeUtils;
import com.yiwise.dialogflow.entity.bo.stats.BotStatsAnalysisResult;
import com.yiwise.dialogflow.entity.dto.llmchat.LLMChatRequest;
import com.yiwise.dialogflow.entity.enums.AssignInfoSaveModeEnum;
import com.yiwise.dialogflow.entity.enums.VariableTypeEnum;
import com.yiwise.dialogflow.entity.po.SessionRecordPO;
import com.yiwise.dialogflow.entity.po.VariablePO;
import com.yiwise.dialogflow.service.llm.LLMChatService;
import com.yiwise.dialogflow.service.magic.MagicActivityConfigService;
import com.yiwise.dialogflow.service.stats.BotStatsFacadeService;
import com.yiwise.dialogflow.utils.QueryNodeApiTestUtils;
import javaslang.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("BotChatService")
public class BotChatServiceImpl implements BotChatService {
    @Resource
    private IntentLevelAnalysisService intentLevelAnalysisService;

    @Resource
    private BotStatsAnalysisService botStatsAnalysisService;

    @Resource
    private BotStatsFacadeService botStatsFacadeService;

    @Resource
    private BotBusiMetricsAnalysisService botBusiMetricsAnalysisService;

    @Resource
    private MagicActivityConfigService magicActivityConfigService;

    @Resource
    private LLMChatService llmChatService;

    @Resource
    private YixinAnalysisService yixinAnalysisService;

    @Override
    public Mono<SessionInfo> createSessionAsync(Long botId, Integer version, RobotSnapshotUsageTargetEnum usageTarget, ChatMetaData chatData) {
        if (Objects.isNull(botId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "机器人id不能为空");
        }
        if (Objects.isNull(usageTarget)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "机器人版本号不能为空");
        }
        if (Objects.isNull(version)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "机器人快照版本不能为空");
        }

        if (chatData == null) {
            chatData = new ChatMetaData();
        }
        SessionRecordPO po = createSessionRecordPO(botId, version, chatData);
        String sessionId = po.getId();
        ChatMetaData finalChatMetaData = chatData;
        return ChatEnginePool.asyncBorrowEngine(botId, usageTarget, version)
                .flatMap(engine -> {
                    Map<String, String> emptyMap = Collections.emptyMap();
                    if (V3BotTypeEnum.isMagicTemplate(engine.getResource().getType())
                            && RobotSnapshotUsageTargetEnum.CALL_OUT.equals(usageTarget)) {
                        if (Objects.isNull(finalChatMetaData.getCallJobId())) {
                            return Mono.just(Tuple.of(engine, emptyMap));
                        }
                        return magicActivityConfigService.asyncGetVarValueFromCache(botId, finalChatMetaData.getCallJobId(), engine.getResource().getVariableIdMap())
                                .switchIfEmpty(Mono.defer(() -> {
                                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "未获取到轻量化活动变量配置");
                                }))
                                .map(varMap -> Tuple.of(engine, varMap));
                    } else {
                        return Mono.just(Tuple.of(engine, emptyMap));
                    }
                })
                .map((tuple -> {
                    ChatEngine engine = tuple._1;
                    Map<String, String> magicTemplateVarValueMap = tuple._2;
                    log.info("创建会话成功, botId={}, version={}, session={}, chatData={}", botId, version, sessionId, finalChatMetaData);
                    SessionInfo sessionInfo = new SessionInfo();
                    sessionInfo.setBotId(botId);
                    sessionInfo.setSessionId(sessionId);
                    sessionInfo.setVersion(version);
                    SessionContext sessionContext = engine.generateAndInitSessionContext(sessionInfo, finalChatMetaData, magicTemplateVarValueMap);
                    sessionInfo.setSessionContextJson(sessionContextToJson(sessionContext));
                    ChatEnginePool.returnEngine(engine);

                    asyncCreateLlmCache(engine.getResource(), sessionContext);

                    return sessionInfo;
                }))
                .subscriberContext(context -> context.put(ApplicationConstant.MDC_LOG_ID, generateLogId(finalChatMetaData, botId, sessionId)));
    }

    private void asyncCreateLlmCache(RobotRuntimeResource resource, SessionContext sessionContext) {
        ImmutableMap<String, StepRuntime> stepIdMap = resource.getStepIdMap();
        stepIdMap.forEach((stepId, stepRuntime) -> {
            if (stepRuntime.isLlmStep()) {
                LLMChatRequest request = LLMRequestHelper.initRequest(sessionContext, resource, (LLMStepRuntime) stepRuntime, new EventContext());
                llmChatService.asyncCache(request);
            }
        });
    }

    @Override
    public Mono<ChatResponse> processRequestAsync(ChatRequest request) {
        validRequest(request);
        String oldId = MDC.get(ApplicationConstant.MDC_LOG_ID);
        String logId = generateLogId(request, oldId);
        printSessionContext(request);
        MDC.put(ApplicationConstant.MDC_LOG_ID, logId);
        log.info("处理对话请求, sequence={}, param={}", request.getSequence(), JSON.toJSONString(request.getParam()));
        return ChatEnginePool.asyncBorrowEngine(request.getBotId(), request.getUsageTarget(), request.getVersion())
                .flatMap(engine -> engine.processAsync(request).doOnSuccess(r -> ChatEnginePool.returnEngine(engine)))
                .doOnSuccess(this::printResponse)
                .subscriberContext(context -> context.put(ApplicationConstant.MDC_LOG_ID, logId));
    }

    /**
     * 收集对话数据
     * 目前是收集客户属性字段
     */
    private void collectChatData(RobotRuntimeResource resource, SessionContext sessionContext, IntentLevelAnalysisResult result) {
        Map<String, String> dynamicVariableValueMap = new HashMap<>();
        Map<Long, List<String>> customerAttributeValueMap = new HashMap<>();
        for (VariablePO variable : resource.getVariableIdMap().values()) {
            if (VariableTypeEnum.isDynamicVariable(variable.getType())
                    && resource.getUsedVariableNameSet().contains(variable.getName())) {
                List<String> historyValueList = sessionContext.getVariableAssignHistoryMap()
                        .getOrDefault(variable.getName(), Collections.emptyList());
                // 相同实体值去重
                if (BooleanUtils.isTrue(variable.getEnableDeduplicate())) {
                    historyValueList = historyValueList.stream().distinct().collect(Collectors.toList());
                }
                String strValue;
                if (CollectionUtils.isEmpty(historyValueList) || AssignInfoSaveModeEnum.ALL.equals(variable.getSaveMode())) {
                    strValue = String.join(";", historyValueList);
                } else {
                    strValue = historyValueList.get(historyValueList.size() - 1);
                }
                if (StringUtils.isNotBlank(strValue) && strValue.length() > 1024) {
                    log.warn("变量值长度超过512, 对其进行截取, 变量名=[{}], 变量值=[{}]", variable.getName(), strValue);
                    strValue = strValue.substring(0, 1024);
                }
                dynamicVariableValueMap.put(variable.getName(), strValue);
                if (BooleanUtils.isTrue(variable.getEnableSave())
                        && Objects.nonNull(variable.getCustomerAttributeId())
                        && StringUtils.isNotBlank(strValue)) {
                    customerAttributeValueMap.put(variable.getCustomerAttributeId(), Collections.singletonList(strValue));
                }
            }
        }
        result.setDynamicVariableValueMap(dynamicVariableValueMap);
        result.setCustomerAttributeValueMap(customerAttributeValueMap);
        if (MapUtils.isNotEmpty(dynamicVariableValueMap) || MapUtils.isNotEmpty(customerAttributeValueMap)) {
            log.info("收集对话数据, dynamicVariableValueMap={}, customerAttributeValueMap={}", dynamicVariableValueMap, customerAttributeValueMap);
        }
    }

    private void printSessionContext(ChatRequest request) {
        if (Objects.isNull(request) || !ApplicationConstant.enableDebug) {
            return;
        }
        if (StringUtils.isBlank(request.getSessionContextJson())) {
            log.info("sessionContext 为空");
        } else {
            log.info("sessionContext: {}", request.getSessionContextJson());
        }
    }

    private void printResponse(ChatResponse response) {
        if (response == null) {
            return;
        }
        log.debug("ChatResponse=[{}]", response.toPrintString());
    }

    @Override
    public Mono<IntentLevelAnalysisResult> analysisInCallAsync(CallDataInfo callDataInfo) {
        SessionInfo sessionInfo = callDataInfo.getSessionInfo();
        SessionContext sessionContext = jsonToSessionContext(callDataInfo.getSessionContextJson());
        if (Objects.isNull(sessionContext)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "会话上下文不能为空");
        }
        String oldLogId = MDC.get(ApplicationConstant.MDC_LOG_ID);
        String logId = generateLogId(callDataInfo, oldLogId);
        MDC.put(ApplicationConstant.MDC_LOG_ID, logId);

        List<String> eventLogList = callDataInfo.getEventLogContentList();
        callDataInfo.setEventLogContentList(null);
        callDataInfo.setSessionContextJson(null);
        log.debug("分析对话数据, callDataInfo=[{}]", JsonUtils.object2String(callDataInfo));

        OriginChatData originChatData = recoveryOriginChatData(sessionContext, eventLogList);
        log.debug("eventLogList=[{}]", JsonUtils.object2String(originChatData.getLogList()));

        return RobotRuntimeResourceFactory.asyncGetRuntimeResource(sessionContext.getBotId(), sessionContext.getUsageTarget(), sessionContext.getVersion())
                .flatMap(resource -> intentLevelAnalysisService.analysisAsync(resource, sessionContext, sessionInfo, callDataInfo, originChatData))
                .subscriberContext(context -> context.put(ApplicationConstant.MDC_LOG_ID, logId));
    }

    @Override
    public Mono<IntentLevelAnalysisResult> analysisAsync(CallDataInfo callDataInfo) {
        SessionInfo sessionInfo = callDataInfo.getSessionInfo();
        SessionContext sessionContext = jsonToSessionContext(callDataInfo.getSessionContextJson());
        if (Objects.isNull(sessionContext)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "会话上下文不能为空");
        }
        String oldLogId = MDC.get(ApplicationConstant.MDC_LOG_ID);
        String logId = generateLogId(callDataInfo, oldLogId);
        MDC.put(ApplicationConstant.MDC_LOG_ID, logId);

        List<String> eventLogList = callDataInfo.getEventLogContentList();
        callDataInfo.setEventLogContentList(null);
        callDataInfo.setSessionContextJson(null);
        log.debug("分析对话数据, callDataInfo=[{}]", JsonUtils.object2String(callDataInfo));

        OriginChatData originChatData = recoveryOriginChatData(sessionContext, eventLogList);
        log.debug("eventLogList=[{}]", JsonUtils.object2String(originChatData.getLogList()));

        return RobotRuntimeResourceFactory.asyncGetRuntimeResource(sessionContext.getBotId(), sessionContext.getUsageTarget(), sessionContext.getVersion())
                .flatMap(resource -> intentLevelAnalysisService.analysisAsync(resource, sessionContext, sessionInfo, callDataInfo, originChatData)
                        .doOnNext(intentAnalysisResult -> {
                            // 收集对话数据
                            collectChatData(resource, sessionContext, intentAnalysisResult);

                            // 分析bot统计数据
                            try {
                                analysisStatsData(resource, sessionInfo, callDataInfo, originChatData, intentAnalysisResult);
                            } catch (Exception e) {
                                log.error("统计分析异常", e);
                            }
                            // 分析bot业务指标
                            try {
                                BotBusiMetrics botBusiMetrics = analysisBusiMetrics(resource, sessionInfo, callDataInfo, originChatData);
                                intentAnalysisResult.setBotBusiMetrics(botBusiMetrics);
                            } catch (Exception e) {
                                log.error("业务指标分析异常", e);
                            }
                            try {
                                intentAnalysisResult.setYixinMatchIntentNameList(yixinAnalysisService.analysis(resource, sessionContext, sessionInfo, callDataInfo, originChatData));
                            } catch (Exception e) {
                                log.error("宜信定制分析失败", e);
                            }
                            log.info("意向等级分析结果:{}", JSON.toJSONString(intentAnalysisResult));
                        })).subscriberContext(context -> context.put(ApplicationConstant.MDC_LOG_ID, logId));
    }

    private OriginChatData recoveryOriginChatData(SessionContext sessionContext, List<String> eventLogContentList) {
        OriginChatData originChatData = new OriginChatData();
        originChatData.setLogList(new ArrayList<>());
        if (CollectionUtils.isNotEmpty(eventLogContentList)) {
            eventLogContentList.stream()
                    .map(json -> {
                        try {
                            return EventLogSerializeUtils.deserialize(json);
                        } catch (Exception e) {
                            log.error("解析eventLog异常", e);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .filter(log -> sessionContext.getValidSequenceSet().contains(log.getSequence()))
                    .sorted(Comparator.comparing(EventLog::getSequence))
                    .forEach(originChatData.getLogList()::add);
        }
        return originChatData;
    }

    private String generateLogId(ChatRequest request, String oldId) {
        if (Objects.nonNull(request) && StringUtils.isNotBlank(request.getLogId())) {
            return oldId + "_" + request.getLogId();
        }
        return request.getSessionId() + "_" + request.getBotId() + "_" + MDC.get(ApplicationConstant.MDC_LOG_ID);
    }

    private String generateLogId(ChatMetaData chatMetaData, Long botId, String sessionId) {
        if (Objects.nonNull(chatMetaData) && StringUtils.isNotBlank(chatMetaData.getLogId())) {
            return chatMetaData.getLogId();
        }
        return sessionId + "_" + botId + "_" + MDC.get(ApplicationConstant.MDC_LOG_ID);
    }

    private String generateLogId(CallDataInfo callDataInfo, String oldLogId) {
        if (Objects.nonNull(callDataInfo) && StringUtils.isNotBlank(callDataInfo.getLogId())) {
            return oldLogId + "_" + callDataInfo.getLogId();
        }
        return callDataInfo.getSessionInfo().getSessionId() + "_" + callDataInfo.getSessionInfo().getBotId() + "_" + MDC.get(ApplicationConstant.MDC_LOG_ID);
    }

    private String sessionContextToJson(SessionContext sessionContext) {
        return SessionContextSerializeUtils.serialize(sessionContext);
    }

    private SessionContext jsonToSessionContext(String json) {
        return SessionContextSerializeUtils.deserialize(json);
    }

    private SessionRecordPO createSessionRecordPO(Long botId, Integer version, ChatMetaData chatData) {
        SessionRecordPO sessionRecordPO = new SessionRecordPO();
        sessionRecordPO.setBotId(botId);
        sessionRecordPO.setCallRecordId(chatData.getCallRecordId());
        sessionRecordPO.setBotVersion(version);
        sessionRecordPO.setCreateTime(LocalDateTime.now());
        sessionRecordPO.setUpdateTime(LocalDateTime.now());
        sessionRecordPO.setId(new ObjectId().toString());
        return sessionRecordPO;
    }

    private BotBusiMetrics analysisBusiMetrics(RobotRuntimeResource resource,
                                               SessionInfo sessionInfo,
                                               CallDataInfo callDataInfo,
                                               OriginChatData chatData) {
        return botBusiMetricsAnalysisService.analysis(resource, sessionInfo, callDataInfo, chatData);
    }

    private void analysisStatsData(RobotRuntimeResource resource,
                                   SessionInfo sessionInfo,
                                   CallDataInfo callDataInfo,
                                   OriginChatData originChatData,
                                   IntentLevelAnalysisResult intentLevelAnalysisResult) {
        BotStatsAnalysisResult analysisResult = botStatsAnalysisService.analysis(resource, sessionInfo, callDataInfo, originChatData, intentLevelAnalysisResult);
        botStatsFacadeService.saveAnalysisResult(analysisResult, callDataInfo);
    }

    private void validRequest(ChatRequest request) {
        if (Objects.isNull(request)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "request不能为空");
        }
        if (Objects.isNull(request.getBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空");
        }
        if (Objects.isNull(request.getVersion())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "version不能为空");
        }
        if (Objects.isNull(request.getUsageTarget())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "usageTarget不能为空");
        }
        if (StringUtils.isBlank(request.getSessionId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "sessionId不能为空");
        }
        if (Objects.isNull(request.getParam())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "request.param不能为空");
        }
        if (request.getParam() instanceof UserSayFinishEvent) {
            UserSayFinishEvent event = (UserSayFinishEvent) request.getParam();
            if (StringUtils.isBlank(event.getInputText())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "输入内容不能为空");
            }
        }
        if (StringUtils.isBlank(request.getSessionContextJson())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "sessionContextJson不能为空");
        }
    }

    @Override
    public Mono<Map<String, String>> httpRequestAsync(HttpRequest req) {
        String logId = req.getLogId();
        MDC.put(ApplicationConstant.MDC_LOG_ID, logId);

        // 这里只需要返回提取的结果就行了
        return QueryNodeApiTestUtils.test(req.getHttpRequestInfo())
                .map(QueryNodeApiTestResult::getVarIdValueMap)
                .subscriberContext(context -> context.put(ApplicationConstant.MDC_LOG_ID, logId));
    }

    @Override
    public Flux<ChatResponse> processRequestWithLLMAsync(ChatRequest request) {
        validRequest(request);
        String oldId = MDC.get(ApplicationConstant.MDC_LOG_ID);
        String logId = generateLogId(request, oldId);
        printSessionContext(request);
        MDC.put(ApplicationConstant.MDC_LOG_ID, logId);
        log.info("处理对话请求, sequence={}, param={}", request.getSequence(), JSON.toJSONString(request.getParam()));
        return ChatEnginePool.asyncBorrowEngine(request.getBotId(), request.getUsageTarget(), request.getVersion())
                .flatMapMany(engine -> engine.llmProcess(request).doOnComplete(() -> ChatEnginePool.returnEngine(engine)))
                .doOnNext(this::printResponse)
                .subscriberContext(context -> context.put(ApplicationConstant.MDC_LOG_ID, logId));
    }
}
