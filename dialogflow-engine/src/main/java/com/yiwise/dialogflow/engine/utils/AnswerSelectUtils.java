package com.yiwise.dialogflow.engine.utils;

import com.yiwise.dialogflow.engine.resource.BaseAnswerRuntime;
import com.yiwise.dialogflow.entity.po.BaseAnswerContent;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class AnswerSelectUtils {


    public static Optional<String> getNextAvailableId(String preAnswerId,
                                                      List<? extends BaseAnswerRuntime<?>> answerList,
                                                      Predicate<BaseAnswerContent> filterPredicate) {
        return getNextAvailableAnswer(preAnswerId, answerList, filterPredicate).map(BaseAnswerRuntime::getUniqueId);
    }

    public static <T extends BaseAnswerRuntime<?>> Optional<T> getNextAvailableAnswer(String preAnswerId,
                                                                                      List<T> answerList,
                                                                                      Predicate<BaseAnswerContent> filterPredicate) {
        if (CollectionUtils.isEmpty(answerList)) {
            return Optional.empty();
        }
        // 只要有答案, 就一定会有默认答案, 过滤后的答案列表不应该为空
        List<T> filterAnswerIdList = answerList.stream()
                .filter(item -> filterPredicate.test(item.origin))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterAnswerIdList)) {
            // 不应该过滤完还没有答案了
            return Optional.empty();
        }

        //按照是否开启了变量参数进行分组,优先在指定了变量参数的答案内进行循环播报
        Map<Boolean, List<T>> collect = filterAnswerIdList.stream()
                .collect(Collectors.groupingBy(content -> BooleanUtils.isTrue(content.origin.getEnableVarCondition())));
        filterAnswerIdList = collect.getOrDefault(true, collect.get(false));

        int preIndex = -1;
        for (int i = 0; i < filterAnswerIdList.size(); i++) {
            if (filterAnswerIdList.get(i).getUniqueId().equals(preAnswerId)) {
                preIndex = i;
            }
        }
        int nextIndex = preIndex + 1;
        T answer = filterAnswerIdList.get(nextIndex % filterAnswerIdList.size());
        return Optional.of(answer);
    }

}
