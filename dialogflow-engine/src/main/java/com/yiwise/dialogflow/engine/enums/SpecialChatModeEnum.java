package com.yiwise.dialogflow.engine.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum SpecialChatModeEnum implements CodeDescEnum {
    DELAY_HANGUP(1, "延迟挂机模式, 这个状态下只能通过意图触发问答知识和流程"),
    INAUDIBLE_REPEAT(2, "听不清重复上一句, 这个状态下如果用户输入没有触发问答知识, 则重播上一句答案"),
    UNINTERRUPTED(3, "不可打断状态, 这个时候, 只能命中节点设置允许打断的问答知识/流程"),
    KEY_CAPTURE(4, "按键采集，这个模式下只能触发采集成功、采集失败、配置允许跳转的问答知识和流程"),
    ;


    Integer code;
    String desc;
    SpecialChatModeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
