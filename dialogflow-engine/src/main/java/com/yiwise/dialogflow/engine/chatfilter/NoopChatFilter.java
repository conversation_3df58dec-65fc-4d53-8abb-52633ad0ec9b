package com.yiwise.dialogflow.engine.chatfilter;

import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;

import java.util.Optional;

public class NoopChatFilter extends AbstractChatFilter{
    @Override
    public Optional<ChatResponse> doFilter(SessionContext sessionContext, EventContext context) {
        return Optional.empty();
    }

    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return "NoopChatFilter";
    }
}
