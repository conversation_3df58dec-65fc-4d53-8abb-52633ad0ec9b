package com.yiwise.dialogflow.engine.resource;

import com.google.common.collect.ImmutableSet;
import com.yiwise.dialogflow.entity.enums.llm.LlmGuideAnswerConfigEnum;
import lombok.Data;


@Data
public class BotConfigRuntime {

    boolean enableToneInterrupt;

    // 语气词打断百分比
    private int toneInterruptPercent;

    // 语气词列表
    private ImmutableSet<String> toneWordSet;

    /**
     * 默认用户无应答时长设置
     */
    private int userSilenceMs = 7000;

    private LlmGuideAnswerConfigEnum llmGuideAnswerConfig = LlmGuideAnswerConfigEnum.COMPOSE_OPTIMIZATION;
}
