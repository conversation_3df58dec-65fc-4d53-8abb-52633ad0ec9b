package com.yiwise.dialogflow.engine.service.impl;

import com.yiwise.dialogflow.engine.analysis.EventLog;
import com.yiwise.dialogflow.engine.analysis.OriginChatData;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.resource.StepRuntime;
import com.yiwise.dialogflow.engine.service.YixinAnalysisService;
import com.yiwise.dialogflow.engine.share.CallDataInfo;
import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.engine.share.response.SessionInfo;
import com.yiwise.dialogflow.entity.enums.DialStatusEnum;
import com.yiwise.dialogflow.entity.enums.StepSubTypeEnum;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class YixinAnalysisServiceImpl implements YixinAnalysisService {
    @Override
    public List<String> analysis(RobotRuntimeResource resource,
                                 SessionContext sessionContext,
                                 SessionInfo sessionInfo,
                                 CallDataInfo callDataInfo,
                                 OriginChatData originChatData) {
        // 命中的意图名称列表

        List<String> intentNameList = new ArrayList<>();

        // 判断通话状态, 如果未接通, 则直接返回
        if (DialStatusEnum.ANSWERED.getCode() != callDataInfo.getDialStatus()) {
            return intentNameList;
        }

        for (EventLog eventLog : originChatData.getLogList()) {
            if (ChatEventTypeEnum.USER_SAY_FINISH.equals(eventLog.getEvent())) {
                if (Objects.nonNull(eventLog.getPredictResult())) {
                    intentNameList.add(eventLog.getPredictResult().getIntentName());
                } else {
                    if (Objects.nonNull(eventLog.getAnswerLocate())) {
                        // 判断是大模型流程还是大模型特殊语境
                        AnswerLocateBO locate = eventLog.getAnswerLocate();
                        if (StringUtils.isNotBlank(locate.getSpecialAnswerConfigName())
                                && SpecialAnswerConfigPO.LLM.equals(locate.getSpecialAnswerConfigName())) {
                            intentNameList.add("大模型对话");
                        } else if (StringUtils.isNotBlank(locate.getStepId())
                                && Objects.nonNull(resource.getStepIdMap().get(locate.getStepId()))
                                && StepSubTypeEnum.isLlm(resource.getStepIdMap().get(locate.getStepId()).getSubType())){
                            intentNameList.add("大模型流程");
                        } else {
                            intentNameList.add("不理解");
                        }
                    }
                }
            } else if (ChatEventTypeEnum.USER_SILENCE.equals(eventLog.getEvent())) {
                intentNameList.add("用户无应答");
            }
        }

        return intentNameList.stream().distinct().collect(Collectors.toList());
    }
}
