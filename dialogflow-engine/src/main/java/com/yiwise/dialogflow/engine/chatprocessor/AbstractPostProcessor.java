package com.yiwise.dialogflow.engine.chatprocessor;

import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

import java.util.Optional;

@Slf4j
public abstract class AbstractPostProcessor implements ChatPostProcessor {

    @Override
    public Optional<ChatResponse> process(SessionContext sessionContext,
                                   EventContext eventContext,
                                   ChatResponse chatResponse) {
        return doProcess(sessionContext, eventContext, chatResponse);
    }

    @Override
    public final Mono<ChatResponse> processAsync(SessionContext sessionContext,
                                                 EventContext eventContext,
                                                 ChatResponse chatResponse){
        return doProcessAsync(sessionContext, eventContext, chatResponse);
    }

    protected abstract Optional<ChatResponse> doProcess(SessionContext sessionContext,
                                                        EventContext eventContext,
                                                        ChatResponse chatResponse);

    protected Mono<ChatResponse> doProcessAsync(SessionContext sessionContext,
                                            EventContext eventContext,
                                            ChatResponse chatResponse) {
        return Mono.justOrEmpty(doProcess(sessionContext, eventContext, chatResponse));
    }
}
