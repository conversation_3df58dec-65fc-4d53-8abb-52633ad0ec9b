package com.yiwise.dialogflow.engine;

import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.enums.ReceptionTypeEnum;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import lombok.Data;

import java.util.Objects;
import java.util.Optional;

/**
 * 为了解决统计ai重复上一句这种自己不会生成答案, 只会重复原chatManager的答案, 所以如果从答案中获取, 就无法获取到ai重复上一句的命中情况了
 *
 * <AUTHOR>
 */
@Data
public class ReceptionInfo {

    AnswerSourceEnum type;

    String targetId;

    String targetName;

    String targetLabel;
}
