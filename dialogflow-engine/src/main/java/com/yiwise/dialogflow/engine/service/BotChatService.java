package com.yiwise.dialogflow.engine.service;

import com.yiwise.dialogflow.engine.share.*;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.engine.share.request.ChatRequest;
import com.yiwise.dialogflow.engine.share.request.HttpRequest;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import com.yiwise.dialogflow.engine.share.response.SessionInfo;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface BotChatService {
    /**
     * 创建会话
     */
    Mono<SessionInfo> createSessionAsync(Long botId, Integer version, RobotSnapshotUsageTargetEnum usageTarget, ChatMetaData chatData);

    /**
     * 处理对话请求
     */
    Mono<ChatResponse> processRequestAsync(ChatRequest request);

    /**
     * 分析对话数据
     */
    Mono<IntentLevelAnalysisResult> analysisAsync(CallDataInfo callDataInfo);

    /**
     * 通话中分析对话数据
     */
    Mono<IntentLevelAnalysisResult> analysisInCallAsync(CallDataInfo callDataInfo);

    /**
     * http请求
     */
    Mono<Map<String, String>> httpRequestAsync(HttpRequest req);

    /**
     * 基于大模型生成答案
     */
    Flux<ChatResponse> processRequestWithLLMAsync(ChatRequest request);
}
