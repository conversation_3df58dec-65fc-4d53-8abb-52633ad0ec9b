package com.yiwise.dialogflow.engine.resource;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.Data;

import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Pattern;

/**
 * 对正则表达式的缓存全局缓存, 同一个正则, 在全局进有一个缓存数据
 */
@Data
public class PatternCache {
    final String regex;

    final Pattern pattern;

    final AtomicLong hitCount = new AtomicLong(0);

    public PatternCache(String regex, Pattern pattern) {
        this.regex = regex;
        this.pattern = pattern;
    }

    public void hit() {
        hitCount.incrementAndGet();
    }

    private static final Cache<String, PatternCache> CACHE = CacheBuilder.newBuilder()
            // 10万
            .maximumSize(10_0000)
            .expireAfterAccess(1, TimeUnit.DAYS)
            .build();

    public static Pattern compile(String regex) {
        PatternCache patternCache = CACHE.getIfPresent(regex);
        if (patternCache != null) {
            patternCache.hit();
            return patternCache.getPattern();
        }

        Pattern pattern = Pattern.compile(regex);
        patternCache = new PatternCache(regex, pattern);
        CACHE.put(regex, patternCache);
        return pattern;
    }

    public static Map<String, Long> getCacheInfo() {
        return CACHE.asMap().entrySet().stream()
                .collect(java.util.stream.Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getHitCount().get()));
    }

}
