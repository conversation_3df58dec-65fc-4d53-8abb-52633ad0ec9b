package com.yiwise.dialogflow.engine.dispatcher;

import com.yiwise.dialogflow.engine.context.EventContext;
import com.yiwise.dialogflow.engine.context.SessionContext;
import com.yiwise.dialogflow.engine.resource.RobotRuntimeResource;
import com.yiwise.dialogflow.engine.share.response.AnswerResult;
import com.yiwise.dialogflow.engine.share.response.ChatResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.Optional;

@Slf4j
public class UserSayDispatcher extends AbstractChatDispatcher {

    protected RobotRuntimeResource resource;

    public UserSayDispatcher(RobotRuntimeResource resource) {
        this.resource = resource;
    }

    @Override
    public void initContext(SessionContext sessionContext) {

    }

    @Override
    public String getName() {
        return "UserSayDispatcher";
    }

    @Override
    public Optional<ChatResponse> doDispatch(SessionContext sessionContext, EventContext eventContext) {
        return Optional.of(generateResponse(new AnswerResult()));
    }

    protected ChatResponse generateResponse(AnswerResult answerResult) {
        answerResult.setCustomInterruptThreshold(0);
        ChatResponse response = new ChatResponse();
        response.setAnswer(answerResult);
        response.setActionList(Collections.emptyList());
        return response;
    }
}
