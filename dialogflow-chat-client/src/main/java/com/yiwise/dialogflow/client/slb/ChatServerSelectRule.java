package com.yiwise.dialogflow.client.slb;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.netflix.client.config.IClientConfig;
import com.netflix.loadbalancer.AbstractLoadBalancerRule;
import com.netflix.loadbalancer.ILoadBalancer;
import com.netflix.loadbalancer.RandomRule;
import com.netflix.loadbalancer.Server;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
public class ChatServerSelectRule extends RandomRule {

    private final Cache<String, String> callRecordSelectServerCache = CacheBuilder.newBuilder()
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .maximumSize(100_000)
            .build();

    private AbstractLoadBalancerRule parentRule = null;

    public ChatServerSelectRule() {
        super();
    }

    public ChatServerSelectRule(AbstractLoadBalancerRule parentRule) {
        this();
        this.parentRule = parentRule;
    }

    @Override
    public void initWithNiwsConfig(IClientConfig iClientConfig) {
        if (parentRule != null) {
            this.parentRule.initWithNiwsConfig(iClientConfig);
        }
    }

    @Override
    public void setLoadBalancer(ILoadBalancer lb) {
        super.setLoadBalancer(lb);
        if (parentRule != null) {
            parentRule.setLoadBalancer(lb);
        }
    }

    @Override
    public Server choose(Object key) {
        Optional<String> callTaskIdOpt = getCallTaskId(key);
        if (!isChatRequest(key) || !callTaskIdOpt.isPresent()) {
            Server server = doChoose(key);
            log.info("不是对话接口或callTaskId为null, server={}", server);
            return server;
        }
        String callTaskId = callTaskIdOpt.get();
        Optional<Server> cacheServer = selectFromCache(callTaskId);
        boolean switchServer = isSwitchServer();
        if (cacheServer.isPresent()) {
            if (switchServer) {
                log.info("callTaskId={}对应的对话服务已经选择, 但是需要切换服务重试, server={}", callTaskId, cacheServer.get());
                String cacheServerInfo = convertServerInfo(cacheServer.get());
                Server server = null;
                int retry = 0;
                int maxRetry = 10;
                do {
                    retry++;
                    if (retry > maxRetry) {
                        break;
                    }
                    server = doChoose(key);
                } while ( Objects.isNull(server) || StringUtils.equals(cacheServerInfo, convertServerInfo(server)));
                if (Objects.nonNull(server)) {
                    flushCache(callTaskId, server);
                    log.info("select server from  rule, switchServer=true, callTaskId={}, server={}", callTaskId, server);
                    return server;
                }
            }
            log.info("select server from cache, callTaskId={}, server={}", callTaskId, cacheServer.get());
            return cacheServer.get();
        }
        Server server = doChoose(key);
        flushCache(callTaskId, server);
        log.info("select server from  rule, callTaskId={}, server={}", callTaskId, server);
        return server;
    }

    private Server doChoose(Object key) {
        return Objects.isNull(parentRule) ? super.choose(key) : parentRule.choose(key);
    }

    private void flushCache(String callTaskId, Server server) {
        if (Objects.nonNull(server)) {
            callRecordSelectServerCache.put(callTaskId, convertServerInfo(server));
        }
    }

    private Optional<String> getCallTaskId(Object key) {
        BotChatServerSelectContext context = BotChatContextService.getChatServerSelectContext();
        if (Objects.isNull(context) || StringUtils.isBlank(context.getTaskId()) || "null".equals(context.getTaskId()) || "0".equals(context.getTaskId())) {
            return Optional.empty();
        }
        return Optional.ofNullable(context.getTaskId());
    }

    private boolean isChatRequest(Object key) {
        return Objects.nonNull(BotChatContextService.getChatServerSelectContext());
    }

    private Optional<Server> selectFromCache(String callTaskId) {
        String serverInfo = callRecordSelectServerCache.getIfPresent(callTaskId);
        if (StringUtils.isBlank(serverInfo)) {
            return Optional.empty();
        }
        Optional<Server> result = getLoadBalancer().getReachableServers()
                .stream()
                .filter(item -> serverInfo.equals(convertServerInfo(item)))
                .findFirst();
        if (!result.isPresent()) {
            log.info("检测到缓存中的server已不可用, 重新选择服务, serverInfo:{}", serverInfo);
        }
        return result;
    }

    private boolean isSwitchServer() {
        BotChatServerSelectContext context = BotChatContextService.getChatServerSelectContext();
        return Objects.nonNull(context) && BooleanUtils.isTrue(context.getSwitchServerRetry());
    }

    private String convertServerInfo(Server server) {
        return server.getId();
    }

}
