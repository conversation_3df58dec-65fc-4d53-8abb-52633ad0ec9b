package com.yiwise.dialogflow.client.asr;

import lombok.Data;

/**
 * 音频人声数据
 */
@Data
public class AudioVoiceInfo {
    /**
     * 原始音频数据的起始位置, 单位毫秒
     */
    int startTime;

    /**
     * 原始音频数据的结束位置, 单位毫秒
     */
    int endTime;

    /**
     * 持续时间
     */
    int duration;

    /**
     * 是否是跳过的音频数据
     */
    boolean skip;

    /**
     * 在这段音频之前, 一个多长时间的音频数据
     */
    int preSkipTime;

    public int getDuration() {
        return endTime - startTime;
    }

    public int getSendBegin() {
        return startTime - preSkipTime;
    }

    public int getSendEnd() {
        return endTime - preSkipTime;
    }
}
