package com.yiwise.dialogflow.client.asr;

import com.yiwise.dialogflow.client.config.DialogEngineConstant;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public abstract class AbstractAudioFilter implements MuteAudioFilter {

    protected final AtomicInteger waitingSkipAudioData = new AtomicInteger(0);

    @Override
    public void waitingAsrInitSkipAudioData(int length) {
        waitingSkipAudioData.addAndGet(length);
    }

    @Override
    public int convertToRealTime(int sendTime) {
        int convertResult =  doConvert(sendTime);
        int result = convertResult + waitingSkipAudioData.get() / DialogEngineConstant.MONO_FILE_LENGTH_PER_MS;
        log.debug("convertToRealTime sendTime: {}, convertResult: {}, finalResult:{}, waitingSkipAudioData: {}", sendTime, convertResult, result, waitingSkipAudioData.get());
        return result;
    }

    protected abstract int doConvert(int sendTime);

    @Override
    public int getWaitingSkipAudioData() {
        return waitingSkipAudioData.get();
    }
}
