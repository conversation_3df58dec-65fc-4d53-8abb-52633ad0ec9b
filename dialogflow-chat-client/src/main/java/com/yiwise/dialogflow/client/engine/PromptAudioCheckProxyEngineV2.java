package com.yiwise.dialogflow.client.engine;

import com.yiwise.base.common.thread.ApplicationExecutor;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.dialogflow.client.asr.AsrResultFilter;
import com.yiwise.dialogflow.client.asr.DefaultAsrResultFilter;
import com.yiwise.dialogflow.client.listener.*;
import com.yiwise.dialogflow.client.model.engine.SimpleUserSayResultInfo;
import com.yiwise.dialogflow.engine.share.BotMetaData;
import com.yiwise.dialogflow.engine.share.CallDataInfo;
import com.yiwise.dialogflow.engine.share.IntentLevelAnalysisResult;
import com.yiwise.dialogflow.engine.share.SystemMetaData;
import com.yiwise.dialogflow.engine.share.response.SessionInfo;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Nullable;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Consumer;

public class PromptAudioCheckProxyEngineV2  implements SpeechDialogEngine, AudioPlayEventListener, UserVoiceEventListener, FastHangupListener, KeyCaptureListener {

    private static final Logger log = LoggerFactory.getLogger(PromptAudioCheckProxyEngineV2.class);
    private volatile DefaultSpeechDialogEngine realSpeechDialogEngine;

    private final BotMetaData botMetaData;
    private final List<Runnable> addWechatCallbackList = new ArrayList<>();
    private final List<Runnable> switchToHumanServiceExecutorList = new ArrayList<>();
    private final List<Runnable> humanInterventionExecutorList = new ArrayList<>();
    private final List<Consumer<Collection<Long>>> sendSmsConsumerList = new ArrayList<>();
    private final List<CallDetailListener> callDetailListenerList = new ArrayList<>();
    private final List<HangupEventListener> hangupEventListenerList = new ArrayList<>();

    private boolean enable;
    private final int timeout;
    private final List<String> preRegexList;
    private final List<String> excludeAiAssistantRegexList;
    private final SessionInfo backupSessionInfo;
    private final AudioPlayManager audioPlayManager;
    private final ApplicationExecutor executor;
    private final DTMFManager dtmfManager;
    private final SystemMetaData systemMetaData;
    private final int RESET_TIMEOUT_MS;
    private final int USER_INPUT_MERGE_INTERVAL_MS;

    private volatile boolean pauseByMatchPromptAudio = false;

    private volatile long resetTimestamp = 0;

    private volatile UserSayInfo lastUserSayInfo;

    private final Lock lock = new ReentrantLock();

    /**
     * 是否匹配过语音助手的正则
     */
    private volatile boolean matchedAiAssistantRegex = false;

    private volatile Long engineResetTimestamp;

    public PromptAudioCheckProxyEngineV2(@Nullable Long tenantId,
                                         BotMetaData botMetaData,
                                         SessionInfo sessionInfo,
                                         AudioPlayManager audioPlayManager,
                                         ApplicationExecutor executor,
                                         DTMFManager dtmfManager) {
        this.systemMetaData = SystemMetaDataHelper.getSystemMetaData().orElse(null);
        this.botMetaData = botMetaData;
        this.backupSessionInfo = MyBeanUtils.copy(sessionInfo, SessionInfo.class);
        this.audioPlayManager = audioPlayManager;
        this.executor = executor;
        this.dtmfManager = dtmfManager;
        if (systemMetaData != null) {
            timeout = systemMetaData.getCheckPromptAudioBeforeSeconds() != null ? systemMetaData.getCheckPromptAudioBeforeSeconds() * 1000 : 0;
            preRegexList = systemMetaData.getV2OperatorPromptAudioRegexList();
            excludeAiAssistantRegexList = systemMetaData.getPromptAudioExcludeAiAssistantRegexList();
            enable = timeout > 0 && CollectionUtils.isNotEmpty(preRegexList);
            if (CollectionUtils.isNotEmpty(systemMetaData.getDisablePromptAudioCheckTenantIdList())
                    && systemMetaData.getDisablePromptAudioCheckTenantIdList().contains(tenantId)) {
                enable = false;
            }
            if (BooleanUtils.isTrue(botMetaData.getDisablePromptAudioCheck())) {
                enable = false;
            }
            RESET_TIMEOUT_MS = systemMetaData.getResetTimeoutMs() == null ? 1000 : systemMetaData.getResetTimeoutMs();
            USER_INPUT_MERGE_INTERVAL_MS = systemMetaData.getUserInputMergeIntervalMs() == null ? 600 : systemMetaData.getUserInputMergeIntervalMs();
        } else {
            log.warn("systemMetaData is null");
            timeout = 0;
            preRegexList = Collections.emptyList();
            excludeAiAssistantRegexList = Collections.emptyList();
            enable = false;
            RESET_TIMEOUT_MS = 0;
            USER_INPUT_MERGE_INTERVAL_MS = 600;
        }

        realSpeechDialogEngine = new DefaultSpeechDialogEngine(botMetaData, sessionInfo, audioPlayManager, executor, dtmfManager);
        if (enable) {
            audioPlayManager.registerListener(this);
        }

        log.debug("enable: {}, timeout: {}, preRegexList: {}, excludeAiAssistantRegexList:{}, RESET_TIMEOUT_MS:{}, USER_INPUT_MERGE_INTERVAL_MS:{}",
                enable, timeout, preRegexList, excludeAiAssistantRegexList, RESET_TIMEOUT_MS, USER_INPUT_MERGE_INTERVAL_MS);
    }

    @Override
    public void init() {
        realSpeechDialogEngine.init();
    }

    @Override
    public void enter() {
        realSpeechDialogEngine.enter();
    }

    @Override
    public void onSentenceBegin() {
        realSpeechDialogEngine.onSentenceBegin();
    }

    @Override
    public void processUserSay(String userSayText, boolean userSayFinish, int beginTime, int endTime) {
        log.debug("processUserSay: {}, {}, {}, {}", userSayText, userSayFinish, beginTime, endTime);
        try {
            if (enable && !matchedAiAssistantRegex && endTime < timeout) {
                String mergeUserSayTextResult = mergeUserSayText(userSayText, beginTime, endTime, userSayFinish);
                // 命中语音提示音了
                if (checkHit(mergeUserSayTextResult, preRegexList, "匹配运营商提示音")) {
                    // 判断是否能够命中语音助手的提示音
                    if (userSayFinish && checkHit(mergeUserSayTextResult, excludeAiAssistantRegexList, "匹配语音助手")) {
                        onMatchAiAssistant(userSayText, userSayFinish, beginTime, endTime);
                        return;
                    }
                    int delayResetTimeoutMs = RESET_TIMEOUT_MS;
                    if (!userSayFinish) {
                        delayResetTimeoutMs = RESET_TIMEOUT_MS * 5;
                    }
                    try {
                        if (lock.tryLock(1, TimeUnit.SECONDS)) {
                            try {
                                onMatch(delayResetTimeoutMs);
                            } finally {
                                lock.unlock();
                            }
                        } else {
                            log.warn("获取锁失败");
                            onMatch(delayResetTimeoutMs);
                        }
                    } catch (InterruptedException e) {
                        log.warn("获取锁异常", e);
                    }
                    createAndCallbackRecord(userSayText, beginTime, endTime, "命中运营商提示音, 暂停播放");
                } else if (pauseByMatchPromptAudio) {
                    if (userSayFinish) {
                        if (checkHit(mergeUserSayTextResult, excludeAiAssistantRegexList, "匹配语音助手")) {
                            onMatchAiAssistant(userSayText, userSayFinish, beginTime, endTime);
                        } else {
                            createAndCallbackRecord(userSayText, beginTime, endTime, "当前处于运营商提示音暂停状态, 忽略当前输入");
                        }
                    }
                } else {
                    realSpeechDialogEngine.processUserSay(userSayText, userSayFinish, beginTime, endTime);
                }
            } else {
                realSpeechDialogEngine.processUserSay(userSayText, userSayFinish, beginTime, endTime);
            }
        } catch (Exception e) {
            log.error("[LogHub_Warn]处理用户输入异常", e);
        }
    }

    private void onMatchAiAssistant(String userSayText, boolean userSayFinish, int beginTime, int endTime) {
        log.debug("在暂停或命中虚拟号提示音的情况下, 同时命中语音助手的正则, 需要把用户输入内容送给对话系统进行处理");
        matchedAiAssistantRegex = true;
        pauseByMatchPromptAudio = false;
        resetTimestamp = 0;
        realSpeechDialogEngine.processUserSay(userSayText, userSayFinish, beginTime, endTime);
    }

    private void onMatch(int delayResetTimeoutMs) {
        resetTimestamp = System.currentTimeMillis() + delayResetTimeoutMs;
        LocalDateTime nextTime = LocalDateTime.now().plus(delayResetTimeoutMs, ChronoUnit.MILLIS);
        log.debug("检测到 preRegexList, 先暂停音频播放, resetTimestamp:{}, {}, delayResetTimeoutMs:{}",
                resetTimestamp, nextTime, delayResetTimeoutMs);
        audioPlayManager.pause();
        pauseByMatchPromptAudio = true;
    }

    private String mergeUserSayText(String userSayText, int beginTime, int endTime, boolean sayFinish) {
        log.debug("mergeUserSayText, lastUserSayInfo:{}", JsonUtils.object2String(lastUserSayInfo));
        if (lastUserSayInfo == null) {
            if (sayFinish) {
                UserSayInfo userSayInfo = new UserSayInfo();
                userSayInfo.text = userSayText;
                userSayInfo.beginTime = beginTime;
                userSayInfo.endTime = endTime;
                lastUserSayInfo = userSayInfo;
                log.debug("lastUserSayInfo:{}", JsonUtils.object2String(lastUserSayInfo));
            }
            return userSayText;
        } else {
            boolean needMerge = (beginTime - lastUserSayInfo.endTime) <= USER_INPUT_MERGE_INTERVAL_MS;
            String resultText = needMerge ? lastUserSayInfo.text + ", " + userSayText : userSayText;
            log.debug("needMerge:{}, resultText:{}", needMerge, resultText);
            if (sayFinish) {
                // 判断是否需要断句补齐
                UserSayInfo userSayInfo = new UserSayInfo();
                userSayInfo.text = resultText;
                userSayInfo.beginTime = needMerge ? lastUserSayInfo.beginTime : beginTime;
                userSayInfo.endTime = endTime;
                lastUserSayInfo = userSayInfo;
                log.debug("lastUserSayInfo:{}", JsonUtils.object2String(lastUserSayInfo));
            }
            return resultText;
        }
    }

    private void createAndCallbackRecord(String userSayText, int beginTime, int endTime, String debugLog) {
        SimpleUserSayResultInfo userSayResultInfo = new SimpleUserSayResultInfo();
        userSayResultInfo.setUserInput(userSayText);
        userSayResultInfo.setBeginTime(beginTime);
        userSayResultInfo.setEndTime(endTime);
        List<String> debugLogList = Collections.singletonList(debugLog);
        userSayResultInfo.setDebugLogList(debugLogList);
        userSayResultInfo.setSimpleDebugLogList(debugLogList);
        for (CallDetailListener callDetailListener : callDetailListenerList) {
            callDetailListener.createUserSayRecord(userSayResultInfo);
        }
    }

    private boolean checkHit(String userSayText, List<String> regexList, String logPrefix) {
        if (CollectionUtils.isEmpty(regexList)) {
            return false;
        }
        try {
            for (String regex : regexList) {
                if (userSayText.matches(regex)) {
                    log.debug("{}, 命中正则: {}", logPrefix, regex);
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("[LogHub_Warn]{}异常", logPrefix, e);
        }
        return false;
    }

    private void resetEngine() {
        log.debug("检测到运营商提示音, 提前释放当前 engine");
        engineResetTimestamp = System.currentTimeMillis();
        audioPlayManager.reset();
        realSpeechDialogEngine.finishByMatchPromptAudio();
        AsrResultFilter oldFilter = realSpeechDialogEngine.asrResultFilter;
        long oldAudioReadTime = realSpeechDialogEngine.getAudioReadTime();
        SessionInfo sessionInfo = MyBeanUtils.copy(backupSessionInfo, SessionInfo.class);
        realSpeechDialogEngine = new DefaultSpeechDialogEngine(botMetaData, sessionInfo, audioPlayManager, executor, dtmfManager);
        realSpeechDialogEngine.init();
        realSpeechDialogEngine.setAudioReadTime(oldAudioReadTime);
        AsrResultFilter newFilter = realSpeechDialogEngine.asrResultFilter;
        if (newFilter instanceof DefaultAsrResultFilter && oldFilter instanceof DefaultAsrResultFilter) {
            DefaultAsrResultFilter oldDefaultFilter = (DefaultAsrResultFilter) oldFilter;
            DefaultAsrResultFilter newDefaultFilter = (DefaultAsrResultFilter) newFilter;
            log.debug("reset AsrResultFilter");
            newDefaultFilter.setWaitAsrInitSkipAudioData(oldDefaultFilter.getWaitAsrInitSkipAudioData());
            newDefaultFilter.setWriteTime(oldDefaultFilter.getWriteTime());
        }
        addWechatCallbackList.forEach(realSpeechDialogEngine::registerAddWechatCallback);
        callDetailListenerList.forEach(realSpeechDialogEngine::registerCallDetailListener);
        hangupEventListenerList.forEach(realSpeechDialogEngine::registerHangupEventListener);
        sendSmsConsumerList.forEach(realSpeechDialogEngine::registerSendSmsConsumer);
        switchToHumanServiceExecutorList.forEach(realSpeechDialogEngine::registerSwitchToHumanServiceExecutor);
        humanInterventionExecutorList.forEach(realSpeechDialogEngine::registerHumanInterventionExecutor);
        audioPlayManager.registerListener(this);
    }

    @Override
    public void realProcessUserSay(String userSayText, boolean userSayFinish, int beginTime, int endTime) {
        realSpeechDialogEngine.realProcessUserSay(userSayText, userSayFinish, beginTime, endTime);
    }

    @Override
    public void registerCallDetailListener(CallDetailListener listener) {
        callDetailListenerList.add(listener);
        realSpeechDialogEngine.registerCallDetailListener(listener);
    }

    @Override
    public void registerHangupEventListener(HangupEventListener hangupEventListener) {
        hangupEventListenerList.add(hangupEventListener);
        realSpeechDialogEngine.registerHangupEventListener(hangupEventListener);
    }

    @Override
    public String getSessionContextContent() {
        return realSpeechDialogEngine.getSessionContextContent();
    }

    @Override
    public DefaultSpeechDialogEngine.InterruptConfig getInterruptConfig() {
        return realSpeechDialogEngine.getInterruptConfig();
    }

    @Override
    public IntentLevelAnalysisResult intentLevelAnalysis(CallDataInfo callDataInfo) {
        callDataInfo.setEngineResetTimestamp(engineResetTimestamp);
        return realSpeechDialogEngine.intentLevelAnalysis(callDataInfo);
    }

    @Override
    public IntentLevelAnalysisResult intentLevelAnalysisInCall(CallDataInfo callDataInfo) {
        callDataInfo.setEngineResetTimestamp(engineResetTimestamp);
        return realSpeechDialogEngine.intentLevelAnalysisInCall(callDataInfo);
    }

    @Override
    public void release(String reason) {
        realSpeechDialogEngine.release(reason);
    }

    @Override
    public void registerAddWechatCallback(Runnable addWechatCallback) {
        addWechatCallbackList.add(addWechatCallback);
        realSpeechDialogEngine.registerAddWechatCallback(addWechatCallback);
    }

    @Override
    public void registerSwitchToHumanServiceExecutor(Runnable switchToHumanServiceExecutor) {
        switchToHumanServiceExecutorList.add(switchToHumanServiceExecutor);
        realSpeechDialogEngine.registerSwitchToHumanServiceExecutor(switchToHumanServiceExecutor);
    }

    @Override
    public void registerHumanInterventionExecutor(Runnable humanInterventionExecutor) {
        humanInterventionExecutorList.add(humanInterventionExecutor);
        realSpeechDialogEngine.registerHumanInterventionExecutor(humanInterventionExecutor);
    }

    @Override
    public void registerSendSmsConsumer(Consumer<Collection<Long>> sendSmsConsumer) {
        sendSmsConsumerList.add(sendSmsConsumer);
        realSpeechDialogEngine.registerSendSmsConsumer(sendSmsConsumer);
    }

    @Override
    public void onUserHangup() {
        realSpeechDialogEngine.onUserHangup();
    }

    @Override
    public void onAiHangup() {
        realSpeechDialogEngine.onAiHangup();
    }

    @Override
    public void finishByError() {
        realSpeechDialogEngine.finishByError();
    }

    @Override
    public void finishByMatchPromptAudio() {
        realSpeechDialogEngine.finishByMatchPromptAudio();
    }

    @Override
    public List<String> getAllUserSayContentList() {
        return realSpeechDialogEngine.getAllUserSayContentList();
    }

    @Override
    public String getLastAnswerId() {
        return realSpeechDialogEngine.getLastAnswerId();
    }

    @Override
    public void onReadData() {
        long now = System.currentTimeMillis();
        if (enable && pauseByMatchPromptAudio && !matchedAiAssistantRegex && !audioPlayManager.isPause()) {
            log.debug("已经暂停的音频被恢复播放了");
            audioPlayManager.pause();
        }
        if (enable && pauseByMatchPromptAudio && !matchedAiAssistantRegex && resetTimestamp > 0 && now > resetTimestamp) {
            if (lock.tryLock()) {
                try {
                    log.debug("用户已说完, 重置引擎");
                    if (lastUserSayInfo != null) {
                        createAndCallbackRecord(lastUserSayInfo.text, lastUserSayInfo.beginTime, lastUserSayInfo.endTime, "命中运营商提示音, 重置对话");
                    } else {
                        createAndCallbackRecord("命中运营商提示音", 0, 0, "命中运营商提示音, 重置对话");
                    }
                    pauseByMatchPromptAudio = false;
                    resetTimestamp = 0;
                    resetEngine();
                    realSpeechDialogEngine.enter();
                } finally {
                    lock.unlock();
                }
            } else {
                log.warn("获取锁失败, 下次重试");
            }
        }
    }

    @Override
    public void onAiPlayBegin(int index) {
        log.debug("onAiPlayBegin:{}", index);
    }

    @Override
    public void onAiPlayEnd(int index) {
        log.debug("onAiPlayEnd:{}", index);
    }

    @Override
    public void onPause(int index) {
        log.debug("onPause:{}", index);
    }

    @Override
    public void onResume(int index) {
        log.debug("onResume:{}", index);
    }

    @Override
    public void onFastHangup(String reason) {
        realSpeechDialogEngine.onFastHangup(reason);
    }

    @Override
    public void onReplay() {
        realSpeechDialogEngine.onReplay();
    }

    @Override
    public void onSuccess(String result) {
        realSpeechDialogEngine.onSuccess(result);
    }

    @Override
    public void onFailed() {
        realSpeechDialogEngine.onFailed();
    }

    @Override
    public void writeData(short[] data) {
        realSpeechDialogEngine.writeData(data);
    }

    @Override
    public void userSilence() {
        realSpeechDialogEngine.userSilence();
    }

    @Override
    public void waitingAsrInitSkipAudioData(int length) {
        realSpeechDialogEngine.waitingAsrInitSkipAudioData(length);
    }

    @Override
    public void onUserVoiceBegin() {
        realSpeechDialogEngine.onUserVoiceBegin();
    }

    @Override
    public void onUserVoiceEnd() {
        realSpeechDialogEngine.onUserVoiceEnd();
    }

    @Data
    public static class UserSayInfo {
        String text;
        int beginTime;
        int endTime;
    }
}