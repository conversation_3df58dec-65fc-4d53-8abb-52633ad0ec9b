package com.yiwise.dialogflow.client.listener;

import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.engine.share.SystemMetaData;
import com.yiwise.dialogflow.engine.share.service.SystemMetaDataApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class SystemMetaDataHelper {

    private static final SystemMetaDataApi systemMetaDataService = AppContextUtils.getBean(SystemMetaDataApi.class);

    private static final long TIMEOUT = 60 * 1000L;
    private static final AtomicReference<SystemMetaData> holder = new AtomicReference<>();

    private static final AtomicLong flushTime = new AtomicLong();
    private static final Logger log = LoggerFactory.getLogger(SystemMetaDataHelper.class);
    private static final Lock lock = new ReentrantLock();

    public static Optional<SystemMetaData> getSystemMetaData() {
        try {
            SystemMetaData systemMetaData = holder.get();
            if (systemMetaData != null) {
                long lastFlush = flushTime.get();
                long now = System.currentTimeMillis();
                long duration = now - lastFlush;
                if (duration > TIMEOUT && flushTime.compareAndSet(lastFlush, now)) {
                    Optional<SystemMetaData> flushResult = flush();
                    if (flushResult.isPresent()) {
                        holder.set(flushResult.get());
                        return flushResult;
                    } else {
                        flushTime.set(lastFlush);
                    }
                }
                return Optional.of(systemMetaData);
            } else {
                lock.lock();
                try {
                    systemMetaData = holder.get();
                    if (systemMetaData != null) {
                        return Optional.of(systemMetaData);
                    } else {
                        Optional<SystemMetaData> flushResult = flush();
                        flushResult.ifPresent(sss -> {
                            holder.compareAndSet(null, flushResult.get());
                        });
                        return flushResult;
                    }
                } finally {
                    lock.unlock();
                }
            }
        } catch (Exception e) {
            log.warn("获取 SystemMetaData 异常");
        }
        return Optional.empty();
    }

    private static Optional<SystemMetaData> flush() {
        try {
            log.debug("开始刷新 systemMetaData");
            ResultObject<SystemMetaData> systemMetaData = systemMetaDataService.getMetaData();
            log.debug("刷新 systemMetaData:{}", JsonUtils.object2String(systemMetaData));
            return Optional.ofNullable(systemMetaData.getData());
        } catch (Exception e) {
            log.warn("刷新 systemMetaData 异常", e);
        }
        return Optional.empty();
    }
}
