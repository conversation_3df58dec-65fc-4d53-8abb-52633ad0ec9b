package com.yiwise.dialogflow.client.model.asr;


import com.yiwise.base.model.bean.po.BaseTimePO;
import com.yiwise.dialogflow.engine.share.enums.AsrProviderEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * 热词组与厂商关联表
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AsrVocabProviderRelationPO extends BaseTimePO implements Serializable {

	Long asrVocabProviderRelationId;

    /**
     * 热词组id
     */
    Long asrVocabId;

    /**
     * 供应商
     */
    AsrProviderEnum provider;

    /**
     * 供应商侧热词组id
     */
    String vocabularyId;

}
