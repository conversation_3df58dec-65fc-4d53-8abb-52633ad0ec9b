package com.yiwise.dialogflow.client.slb;

import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import lombok.Getter;

@Getter
public class BotChatServerSelectContext {
    RobotSnapshotUsageTargetEnum usageTarget;
    String taskId;
    Boolean switchServerRetry;

    public BotChatServerSelectContext(RobotSnapshotUsageTargetEnum usageTarget, String taskId, Boolean switchServerRetry) {
        this.usageTarget = usageTarget;
        this.taskId = taskId;
        this.switchServerRetry = switchServerRetry;
    }

    public BotChatServerSelectContext(RobotSnapshotUsageTargetEnum usageTarget, String taskId) {
        this.usageTarget = usageTarget;
        this.taskId = taskId;
    }

    public BotChatServerSelectContext(RobotSnapshotUsageTargetEnum usageTarget) {
        this.usageTarget = usageTarget;
    }
}

