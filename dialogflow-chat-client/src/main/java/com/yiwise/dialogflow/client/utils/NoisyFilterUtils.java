package com.yiwise.dialogflow.client.utils;


import com.google.common.collect.Sets;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.regex.Pattern;



public class NoisyFilterUtils {

    public static final String HAN_NUMBER = "一二三四五六七八九零0123456789";

    public static Set<String> oKSet = new HashSet<>(Arrays.asList("好", "到", "是", "性", "在", "有", "会", "幸", "能", "型", "行", "对"));

    public static Set<String> rejectSet = new HashSet<>(Arrays.asList("忙", "没", "滚"));

    public static Set<String> elseSet = new HashSet<>(Arrays.asList("为", "微", "喂", "未", "位", "啥"));

    public static final Set<String> EXCLUDE_SET = Sets.newHashSet("好", "行", "性", "型", "幸", "对", "在", "有", "会", "到", "是", "能", "滚", "忙", "没", "喂", "位", "未", "为", "微");

    public static final Set<String> NOISE_SET = Sets.newHashSet("喂", "位", "未", "为", "微", "对", "嗯", "哦", "啊", "在", "说");

    // 纯数字正则
    public static Pattern numPattern = Pattern.compile("^\\d$");

    public static boolean isNoisy(String userSay) {
        if (org.apache.commons.lang.StringUtils.isEmpty(userSay)) {
            return true;
        }

        // 如果是单个字，则处理为噪声
        if (StringUtils.length(userSay) == 1 &&
                !oKSet.contains(userSay) &&
                !rejectSet.contains(userSay) &&
                !elseSet.contains(userSay) &&
                !numPattern.matcher(userSay).matches()
                && !HAN_NUMBER.contains(userSay)) {
            return true;
        }
        return false;
    }
}

