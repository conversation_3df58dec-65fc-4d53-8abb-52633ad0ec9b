package com.yiwise.dialogflow.client.asr;

import com.yiwise.base.common.audio.AudioHandleUtils;
import com.yiwise.base.common.audio.vad.VadProcessor;
import com.yiwise.base.common.audio.vad.VadProcessorCallback;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.dialogflow.client.engine.DefaultSpeechDialogEngine;
import com.yiwise.dialogflow.client.engine.SpeechDialogEngine;
import com.yiwise.dialogflow.engine.share.BotMetaData;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
public class DefaultAsrResultFilter extends AbstractAsrResultFilter implements VadProcessorCallback {

    private final Queue<DefaultSpeechDialogEngine.UserSayInfo> userSayInfoQueue = new java.util.concurrent.ConcurrentLinkedQueue<>();

    private volatile VadProcessor vadProcessor;

    private final AtomicInteger writeTime = new AtomicInteger(0);

    private final AtomicInteger lastUserSayFinishEndTime = new AtomicInteger(0);
    private final AtomicInteger nextVadStartTimeoutTime = new AtomicInteger(0);
    private final AtomicInteger nextAsrStartTimeoutTime = new AtomicInteger(0);
    private final AtomicInteger nextAsrEndTimeoutTime = new AtomicInteger(0);

    private final Lock lock = new ReentrantLock(true);

    private final int maxSentenceSilenceMs;

    private final int originMaxSentenceSilenceMs;

    private final List<VoiceInfo> queue;

    private final AtomicInteger queueWriteIndex = new AtomicInteger();

    private volatile Status status = Status.INIT;

    private final Config config;

    DefaultAsrResultFilter(BotMetaData botMetaData, SpeechDialogEngine speechDialogEngine) {
        super(speechDialogEngine);
        originMaxSentenceSilenceMs = botMetaData.getBotAsrConfig().getMaxSentenceSilence();
        config = parseConfig(botMetaData.getBotAsrConfig().getAsrOptimizationConfigJson());

        maxSentenceSilenceMs = originMaxSentenceSilenceMs + config.sentenceSilenceDelayMs;

        log.debug("config:{}", JsonUtils.object2String(config));
        queue = new ArrayList<>(50);
        for (int i = 0; i < 50; i++) {
            queue.add(new VoiceInfo());
        }
        vadProcessor = createVadProcessor(config, this);
    }

    private Config parseConfig(String asrOptimizationConfigJson) {
        log.debug("asrOptimizationConfigJson:{}", asrOptimizationConfigJson);
        if (StringUtils.isBlank(asrOptimizationConfigJson)) {
            return new Config();
        }
        try {
            return JsonUtils.string2Object(asrOptimizationConfigJson, Config.class);
        } catch (Exception e) {
            log.error("parse asrOptimizationConfigJson error, {}", e.getMessage());
        }
        return new Config();
    }


    @Override
    public void onSentenceBegin() {
        lock.lock();
        try {
            log.debug("onSentenceBegin, writeTime: {}, status:{}", writeTime.get(), status);
            if (status == Status.WAITING_ASR_START || status == Status.WAITING_VAD_START) {
                updateStatus(Status.WAITING_ASR_END);
                nextAsrEndTimeoutTime.set(writeTime.get() + 5000);
                log.debug("nextAsrEndTimeoutTime: {}", nextAsrEndTimeoutTime);
            }
        } finally {
            lock.unlock();
        }
    }

    private static VadProcessor createVadProcessor(Config config, VadProcessorCallback callback) {
        VadProcessor processor = new VadProcessor(callback);
        processor.setMutePointValueGate(config.vadMutePointValueGate);
        processor.setMuteGateFrameCount(config.vadMuteGateFrameCount);
        processor.setMaxRecordTime(config.vadMaxRecordTime);
        processor.setMaxStallTime(config.vadMaxStallTime);
        processor.setVoiceCountGate(config.vadVoiceCountGate);
        try {
            Field field = VadProcessor.class.getDeclaredField("lastVoiceEndFrameIndex");
            field.setAccessible(true);
            field.setInt(processor, 1);
        } catch (Exception e) {
            log.warn("LogHub_Warn VadProcessor底层实现变更", e);
        }
        log.debug("createVadProcessor, vad:{}", JsonUtils.object2String(processor));
        return processor;
    }

    @Override
    public void processUserSay(String userSayText, boolean userSayFinish, int beginTime, int endTime) {
        lock.lock();
        try {
            log.debug("processUserSay, text:{}, finish:{}, begin: {}, end:{}, writeTime:{}, status:{}",
                    userSayText, userSayFinish, beginTime, endTime, writeTime.get(), status);
            if (userSayFinish) {
                // 如果用户输入完成, 且开启了 asr 优化, 则加入到队列中,
                endTime = calLastUserSayEndTime(endTime, writeTime.get());
                lastUserSayFinishEndTime.set(endTime);
                // 计算 vad start 信号超时时间
                nextVadStartTimeoutTime.set(lastUserSayFinishEndTime.get() + maxSentenceSilenceMs);
                updateStatus(Status.WAITING_VAD_START);
                log.debug("lastUserSayFinishEndTime:{}, nextVadStartTimeoutTime:{}",
                        lastUserSayFinishEndTime.get(), nextVadStartTimeoutTime.get());

                DefaultSpeechDialogEngine.UserSayInfo userSayInfo = new DefaultSpeechDialogEngine.UserSayInfo();
                userSayInfo.setText(userSayText);
                userSayInfo.setBeginTime(beginTime);
                userSayInfo.setEndTime(endTime);
                userSayInfoQueue.add(userSayInfo);
                tryMerge();

                vadProcessor = createVadProcessor(config, this);
                writePreDataToVad();
            } else {
                if (status == Status.WAITING_ASR_START
                        || status == Status.WAITING_VAD_START
                        || status == Status.WAITING_ASR_END) {
                    updateStatus(Status.WAITING_ASR_END);
                    nextAsrEndTimeoutTime.set(writeTime.get() + 5000);
                }
                speechDialogEngine.realProcessUserSay(userSayText, userSayFinish, beginTime, endTime);
            }
        } finally {
            lock.unlock();
        }
    }

    private void writePreDataToVad() {
        int index = queueWriteIndex.get() - config.getVadProcessPreFrameCount();
        if (index < 0) {
            index = 0;
        }
        log.debug("writePreDataToVad, index:{}, queueWriteIndex:{}", index, queueWriteIndex.get());
        boolean logged = false;
        for (int i = index; i < queueWriteIndex.get(); i++) {
            VoiceInfo voiceInfo = queue.get(i % queue.size());
            if (voiceInfo != null && voiceInfo.data != null) {
                if (!logged) {
                    log.debug("writePreDataToVad, beginTime:{}", voiceInfo.beginTime);
                    logged = true;
                }
                vadProcessor.update(voiceInfo.data);
            }
        }
    }

    private int calLastUserSayEndTime(int endTime, int writeTime) {
        int tmpEndTime = writeTime - originMaxSentenceSilenceMs;
        if (tmpEndTime >= endTime) {
            return endTime;
        }
        return (tmpEndTime + endTime) / 2;
    }

    private void tryMerge() {
        if (userSayInfoQueue.isEmpty()) {
            return;
        }
        log.debug("before merge, userSayInfoQueue:{}", JsonUtils.object2String(userSayInfoQueue));
        // 将多个结果合并
        DefaultSpeechDialogEngine.UserSayInfo userSayInfo = userSayInfoQueue.poll();
        while (true) {
            DefaultSpeechDialogEngine.UserSayInfo nextUserSayInfo = userSayInfoQueue.poll();
            if (nextUserSayInfo == null) {
                break;
            }
            userSayInfo.setText(userSayInfo.getText() + nextUserSayInfo.getText());
            userSayInfo.setEndTime(nextUserSayInfo.getEndTime());
        }
        userSayInfoQueue.add(userSayInfo);
        log.debug("after merge, userSayInfoQueue:{}", JsonUtils.object2String(userSayInfoQueue));
    }

    @Override
    public void writeData(short[] data) {
        lock.lock();
        try {
            if (writeTime.compareAndSet(0, skipFrameCount.get() * 20)) {
                log.debug("init writeTime:{}", writeTime.get());
            }
            short[] copy = new short[data.length];
            System.arraycopy(data, 0, copy, 0, data.length);
            int beginTime = writeTime.getAndAdd(data.length / 8);
            int index = getQueueIndex(queueWriteIndex.getAndIncrement());
            VoiceInfo voiceInfo = queue.get(index);
            voiceInfo.data = copy;
            voiceInfo.beginTime = beginTime;

            switch (status) {
                case WAITING_VAD_START:
                    vadProcessor.update(data);
                    break;
                case INIT:
                case WAITING_ASR_START:
                case WAITING_ASR_END:
                default:
                    break;
            }
            tryEmitUserSayFinishEvent();
        } finally {
            lock.unlock();
        }
    }

    private int getQueueIndex(int indexValue) {
        return indexValue % queue.size();
    }

    private void updateStatus(Status status) {
        log.debug("updateStatus, preStatus:{}, status:{}, writeTime:{}",
                this.status, status, this.writeTime);
        this.status = status;
    }

    private void tryEmitUserSayFinishEvent() {
        if (userSayInfoQueue.isEmpty()) {
            return;
        }
        switch (status) {
            case INIT:
                log.warn("数据处理异常, status:{}, writeTime:{}, ",
                        status, writeTime.get());
                break;
            case WAITING_VAD_START:
                if (writeTime.get() > nextVadStartTimeoutTime.get()) {
                    log.debug("writeTime:{} > nextVadStartTimeoutTime:{}, 未等到 vad 开始的信号",
                            writeTime.get(), nextVadStartTimeoutTime.get());
                    DefaultSpeechDialogEngine.UserSayInfo userSayInfo = userSayInfoQueue.poll();
                    speechDialogEngine.realProcessUserSay(userSayInfo.getText(), true, (int) userSayInfo.getBeginTime(), (int) userSayInfo.getEndTime());
                    updateStatus(Status.INIT);
                }
                break;
            case WAITING_ASR_START:
                if (writeTime.get() > nextAsrStartTimeoutTime.get()) {
                    log.debug("writeTime:{} > nextAsrStartTimeoutTime:{}, 未等到 asr 开始的信号",
                            writeTime.get(), nextAsrStartTimeoutTime.get());
                    DefaultSpeechDialogEngine.UserSayInfo userSayInfo = userSayInfoQueue.poll();
                    speechDialogEngine.realProcessUserSay(userSayInfo.getText(), true, (int) userSayInfo.getBeginTime(), (int) userSayInfo.getEndTime());
                    updateStatus(Status.INIT);
                }
                break;
            case WAITING_ASR_END:
                if (writeTime.get() > nextAsrEndTimeoutTime.get()) {
                    log.warn("writeTime:{} > nextAsrEndTimeoutTime:{}, 未等到 asr 结束的信号",
                            writeTime.get(), nextAsrEndTimeoutTime.get());
                    DefaultSpeechDialogEngine.UserSayInfo userSayInfo = userSayInfoQueue.poll();
                    speechDialogEngine.realProcessUserSay(userSayInfo.getText(), true, (int) userSayInfo.getBeginTime(), (int) userSayInfo.getEndTime());
                    updateStatus(Status.INIT);
                }
            default:
                break;
        }
    }

    @Override
    public void onVoiceStart() {
        log.debug("VAD onVoiceStart, writeTime:{}", writeTime);
        if (status == Status.WAITING_VAD_START) {
            updateStatus(Status.WAITING_ASR_START);
            // 更新等待 asr 超时时间
            nextAsrStartTimeoutTime.set(writeTime.get() + config.asrResultTimeoutMs);
            log.debug("nextAsrStartTimeoutTime: {}", nextAsrStartTimeoutTime.get());
        }
    }

    @Override
    public void onVoiceEnd(long frameIndex) {
        log.debug("VAD onVoiceEnd, writeTime:{}", writeTime);
    }


    public int getWriteTime() {
        return writeTime.get();
    }

    public void setWriteTime(int value) {
        log.debug("set writeTime:{}", value);
        writeTime.set(value);
    }

    enum Status {
        INIT, WAITING_VAD_START, WAITING_ASR_START, WAITING_ASR_END
    }

    @Data
    static class Config {

        int asrResultTimeoutMs = 500;

        int sentenceSilenceDelayMs = 100;

        int vadMutePointValueGate = 500;

        int vadMuteGateFrameCount = 5;

        int vadMaxRecordTime = 300 * 1000;

        int vadMaxStallTime = 200;

        int vadVoiceCountGate = 2;

        int vadProcessPreFrameCount = 10;
    }

    static class VoiceInfo {
        volatile short[] data;
        volatile int beginTime;
    }

    public static void main(String[] args) throws Exception {
        String configJson = "{\n" +
                "    \"vadVoiceCountGate\": 5,\n" +
                "    \"vadMaxStallTime\": 200,\n" +
                "    \"vadMaxRecordTime\": 300000,\n" +
                "    \"vadMuteGateFrameCount\": 35,\n" +
                "    \"vadMutePointValueGate\": 1000,\n" +
                "    \"asrResultTimeoutMs\": 1000,\n" +
                "    \"sentenceSilenceDelayMs\": 0,\n" +
                "    \"vadProcessPreFrameCount\": 10\n" +
                "}";

        Config config = JsonUtils.string2Object(configJson, Config.class);
        AtomicInteger index = new AtomicInteger();
        VadProcessor vadProcessor = createVadProcessor(config, new VadProcessorCallback() {
            @Override
            public void onVoiceStart() {
                log.debug("onVoiceStart, index:{}", index.get() / 16);
            }

            @Override
            public void onVoiceEnd(long frameIndex) {
                log.debug("onVoiceEnd, index:{}", index.get() / 16);
            }
        });

        File file = new File("/Users/<USER>/Downloads/aaaa.wav");

        // 读取wav文件
        FileInputStream fis = new FileInputStream(file);
        fis.read(new byte[44]);
        byte[] data = new byte[320];

        vadProcessor.update(new short[160]);
        while (fis.read(data) != -1) {
            index.getAndAdd(data.length);
            short[] shorts = AudioHandleUtils.getShorts(data);
            vadProcessor.update(shorts);
        }

        System.out.println("end");

    }
}
