package com.yiwise.dialogflow.client.model.asr;


import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.util.List;

/**
 * 纠错模型信息
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AsrErrorCorrectionDetailPO extends BaseTimeUserIdPO {
    public static final String COLLECTION_NAME = "asrErrorCorrectionDetail";

    String asrErrorCorrectionDetailId;

    /**
     * 模型名称
     */
    String name;

    /**
     * 描述
     */
    String description;

    /**
     * 训练状态（0-未训练；1-训练中；2-已训练）
     */
    AsrErrorCorrectionTrainStatusEnum trainStatus;

    /**
     * 纠错阈值
     */
    @DecimalMin("0.01")
    @DecimalMax("1.00")
    BigDecimal threshold;

    /**
     * 纠错语料
     */
    List<String> corpusList;

    /**
     * 纠错白名单
     */
    List<String> whiteList;
}
