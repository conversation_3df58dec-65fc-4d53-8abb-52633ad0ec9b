package com.yiwise.dialogflow.client.asr;


import java.util.List;

public interface MuteAudioFilter {

    /**
     * 电话已经接通, 但是 asr 还未初始化完成, 记录此时跳过的音频数据
     */
    void waitingAsrInitSkipAudioData(int length);

    /**
     * 过滤音频
     * @param bytes 1帧音频数据
     * @return 待发送的音频数据列表(因为过滤器会缓存临时被过滤的音频, 后续可能会重新判断为发送)
     */
    List<FilterResult> filter(byte[] bytes, int offset, int length);

    List<AudioVoiceInfo> getFilterAudioInfo();

    /**
     * 将 asr 返回的时间, 转换为接通的时间
     * @param sendTime asr 返回的时间
     * @return 接通的时间
     */
    int convertToRealTime(int sendTime);

    int getWaitingSkipAudioData();

    int getTotalSkipDuration();

}
