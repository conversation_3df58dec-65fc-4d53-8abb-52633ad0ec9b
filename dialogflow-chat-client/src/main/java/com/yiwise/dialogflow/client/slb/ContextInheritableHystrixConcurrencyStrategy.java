package com.yiwise.dialogflow.client.slb;

import com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategy;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.concurrent.Callable;

@Slf4j
public class ContextInheritableHystrixConcurrencyStrategy extends HystrixConcurrencyStrategy {

    @Override
    public <T> Callable<T> wrapCallable(Callable<T> callable) {
        String mdc = MDC.get("MDC_LOG_ID");
        return new ChatServiceCallable<T>(BotChatContextService.getChatServerSelectContext(), mdc, RequestContextHolder.getRequestAttributes(), callable);
    }

    private static class ChatServiceCallable<T> implements Callable<T> {

        BotChatServerSelectContext context;
        private Callable<T> callable;
        private String mdcRequest;

        private RequestAttributes attributes;

        public ChatServiceCallable(BotChatServerSelectContext context, String mdc, RequestAttributes attributes, Callable<T> callable) {
            this.context = context;
            this.mdcRequest = mdc;
            this.attributes = attributes;
            this.callable = callable;
        }

        @Override
        public T call() throws Exception {
            try {
                MDC.put("MDC_LOG_ID", mdcRequest);
                log.info("context={}, attributes={}", context, attributes);
                BotChatContextService.setChatServerSelectContext(context);
                RequestContextHolder.setRequestAttributes(attributes, true);
                return callable.call();
            } finally {
                BotChatContextService.clearChatServerSelectContext();
                RequestContextHolder.resetRequestAttributes();
            }
        }
    }
}
