package com.yiwise.dialogflow.client.engine;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.yiwise.dialogflow.client.utils.AudioUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
public class AudioSizeCache {
    private static final Cache<String, Integer> cache =
            CacheBuilder.newBuilder()
                    .maximumSize(10000)
                    .expireAfterWrite(1, TimeUnit.MINUTES)
                    .build();

    private static final Lock lock = new ReentrantLock();


    public static int getAudioByteSize(String filePath) {
        Integer size = cache.getIfPresent(filePath);
        if (size == null || size < 1) {
            lock.lock();
            try {
                size = cache.getIfPresent(filePath);
                if (size == null || size < 1) {
                    size = calculateAudioByteSize(filePath);
                    cache.put(filePath, size);
                }
            } finally {
                lock.unlock();
            }
        }
        return size < 1 ? 0 : size;
    }

    private static int calculateAudioByteSize(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                return -1;
            }
            if (filePath.endsWith(".wav")) {
                // 计算WAV音频文件的字节大小
                return AudioUtils.getWavAudioFileSize(new File(filePath));
            }
            return (int) file.length();
        } catch (Exception e) {
            log.warn("[LogHub_Warn]计算音频大小失败：{}", filePath, e);
            return -1;
        }
    }
}
