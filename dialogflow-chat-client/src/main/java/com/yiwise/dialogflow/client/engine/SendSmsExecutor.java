package com.yiwise.dialogflow.client.engine;

import com.yiwise.dialogflow.engine.share.action.SendSmsAction;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.function.Consumer;

@Slf4j
public class SendSmsExecutor {

    /**
     * 已发送的短信模板id列表
     */
    private Set<Long> sendSmsTemplateIdList = new HashSet<>();

    private Consumer<Collection<Long>> sendSmsConsumer;

    public SendSmsExecutor() {
        this.sendSmsConsumer = smsTemplateIdList -> {
            log.warn("未设置发送短信的消费者");
        };
    }

    public void setSendSmsConsumer(Consumer<Collection<Long>> sendSmsConsumer) {
        this.sendSmsConsumer = sendSmsConsumer;
    }

    public void execute(List<SendSmsAction> actionList) {
        if (CollectionUtils.isEmpty(actionList)) {
            return;
        }

        Set<Long> templateIdList = new HashSet<>();
        for (SendSmsAction sendSmsAction : actionList) {
            sendSmsAction.getSmsTemplateIdList().stream()
                    .filter(templateId -> !sendSmsTemplateIdList.contains(templateId))
                    .forEach(templateIdList::add);
        }
        if (CollectionUtils.isNotEmpty(templateIdList)) {
            log.info("发送短信模板id列表: {}", templateIdList);
            sendSmsTemplateIdList.addAll(templateIdList);
            try {
                sendSmsConsumer.accept(templateIdList);
            } catch (Exception e) {
                log.warn("执行发送短信回调失败, 模板id列表: {}", templateIdList, e);
            }
        }
    }

    public void execute(SendSmsAction action) {
        execute(Collections.singletonList(action));
    }
}
