package com.yiwise.dialogflow.client.engine;

import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @date 2023/5/25
 */
@Slf4j
public class OneTimeAddWechatExecutor {

    private Runnable addWechatCallback;
    private RobotSnapshotUsageTargetEnum usageTarget;
    private final AtomicBoolean executed = new AtomicBoolean(false);

    public OneTimeAddWechatExecutor(RobotSnapshotUsageTargetEnum usageTarget) {
        this.usageTarget = usageTarget;
        this.addWechatCallback = () -> {};
    }

    public void setAddWechatCallback(Runnable addWechatCallback) {
        if (Objects.nonNull(addWechatCallback)) {
            this.addWechatCallback = addWechatCallback;
        }
    }

    public void execute() {
        try {
            log.info("[话术加微]收到加微指令");
            if (!RobotSnapshotUsageTargetEnum.CALL_OUT.equals(usageTarget)) {
                log.info("[话术加微]非外呼不执行加微操作");
                return;
            }
            if (executed.compareAndSet(false, true)) {
                log.info("[话术加微]开始");
                addWechatCallback.run();
                log.info("[话术加微]结束");
            } else {
                log.info("[话术加微]已经执行过加微操作");
            }
        } catch (Exception e) {
            log.error("[话术加微]失败", e);
        }
    }
}
