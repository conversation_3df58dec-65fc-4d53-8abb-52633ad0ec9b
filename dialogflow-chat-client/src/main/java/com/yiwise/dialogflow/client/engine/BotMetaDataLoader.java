package com.yiwise.dialogflow.client.engine;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.client.service.BotMetaDataService;
import com.yiwise.dialogflow.engine.share.BotMetaData;
import com.yiwise.dialogflow.engine.share.MagicActivityConfig;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import javaslang.Tuple;
import javaslang.Tuple3;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
public class BotMetaDataLoader {

    private static final ReentrantLock lock = new ReentrantLock();

    private static final BotMetaDataService botMetaDataService = AppContextUtils.getBean(BotMetaDataService.class);

    private static final Cache<Key, BotMetaDataWrap> COLD_CACHE = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(12, TimeUnit.HOURS)
            .build();

    private static final Cache<Key, BotMetaDataWrap> HOT_CACHE = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    private static final Cache<String, Integer> BOT_VERSION_CACHE = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(1, TimeUnit.SECONDS)
            .build();

    private static final Cache<Tuple3<Long, RobotSnapshotUsageTargetEnum, Long>, MagicActivityConfig> MAGIC_AUDIO_CONFIG_MAP = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    // 这里其实不应该在任务中检查话术的新版本, 因为任务导入的时候其实是依赖的一个版本, 之后如果变更了, 可能后面的依赖的东西已经变了
    public static BotMetaData getBotMetaData(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget) {
        BotMetaDataWrap wrap = getRuntimeResource(new Key(dialogFlowId, usageTarget));
        log.info("dialogFlowId={}, botId={}, version={}, name:{}", dialogFlowId, wrap.getData().getBotId(), wrap.getVersion(), wrap.getData().getName());
        return wrap.data;
    }

    private static BotMetaDataWrap getRuntimeResource(Key key) {
        BotMetaDataWrap resource;
        if (RobotSnapshotUsageTargetEnum.TEXT_TEST.equals(key.getUsageTarget())
                || RobotSnapshotUsageTargetEnum.SPEECH_TEST.equals(key.getUsageTarget())) {
            // 如果是文本/语音测试, 则检查并加载最新的版本
            resource = checkAndLoadResource(key);
        } else {
            resource = HOT_CACHE.getIfPresent(key);
        }
        if (Objects.isNull(resource)) {
            lock.lock();
            try {
                resource = HOT_CACHE.getIfPresent(key);
                if (Objects.isNull(resource)) {
                    resource = checkAndLoadResource(key);
                    HOT_CACHE.put(key, resource);
                }
            } finally {
                lock.unlock();
            }
        }
        return resource;
    }

    private static BotMetaDataWrap checkAndLoadResource(Key key) {
        BotMetaDataWrap coldCache = COLD_CACHE.getIfPresent(key);
        Integer lastVersion = getLastVersionWithRetry(key.getDialogFlowId(), key.getUsageTarget());
        if (Objects.isNull(lastVersion) || lastVersion < 0) {
            if (Objects.isNull(coldCache)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("加载话术元信息失败, 无法对话, cacheKey=%s", key));
            } else {
                return coldCache;
            }
        }
        if (Objects.nonNull(coldCache) && lastVersion.equals(coldCache.getVersion())) {
            // 没有新版本
            return coldCache;
        }

        BotMetaDataWrap newVersionCache = loadResource(key.getDialogFlowId(), key.getUsageTarget(), lastVersion);
        if (Objects.nonNull(newVersionCache)) {
            return newVersionCache;
        } else if (Objects.nonNull(coldCache)) {
            return coldCache;
        } else {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("加载话术元信息失败, 无法对话, key=%s", key));
        }
    }

    public static Integer getLastVersionWithRetry(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget) {
        String cacheKey = dialogFlowId + "_" + usageTarget;
        Integer cacheVersion = BOT_VERSION_CACHE.getIfPresent(cacheKey);
        if (Objects.nonNull(cacheVersion)) {
            return cacheVersion;
        }

        Integer lastVersion = null;
        int maxRetry = 3;
        for (int i = 0; i < maxRetry; i++) {
            try {
                lastVersion = botMetaDataService.getLastVersion(dialogFlowId, usageTarget);
                if (Objects.nonNull(lastVersion)) {
                    break;
                }

            } catch (Exception e) {
                log.warn("获取话术最新版本失败, dialogFlowId={}, usageTarget={}, retry={}", dialogFlowId, usageTarget, i, e);
            }
            // 延迟随机时间, 避免同时重试
            try {
                Thread.sleep((long) (Math.random() * 1000));
            } catch (InterruptedException e) {
                log.warn("获取话术最新版本失败, dialogFlowId={}, usageTarget={}, retry={}", dialogFlowId, usageTarget, i, e);
            }
        }
        if (Objects.nonNull(lastVersion)) {
            BOT_VERSION_CACHE.put(cacheKey, lastVersion);
        }
        return lastVersion;
    }

    public static BotMetaData loadNewestResource(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget) {
        if (Objects.isNull(dialogFlowId) || Objects.isNull(usageTarget)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "dialogFlowId和usageTarget不能为空");
        }
        Key key = new Key(dialogFlowId, usageTarget);
        BotMetaDataWrap resource= checkAndLoadResource(key);
        if (Objects.nonNull(resource)) {
            lock.lock();
            try {
                HOT_CACHE.put(key, resource);
            } finally {
                lock.unlock();
            }
            return resource.data;
        }
        return null;
    }

    public static MagicActivityConfig getMagicActivityConfig(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version, Long callJobId) {
        Tuple3<Long, RobotSnapshotUsageTargetEnum, Long> key = Tuple.of(botId, usageTarget, callJobId);
        MagicActivityConfig magicActivityConfig = MAGIC_AUDIO_CONFIG_MAP.getIfPresent(key);
        if (magicActivityConfig == null) {
            magicActivityConfig = botMetaDataService.getMagicActivityConfig(botId, usageTarget, version, callJobId);
            if (Objects.nonNull(magicActivityConfig)) {
                MAGIC_AUDIO_CONFIG_MAP.put(key, magicActivityConfig);
            }
        }
        return magicActivityConfig;
    }

    private static BotMetaDataWrap loadResource(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        BotMetaData resourceOpt = doLoadResourceWithRetry(dialogFlowId, usageTarget, version);
        if (Objects.nonNull(resourceOpt)) {
            BotMetaDataWrap wrap = new BotMetaDataWrap();
            wrap.setData(resourceOpt);
            wrap.setVersion(version);
            COLD_CACHE.put(new Key(dialogFlowId, usageTarget), wrap);
            return wrap;
        }
        return null;
    }

    private static BotMetaData doLoadResourceWithRetry(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget, Integer version) {
        BotMetaData resource = null;
        int maxRetry = 3;
        for (int i = 0; i < maxRetry; i++) {
            try {
                resource = botMetaDataService.getBotMetaData(dialogFlowId, usageTarget, version);
                if (Objects.nonNull(resource)) {
                    resource.setDialogFlowId(dialogFlowId);
                    break;
                }
                // 延迟随机时间, 避免同时重试
                Thread.sleep((long) (Math.random() * 1000));
            } catch (Exception e) {
                log.warn(String.format("加载话术元信息失败, dialogFlowId=%d, usageTarget=%s, version=%d, retry=%d", dialogFlowId, usageTarget, version, i), e);
            }
        }
        return resource;
    }

    @Data
    static class BotMetaDataWrap {
        BotMetaData data;
        int version;
    }

    @Data
    @EqualsAndHashCode
    public static class Key {
        Long dialogFlowId;
        RobotSnapshotUsageTargetEnum usageTarget;

        public Key(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget) {
            this.dialogFlowId = dialogFlowId;
            this.usageTarget = usageTarget;
        }
    }
}
