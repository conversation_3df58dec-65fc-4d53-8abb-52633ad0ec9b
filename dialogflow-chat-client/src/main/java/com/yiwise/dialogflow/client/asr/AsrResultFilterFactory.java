package com.yiwise.dialogflow.client.asr;

import com.yiwise.dialogflow.client.engine.SpeechDialogEngine;
import com.yiwise.dialogflow.client.engine.asr.BaseAsrContext;
import com.yiwise.dialogflow.client.engine.asr.V3AsrContextFactory;
import com.yiwise.dialogflow.engine.share.BotMetaData;
import org.apache.commons.lang3.BooleanUtils;

public class AsrResultFilterFactory {

    public static AsrResultFilter create(BotMetaData botMetaData, SpeechDialogEngine engine) {
        BaseAsrContext asrContext = V3AsrContextFactory.createContext(botMetaData.getDialogFlowId(), botMetaData.getUsageTarget());
        if (BooleanUtils.isTrue(asrContext.getEnableAsrOptimization())) {
            return new DefaultAsrResultFilter(botMetaData, engine);
        }
        return new NoopAsrResultFilter(engine);
    }

}
