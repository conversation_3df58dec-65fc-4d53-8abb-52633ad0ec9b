package com.yiwise.dialogflow.client.engine.asr;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.string.MyRandomStringUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.client.engine.BotMetaDataLoader;
import com.yiwise.dialogflow.client.service.BotMetaDataService;
import com.yiwise.dialogflow.engine.share.BotMetaData;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.engine.share.model.BotAsrConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.MDC;

import java.util.*;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;


/**
 * <AUTHOR>
 * @date 2022/11/9
 */
@Slf4j
public class V3AsrContextFactory {
    public static BaseAsrContext createContext(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget) {
        return createV3AsrContext(dialogFlowId, usageTarget);
    }

    private static final BotMetaDataService botMetaDataService = AppContextUtils.getBean(BotMetaDataService.class);

    // asrContext中有一部分是需要实时获取的, 不需要从bot快照中获取
    // 1. asrProvider, 可能出现的情况是后端批量更新asrProvider信息, 不需要发布审核, 即时生效
    // 2. asr热词配置信息, 因为有个需求是在每个通话中记录当前热词的配置信息, 方便ait排查问题, 而热词的更新是和bot无关的, 所以也需要实时获取

    // 这里采用异步更新的方式更新这两个部分
    private static final Cache<Long, BotAsrConfig> REAL_ASR_CONFIG = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build();

    private static final AtomicReference<Set<BotMetaDataLoader.Key>> asyncRequestBotIds = new AtomicReference<>(null);

    private static final Thread thread = new Thread(V3AsrContextFactory::asyncFlush);

    static {
        init();
    }

    public static void init() {
        thread.setName("V3AsrContextFactory-AsyncFlush");
        thread.setDaemon(true);
        thread.start();
    }

    private static void asyncFlush() {
        log.info("start asyncFlush asrContext");
        while (true) {
            try {
                doAsyncFlush();
            } catch (Exception e) {
                log.error("asyncFlush error", e);
            }
            try {
                Thread.sleep(5 * 60 * 1000);
            } catch (InterruptedException e) {
                log.error("asyncFlush error", e);
            }
        }
    }

    private static void doAsyncFlush() {
        MDC.put("MDC_LOG_ID", MyRandomStringUtils.getRandomStringByLength(8));
        try {
            Set<BotMetaDataLoader.Key> keySet = asyncRequestBotIds.getAndSet(null);
            if (CollectionUtils.isEmpty(keySet)) {
                return;
            }
            keySet.forEach(key -> {
                try {
                    log.info("asyncFlush, key={}", key);
                    BotMetaData botMetaData = BotMetaDataLoader.getBotMetaData(key.getDialogFlowId(), key.getUsageTarget());
                    if (Objects.nonNull(botMetaData)) {
                        try {
                            BotAsrConfig botAsrConfig = botMetaDataService.getBotRealAsrConfig(botMetaData.getBotId(), key.getUsageTarget(), botMetaData.getVersion());
                            if (Objects.nonNull(botAsrConfig)) {
                                log.info("异步更新asrContext， botId:{}, asrConfig:{}", botMetaData.getBotId(), JsonUtils.object2String(botAsrConfig));
                                REAL_ASR_CONFIG.put(botMetaData.getBotId(), botAsrConfig);
                            }
                        } catch (Exception e) {
                            log.warn("asyncFlush error, botId={}", botMetaData.getBotId(), e);
                        }
                    }
                } catch (Exception e) {
                    log.error("asyncFlush error, botId={}", key.getDialogFlowId(), e);
                }
            });
        } finally {
            MDC.remove("MDC_LOG_ID");
        }
    }

    /**
     * 添加创建asrContext的请求
     * 线程安全的
     */
    private static void addRequest(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget) {
        Set<BotMetaDataLoader.Key> botIdSet = asyncRequestBotIds.get();
        while (botIdSet == null) {
            botIdSet = new CopyOnWriteArraySet<>();
            if (asyncRequestBotIds.compareAndSet(null, botIdSet)) {
                break;
            }
            botIdSet = asyncRequestBotIds.get();
        }
        botIdSet.add(new BotMetaDataLoader.Key(dialogFlowId, usageTarget));
    }

    /**
     * todo asrProvider和热词信息需要获取实时配置数据, 不能从bot元数据中获取
     */
    private static BaseAsrContext createV3AsrContext(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget) {
        BaseAsrContext asrContext = new BaseAsrContext();

        BotMetaData botMetaData = BotMetaDataLoader.getBotMetaData(dialogFlowId, usageTarget);
        if (Objects.isNull(botMetaData)) {
            log.warn("[LogHub_Warn]dialogFlowId={}，usageTarget={}, 加载bot元数据为空", dialogFlowId, usageTarget);
            throw new ComException(ComErrorCode.NOT_EXIST, "dialogFlowId=" + dialogFlowId + "，usageTarget=" + usageTarget + ", 加载bot元数据为空");
        }

        addRequest(dialogFlowId, usageTarget);
        BotAsrConfig botAsrConfig = REAL_ASR_CONFIG.getIfPresent(botMetaData.getBotId());
        if (Objects.isNull(botAsrConfig)) {
            botAsrConfig = botMetaData.getBotAsrConfig();
        }
        asrContext.setMaxSentenceSilence(botAsrConfig.getMaxSentenceSilence());
        asrContext.setAsrProvider(botAsrConfig.getAsrProvider());
        asrContext.setAsrAppkey(botAsrConfig.getAsrAppkey());
        asrContext.setAsrParam(botAsrConfig.getAsrParam());

        asrContext.setAsrVocabDetailId(botAsrConfig.getAsrVocabDetailId());
        asrContext.setAsrVocabId(botAsrConfig.getAsrVocabId());

        asrContext.setAsrSelfLearningDetailId(botAsrConfig.getAsrSelfLearningDetailId());
        asrContext.setAsrSelfLearningId(botAsrConfig.getAsrSelfLearningId());

        asrContext.setAsrErrorCorrectionDetailId(botAsrConfig.getAsrErrorCorrectionDetailId());
        asrContext.setAsrCorrectionThreshold(botAsrConfig.getAsrCorrectionThreshold());
        asrContext.setMaxSentenceSilence(botAsrConfig.getMaxSentenceSilence());
        if (Objects.isNull(asrContext.getMaxSentenceSilence())) {
            asrContext.setMaxSentenceSilence(350);
        }
        if (CollectionUtils.isNotEmpty(botAsrConfig.getAsrVocabDetailContentList())) {
            asrContext.setAsrVocabDetailContentList(botAsrConfig.getAsrVocabDetailContentList());
        } else {
            asrContext.setAsrVocabDetailContentList(Collections.emptyList());
        }

        asrContext.setEnableAsrOptimization(BooleanUtils.isTrue(botAsrConfig.getEnableAsrOptimization()));

        log.info("初始化v3 ASR上下文，asrContext:{}", JsonUtils.object2String(asrContext));
        return asrContext;
    }
}
