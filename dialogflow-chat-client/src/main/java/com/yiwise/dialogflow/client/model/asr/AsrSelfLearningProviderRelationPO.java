package com.yiwise.dialogflow.client.model.asr;


import com.yiwise.base.model.bean.po.BaseTimePO;
import com.yiwise.dialogflow.engine.share.enums.AsrProviderEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * 自学习模型与供应商关联表
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AsrSelfLearningProviderRelationPO extends BaseTimePO implements Serializable {

    Long asrSelfLearningProviderRelationId;

    /**
     * 自学习模型主键
     */
    Long asrSelfLearningId;

    /**
     * 供应商名称
     */
    AsrProviderEnum provider;

    /**
     * 供应商侧模型id
     */
    String providerModelId;

    /**
     * 训练状态
     */
    AsrSlefLearningTrainStatusEnum trainStatus;

    /**
     * 厂商侧数据集id（腾讯无此参数）
     */
    String dataId;

}
