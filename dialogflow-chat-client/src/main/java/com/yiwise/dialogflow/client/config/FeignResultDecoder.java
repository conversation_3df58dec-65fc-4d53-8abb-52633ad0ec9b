package com.yiwise.dialogflow.client.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import feign.FeignException;
import feign.Response;
import feign.Util;
import feign.codec.DecodeException;
import feign.codec.Decoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;

public class FeignResultDecoder implements Decoder {
    private static final Logger log = LoggerFactory.getLogger(FeignResultDecoder.class);

    public FeignResultDecoder() {
    }

    public Object decode(Response response, Type type) throws FeignException {
        try {
            if (response.body() == null) {
                throw new DecodeException(response.status(), "没有返回有效的数据", response.request());
            } else {

                String bodyStr = readResponseBodyJson(response);
                ResultObject result = parseRpcResult(bodyStr, type);
                if (!result.isSuccess()) {
                    throw new DecodeException(response.status(), result.getResultMsg(), response.request());
                } else {
                    return result.getData();
                }
            }
        } catch (Throwable var5) {
            throw var5;
        }
    }

    private String readResponseBodyJson(Response response) {
        try {
            return Util.toString(response.body().asReader());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private static ResultObject parseRpcResult(String bodyString, Type type) {
        JSONObject jsonObject = JSON.parseObject(bodyString);
        Integer code = (Integer)jsonObject.get("code");
        if (ComErrorCode.SUCCESS.getCode().equals(code)) {
            Object data = jsonObject.get("data");
            data = JSON.parseObject(JSON.toJSONString(data), type, new Feature[0]);
            return ResultObject.success(data);
        } else {
            log.error("rpc请求异常.result=[{}]", bodyString);
            String resultMsg = (String)jsonObject.get("resultMsg");
            return new ResultObject(code, (Object)null, resultMsg, (String)null);
        }

    }

    public static void main(String[] args) {
        String bodyString = "{\"code\":200,\"data\":\"测试\",\"resultMsg\":\"成功\"}";

        ResultObject<String> result = parseRpcResult(bodyString, String.class);

        System.out.println(result);
    }
}

