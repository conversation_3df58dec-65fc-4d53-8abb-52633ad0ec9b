package com.yiwise.dialogflow.client.model.asr;


import com.yiwise.base.model.bean.po.BaseTimePO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * asr自学习模型信息表
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AsrSelfLearningDetailPO extends BaseTimePO implements Serializable {

	Long asrSelfLearningDetailId;

	/**
	 * 模型名称
	 */
	String name;

	/**
	 * 描述
	 */
	String description;

	/**
	 * 开启状态（0-停用；1-开启）
	 */
	Integer status;

	/**
	 * 语料地址
	 */
	String url;

}
