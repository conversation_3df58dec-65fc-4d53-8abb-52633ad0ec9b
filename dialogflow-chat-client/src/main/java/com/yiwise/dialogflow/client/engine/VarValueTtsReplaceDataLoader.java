package com.yiwise.dialogflow.client.engine;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.yiwise.dialogflow.api.VarValueTtsReplaceClient;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.dialogflow.api.dto.response.VarValueTtsReplaceDTO;
import com.yiwise.middleware.tts.convert.CustomVarConvertItem;
import com.yiwise.middleware.tts.enums.TtsProviderEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

@Slf4j
public class VarValueTtsReplaceDataLoader {

    private static final VarValueTtsReplaceClient varValueTtsReplaceClient = AppContextUtils.getBean(VarValueTtsReplaceClient.class);

    private static final LoadingCache<Long, CacheValue> cache = CacheBuilder.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .maximumSize(1000)
            .build(CacheLoader.from((tenantId) -> new CacheValue()));

    private static final Map<Long, Lock> lockMap = new ConcurrentHashMap<>();

    private static final long RELOAD_INTERVAL = 10 * 60 * 1000;

    private static final BlockingQueue<Long> queue = new ArrayBlockingQueue<>(1024);

    private static final Thread thread = new Thread(VarValueTtsReplaceDataLoader::run);

    static {
        thread.setName("VarValueTtsReplaceDataLoader");
        thread.setDaemon(true);
        thread.start();
    }

    private static void run() {
        while (true) {
            try {
                Long tenantId = queue.take();
                if (tenantId < 1L) {
                    log.debug("tenantId:{} is invalid", tenantId);
                    continue;
                }

                reload(tenantId);
            } catch (Exception e) {
                log.warn("VarValueTtsReplaceDataLoader error", e);
            }
        }
    }

    private static void reload(Long tenantId) {
        log.debug("开始加载租户:{}读音值数据", tenantId);
        List<VarValueTtsReplaceDTO> items = varValueTtsReplaceClient.queryByTenantId(tenantId);
        log.debug("tenantId:{}, 读音值替换数据:{}", tenantId, items);
        CacheValue cacheValue = new CacheValue();
        cacheValue.items = convert(items);
        cacheValue.nextReloadTime.set(System.currentTimeMillis() + RELOAD_INTERVAL);
        cache.put(tenantId, cacheValue);
    }

    private static List<CustomVarConvertItem> convert(List<VarValueTtsReplaceDTO> list) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }
        return list.stream()
                .map(item -> {
                    CustomVarConvertItem convertItem = new CustomVarConvertItem();
                    convertItem.setReplacement(item.getReplacement());
                    convertItem.setVarValue(item.getVarValue());
                    convertItem.setVarName(item.getVarName());
                    convertProvider(item.getTtsProviderCode()).ifPresent(convertItem::setProvider);
                    return convertItem;
                })
                .filter(item -> item.getProvider() != null)
                .collect(Collectors.toList());
    }

    private static Optional<TtsProviderEnum> convertProvider(Integer providerCode) {
        return Arrays.stream(TtsProviderEnum.values()).filter(item -> item.getCode().equals(providerCode))
                .findAny();
    }

    public static List<CustomVarConvertItem> getByTenantId(Long tenantId) {
        if (tenantId == null || tenantId < 1L) {
            log.debug("tenantId:{} is invalid", tenantId);
            return Collections.emptyList();
        }

        long now = System.currentTimeMillis();
        CacheValue cacheValue = null;
        try {
            cacheValue = cache.get(tenantId);

            if (cacheValue.nextReloadTime.get() < 1L) {
                Lock lock = lockMap.computeIfAbsent(tenantId, (i) -> new ReentrantLock());
                lock.lock();
                try {
                    try {
                        cacheValue = cache.get(tenantId);
                        if (cacheValue.nextReloadTime.get() < 1L) {
                            log.debug("第一次同步加载数据 tenantId={}", tenantId);
                            reload(tenantId);
                            cacheValue = cache.get(tenantId);
                        } else {
                            log.debug("第一次同步加载数据 tenantId={} 已经加载过", tenantId);
                        }
                    } catch (Exception e) {
                        log.warn("第一次同步加载数据异常 tenantId=" + tenantId, e);
                    }
                } finally {
                    lock.unlock();
                }
            }

            long nextReloadTime = cacheValue.nextReloadTime.get();
            if (nextReloadTime < now) {
                long nextReload = nextReloadTime + RELOAD_INTERVAL;
                if (cacheValue.nextReloadTime.compareAndSet(nextReloadTime, nextReload)) {
                    queue.offer(tenantId, 1, TimeUnit.SECONDS);
                }
            }
            return cacheValue.items;
        } catch (Exception e) {
            log.warn("VarValueTtsReplaceDataLoader error", e);
        }
        return Collections.emptyList();
    }

    private static class CacheValue {
        List<CustomVarConvertItem> items = new ArrayList<>();
        AtomicLong nextReloadTime = new AtomicLong(0);
    }

}
