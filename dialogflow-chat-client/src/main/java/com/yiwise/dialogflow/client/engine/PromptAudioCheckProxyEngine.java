package com.yiwise.dialogflow.client.engine;

import com.yiwise.base.common.thread.ApplicationExecutor;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.dialogflow.client.asr.AsrResultFilter;
import com.yiwise.dialogflow.client.asr.DefaultAsrResultFilter;
import com.yiwise.dialogflow.client.listener.*;
import com.yiwise.dialogflow.client.model.engine.SimpleUserSayResultInfo;
import com.yiwise.dialogflow.engine.share.BotMetaData;
import com.yiwise.dialogflow.engine.share.CallDataInfo;
import com.yiwise.dialogflow.engine.share.IntentLevelAnalysisResult;
import com.yiwise.dialogflow.engine.share.SystemMetaData;
import com.yiwise.dialogflow.engine.share.response.SessionInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Nullable;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Consumer;

/**
 * 检查运营商提示音代理引擎,
 */
public class PromptAudioCheckProxyEngine implements SpeechDialogEngine, AudioPlayEventListener, UserVoiceEventListener, FastHangupListener, KeyCaptureListener  {

    private static final Logger log = LoggerFactory.getLogger(PromptAudioCheckProxyEngine.class);
    private volatile DefaultSpeechDialogEngine realSpeechDialogEngine;

    private final Lock lock = new ReentrantLock(true);

    private final BotMetaData botMetaData;
    private final List<Runnable> addWechatCallbackList = new ArrayList<>();
    private final List<Runnable> switchToHumanServiceExecutorList = new ArrayList<>();
    private final List<Runnable> humanInterventionExecutorList = new ArrayList<>();
    private final List<Consumer<Collection<Long>>> sendSmsConsumerList = new ArrayList<>();
    private final List<CallDetailListener> callDetailListenerList = new ArrayList<>();
    private final List<HangupEventListener> hangupEventListenerList = new ArrayList<>();

    private boolean enable;
    private final int timeout;
    private final List<String> postRegexList;
    private final List<String> preRegexList;
    private final SessionInfo backupSessionInfo;
    private final AudioPlayManager audioPlayManager;
    private final ApplicationExecutor executor;
    private final DTMFManager dtmfManager;
    private final SystemMetaData systemMetaData;

    private volatile Long engineResetTimestamp;

    public PromptAudioCheckProxyEngine(@Nullable Long tenantId,
                                       BotMetaData botMetaData,
                                       SessionInfo sessionInfo,
                                       AudioPlayManager audioPlayManager,
                                       ApplicationExecutor executor,
                                       DTMFManager dtmfManager) {
        this.systemMetaData = SystemMetaDataHelper.getSystemMetaData().orElse(null);
        this.botMetaData = botMetaData;
        this.backupSessionInfo = MyBeanUtils.copy(sessionInfo, SessionInfo.class);
        this.audioPlayManager = audioPlayManager;
        this.executor = executor;
        this.dtmfManager = dtmfManager;
        if (systemMetaData != null) {
            timeout = systemMetaData.getCheckPromptAudioBeforeSeconds() != null ? systemMetaData.getCheckPromptAudioBeforeSeconds() * 1000 : 0;
            postRegexList = systemMetaData.getOperatorPromptAudioRegexList();
            preRegexList = systemMetaData.getOperatorPromptAudioPreRegexList();
            enable = timeout > 0 && CollectionUtils.isNotEmpty(preRegexList) && CollectionUtils.isNotEmpty(postRegexList);
            if (CollectionUtils.isNotEmpty(systemMetaData.getDisablePromptAudioCheckTenantIdList())
                    && systemMetaData.getDisablePromptAudioCheckTenantIdList().contains(tenantId)) {
                enable = false;
            }
            if (BooleanUtils.isTrue(botMetaData.getDisablePromptAudioCheck())) {
                enable = false;
            }
        } else {
            log.warn("systemMetaData is null");
            timeout = 0;
            postRegexList = Collections.emptyList();
            preRegexList = Collections.emptyList();
            enable = false;
        }

        realSpeechDialogEngine = new DefaultSpeechDialogEngine(botMetaData, sessionInfo, audioPlayManager, executor, dtmfManager);
        log.debug("enable: {}, timeout: {}, preRegexList: {}, postRegexList:{}", enable, timeout, preRegexList, postRegexList);
    }

    @Override
    public void init() {
        realSpeechDialogEngine.init();
    }

    @Override
    public void enter() {
        realSpeechDialogEngine.enter();
    }

    @Override
    public void onSentenceBegin() {
        realSpeechDialogEngine.onSentenceBegin();
    }

    @Override
    public void processUserSay(String userSayText, boolean userSayFinish, int beginTime, int endTime) {
        log.debug("processUserSay: {}, {}, {}, {}", userSayText, userSayFinish, beginTime, endTime);
        if (enable && endTime < timeout) {
            lock.lock();
            try {
                // 用户已说完, 检测 postRegexList
                if (userSayFinish) {
                    if (checkHit(userSayText, postRegexList)) {
                        log.debug("用户已说完, 重置引擎");
                        resetEngine();
                        createAndCallbackRecord(userSayText, beginTime, endTime, "命中运营商提示音, 重置对话");
                        realSpeechDialogEngine.enter();
                    } else if (checkHit(userSayText, preRegexList)) {
                        log.debug("检测到 preRegexList, 先暂停音频播放");
                        audioPlayManager.pause();
                        createAndCallbackRecord(userSayText, beginTime, endTime, "命中运营商提示音, 暂停播放");
                    } else {
                        realSpeechDialogEngine.processUserSay(userSayText, userSayFinish, beginTime, endTime);
                    }
                } else {
                    // 用户没有说完, 检测 preRegexList 或 postRegexList
                    if (checkHit(userSayText, preRegexList)) {
                        log.debug("用户还未说完, 先暂停音频播放");
                        audioPlayManager.pause();
                        createAndCallbackRecord(userSayText, beginTime, endTime, "命中运营商提示音, 暂停播放");
                    } else {
                        realSpeechDialogEngine.processUserSay(userSayText, userSayFinish, beginTime, endTime);
                    }
                }
            } finally {
                lock.unlock();
            }
        } else {
            realSpeechDialogEngine.processUserSay(userSayText, userSayFinish, beginTime, endTime);
        }
    }

    private void createAndCallbackRecord(String userSayText, int beginTime, int endTime, String debugLog) {
        SimpleUserSayResultInfo userSayResultInfo = new SimpleUserSayResultInfo();
        userSayResultInfo.setUserInput(userSayText);
        userSayResultInfo.setBeginTime(beginTime);
        userSayResultInfo.setEndTime(endTime);
        List<String> debugLogList = Collections.singletonList(debugLog);
        userSayResultInfo.setDebugLogList(debugLogList);
        userSayResultInfo.setSimpleDebugLogList(debugLogList);
        for (CallDetailListener callDetailListener : callDetailListenerList) {
            callDetailListener.createUserSayRecord(userSayResultInfo);
        }
    }

    private boolean checkHit(String userSayText, List<String> regexList) {
        try {
            for (String regex : regexList) {
                if (userSayText.matches(regex)) {
                    log.debug("检测到运营商提示音, 命中正则: {}", regex);
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("[LogHub_Warn]检测运营商提示音命中正则时异常", e);
        }
        return false;
    }

    private void resetEngine() {
        log.debug("检测到运营商提示音, 提前释放当前 engine");
        engineResetTimestamp = System.currentTimeMillis();
        audioPlayManager.reset();
        realSpeechDialogEngine.finishByMatchPromptAudio();
        AsrResultFilter oldFilter = realSpeechDialogEngine.asrResultFilter;
        long oldAudioReadTime = realSpeechDialogEngine.getAudioReadTime();
        SessionInfo sessionInfo = MyBeanUtils.copy(backupSessionInfo, SessionInfo.class);
        realSpeechDialogEngine = new DefaultSpeechDialogEngine(botMetaData, sessionInfo, audioPlayManager, executor, dtmfManager);
        realSpeechDialogEngine.init();
        realSpeechDialogEngine.setAudioReadTime(oldAudioReadTime);
        AsrResultFilter newFilter = realSpeechDialogEngine.asrResultFilter;
        if (newFilter instanceof DefaultAsrResultFilter && oldFilter instanceof DefaultAsrResultFilter) {
            DefaultAsrResultFilter oldDefaultFilter = (DefaultAsrResultFilter) oldFilter;
            DefaultAsrResultFilter newDefaultFilter = (DefaultAsrResultFilter) newFilter;
            log.debug("reset AsrResultFilter");
            newDefaultFilter.setWaitAsrInitSkipAudioData(oldDefaultFilter.getWaitAsrInitSkipAudioData());
            newDefaultFilter.setWriteTime(oldDefaultFilter.getWriteTime());
        }
        addWechatCallbackList.forEach(realSpeechDialogEngine::registerAddWechatCallback);
        callDetailListenerList.forEach(realSpeechDialogEngine::registerCallDetailListener);
        hangupEventListenerList.forEach(realSpeechDialogEngine::registerHangupEventListener);
        sendSmsConsumerList.forEach(realSpeechDialogEngine::registerSendSmsConsumer);
        switchToHumanServiceExecutorList.forEach(realSpeechDialogEngine::registerSwitchToHumanServiceExecutor);
        humanInterventionExecutorList.forEach(realSpeechDialogEngine::registerHumanInterventionExecutor);
    }

    @Override
    public void realProcessUserSay(String userSayText, boolean userSayFinish, int beginTime, int endTime) {
        realSpeechDialogEngine.realProcessUserSay(userSayText, userSayFinish, beginTime, endTime);
    }

    @Override
    public void registerCallDetailListener(CallDetailListener listener) {
        callDetailListenerList.add(listener);
        realSpeechDialogEngine.registerCallDetailListener(listener);
    }

    @Override
    public void registerHangupEventListener(HangupEventListener hangupEventListener) {
        hangupEventListenerList.add(hangupEventListener);
        realSpeechDialogEngine.registerHangupEventListener(hangupEventListener);
    }

    @Override
    public String getSessionContextContent() {
        return realSpeechDialogEngine.getSessionContextContent();
    }

    @Override
    public DefaultSpeechDialogEngine.InterruptConfig getInterruptConfig() {
        return realSpeechDialogEngine.getInterruptConfig();
    }

    @Override
    public IntentLevelAnalysisResult intentLevelAnalysis(CallDataInfo callDataInfo) {
        callDataInfo.setEngineResetTimestamp(engineResetTimestamp);
        return realSpeechDialogEngine.intentLevelAnalysis(callDataInfo);
    }

    @Override
    public IntentLevelAnalysisResult intentLevelAnalysisInCall(CallDataInfo callDataInfo) {
        callDataInfo.setEngineResetTimestamp(engineResetTimestamp);
        return realSpeechDialogEngine.intentLevelAnalysisInCall(callDataInfo);
    }

    @Override
    public void release(String reason) {
        realSpeechDialogEngine.release(reason);
    }

    @Override
    public void registerAddWechatCallback(Runnable addWechatCallback) {
        addWechatCallbackList.add(addWechatCallback);
        realSpeechDialogEngine.registerAddWechatCallback(addWechatCallback);
    }

    @Override
    public void registerSwitchToHumanServiceExecutor(Runnable switchToHumanServiceExecutor) {
        switchToHumanServiceExecutorList.add(switchToHumanServiceExecutor);
        realSpeechDialogEngine.registerSwitchToHumanServiceExecutor(switchToHumanServiceExecutor);
    }

    @Override
    public void registerHumanInterventionExecutor(Runnable humanInterventionExecutor) {
        humanInterventionExecutorList.add(humanInterventionExecutor);
        realSpeechDialogEngine.registerHumanInterventionExecutor(humanInterventionExecutor);
    }

    @Override
    public void registerSendSmsConsumer(Consumer<Collection<Long>> sendSmsConsumer) {
        sendSmsConsumerList.add(sendSmsConsumer);
        realSpeechDialogEngine.registerSendSmsConsumer(sendSmsConsumer);
    }

    @Override
    public void onUserHangup() {
        realSpeechDialogEngine.onUserHangup();
    }

    @Override
    public void onAiHangup() {
        realSpeechDialogEngine.onAiHangup();
    }

    @Override
    public void finishByError() {
        realSpeechDialogEngine.finishByError();
    }

    @Override
    public void finishByMatchPromptAudio() {
        realSpeechDialogEngine.finishByMatchPromptAudio();
    }

    @Override
    public List<String> getAllUserSayContentList() {
        return realSpeechDialogEngine.getAllUserSayContentList();
    }

    @Override
    public String getLastAnswerId() {
        return realSpeechDialogEngine.getLastAnswerId();
    }

    @Override
    public void onReadData() {
        realSpeechDialogEngine.onReadData();
    }

    @Override
    public void onAiPlayBegin(int index) {
        realSpeechDialogEngine.onAiPlayBegin(index);
    }

    @Override
    public void onAiPlayEnd(int index) {
        realSpeechDialogEngine.onAiPlayEnd(index);
    }

    @Override
    public void onPause(int index) {
        realSpeechDialogEngine.onPause(index);
    }

    @Override
    public void onResume(int index) {
        realSpeechDialogEngine.onResume(index);
    }

    @Override
    public void onFastHangup(String reason) {
        realSpeechDialogEngine.onFastHangup(reason);
    }

    @Override
    public void onReplay() {
        realSpeechDialogEngine.onReplay();
    }

    @Override
    public void onSuccess(String result) {
        realSpeechDialogEngine.onSuccess(result);
    }

    @Override
    public void onFailed() {
        realSpeechDialogEngine.onFailed();
    }

    @Override
    public void writeData(short[] data) {
        realSpeechDialogEngine.writeData(data);
    }

    @Override
    public void userSilence() {
        realSpeechDialogEngine.userSilence();
    }

    @Override
    public void waitingAsrInitSkipAudioData(int length) {
        realSpeechDialogEngine.waitingAsrInitSkipAudioData(length);
    }

    @Override
    public void onUserVoiceBegin() {
        realSpeechDialogEngine.onUserVoiceBegin();
    }

    @Override
    public void onUserVoiceEnd() {
        realSpeechDialogEngine.onUserVoiceEnd();
    }
}
