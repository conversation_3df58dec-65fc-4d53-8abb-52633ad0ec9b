package com.yiwise.dialogflow.config;

import com.yiwise.base.common.web.RestTemplateResponseErrorHandler;
import com.yiwise.base.common.web.YiwiseRestTemplate;
import com.yiwise.middleware.mysql.config.MySQLConfig;
import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import org.apache.http.conn.ConnectionKeepAliveStrategy;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeader;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.http.MediaType;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import javax.sql.DataSource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;



public class CommonContextConfig {
    private static final Logger logger = LoggerFactory.getLogger(CommonContextConfig.class);
    private static final List<BasicHeader> defaultHeaders = Stream.of(
            new BasicHeader("Accept", "application/json, text/plain, */*"),
            new BasicHeader("Accept-Encoding", "gzip,deflate"),
            new BasicHeader("Accept-Language", "zh-CN,zh;q=0.9,zh-TW;q=0.8,pl;q=0.7"),
            new BasicHeader("User-Agent", "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.16 Safari/537.36"))
            .collect(Collectors.toList());

    @Bean
    public HttpComponentsClientHttpRequestFactory httpComponentsClientHttpRequestFactory() {
        HttpComponentsClientHttpRequestFactory factory = getHttpComponentsClientHttpRequestFactory();
        factory.setConnectTimeout(5_000);
        factory.setReadTimeout(30_000);
        factory.setConnectionRequestTimeout(10_000);
        return factory;
    }

    @Bean(name = "lowLatencyHttpComponentsClientHttpRequestFactory")
    public HttpComponentsClientHttpRequestFactory lowLatencyHttpComponentsClientHttpRequestFactory() {
        HttpComponentsClientHttpRequestFactory factory = getHttpComponentsClientHttpRequestFactory();
        factory.setReadTimeout(300);
        factory.setConnectTimeout(2000);
        factory.setConnectionRequestTimeout(2000);
        return factory;
    }

    @Bean(name = "longestLatencyHttpComponentsClientHttpRequestFactory")
    public HttpComponentsClientHttpRequestFactory longestLatencyHttpComponentsClientHttpRequestFactory() {
        HttpComponentsClientHttpRequestFactory factory = getHttpComponentsClientHttpRequestFactory();
        factory.setReadTimeout(1000 * 180);
        factory.setConnectTimeout(1000 * 180);
        factory.setConnectionRequestTimeout(5_000);
        return factory;
    }

    @Bean(name = "calloutHttpComponentsClientHttpRequestFactory")
    public HttpComponentsClientHttpRequestFactory calloutHttpComponentsClientHttpRequestFactory() {
        HttpComponentsClientHttpRequestFactory factory = getHttpComponentsClientHttpRequestFactory();
        factory.setConnectTimeout(3_000);
        factory.setReadTimeout(3_000);
        factory.setConnectionRequestTimeout(3_000);
        return factory;
    }

    @Bean
    public HttpComponentsClientHttpRequestFactory isvCallBackHttpComponentsClientHttpRequestFactory() {
        HttpComponentsClientHttpRequestFactory factory = getHttpComponentsClientHttpRequestFactory();
        // 建立连接的超时时间
        factory.setConnectTimeout(5_000);
        // 响应超时时间
        factory.setReadTimeout(5_000);
        // 从连接池获取超时时间
        factory.setConnectionRequestTimeout(5_000);
        return factory;
    }

    private HttpComponentsClientHttpRequestFactory getHttpComponentsClientHttpRequestFactory() {
        logger.info("==== httpComponentsClientHttpRequestFactory execute ====");

        ConnectionKeepAliveStrategy connectionKeepAliveStrategy = (httpResponse, httpContext) -> {
            // 在Connector中keepAliveTimeout可以配置连接空闲多久就关闭该连接，
            // 它的值默认是和connectionTimeout一样，而在server.xml中connectionTimeout值默认为20s。
            return 20_000;
        };

        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        // 整个连接池的并发
        connectionManager.setMaxTotal(300);
        // 每个主机的并发
        connectionManager.setDefaultMaxPerRoute(150);
        HttpClientBuilder builder = HttpClientBuilder.create();
        builder.setConnectionManager(connectionManager);
        builder.setDefaultHeaders(defaultHeaders);
        builder.setKeepAliveStrategy(connectionKeepAliveStrategy);
        return new HttpComponentsClientHttpRequestFactory(builder.build());
    }

    @Bean(name = "restTemplate")
    public RestTemplate restTemplate(MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter,
                                     HttpComponentsClientHttpRequestFactory httpComponentsClientHttpRequestFactory) {
        return buildRestTemplate(mappingJackson2HttpMessageConverter, httpComponentsClientHttpRequestFactory);
    }

    @Bean(name = "lowLatencyRestTemplate")
    public RestTemplate lowLatencyRestTemplate(MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter,
                                               HttpComponentsClientHttpRequestFactory lowLatencyHttpComponentsClientHttpRequestFactory) {
        return buildRestTemplate(mappingJackson2HttpMessageConverter, lowLatencyHttpComponentsClientHttpRequestFactory);
    }

    @Bean(name = "longestLatencyRestTemplate")
    public RestTemplate longestLatencyRestTemplate(MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter,
                                               HttpComponentsClientHttpRequestFactory longestLatencyHttpComponentsClientHttpRequestFactory) {
        return buildRestTemplate(mappingJackson2HttpMessageConverter, longestLatencyHttpComponentsClientHttpRequestFactory);
    }

    private RestTemplate buildRestTemplate(MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter,
                                           HttpComponentsClientHttpRequestFactory httpComponentsClientHttpRequestFactory) {
        logger.info("==== restTemplate execute ====");
        RestTemplate template = new YiwiseRestTemplate(httpComponentsClientHttpRequestFactory);
        List<HttpMessageConverter<?>> httpMessageConverterList = template.getMessageConverters();
        httpMessageConverterList.forEach((obj) -> {
            if (obj instanceof StringHttpMessageConverter) {
                ((StringHttpMessageConverter) obj).setDefaultCharset(StandardCharsets.UTF_8);
            }
        });
        httpMessageConverterList.add(mappingJackson2HttpMessageConverter);
        template.setErrorHandler(new RestTemplateResponseErrorHandler());
        return template;
    }

    @Bean
    @Primary
    public SqlSessionTemplate sqlSessionTemplate(@Qualifier("sqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory, ExecutorType.SIMPLE);
    }

    @Bean
    public DataSource batchDataSource() {
        return MySQLConfig.getDataSource("MASTER");
    }

    @Primary
    @Bean(name = "lowLatencyWebClient")
    public WebClient webClient() {
        HttpClient httpClient = HttpClient.create()
                .tcpConfiguration(client ->
                        // 设置连接超时时间
                client.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 1000 * 2)
                        .option(ChannelOption.SO_KEEPALIVE, true)
                        .option(ChannelOption.TCP_NODELAY, true)
                        .doOnConnected(conn -> conn
                                // 设置读超时时间
                                .addHandlerLast(new ReadTimeoutHandler(300, TimeUnit.MILLISECONDS))
                                // 设置写超时时间
                                .addHandlerLast(new WriteTimeoutHandler(300, TimeUnit.MILLISECONDS))));
        return WebClient.builder()
                .clientConnector( new ReactorClientHttpConnector(httpClient))
                .defaultHeaders(httpHeaders -> httpHeaders.add("Content-Type", MediaType.APPLICATION_JSON_VALUE))
                .exchangeStrategies(ExchangeStrategies.builder()
                        .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(1024 * 1024 * 2))
                        .build()
                ).build();
    }
    @Bean(name = "longestLatencyWebClient")
    public WebClient longestLatencyWebClient() {
        HttpClient httpClient = HttpClient.create()
                .tcpConfiguration(client ->
                        // 设置连接超时时间
                client.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 1000 * 2)
                        .option(ChannelOption.SO_KEEPALIVE, true)
                        .option(ChannelOption.TCP_NODELAY, true)
                        .doOnConnected(conn -> conn
                                // 设置读超时时间
                                .addHandlerLast(new ReadTimeoutHandler(60000, TimeUnit.MILLISECONDS))
                                // 设置写超时时间
                                .addHandlerLast(new WriteTimeoutHandler(300, TimeUnit.MILLISECONDS))));
        return WebClient.builder()
                .clientConnector( new ReactorClientHttpConnector(httpClient))
                .defaultHeaders(httpHeaders -> httpHeaders.add("Content-Type", MediaType.APPLICATION_JSON_VALUE))
                .exchangeStrategies(ExchangeStrategies.builder()
                        .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(1024 * 1024 * 2))
                        .build()
                ).build();
    }

    @Bean(name = "interruptWebClient")
    public WebClient interruptWebClient() {
        HttpClient httpClient = HttpClient.create()
                .tcpConfiguration(client ->
                        // 设置连接超时时间
                client.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 1000 * 2)
                        .option(ChannelOption.SO_KEEPALIVE, true)
                        .option(ChannelOption.TCP_NODELAY, true)
                        .doOnConnected(conn -> conn
                                // 设置读超时时间
                                .addHandlerLast(new ReadTimeoutHandler(100, TimeUnit.MILLISECONDS))
                                // 设置写超时时间
                                .addHandlerLast(new WriteTimeoutHandler(100, TimeUnit.MILLISECONDS))));
        return WebClient.builder()
                .clientConnector( new ReactorClientHttpConnector(httpClient))
                .defaultHeaders(httpHeaders -> httpHeaders.add("Content-Type", MediaType.APPLICATION_JSON_VALUE))
                .exchangeStrategies(ExchangeStrategies.builder()
                        .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(1024 * 1024 * 2))
                        .build()
                ).build();
    }


    @Bean(name = "remoteServiceWebClient")
    public WebClient remoteServiceWebClient(@Qualifier(value = "loadBalancerExchangeFilterFunction") ExchangeFilterFunction filterFunction) {
        HttpClient httpClient = HttpClient.create()
                .tcpConfiguration(client ->
                        // 设置连接超时时间
                client.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 1000 * 5)
                        .option(ChannelOption.SO_KEEPALIVE, true)
                        .option(ChannelOption.TCP_NODELAY, true)
                        .doOnConnected(conn -> conn
                                // 设置读超时时间
                                .addHandlerLast(new ReadTimeoutHandler(3000, TimeUnit.MILLISECONDS))
                                // 设置写超时时间
                                .addHandlerLast(new WriteTimeoutHandler(3000, TimeUnit.MILLISECONDS))));
        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .defaultHeaders(httpHeaders -> httpHeaders.add("Content-Type", MediaType.APPLICATION_JSON_VALUE))
                .exchangeStrategies(
                        ExchangeStrategies.builder()
                                .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(1024 * 1024 * 2))
                                .build()
                ).filter(filterFunction)
                .build();
    }

    @Bean(name = "queryNodeWebClient")
    public WebClient queryNodeWebClient() {
        HttpClient httpClient = HttpClient.create()
                .tcpConfiguration(client ->
                        // 设置连接超时时间
                        client.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 1000 * 2)
                                .option(ChannelOption.SO_KEEPALIVE, true)
                                .option(ChannelOption.TCP_NODELAY, true)
                                .doOnConnected(conn -> conn
                                        // 设置读超时时间，查询节点最多10s超时
                                        .addHandlerLast(new ReadTimeoutHandler(10000, TimeUnit.MILLISECONDS))
                                        // 设置写超时时间
                                        .addHandlerLast(new WriteTimeoutHandler(3000, TimeUnit.MILLISECONDS))));
        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .defaultHeaders(httpHeaders -> httpHeaders.add("Content-Type", MediaType.APPLICATION_JSON_VALUE))
                .exchangeStrategies(ExchangeStrategies.builder()
                        .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(1024 * 1024 * 2))
                        .build()
                ).build();
    }

    @Bean(name = "forwardWebClient")
    public WebClient forwardWebClient() {
        HttpClient httpClient = HttpClient.create()
                .tcpConfiguration(client ->
                        // 设置连接超时时间
                        client.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 1000 * 2)
                                .option(ChannelOption.SO_KEEPALIVE, true)
                                .option(ChannelOption.TCP_NODELAY, true)
                                .doOnConnected(conn -> conn
                                        // 设置读超时时间
                                        .addHandlerLast(new ReadTimeoutHandler(15000, TimeUnit.MILLISECONDS))
                                        // 设置写超时时间
                                        .addHandlerLast(new WriteTimeoutHandler(3000, TimeUnit.MILLISECONDS))));
        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .defaultHeaders(httpHeaders -> httpHeaders.add("Content-Type", MediaType.APPLICATION_JSON_VALUE))
                .exchangeStrategies(ExchangeStrategies.builder()
                        .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(1024 * 1024 * 2))
                        .build()
                ).build();
    }

    @Bean(name = "largeModelCollectWebClient")
    public WebClient largeModelCollectWebClient() {
        HttpClient httpClient = HttpClient.create()
                .tcpConfiguration(client ->
                        // 设置连接超时时间
                        client.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 1000 * 2)
                                .option(ChannelOption.SO_KEEPALIVE, true)
                                .option(ChannelOption.TCP_NODELAY, true)
                                .doOnConnected(conn -> conn
                                        // 设置读超时时间
                                        .addHandlerLast(new ReadTimeoutHandler(1500, TimeUnit.MILLISECONDS))
                                        // 设置写超时时间
                                        .addHandlerLast(new WriteTimeoutHandler(300, TimeUnit.MILLISECONDS))));
        return WebClient.builder()
                .clientConnector( new ReactorClientHttpConnector(httpClient))
                .defaultHeaders(httpHeaders -> httpHeaders.add("Content-Type", MediaType.APPLICATION_JSON_VALUE))
                .exchangeStrategies(ExchangeStrategies.builder()
                        .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(1024 * 1024 * 2))
                        .build()
                ).build();
    }

    @Bean(name = "llmChatClient")
    public WebClient llmChatClient() {
        HttpClient httpClient = HttpClient.create()
                .tcpConfiguration(client ->
                        // 设置连接超时时间
                        client.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 1000 * 2)
                                .option(ChannelOption.SO_KEEPALIVE, true)
                                .option(ChannelOption.TCP_NODELAY, true)
                                .doOnConnected(conn -> conn
                                        // 设置读超时时间
                                        .addHandlerLast(new ReadTimeoutHandler(1000 * 4, TimeUnit.MILLISECONDS))
                                        // 设置写超时时间
                                        .addHandlerLast(new WriteTimeoutHandler(1000, TimeUnit.MILLISECONDS))));
        return WebClient.builder()
                .clientConnector( new ReactorClientHttpConnector(httpClient))
                .defaultHeaders(httpHeaders -> httpHeaders.add("Content-Type", MediaType.APPLICATION_JSON_VALUE))
                .exchangeStrategies(ExchangeStrategies.builder()
                        .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(1024 * 1024 * 2))
                        .build()
                ).build();
    }
}
