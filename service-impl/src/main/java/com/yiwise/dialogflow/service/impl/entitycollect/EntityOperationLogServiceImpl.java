package com.yiwise.dialogflow.service.impl.entitycollect;

import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.entity.dto.OperationLogDTO;
import com.yiwise.dialogflow.entity.enums.LargeModelEntityModelTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.service.OperationLogService;
import com.yiwise.dialogflow.service.entitycollect.EntityOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EntityOperationLogServiceImpl implements EntityOperationLogService {

    @Resource
    private OperationLogService operationLogService;

    @Override
    public void compareAndCreateOperationLog(Long botId, BaseEntityPO oldEntity, BaseEntityPO newEntity, Long userId) {
        try {
            doCompareAndCreateOperationLog(botId, oldEntity, newEntity, userId);
        } catch (Exception e) {
            log.warn("处理操作日志异常", e);
        }
    }

    private void doCompareAndCreateOperationLog(Long botId, BaseEntityPO oldEntity, BaseEntityPO newEntity, Long userId) {
        if (Objects.isNull(oldEntity) && Objects.isNull(newEntity)) {
            return;
        }
        List<String> logDetailList = new ArrayList<>();
        if (Objects.isNull(oldEntity)) {
            // 新增实体
            logDetailList.add(String.format("新增%s:【%s】", newEntity.getType().getDesc(), newEntity.getName()));
        } else if (Objects.isNull(newEntity)) {
            // 删除实体
            logDetailList.add(String.format("删除%s:【%s】", oldEntity.getType().getDesc(), oldEntity.getName()));
        } else {
            // 更新实体
            // 变更类型
            if (!StringUtils.equals(oldEntity.getName(), newEntity.getName())) {
                logDetailList.add(String.format("%s名称修改为【%s】", modifyPrefix(oldEntity), newEntity.getName()));
            }
            if (!Objects.equals(oldEntity.getType(), newEntity.getType())) {
                logDetailList.add(String.format("%s实体类型修改为【%s】", modifyPrefix(oldEntity), newEntity.getType().getDesc()));
            } else {
                // 标准实体
                if (oldEntity instanceof StandardEntityPO) {
                    StandardEntityPO oldStandardEntity = (StandardEntityPO) oldEntity;
                    StandardEntityPO newStandardEntity = (StandardEntityPO) newEntity;

                    Map<String, EntitySynonymPO> oldSynonymMap = MyCollectionUtils.listToMap(oldStandardEntity.getMemberList(), EntitySynonymPO::getName);
                    Map<String, EntitySynonymPO> newSynonymMap = MyCollectionUtils.listToMap(newStandardEntity.getMemberList(), EntitySynonymPO::getName);

                    // 增加的同义词
                    Set<String> addSynonymSet = getAddSet(oldSynonymMap.keySet(), newSynonymMap.keySet());
                    // 删除的同义词
                    Set<String> deleteSynonymSet = getDeleteSet(oldSynonymMap.keySet(), newSynonymMap.keySet());
                    // 修改的同义词
                    Set<String> modifySynonymSet = getModifySet(oldSynonymMap.keySet(), newSynonymMap.keySet());
                    if (CollectionUtils.isNotEmpty(addSynonymSet)) {
                        logDetailList.add(String.format("%s增加实体成员: 【%s】", modifyPrefix(oldEntity), String.join(", ", addSynonymSet)));
                    }
                    if (CollectionUtils.isNotEmpty(deleteSynonymSet)) {
                        logDetailList.add(String.format("%s删除实体成员: 【%s】",
                                modifyPrefix(oldEntity), String.join(", ", deleteSynonymSet)));
                    }
                    if (CollectionUtils.isNotEmpty(modifySynonymSet)) {
                        modifySynonymSet.forEach(item -> {
                            EntitySynonymPO oldSynonym = oldSynonymMap.get(item);
                            EntitySynonymPO newSynonym = newSynonymMap.get(item);

                            // 比较同义词是否变更
                            Set<String> deleteSynonymList = getDeleteSet(oldSynonym.getSynonymList(), newSynonym.getSynonymList());
                            Set<String> addSynonymList = getAddSet(oldSynonym.getSynonymList(), newSynonym.getSynonymList());
                            if (CollectionUtils.isNotEmpty(addSynonymList)) {
                                logDetailList.add(String.format("%s实体成员【%s】增加同义词:【%s】",
                                        modifyPrefix(oldEntity), oldSynonym.getName(), String.join(", ", addSynonymList)));
                            }
                            if (CollectionUtils.isNotEmpty(deleteSynonymList)) {
                                logDetailList.add(String.format("%s实体成员【%s】删除同义词:【%s】",
                                        modifyPrefix(oldEntity), oldSynonym.getName(), String.join(", ", deleteSynonymList)));
                            }
                            Set<String> deleteRegexSynonymList = getDeleteSet(oldSynonym.getRegexSynonymList(), newSynonym.getRegexSynonymList());
                            Set<String> addRegexSynonymList = getAddSet(oldSynonym.getRegexSynonymList(), newSynonym.getRegexSynonymList());
                            if (CollectionUtils.isNotEmpty(addRegexSynonymList)) {
                                logDetailList.add(String.format("%s实体成员【%s】增加正则:【%s】",
                                        modifyPrefix(oldEntity), oldSynonym.getName(), String.join(", ", addRegexSynonymList)));
                            }
                            if (CollectionUtils.isNotEmpty(deleteRegexSynonymList)) {
                                logDetailList.add(String.format("%s实体成员【%s】删除正则:【%s】",
                                        modifyPrefix(oldEntity), oldSynonym.getName(), String.join(", ", deleteRegexSynonymList)));
                            }
                        });
                    }
                } else if (oldEntity instanceof RegexEntityPO) {
                    RegexEntityPO oldRegexEntity = (RegexEntityPO) oldEntity;
                    RegexEntityPO newRegexEntity = (RegexEntityPO) newEntity;
                    Set<String> deleteRegexList = getDeleteSet(oldRegexEntity.getRegexList(), newRegexEntity.getRegexList());
                    Set<String> addRegexList = getAddSet(oldRegexEntity.getRegexList(), newRegexEntity.getRegexList());
                    if (CollectionUtils.isNotEmpty(addRegexList)) {
                        logDetailList.add(String.format("%s增加正则:【%s】", modifyPrefix(oldEntity), String.join(",", addRegexList)));
                    }
                    if (CollectionUtils.isNotEmpty(deleteRegexList)) {
                        logDetailList.add(String.format("%s删除正则:【%s】", modifyPrefix(oldEntity), String.join(", ", deleteRegexList)));
                    }
                } else if (oldEntity instanceof SystemEntityPO) {
                    // 系统实体
                    SystemEntityPO oldSystemEntity = (SystemEntityPO) oldEntity;
                    SystemEntityPO newSystemEntity = (SystemEntityPO) newEntity;
                    Set<String> deleteRegexList = getDeleteSet(oldSystemEntity.getExtraRegexList(), newSystemEntity.getExtraRegexList());
                    Set<String> addRegexList = getAddSet(oldSystemEntity.getExtraRegexList(), newSystemEntity.getExtraRegexList());
                    if (CollectionUtils.isNotEmpty(addRegexList)) {
                        logDetailList.add(String.format("%s增加正则:【%s】", modifyPrefix(oldEntity), String.join(", ", addRegexList)));
                    }
                    if (CollectionUtils.isNotEmpty(deleteRegexList)) {
                        logDetailList.add(String.format("%s删除正则:【%s】", modifyPrefix(oldEntity), String.join(", ", deleteRegexList)));
                    }
                } else if (oldEntity instanceof LargeModelEntityPO) {
                    LargeModelEntityPO oldLargeModelEntity = (LargeModelEntityPO) oldEntity;
                    LargeModelEntityPO newLargeModelEntity = (LargeModelEntityPO) newEntity;

                    LargeModelEntityModelTypeEnum modelType = newLargeModelEntity.getModelType();
                    if (LargeModelEntityModelTypeEnum.isTuneModel(modelType)) {
                        if (!StringUtils.equals(oldLargeModelEntity.getDesc(), newLargeModelEntity.getDesc())) {
                            logDetailList.add(String.format("%s描述修改为【%s】", modifyPrefix(oldEntity), newLargeModelEntity.getDesc()));
                        }
                        Map<String, EntitySynonymPO> oldSynonymMap = MyCollectionUtils.listToMap(oldLargeModelEntity.getMemberList(), EntitySynonymPO::getName);
                        Map<String, EntitySynonymPO> newSynonymMap = MyCollectionUtils.listToMap(newLargeModelEntity.getMemberList(), EntitySynonymPO::getName);

                        // 增加的同义词
                        Set<String> addSynonymSet = getAddSet(oldSynonymMap.keySet(), newSynonymMap.keySet());
                        // 删除的同义词
                        Set<String> deleteSynonymSet = getDeleteSet(oldSynonymMap.keySet(), newSynonymMap.keySet());
                        // 修改的同义词
                        Set<String> modifySynonymSet = getModifySet(oldSynonymMap.keySet(), newSynonymMap.keySet());
                        if (CollectionUtils.isNotEmpty(addSynonymSet)) {
                            logDetailList.add(String.format("%s增加实体成员: 【%s】", modifyPrefix(oldEntity), String.join(", ", addSynonymSet)));
                        }
                        if (CollectionUtils.isNotEmpty(deleteSynonymSet)) {
                            logDetailList.add(String.format("%s删除实体成员: 【%s】",
                                    modifyPrefix(oldEntity), String.join(", ", deleteSynonymSet)));
                        }
                        if (CollectionUtils.isNotEmpty(modifySynonymSet)) {
                            modifySynonymSet.forEach(item -> {
                                EntitySynonymPO oldSynonym = oldSynonymMap.get(item);
                                EntitySynonymPO newSynonym = newSynonymMap.get(item);

                                // 比较同义词是否变更
                                Set<String> deleteSynonymList = getDeleteSet(oldSynonym.getSynonymList(), newSynonym.getSynonymList());
                                Set<String> addSynonymList = getAddSet(oldSynonym.getSynonymList(), newSynonym.getSynonymList());
                                if (CollectionUtils.isNotEmpty(addSynonymList)) {
                                    logDetailList.add(String.format("%s实体成员【%s】增加描述:【%s】",
                                            modifyPrefix(oldEntity), oldSynonym.getName(), String.join(", ", addSynonymList)));
                                }
                                if (CollectionUtils.isNotEmpty(deleteSynonymList)) {
                                    logDetailList.add(String.format("%s实体成员【%s】删除描述:【%s】",
                                            modifyPrefix(oldEntity), oldSynonym.getName(), String.join(", ", deleteSynonymList)));
                                }
                            });
                        }
                    }

                    if (LargeModelEntityModelTypeEnum.isCommonModel(modelType)) {
                        if (!StringUtils.equals(oldLargeModelEntity.getPrompt(), newLargeModelEntity.getPrompt())) {
                            logDetailList.add(String.format("%s提示词修改为【%s】", modifyPrefix(oldEntity), newLargeModelEntity.getPrompt()));
                        }
                    }
                }
            }
            // 比较反例是否变更
            Set<String> deleteNegativeExampleSet = getDeleteSet(oldEntity.getSkipRegexList(), newEntity.getSkipRegexList());
            Set<String> addNegativeExampleSet = getAddSet(oldEntity.getSkipRegexList(), newEntity.getSkipRegexList());
            if (CollectionUtils.isNotEmpty(addNegativeExampleSet)) {
                logDetailList.add(String.format("%s增加正则反例:【%s】", modifyPrefix(oldEntity), String.join(", ", addNegativeExampleSet)));
            }
            if (CollectionUtils.isNotEmpty(deleteNegativeExampleSet)) {
                logDetailList.add(String.format("%s删除正则反例:【%s】", modifyPrefix(oldEntity), String.join(", ", deleteNegativeExampleSet)));
            }
        }
        save(botId, logDetailList, userId);
    }

    private String modifyPrefix(BaseEntityPO oldEntity) {
        return String.format("编辑%s: 原【%s】", oldEntity.getType().getDesc(), oldEntity.getName());
    }

    private void save(Long botId, List<String> detailList, Long userId) {
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }

        List<OperationLogDTO> logs = detailList.stream()
                .map(detail -> OperationLogDTO.builder()
                        .botId(botId)
                        .operatorId(userId)
                        .detail(detail)
                        .type(OperationLogTypeEnum.ENTITY)
                        .resourceType(OperationLogResourceTypeEnum.ENTITY)
                        .build()
                )
                .collect(Collectors.toList());
        operationLogService.batchSave(logs);
    }

    private Set<String> getDeleteSet(Collection<String> oldSet, Collection<String> newSet) {
        if (CollectionUtils.isEmpty(oldSet)) {
            return Collections.emptySet();
        }
        if (CollectionUtils.isEmpty(newSet)) {
            return new HashSet<>(oldSet);
        }
        return oldSet.stream()
                .filter(item -> !newSet.contains(item))
                .collect(Collectors.toSet());
    }
    // 可以用集合操作实现
    private Set<String> getAddSet(Collection<String> oldSet, Collection<String> newSet) {
        if (CollectionUtils.isEmpty(newSet)) {
            return Collections.emptySet();
        }
        if (CollectionUtils.isEmpty(oldSet)) {
            return new HashSet<>(newSet);
        }
        return newSet.stream()
                .filter(item -> !oldSet.contains(item))
                .collect(Collectors.toSet());
    }

    private Set<String> getModifySet(Collection<String> oldSet, Collection<String> newSet) {
        if (CollectionUtils.isEmpty(oldSet) || CollectionUtils.isEmpty(newSet)) {
            return Collections.emptySet();
        }
        return newSet.stream()
                .filter(oldSet::contains)
                .collect(Collectors.toSet());
    }
}
