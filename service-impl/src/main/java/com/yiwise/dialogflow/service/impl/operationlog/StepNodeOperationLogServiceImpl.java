package com.yiwise.dialogflow.service.impl.operationlog;

import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.engine.share.DialogQueryNodeHttpParamInfo;
import com.yiwise.dialogflow.engine.share.enums.KeyCaptureCommitModeEnum;
import com.yiwise.dialogflow.engine.share.enums.QueryNodeHttpVarTypeEnum;
import com.yiwise.dialogflow.entity.bo.ActionNameResourceBO;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.dto.OperationLogDTO;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.helper.AnswerChangeLogHelper;
import com.yiwise.dialogflow.helper.IntentActionConfigCompareHelper;
import com.yiwise.dialogflow.helper.IntentLevelConfigCompareHelper;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.DependResourceService;
import com.yiwise.dialogflow.service.OperationLogService;
import com.yiwise.dialogflow.service.intent.IntentRuleActionService;
import com.yiwise.dialogflow.service.operationlog.MismatchOperationLogService;
import com.yiwise.dialogflow.service.operationlog.StepNodeOperationLogService;
import com.yiwise.dialogflow.service.operationlog.UninterruptedOperationLogService;
import com.yiwise.dialogflow.service.remote.IntentLevelTagDetailService;
import com.yiwise.dialogflow.utils.AnswerConditionUtil;
import javaslang.Tuple;
import javaslang.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StepNodeOperationLogServiceImpl implements StepNodeOperationLogService {

    @Resource
    private DependResourceService dependResourceService;

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private IntentRuleActionService intentRuleActionService;

    @Resource
    private IntentLevelTagDetailService intentLevelTagDetailService;

    @Resource
    private BotService botService;

    @Resource
    private MismatchOperationLogService mismatchOperationLogService;

    @Resource
    private UninterruptedOperationLogService uninterruptedOperationLogService;
    @Override
    public void compareAndCreateOperationLog(Long botId, String stepId,
                                             List<DialogBaseNodePO> oldNodeList,
                                             List<DialogBaseNodePO> newNodeList,
                                             Long userId) {
        try {
            doCompareAndCreateOperationLog(botId, stepId, oldNodeList, newNodeList, userId);
        } catch (Exception e) {
            log.warn("compareAndCreateOperationLog error, botId:{}, stepId:{}, userId:{}",
                    botId, stepId, userId, e);
        }
    }
    private void doCompareAndCreateOperationLog(Long botId, String stepId,
                                             List<DialogBaseNodePO> oldNodeList,
                                             List<DialogBaseNodePO> newNodeList,
                                             Long userId) {

        Map<String, DialogBaseNodePO> oldNodeMap = MyCollectionUtils.listToMap(oldNodeList, DialogBaseNodePO::getId);
        Map<String, DialogBaseNodePO> newNodeMap = MyCollectionUtils.listToMap(newNodeList, DialogBaseNodePO::getId);
        List<DialogBaseNodePO> addNodeList = new ArrayList<>();
        List<DialogBaseNodePO> deleteNodeList = new ArrayList<>();
        List<Tuple2<DialogBaseNodePO, DialogBaseNodePO>> updateNodeList = new ArrayList<>();
        for (DialogBaseNodePO node : newNodeList) {
            if (!oldNodeMap.containsKey(node.getId())) {
                addNodeList.add(node);
            } else {
                DialogBaseNodePO oldNode = oldNodeMap.get(node.getId());
                updateNodeList.add(Tuple.of(oldNode, node));
            }
        }
        for (DialogBaseNodePO node : oldNodeList) {
            if (!newNodeMap.containsKey(node.getId())) {
                deleteNodeList.add(node);
            }
        }

        List<String> logDetailList = new ArrayList<>();
        // 1. 新增的节点
        if (CollectionUtils.isNotEmpty(addNodeList)) {
            for (DialogBaseNodePO node : addNodeList) {
                logDetailList.add(String.format("新增%s节点: 【%s:%s】", node.getType().getDesc(), node.getLabel(), node.getName()));
            }
        }

        // 2. 删除的节点
        if (CollectionUtils.isNotEmpty(deleteNodeList)) {
            for (DialogBaseNodePO node : deleteNodeList) {
                logDetailList.add(String.format("删除%s节点: 【%s:%s】", node.getType().getDesc(), node.getLabel(), node.getName()));
            }
        }
        ActionNameResourceBO actionNameResource = intentRuleActionService.getSourceId2NameMapByBotId(botId);
        DependentResourceBO resource = dependResourceService.generateByCondition(new DependentResourceBO.Condition(botId).intent().entity().step().knowledge().variable().node().specialAnswerConfig());
        BotPO bot = botService.selectByKey(botId);
        Map<Integer, String> intentLevelCode2NameMap = Collections.emptyMap();
        if (Objects.nonNull(bot) && Objects.nonNull(bot.getIntentLevelTagId())) {
            intentLevelCode2NameMap = intentLevelTagDetailService.getIntentLevelTagDetailCode2NameMap(bot.getIntentLevelTagId());
        }

        // 3. 更新的节点
        for (Tuple2<DialogBaseNodePO, DialogBaseNodePO> tuple : updateNodeList) {
            logDetailList.addAll(compareAndCreateOperateLog(tuple._1, tuple._2, resource, actionNameResource, intentLevelCode2NameMap));
        }

        // 3.1
        List<OperationLogDTO> operationLogList = logDetailList.stream()
                .map(detail -> OperationLogDTO.builder()
                        .detail(detail)
                        .botId(botId)
                        .type(OperationLogTypeEnum.STEP)
                        .resourceType(OperationLogResourceTypeEnum.NODE)
                        .operatorId(userId)
                        .build()
                ).collect(Collectors.toList());

        operationLogService.batchSave(operationLogList);
    }

    private List<String> compareAndCreateOperateLog(DialogBaseNodePO oldNode,
                                                    DialogBaseNodePO newNode,
                                                    DependentResourceBO resource,
                                                    ActionNameResourceBO actionNameResource,
                                                    Map<Integer, String> intentLevelTagDetailMap) {
        List<String> logs = new ArrayList<>();
        // 比较名称
        if (!StringUtils.equals(oldNode.getName(), newNode.getName())) {
            logs.add(String.format("%s节点名称【%s】修改为【%s】",
                    updatePrefix(oldNode), oldNode.getName(), newNode.getName()));
        }

        // 答案列表
        if (!NodeTypeEnum.COLLECT.equals(newNode.getType())) {
            logs.addAll(compareAnswerAndCreateLog(oldNode, newNode, resource));
        }

        // 关联意图

        // 打断设置
        logs.addAll(uninterruptedOperationLogService.compare(updatePrefix(oldNode), oldNode, newNode, resource));

        // 无应答时长
        compareUserSilenceConfig(oldNode, newNode, logs);

        // 意向等级
        compareIntentLevelConfig(oldNode, newNode, logs, intentLevelTagDetailMap);

        // 返回时语音播报
        compareAudioReplyConfig(oldNode, newNode, logs);

        // 关联意图
        compareIntentBranch(oldNode, newNode, logs, resource);

        // 不关联问答知识
        logs.addAll(mismatchOperationLogService.compare(updatePrefix(oldNode), oldNode, newNode, resource));

        // 触发动作设置
        compareActionConfig(oldNode, newNode, logs, actionNameResource);

        compareJumpConfig(oldNode, newNode, logs, resource);

        compareNextNode(oldNode, newNode, logs, resource);

        compareEnableNodeIntentFirst(oldNode, newNode, logs);

        // 动态变量赋值
        compareDynamicVariableAssign(oldNode, newNode, logs, resource);

        // 判断节点的分支连线
        compareBranchNextNode(oldNode, newNode, logs, resource);

        // 判断节点分支条件组
        compareBranchCondition(oldNode, newNode, logs, resource);

        // 等待用户说完
        compareWaitUserSayFinish(oldNode, newNode, logs, resource);

        // 比较查询节点的http配置信息
        compareQueryNodeHttpInfo(oldNode, newNode, logs, resource);

        // 比较按键采集节点信息
        compareKeyCaptureNodeInfo(oldNode, newNode, logs, resource);

        // 信息采集
        compareCollectNode(oldNode, newNode, logs, resource);

        // 拉回
        comparePullbackConfig(oldNode, newNode, logs);

        // 节点标签
        compareNodeLabel(oldNode, newNode, logs);

        // 人工介入
        compareEnableHumanIntervention(oldNode, newNode, logs);
        return logs;
    }

    private void compareEnableHumanIntervention(DialogBaseNodePO oldNode, DialogBaseNodePO newNode, List<String> logs) {
        boolean oldEnableHumanIntervention = BooleanUtils.isTrue(oldNode.getEnableHumanIntervention());
        boolean newEnableHumanIntervention = BooleanUtils.isTrue(newNode.getEnableHumanIntervention());
        if (oldEnableHumanIntervention != newEnableHumanIntervention) {
            logs.add(String.format("%s启动人工介入:【%s】修改为【%s】",
                    updatePrefix(oldNode),
                    oldEnableHumanIntervention ? "开启" : "关闭",
                    newEnableHumanIntervention ? "开启" : "关闭"));
        }
    }

    private void comparePullbackConfig(DialogBaseNodePO oldNode, DialogBaseNodePO newNode, List<String> logs) {
        if (!(oldNode instanceof DialogChatNodePO) || !(newNode instanceof DialogChatNodePO)) {
            return;
        }
        DialogChatNodePO oldChatNode = (DialogChatNodePO) oldNode;
        DialogChatNodePO newChatNode = (DialogChatNodePO) newNode;

        if (BooleanUtils.isTrue(oldChatNode.getEnablePullback()) != BooleanUtils.isTrue(newChatNode.getEnablePullback())) {
            logs.add(String.format("%s返回时执行跳转原主动流程逻辑:【%s】修改为【%s】",
                    updatePrefix(oldNode),
                    oldChatNode.getEnablePullback() ? "开启" : "关闭",
                    newChatNode.getEnablePullback() ? "开启" : "关闭"));
        }
    }

    private void compareNodeLabel(DialogBaseNodePO oldNode, DialogBaseNodePO newNode, List<String> logs) {
        if (!StringUtils.equals(oldNode.getLabel(), newNode.getLabel())) {
            logs.add(String.format("%s节点ID修改为【%s】", updatePrefix(oldNode), newNode.getLabel()));
        }
    }

    private void compareCollectNode(DialogBaseNodePO oldNode,
                                    DialogBaseNodePO newNode,
                                    List<String> logs,
                                    DependentResourceBO resource) {
        if (!(oldNode instanceof DialogCollectNodePO) || !(newNode instanceof DialogCollectNodePO)) {
            return;
        }

        DialogCollectNodePO oldCollectNode = (DialogCollectNodePO) oldNode;
        DialogCollectNodePO newCollectNode = (DialogCollectNodePO) newNode;

        final Map<String, CollectNodeEntityItemPO> oldMap = new HashMap<>();
        final Map<String, CollectNodeEntityItemPO> newMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(oldCollectNode.getEntityCollectList())) {
            oldMap.putAll(MyCollectionUtils.listToMap(oldCollectNode.getEntityCollectList(), CollectNodeEntityItemPO::getEntityId));
        }

        if (CollectionUtils.isNotEmpty(newCollectNode.getEntityCollectList())) {
            newMap.putAll(MyCollectionUtils.listToMap(newCollectNode.getEntityCollectList(), CollectNodeEntityItemPO::getEntityId));
        }

        // 判断删除
        oldMap.forEach((entityId, oldItem) -> {
            if (!newMap.containsKey(entityId)) {
                String desc = entityCollectDesc(oldItem, resource);
                logs.add(String.format("%s实体采集, 删除: %s", updatePrefix(oldNode), desc));
            }
        });

        // 判断新增
        newMap.forEach((entityId, newItem) -> {
            if (!oldMap.containsKey(entityId)) {
                String desc = entityCollectDesc(newItem, resource);
                String answer = "";
                if (CollectionUtils.isNotEmpty(newItem.getAnswerList())) {
                    answer = newItem.getAnswerList().stream()
                            .map(NodeAnswer::getText)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.joining(";"));
                }
                logs.add(String.format("%s实体采集, 新增: %s, 答案列表:【%s】", updatePrefix(oldNode), desc, answer));
            }
        });

        // 判断修改
        newMap.forEach((entityId, newItem) -> {
            if (oldMap.containsKey(entityId)) {
                CollectNodeEntityItemPO oldItem = oldMap.get(entityId);

                String oldDesc = entityCollectDesc(oldItem, resource);
                String newDesc = entityCollectDesc(newItem, resource);
                if (!StringUtils.equals(oldDesc, newDesc)) {
                    logs.add(String.format("%s实体采集,【%s】修改为【%s】",
                            updatePrefix(oldNode),
                            oldDesc,
                            newDesc));
                }
                // 答案列表
                String prefix = String.format("%s实体采集, 实体【%s】", updatePrefix(oldNode), resource.getEntityId2NameMap().getOrDefault(entityId, ""));
                logs.addAll(AnswerChangeLogHelper.compareAnswerChange(oldItem.getAnswerList(), newItem.getAnswerList(), prefix, resource));
            }
        });


        if (BooleanUtils.isTrue(oldCollectNode.getEnableCollectWithPreInput()) != BooleanUtils.isTrue(newCollectNode.getEnableCollectWithPreInput())) {
            logs.add(String.format("%s读取已有对话内容:【%s】修改为【%s】",
                    updatePrefix(oldNode),
                    oldCollectNode.getEnableCollectWithPreInput() ? "开启" : "关闭",
                    newCollectNode.getEnableCollectWithPreInput() ? "开启" : "关闭"));
        }

        String oldEnableSkipConditionDesc = renderSkipCondition(oldCollectNode, resource);
        String newEnableSkipConditionDesc = renderSkipCondition(newCollectNode, resource);
        if (!StringUtils.equals(oldEnableSkipConditionDesc, newEnableSkipConditionDesc)) {
            logs.add(String.format("%s拒答跳过设置:【%s】修改为【%s】",
                    updatePrefix(oldNode),
                    oldEnableSkipConditionDesc,
                    newEnableSkipConditionDesc));
        }
    }

    private String entityCollectDesc(CollectNodeEntityItemPO item, DependentResourceBO resource) {
        String entityName = resource.getEntityId2NameMap().getOrDefault(item.getEntityId(), "");
        String varName = resource.getVariableIdNameMap().getOrDefault(item.getVariableId(), "");
        return String.format("实体【%s】保存至变量【%s】, 引导次数【%s】", entityName, varName, item.getRepeatCount());
    }

    private String renderSkipCondition(DialogCollectNodePO node, DependentResourceBO resource) {
        if (BooleanUtils.isNotTrue(node.getEnableSkipCondition())) {
            return "关闭";
        }

        if (CollectionUtils.isEmpty(node.getSkipConditionList())) {
            return "启用, 但条件为空";
        }

        List<String> list = new ArrayList<>();

        node.getSkipConditionList().stream()
                .map(condition -> AnswerConditionUtil.condition2String(condition, resource.getIntentIdNameMap()))
                .forEach(list::add);
        return String.join("; ", list);

    }

    private static Object nullToEmpty(Object obj) {
        return Objects.isNull(obj) ? "" : obj;
    }

    private void compareKeyCaptureNodeInfo(DialogBaseNodePO oldNode, DialogBaseNodePO newNode, List<String> logs, DependentResourceBO resource) {
        if (!(oldNode instanceof DialogKeyCaptureNodePO) || !(newNode instanceof DialogKeyCaptureNodePO)) {
            return;
        }
        DialogKeyCaptureNodePO oldKeyCaptureNode = (DialogKeyCaptureNodePO) oldNode;
        DialogKeyCaptureNodePO newKeyCaptureNode = (DialogKeyCaptureNodePO) newNode;

        if (!Objects.equals(oldKeyCaptureNode.getResultVarId(), newKeyCaptureNode.getResultVarId())) {
            String oldVarName = resource.getVariableIdNameMap().getOrDefault(oldKeyCaptureNode.getResultVarId(), "");
            String newVarName = resource.getVariableIdNameMap().getOrDefault(newKeyCaptureNode.getResultVarId(), "");
            logs.add(String.format("%s动态变量:【%s】修改为【%s】", updatePrefix(oldNode), nullToEmpty(oldVarName), nullToEmpty(newVarName)));
        }

        String oldCommitModeDesc = Optional.ofNullable(oldKeyCaptureNode.getCommitMode()).map(KeyCaptureCommitModeEnum::getDesc).orElse("");
        String newCommitModeDesc = Optional.ofNullable(newKeyCaptureNode.getCommitMode()).map(KeyCaptureCommitModeEnum::getDesc).orElse("");
        if (!StringUtils.equals(oldCommitModeDesc, newCommitModeDesc)) {
            logs.add(String.format("%s按键提交方式:【%s】修改为【%s】", updatePrefix(oldNode), oldCommitModeDesc, newCommitModeDesc));
        }

        if (!Objects.equals(oldKeyCaptureNode.getTimeout(), newKeyCaptureNode.getTimeout())) {
            logs.add(String.format("%s超时时长:【%s】修改为【%s】", updatePrefix(oldNode), nullToEmpty(oldKeyCaptureNode.getTimeout()), nullToEmpty(newKeyCaptureNode.getTimeout())));
        }

        boolean oldEnableReply = BooleanUtils.isTrue(oldKeyCaptureNode.getEnableReplay());
        boolean newEnableReply = BooleanUtils.isTrue(newKeyCaptureNode.getEnableReplay());
        if (oldEnableReply != newEnableReply) {
            logs.add(String.format("%s按米字键重播当前节点【%s】", updatePrefix(oldNode), newEnableReply ? "开启" : "关闭"));
        }

        boolean oldEnableRetryOnFailed = BooleanUtils.isTrue(oldKeyCaptureNode.getEnableRetryOnFailed());
        boolean newEnableRetryOnFailed = BooleanUtils.isTrue(newKeyCaptureNode.getEnableRetryOnFailed());
        if (oldEnableRetryOnFailed != newEnableRetryOnFailed) {
            logs.add(String.format("%s重复采集【%s】", updatePrefix(oldNode), newEnableRetryOnFailed ? "开启" : "关闭"));
        }
        if (oldEnableRetryOnFailed && newEnableRetryOnFailed && !Objects.equals(oldKeyCaptureNode.getRetryTimes(), newKeyCaptureNode.getRetryTimes())) {
            logs.add(String.format("%s采集失败,重复采集次数:【%s】修改为【%s】", updatePrefix(oldNode), nullToEmpty(oldKeyCaptureNode.getRetryTimes()), nullToEmpty(newKeyCaptureNode.getRetryTimes())));
        }
    }

    private void compareQueryNodeHttpInfo(DialogBaseNodePO oldNode, DialogBaseNodePO newNode, List<String> logs, DependentResourceBO resource) {
        if (!(oldNode instanceof DialogQueryNodePO) || !(newNode instanceof DialogQueryNodePO)) {
            return;
        }
        DialogQueryNodePO oldQueryNode = (DialogQueryNodePO) oldNode;
        DialogQueryNodePO newQueryNode = (DialogQueryNodePO) newNode;

        if (!Objects.equals(oldQueryNode.getApiType(), newQueryNode.getApiType())) {
            logs.add(String.format("%s接口类型:【%s】修改为【%s】", updatePrefix(oldNode), nullToEmpty(oldQueryNode.getApiType().getDesc()), nullToEmpty(newQueryNode.getApiType().getDesc())));
        }

        if (QueryNodeApiTypeEnum.BUILT_IN.equals(newQueryNode.getApiType())) {
            if (!StringUtils.equals(oldQueryNode.getBuiltInApiName(), newQueryNode.getBuiltInApiName())) {
                logs.add(String.format("%s内置接口:【%s】修改为【%s】", updatePrefix(oldNode), nullToEmpty(oldQueryNode.getBuiltInApiName()), nullToEmpty(newQueryNode.getBuiltInApiName())));
            }
        }

        if (QueryNodeApiTypeEnum.CUSTOM.equals(newQueryNode.getApiType())) {
            if (!Objects.equals(oldQueryNode.getHttpMethod(), newQueryNode.getHttpMethod())) {
                logs.add(String.format("%s请求方式:【%s】修改为【%s】", updatePrefix(oldNode), nullToEmpty(oldQueryNode.getHttpMethod()), nullToEmpty(newQueryNode.getHttpMethod())));
            }

            if (!StringUtils.equals(oldQueryNode.getUrl(), newQueryNode.getUrl())) {
                logs.add(String.format("%s接口地址:【%s】修改为【%s】", updatePrefix(oldNode), nullToEmpty(oldQueryNode.getUrl()), nullToEmpty(newQueryNode.getUrl())));
            }

            String oldQueryParamInfo = buildParamDisplayInfo(oldQueryNode.getQueryList(), resource);
            String newQueryParamInfo = buildParamDisplayInfo(newQueryNode.getQueryList(), resource);
            if (!StringUtils.equals(oldQueryParamInfo, newQueryParamInfo)) {
                logs.add(String.format("%sQuery:【%s】修改为【%s】", updatePrefix(oldNode), nullToEmpty(oldQueryParamInfo), nullToEmpty(newQueryParamInfo)));
            }

            String oldHeaderParamInfo = buildParamDisplayInfo(oldQueryNode.getHeaderList(), resource);
            String newHeaderParamInfo = buildParamDisplayInfo(newQueryNode.getHeaderList(), resource);
            if (!StringUtils.equals(oldHeaderParamInfo, newHeaderParamInfo)) {
                logs.add(String.format("%sHeader:【%s】修改为【%s】", updatePrefix(oldNode), nullToEmpty(oldHeaderParamInfo), nullToEmpty(newHeaderParamInfo)));
            }
            if (!StringUtils.equals(oldQueryNode.getBody(), newQueryNode.getBody())) {
                logs.add(String.format("%sBody:【%s】修改为【%s】", updatePrefix(oldNode), nullToEmpty(oldQueryNode.getBody()), nullToEmpty(newQueryNode.getBody())));
            }
        }

        String oldResMapInfo = buildResMapDisplayInfo(oldQueryNode.getResMap(), resource);
        String newResMapInfo = buildResMapDisplayInfo(newQueryNode.getResMap(), resource);
        if (!StringUtils.equals(oldResMapInfo, newResMapInfo)) {
            logs.add(String.format("%s 查询结果:【%s】修改为【%s】", updatePrefix(oldNode), nullToEmpty(oldResMapInfo), nullToEmpty(newResMapInfo)));
        }

        if (!Objects.equals(oldQueryNode.getTimeout(), newQueryNode.getTimeout())) {
            logs.add(String.format("%s 超时时间:【%s】修改为【%s】", updatePrefix(oldNode), nullToEmpty(oldQueryNode.getTimeout()), nullToEmpty(newQueryNode.getTimeout())));
        }

        if (!StringUtils.equals(StringUtils.trimToEmpty(oldQueryNode.getErrorVarId()), StringUtils.trimToEmpty(newQueryNode.getErrorVarId()))) {
            String oldVarName = resource.getVariableIdNameMap().getOrDefault(oldQueryNode.getErrorVarId(), "");
            String newVarName = resource.getVariableIdNameMap().getOrDefault(newQueryNode.getErrorVarId(), "");
            logs.add(String.format("%s 记录失败或者超时结果:【%s】修改为【%s】", updatePrefix(oldNode), nullToEmpty(oldVarName), nullToEmpty(newVarName)));
        }
    }

    private String buildResMapDisplayInfo(Map<String, String> resMap, DependentResourceBO resource) {
        if (MapUtils.isEmpty(resMap)) {
            return "";
        }
        return resMap.entrySet().stream().map(entry -> resource.getVariableIdNameMap().getOrDefault(entry.getKey(), "") + "=" + entry.getValue())
                .collect(Collectors.joining(";"));
    }

    private String buildParamDisplayInfo(List<DialogQueryNodeHttpParamInfo> list, DependentResourceBO resource) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        StringJoiner sj = new StringJoiner("；");
        for (DialogQueryNodeHttpParamInfo paramInfo : list) {
            String value = "";
            if (QueryNodeHttpVarTypeEnum.isVariable(paramInfo.getVariableType())) {
                value = resource.getVariableIdNameMap().getOrDefault(paramInfo.getValue(), "");
            }
            if (QueryNodeHttpVarTypeEnum.isConstant(paramInfo.getVariableType())) {
                value = paramInfo.getValue();
            }
            sj.add(String.format("%s = %s 【%s】", paramInfo.getName(), paramInfo.getVariableType().getDesc(), value));
        }
        return sj.toString();
    }

    private void compareWaitUserSayFinish(DialogBaseNodePO oldNode, DialogBaseNodePO newNode, List<String> logs, DependentResourceBO resource) {
        if (!(oldNode instanceof DialogChatNodePO) || !(newNode instanceof DialogChatNodePO)) {
            return;
        }
        DialogChatNodePO oldChatNode = (DialogChatNodePO) oldNode;
        DialogChatNodePO newChatNode = (DialogChatNodePO) newNode;
        if (BooleanUtils.isTrue(oldChatNode.getEnableWaitUserSayFinish()) != BooleanUtils.isTrue(newChatNode.getEnableWaitUserSayFinish())) {
            String oldStatus = BooleanUtils.isTrue(oldChatNode.getEnableWaitUserSayFinish()) ? "开启" : "关闭";
            String newStatus = BooleanUtils.isTrue(newChatNode.getEnableWaitUserSayFinish()) ? "开启" : "关闭";
            logs.add(String.format("%s设置AI等待时长:【%s】修改为【%s】",
                    updatePrefix(oldNode),
                    oldStatus,
                    newStatus));
        } else if (BooleanUtils.isTrue(newChatNode.getEnableWaitUserSayFinish())) {
            String oldIntentName = "";
            String newIntentName = "";
            if (CollectionUtils.isNotEmpty(oldChatNode.getTriggerWaitIntentIdList())) {
                oldIntentName = oldChatNode.getTriggerWaitIntentIdList().stream()
                        .map(resource.getIntentIdNameMap()::get)
                        .sorted()
                        .collect(Collectors.joining(","));
            }
            if (CollectionUtils.isNotEmpty(newChatNode.getTriggerWaitIntentIdList())) {
                newIntentName = newChatNode.getTriggerWaitIntentIdList().stream()
                        .map(resource.getIntentIdNameMap()::get)
                        .sorted()
                        .collect(Collectors.joining(","));
            }
            String oldConfig = String.format("触发分支名称: [%s], 延长时间: %s毫秒", oldIntentName, oldChatNode.getWaitUserSayFinishMs());
            String newConfig = String.format("触发分支名称: [%s], 延长时间: %s毫秒", newIntentName, newChatNode.getWaitUserSayFinishMs());
            if (!StringUtils.equals(oldConfig, newConfig)) {
                logs.add(String.format("%s设置AI等待时长:【%s】修改为【%s】",
                        updatePrefix(oldNode),
                        oldConfig,
                        newConfig));
            }
        }
    }

    private void compareBranchCondition(DialogBaseNodePO oldNode, DialogBaseNodePO newNode, List<String> logs, DependentResourceBO resource) {
        if (!(oldNode instanceof DialogJudgeNodePO) || !(newNode instanceof DialogJudgeNodePO)) {
            return;
        }

        DialogJudgeNodePO oldJudgeNode = (DialogJudgeNodePO) oldNode;
        DialogJudgeNodePO newJudgeNode = (DialogJudgeNodePO) newNode;

        Map<String, NodeConditionBranchPO> oldBranchMap = MyCollectionUtils.listToMap(oldJudgeNode.getBranchList(), NodeConditionBranchPO::getId);
        Map<String, NodeConditionBranchPO> newBranchMap = MyCollectionUtils.listToMap(newJudgeNode.getBranchList(), NodeConditionBranchPO::getId);

        Map<String, Integer> oldBranchOrderMap = new HashMap<>();
        Map<String, Integer> newBranchOrderMap = new HashMap<>();
        for (int i = 0; i < oldJudgeNode.getBranchList().size(); i++) {
            oldBranchOrderMap.put(oldJudgeNode.getBranchList().get(i).getId(), i + 1);
        }
        for (int i = 0; i < newJudgeNode.getBranchList().size(); i++) {
            newBranchOrderMap.put(newJudgeNode.getBranchList().get(i).getId(), i + 1);
        }

        // 新增的分支(不在oldBranchMap中, 但是在newBranchMap中)
        List<String> addBranchList = newBranchMap.keySet().stream().filter(branchId -> !oldBranchMap.containsKey(branchId)).collect(Collectors.toList());
        // 更新的分支(在oldBranchMap中, 也在newBranchMap中)
        List<String> updateBranchList = newBranchMap.keySet().stream().filter(oldBranchMap::containsKey).collect(Collectors.toList());
        // 删除的分支(在oldBranchMap中, 但是不在newBranchMap中)
        List<String> deleteBranchList = oldBranchMap.keySet().stream().filter(branchId -> !newBranchMap.containsKey(branchId)).collect(Collectors.toList());

        for (String s : addBranchList) {
            NodeConditionBranchPO branch = newBranchMap.get(s);
            logs.add(String.format("%s新增分支:【%s】", updatePrefix(oldNode), branch.getName()));
        }
        for (String s : deleteBranchList) {
            NodeConditionBranchPO branch = oldBranchMap.get(s);
            logs.add(String.format("%s删除分支:【%s】", updatePrefix(oldNode), branch.getName()));
        }

        for (String branchId : updateBranchList) {
            NodeConditionBranchPO oldBranch = oldBranchMap.get(branchId);
            NodeConditionBranchPO newBranch = newBranchMap.get(branchId);
            String oldBranchCondition = AnswerConditionUtil.generateConditionStrContent(oldBranch.getConditionList(), resource.getVariableIdNameMap(), resource.getIntentIdNameMap());
            String newBranchCondition = AnswerConditionUtil.generateConditionStrContent(newBranch.getConditionList(), resource.getVariableIdNameMap(), resource.getIntentIdNameMap());
            boolean changedName = !StringUtils.equals(oldBranch.getName(), newBranch.getName());
            boolean changedCondition = !StringUtils.equals(oldBranchCondition, newBranchCondition);
            if (changedName && changedCondition) {
                logs.add(String.format("%s修改分支: 分支名称【%s】修改为【%s】，分支条件【%s】修改为【%s】",
                        updatePrefix(oldNode), oldBranch.getName(), newBranch.getName(), oldBranchCondition, newBranchCondition));
            } else if (changedName) {
                logs.add(String.format("%s修改分支: 分支名称【%s】修改为【%s】", updatePrefix(oldNode), oldBranch.getName(), newBranch.getName()));
            } else if (changedCondition) {
                logs.add(String.format("%s修改分支: 【%s】分支条件【%s】修改为【%s】", updatePrefix(oldNode), oldBranch.getName(), oldBranchCondition, newBranchCondition));
            }
            // 判断是否调整了顺序
            if (!Objects.equals(oldBranchOrderMap.get(branchId), newBranchOrderMap.get(branchId))) {
                logs.add(String.format("%s修改分支: 【%s】分支顺序调整为【%s】", updatePrefix(oldNode), oldBranch.getName(), newBranchOrderMap.get(branchId)));
            }
            boolean oldEnableAction = BooleanUtils.isTrue(oldBranch.getEnableAction());
            boolean newEnableAction = BooleanUtils.isTrue(newBranch.getEnableAction());
            if (oldEnableAction != newEnableAction) {
                logs.add(String.format("%s修改分支: 【%s】触发分支动作【%s】", updatePrefix(oldNode), oldBranch.getName(), newEnableAction ? "开启" : "关闭"));
            }
            if (oldEnableAction && newEnableAction) {
                String oldBranchActionStr = generateBranchActionStr(oldBranch.getActionList(), resource.getVariableIdNameMap());
                String newBranchActionStr = generateBranchActionStr(newBranch.getActionList(), resource.getVariableIdNameMap());
                if (!StringUtils.equals(oldBranchActionStr, newBranchActionStr)) {
                    logs.add(String.format("%s修改分支: 【%s】触发分支动作【%s】修改为【%s】", updatePrefix(oldNode), oldBranch.getName(), oldBranchActionStr, newBranchActionStr));
                }
            }
        }
    }

    private String generateBranchActionStr(List<BranchActionPO> actionList, Map<String, String> varIdNameMap) {
        if (CollectionUtils.isEmpty(actionList)) {
            return "";
        }
        return actionList.stream().map(action -> {
            String varName = varIdNameMap.get(action.getVarId());
            BranchActionCategoryEnum category = action.getCategory();
            if (BranchActionCategoryEnum.ASSIGN.equals(category)) {
                String value = ConditionVarTypeEnum.isVar(action.getVarType()) ? varIdNameMap.get(action.getValue()) : action.getValue();
                return String.format("动态变量【%s】赋值为【%s】【%s】", varName, Optional.ofNullable(action.getVarType()).map(ConditionVarTypeEnum::getDesc).orElse(""), value);
            } else {
                return String.format("动态变量【%s】清空", varName);
            }
        }).collect(Collectors.joining(";"));
    }

    private void compareBranchNextNode(DialogBaseNodePO oldNode, DialogBaseNodePO newNode, List<String> logs, DependentResourceBO resource) {
        if (!(oldNode instanceof DialogJudgeNodePO) || !(newNode instanceof DialogJudgeNodePO)) {
            return;
        }

        DialogJudgeNodePO oldJudgeNode = (DialogJudgeNodePO) oldNode;
        DialogJudgeNodePO newJudgeNode = (DialogJudgeNodePO) newNode;

        Map<String, String> oldNextNodeMap = oldJudgeNode.getBranchRelatedNodeMap();
        Map<String, String> newNextNodeMap = newJudgeNode.getBranchRelatedNodeMap();
        Map<String, String> deleteMap = new HashMap<>();
        Map<String, String> addMap = new HashMap<>();
        Map<String, Tuple2<String, String>> updateMap = new HashMap<>();

        // 新增的连线
        // 更新的连线
        newNextNodeMap.forEach((branchId, newNextNodeId) -> {
            String oldNextNodeId = oldNextNodeMap.get(branchId);
            if (Objects.isNull(oldNextNodeId)) {
                addMap.put(branchId, newNextNodeId);
            } else if (!StringUtils.equals(oldNextNodeId, newNextNodeId)) {
                updateMap.put(branchId, Tuple.of(oldNextNodeId, newNextNodeId));
            }
        });

        // 删除的连线
        oldNextNodeMap.forEach((branchId, oldNextNodeId) -> {
            String newNextNodeId = newNextNodeMap.get(branchId);
            if (Objects.isNull(newNextNodeId)) {
                deleteMap.put(branchId, oldNextNodeId);
            }
        });

        Map<String, String> branchId2NameMap = new HashMap<>();
        oldJudgeNode.getBranchList().forEach(branch -> branchId2NameMap.put(branch.getId(), branch.getName()));
        newJudgeNode.getBranchList().forEach(branch -> branchId2NameMap.put(branch.getId(), branch.getName()));

        addMap.forEach((branchId, nextNodeId) -> {
            String intentName = branchId2NameMap.get(branchId);
            String nextNodeName = resource.getNodeIdNameMap().get(nextNodeId);
            if (StringUtils.isNotBlank(intentName) && StringUtils.isNotBlank(nextNodeName)) {
                logs.add(String.format("%s新增连线: 【%s】->【%s】", updatePrefix(oldNode), intentName, nextNodeName));
            }
        });

        deleteMap.forEach((branchId, nextNodeId) -> {
            String intentName = branchId2NameMap.get(branchId);
            String nextNodeName = resource.getNodeIdNameMap().get(nextNodeId);
            if (StringUtils.isNotBlank(intentName) && StringUtils.isNotBlank(nextNodeName)) {
                logs.add(String.format("%s删除连线: 【%s】->【%s】", updatePrefix(oldNode), intentName, nextNodeName));
            }
        });

        updateMap.forEach((branchId, tuple) -> {
            String intentName = branchId2NameMap.get(branchId);
            String oldNextNodeName = resource.getNodeIdNameMap().get(tuple._1);
            String newNextNodeName = resource.getNodeIdNameMap().get(tuple._2);
            if (StringUtils.isNotBlank(intentName) && StringUtils.isNotBlank(oldNextNodeName) && StringUtils.isNotBlank(newNextNodeName)) {
                logs.add(String.format("%s连线: 【%s】->【%s】修改为【%s】", updatePrefix(oldNode), intentName, oldNextNodeName, newNextNodeName));
            }
        });
    }

    private void compareDynamicVariableAssign(DialogBaseNodePO oldNode, DialogBaseNodePO newNode, List<String> logs, DependentResourceBO resource) {
        String oldStr = convertAssignToString(oldNode.getEnableAssign(), oldNode, resource);
        String newStr = convertAssignToString(newNode.getEnableAssign(), newNode, resource);
        if (!StringUtils.equals(oldStr, newStr)) {
            logs.add(String.format("%s动态变量赋值: %s 修改为 %s", updatePrefix(oldNode), oldStr, newStr));
        }
    }

    private String convertAssignToString(Boolean enableAssign, DialogBaseNodePO node, DependentResourceBO resource) {
        if (BooleanUtils.isNotTrue(enableAssign)) {
            return "未开启";
        }
        List<String> list = new ArrayList<>();

        if (Objects.nonNull(node.getConstantAssign())) {
            list.add(node.getConstantAssign().toDisplayString(resource));
        }
        if (Objects.nonNull(node.getEntityAssign())) {
            list.add(node.getEntityAssign().toDisplayString(resource));
        }
        if (Objects.nonNull(node.getOriginInputAssign())) {
            list.add(node.getOriginInputAssign().toDisplayString(resource));
        }

        list = list.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            return String.join(";", list);
        }
        return "配置为空";
    }

    private void compareNextNode(DialogBaseNodePO oldNode, DialogBaseNodePO newNode, List<String> logs, DependentResourceBO resource) {
        if (!(oldNode instanceof DialogChatNodePO) || !(newNode instanceof DialogChatNodePO)) {
            return;
        }
        DialogChatNodePO oldChatNode = (DialogChatNodePO) oldNode;
        DialogChatNodePO newChatNode = (DialogChatNodePO) newNode;

        Map<String, String> oldNextNodeMap = oldChatNode.getIntentRelatedNodeMap() == null ? Collections.emptyMap() : oldChatNode.getIntentRelatedNodeMap();
        Map<String, String> newNextNodeMap = newChatNode.getIntentRelatedNodeMap() == null ? Collections.emptyMap() : newChatNode.getIntentRelatedNodeMap();
        Map<String, String> deleteMap = new HashMap<>();
        Map<String, String> addMap = new HashMap<>();
        Map<String, Tuple2<String, String>> updateMap = new HashMap<>();

        // 新增的连线
        // 更新的连线

        newNextNodeMap.forEach((intentId, newNextNodeId) -> {
            String oldNextNodeId = oldNextNodeMap.get(intentId);
            if (Objects.isNull(oldNextNodeId)) {
                addMap.put(intentId, newNextNodeId);
            } else if (!StringUtils.equals(oldNextNodeId, newNextNodeId)) {
                updateMap.put(intentId, Tuple.of(oldNextNodeId, newNextNodeId));
            }
        });

        // 删除的连线
        oldNextNodeMap.forEach((intentId, oldNextNodeId) -> {
            String newNextNodeId = newNextNodeMap.get(intentId);
            if (Objects.isNull(newNextNodeId)) {
                deleteMap.put(intentId, oldNextNodeId);
            }
        });

        addMap.forEach((intentId, nextNodeId) -> {
            String intentName = resource.getIntentIdNameMap().get(intentId);
            String nextNodeName = resource.getNodeIdNameMap().get(nextNodeId);
            if (StringUtils.isNotBlank(intentName) && StringUtils.isNotBlank(nextNodeName)) {
                logs.add(String.format("%s新增连线: 【%s】->【%s】", updatePrefix(oldNode), intentName, nextNodeName));
            }
        });

        deleteMap.forEach((intentId, nextNodeId) -> {
            String intentName = resource.getIntentIdNameMap().get(intentId);
            String nextNodeName = resource.getNodeIdNameMap().get(nextNodeId);
            if (StringUtils.isNotBlank(intentName) && StringUtils.isNotBlank(nextNodeName)) {
                logs.add(String.format("%s删除连线: 【%s】->【%s】", updatePrefix(oldNode), intentName, nextNodeName));
            }
        });

        updateMap.forEach((intentId, tuple) -> {
            String intentName = resource.getIntentIdNameMap().get(intentId);
            String oldNextNodeName = resource.getNodeIdNameMap().get(tuple._1);
            String newNextNodeName = resource.getNodeIdNameMap().get(tuple._2);
            if (StringUtils.isNotBlank(intentName) && StringUtils.isNotBlank(oldNextNodeName) && StringUtils.isNotBlank(newNextNodeName)) {
                logs.add(String.format("%s连线: 【%s】->【%s】修改为【%s】", updatePrefix(oldNode), intentName, oldNextNodeName, newNextNodeName));
            }
        });
    }

    private void compareJumpConfig(DialogBaseNodePO oldNode, DialogBaseNodePO newNode, List<String> logs, DependentResourceBO resource) {
        if (oldNode instanceof DialogJumpNodePO && newNode instanceof DialogJumpNodePO) {
            DialogJumpNodePO oldJumpNode = (DialogJumpNodePO) oldNode;
            DialogJumpNodePO newJumpNode = (DialogJumpNodePO) newNode;
            if (!Objects.equals(oldJumpNode.getJumpType(), newJumpNode.getJumpType())) {
                String oldType = oldJumpNode.getJumpType().getDesc();
                String newType = newJumpNode.getJumpType().getDesc();
                if (JumpTypeEnum.SPECIFIED_STEP.equals(oldJumpNode.getJumpType())) {
                    oldType = String.format("%s:%s", oldType, resource.getStepIdNameMap().getOrDefault(oldJumpNode.getJumpStepId(), ""));
                }
                if (JumpTypeEnum.SPECIFIED_STEP.equals(newJumpNode.getJumpType())) {
                    newType = String.format("%s:%s", newType, resource.getStepIdNameMap().getOrDefault(newJumpNode.getJumpStepId(), ""));
                }
                logs.add(String.format("%s节点跳转【%s】修改为【%s】",
                        updatePrefix(oldNode), oldType, newType));
            }
        }
    }

    private void compareActionConfig(DialogBaseNodePO oldNode,
                                     DialogBaseNodePO newNode,
                                     List<String> logs,
                                     ActionNameResourceBO actionNameResource) {
        logs.addAll(IntentActionConfigCompareHelper.compareIntentActionConfigChangeLog(oldNode, newNode, actionNameResource, updatePrefix(oldNode)));
    }

    private String updatePrefix(DialogBaseNodePO node) {
        return String.format("编辑%s节点: 原【%s:%s】", node.getType().getDesc(), node.getLabel(), node.getName());
    }

    private void compareIntentBranch(DialogBaseNodePO oldNode, DialogBaseNodePO newNode, List<String> logs, DependentResourceBO resource) {
        if (oldNode instanceof DialogChatNodePO && newNode instanceof DialogChatNodePO) {
            DialogChatNodePO oldChatNode = (DialogChatNodePO) oldNode;
            DialogChatNodePO newChatNode = (DialogChatNodePO) newNode;
            // 比较意图列表
            Set<String> oldIntentBranchList = CollectionUtils.isNotEmpty(oldChatNode.getSelectIntentIdList()) ? new HashSet<>(oldChatNode.getSelectIntentIdList()) : new HashSet<>();
            Set<String> newIntentBranchList = CollectionUtils.isNotEmpty(newChatNode.getSelectIntentIdList()) ? new HashSet<>(newChatNode.getSelectIntentIdList()) : new HashSet<>();

            // 意图集合变更了
            if (!compareListIsEqual(oldIntentBranchList, newIntentBranchList)) {
                List<String> oldIntentNameList = oldChatNode.getSelectIntentIdList().stream()
                        .map(resource.getIntentIdNameMap()::get)
                        .filter(StringUtils::isNotBlank)
                        .map(item -> String.format("【%s】", item))
                        .collect(Collectors.toList());

                List<String> newIntentNameList = newChatNode.getSelectIntentIdList().stream()
                        .map(resource.getIntentIdNameMap()::get)
                        .filter(StringUtils::isNotBlank)
                        .map(item -> String.format("【%s】", item))
                        .collect(Collectors.toList());
                logs.add(String.format("%s节点关联意图%s修改为%s",
                        updatePrefix(oldNode), StringUtils.join(oldIntentNameList, "、"), StringUtils.join(newIntentNameList, "、")));
            }
        }
    }

    // 比较集合是否相等
    private boolean compareListIsEqual(Collection<?> oldList, Collection<?> newList) {
        if (CollectionUtils.isEmpty(oldList) && CollectionUtils.isEmpty(newList)) {
            return true;
        }
        if (CollectionUtils.isEmpty(oldList) || CollectionUtils.isEmpty(newList)) {
            return false;
        }
        if (oldList.size() != newList.size()) {
            return false;
        }
        Set<?> oldSet = new HashSet<>(oldList);
        Set<?> newSet = new HashSet<>(newList);
        return oldSet.equals(newSet);
    }

    private void compareAudioReplyConfig(DialogBaseNodePO oldNode, DialogBaseNodePO newNode, List<String> logs) {
        if (oldNode instanceof DialogChatNodePO && newNode instanceof DialogChatNodePO) {
            DialogChatNodePO oldChatNode = (DialogChatNodePO) oldNode;
            DialogChatNodePO newChatNode = (DialogChatNodePO) newNode;
            boolean changeStatus = changeEnableStatus(oldChatNode.getEnableCustomReplay(), newChatNode.getEnableCustomReplay());
            boolean changeValue = isChangeValue(oldChatNode, newChatNode, changeStatus);

            if (changeStatus || changeValue) {
                String oldVal = customerReplayValue(oldChatNode);
                String newVal = customerReplayValue(newChatNode);
                logs.add(String.format("%s返回时语音播报设置【%s】修改为【%s】",
                        updatePrefix(oldNode), oldVal, newVal));
            }
        }
    }

    private String customerReplayValue(DialogChatNodePO chatNode) {
        String strategy = chatNode.getExceedThresholdReplayStrategy().getDesc();

        return BooleanUtils.isNotTrue(chatNode.getEnableCustomReplay())? "未开启": chatNode.getCustomReplayThreshold() + "%, " + strategy;
    }

    private static boolean isChangeValue(DialogChatNodePO oldChatNode, DialogChatNodePO newChatNode, boolean changeStatus) {
        boolean changeReplayThreshold = !Objects.equals(oldChatNode.getCustomReplayThreshold(), newChatNode.getCustomReplayThreshold());
        boolean changeExceedThresholdStrategy = !Objects.equals(oldChatNode.getExceedThresholdReplayStrategy(), newChatNode.getExceedThresholdReplayStrategy());
        return !changeStatus
                && BooleanUtils.isTrue(newChatNode.getEnableCustomReplay())
                && (changeReplayThreshold || changeExceedThresholdStrategy);
    }

    private void compareEnableNodeIntentFirst(DialogBaseNodePO oldNode, DialogBaseNodePO newNode, List<String> logs) {
        if (oldNode instanceof DialogChatNodePO && newNode instanceof DialogChatNodePO) {
            DialogChatNodePO oldChatNode = (DialogChatNodePO) oldNode;
            DialogChatNodePO newChatNode = (DialogChatNodePO) newNode;
            boolean changeStatus = changeEnableStatus(oldChatNode.isEnableNodeIntentFirst(), newChatNode.isEnableNodeIntentFirst());
            if (changeStatus) {
                String oldVal = BooleanUtils.isTrue(oldChatNode.isEnableNodeIntentFirst()) ? "启用" : "停用";
                String newVal = BooleanUtils.isTrue(newChatNode.isEnableNodeIntentFirst()) ? "启用" : "停用";
                logs.add(String.format("%s节点关联意图优先设置【%s】修改为【%s】", updatePrefix(oldNode), oldVal, newVal));
            }
        }
    }

    private void compareIntentLevelConfig(DialogBaseNodePO oldNode, DialogBaseNodePO newNode, List<String> logs, Map<Integer, String> intentLevelCode2NameMap) {
        logs.addAll(IntentLevelConfigCompareHelper.compareIntentLevelConfigChangeLog(oldNode, newNode, updatePrefix(oldNode), intentLevelCode2NameMap));
    }

    private void compareUserSilenceConfig(DialogBaseNodePO oldNode, DialogBaseNodePO newNode, List<String> logs) {
        if (oldNode instanceof DialogChatNodePO && newNode instanceof DialogChatNodePO) {
            DialogChatNodePO oldChatNode = (DialogChatNodePO) oldNode;
            DialogChatNodePO newChatNode = (DialogChatNodePO) newNode;
            boolean changeEnableStats = changeEnableStatus(oldChatNode.getEnableCustomUserSilence(), newChatNode.getEnableCustomUserSilence());
            boolean changeUserSilenceThreshold = !changeEnableStats && BooleanUtils.isTrue(newChatNode.getEnableCustomUserSilence()) && !Objects.equals(oldChatNode.getCustomUserSilenceSecond(), newChatNode.getCustomUserSilenceSecond());
            if (changeEnableStats) {
                String oldVal = BooleanUtils.isNotTrue(oldChatNode.getEnableCustomUserSilence())? "默认设置": oldChatNode.getCustomUserSilenceSecond() + "秒";
                String newVal = BooleanUtils.isNotTrue(newChatNode.getEnableCustomUserSilence())? "默认设置": newChatNode.getCustomUserSilenceSecond() + "秒";
                logs.add(String.format("%s无应答时长设置【%s】修改为【%s】",
                        updatePrefix(oldNode), oldVal, newVal));
            }
            if (changeUserSilenceThreshold) {
                String oldVal = oldChatNode.getCustomUserSilenceSecond() + "秒";
                String newVal = newChatNode.getCustomUserSilenceSecond() + "秒";
                logs.add(String.format("%s无应答时长设置【%s】修改为【%s】",
                        updatePrefix(oldNode), oldVal, newVal));
            }
        }
    }

    private boolean changeEnableStatus(Boolean oldStats, Boolean newStats) {
        return BooleanUtils.isTrue(oldStats) != BooleanUtils.isTrue(newStats);
    }

    private Collection<String> compareAnswerAndCreateLog(DialogBaseNodePO oldNode, DialogBaseNodePO newNode, DependentResourceBO resource) {
        return AnswerChangeLogHelper.compareAnswerChange(oldNode.getAnswerList(), newNode.getAnswerList(), updatePrefix(oldNode), resource);
    }

}
