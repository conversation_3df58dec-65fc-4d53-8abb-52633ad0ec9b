package com.yiwise.dialogflow.service.impl.audio;

import com.yiwise.dialogflow.entity.po.AudioRecordLogPO;
import com.yiwise.dialogflow.service.AudioRecordLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 录音师录音日志
 */
@Slf4j
@Service
public class AudioRecordLogServiceImpl implements AudioRecordLogService {

    @Resource
    private MongoTemplate mongoTemplate;


    @Override
    public void create(Long botId, Long recordUserId, String text, String ossKey) {
        try {
            AudioRecordLogPO log = new AudioRecordLogPO();
            log.setUrl(ossKey);
            log.setText(text);
            log.setRecordUserId(recordUserId);
            log.setBotId(botId);
            log.setCreateTime(LocalDateTime.now());
            log.setUpdateTime(LocalDateTime.now());
            mongoTemplate.insert(log);
        } catch (Exception e) {
            log.error("存储录音日志异常, botId={}, recordUserId={}, text={}, ossKey={}", botId, recordUserId, text, ossKey, e);
        }
    }
}
