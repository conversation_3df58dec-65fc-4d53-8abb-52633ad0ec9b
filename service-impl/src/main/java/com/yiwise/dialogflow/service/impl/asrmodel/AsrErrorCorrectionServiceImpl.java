package com.yiwise.dialogflow.service.impl.asrmodel;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.MdcLogIdLifterTransformer;
import com.yiwise.dialogflow.entity.bo.algorithm.ModelTrainKey;
import com.yiwise.dialogflow.entity.enums.AlgorithmTrainTypeEnum;
import com.yiwise.dialogflow.entity.enums.SnapshotTypeEnum;
import com.yiwise.dialogflow.entity.enums.asr.AsrErrorCorrectionTrainStatusEnum;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.ModelVersionPO;
import com.yiwise.dialogflow.entity.po.TrainResultPO;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrErrorCorrectionDetailPO;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrCorrectionRequestVO;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrErrorCorrectionDetailVO;
import com.yiwise.dialogflow.service.BotRefService;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.asrmodel.AsrErrorCorrectionService;
import com.yiwise.dialogflow.service.remote.UserService;
import com.yiwise.dialogflow.service.train.ModelVersionService;
import com.yiwise.dialogflow.service.train.TrainService;
import com.yiwise.dialogflow.utils.ParseUtil;
import io.netty.handler.timeout.ReadTimeoutException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/14 15:45:13
 */
@Slf4j
@Service
public class AsrErrorCorrectionServiceImpl implements AsrErrorCorrectionService{
    private static final double DEFAULT_THRESHOLD = 0.7d;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private TrainService trainService;

    @Resource
    private ModelVersionService modelVersionService;


    private final LoadingCache<Long, Optional<String>> modelIdCache = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build(CacheLoader.from(this::getAsrErrorCorrectionModelId));

    private final LoadingCache<Long, Optional<BigDecimal>> thresholdCache = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build(CacheLoader.from(this::getAsrErrorCorrectionThreshold));

    private final LoadingCache<AsrCorrectionKey, String> CORRECT_RESULT_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build(CacheLoader.from(this::doErrorCorrection));

    @Resource
    private RestTemplate lowLatencyRestTemplate;

    @Resource
    private UserService userService;
    
    @Resource
    private BotService botService;

    @Resource
    private BotRefService botRefService;

    @Resource
    private WebClient webClient;

    @Override
    public void startTrain(String asrErrorCorrectionDetailId, Long userId, SystemEnum system) {
        AsrErrorCorrectionDetailPO detailPO = get(asrErrorCorrectionDetailId);

        Long tenantId = 0L;
        AlgorithmTrainTypeEnum trainType = AlgorithmTrainTypeEnum.ASR_ERROR_CORRECTION;
        ModelVersionPO modelVersionPO = modelVersionService.getByRefId(tenantId, detailPO.getAsrErrorCorrectionDetailId(), trainType);
        String modelId = modelVersionPO.getModelId();
        TrainResultPO trainResult = new TrainResultPO();
        trainResult.setTrainType(trainType);
        trainResult.setCreateTime(LocalDateTime.now());
        trainResult.setTenantId(0L);
        trainResult.setBotId(0L);
        trainResult.setCreateUserId(userId);
        trainResult.setModelId(modelId);
        trainResult.setSystemType(system);
        trainResult.setModelTrainKey(ModelTrainKey.of(0L, modelId, trainType));
        trainService.create(trainResult);

        detailPO.setTrainStatus(AsrErrorCorrectionTrainStatusEnum.TRAINING);
        detailPO.setModelId(modelId);
        detailPO.setLastTrainTime(LocalDateTime.now());
        detailPO.setUpdateUserId(userId);
        mongoTemplate.save(detailPO, AsrErrorCorrectionDetailPO.COLLECTION_NAME);
    }

    @Override
    public void trainSuccess(String refId) {
        AsrErrorCorrectionDetailPO updatePO = getByModelId(refId);
        updatePO.setTrainStatus(AsrErrorCorrectionTrainStatusEnum.TRAINED);
        updatePO.setUpdateTime(LocalDateTime.now());
        mongoTemplate.save(updatePO, AsrErrorCorrectionDetailPO.COLLECTION_NAME);
    }

    @Override
    public String requestAsrErrorCorrection(Long dialogFlowId, String userInput) {
        Long botId = botRefService.getBotId(dialogFlowId);
        return requestAsrErrorCorrectionByBotId(botId, userInput);
    }

    @Override
    public String requestAsrErrorCorrectionByBotId(Long botId, String userInput) {
        if (StringUtils.isBlank(userInput)) {
            return userInput;
        }
        userInput = userInput.trim();
        if (StringUtils.length(userInput) < 3) {
            return userInput;
        }
        String modelId;
        BigDecimal threshold;
        try {
            Optional<String> opt1 = modelIdCache.get(botId);
            if (!opt1.isPresent()) {
                log.info("当前话术{}不存在已训练完成的模型, 直接返回", botId);
                return userInput;
            } else {
                modelId = opt1.get();
            }
            Optional<BigDecimal> opt2 = thresholdCache.get(botId);
            threshold = opt2.orElseGet(() -> new BigDecimal(DEFAULT_THRESHOLD));
        } catch (Exception e) {
            log.error("缓存错误", e);
            return userInput;
        }

        String result;
        AsrCorrectionKey asrCorrectionKey = new AsrCorrectionKey(modelId, userInput, threshold);
        try {
            result = ApplicationConstant.ENABLE_CACHE ? CORRECT_RESULT_CACHE.get(asrCorrectionKey) : doErrorCorrection(asrCorrectionKey);
        } catch (Exception e) {
            log.error("执行asr纠错异常", e);
            result = doErrorCorrection(asrCorrectionKey);
        }
        return result;
    }

    @Override
    public Mono<String> requestAsrErrorCorrection(AsrCorrectionRequestVO request) {
        if (Objects.isNull(request)
                || StringUtils.isBlank(request.getUserInput())
                || StringUtils.isBlank(request.getModelId())
                || Objects.isNull(request.getBotId())
                || Objects.isNull(request.getThreshold())) {

            return Mono.just(request.getUserInput());
        }
        String userInput = request.getUserInput().trim();
        if (StringUtils.length(userInput) < 3) {
            return Mono.just(userInput);
        }
        String modelId = request.getModelId();
        BigDecimal threshold = request.getThreshold();
        AsrCorrectionKey asrCorrectionKey = new AsrCorrectionKey(modelId, userInput, threshold);
        try {
            if (ApplicationConstant.ENABLE_CACHE) {
                String result = CORRECT_RESULT_CACHE.getIfPresent(asrCorrectionKey);
                if (result == null) {
                    return doErrorCorrectionAsync(asrCorrectionKey)
                            .doOnNext(s -> CORRECT_RESULT_CACHE.put(asrCorrectionKey, s));
                }
                return Mono.just(result);
            }
            return doErrorCorrectionAsync(asrCorrectionKey);
        } catch (Exception e) {
            log.error("执行asr纠错异常", e);
            return Mono.just(userInput);
        }
    }

    private Optional<String> getAsrErrorCorrectionModelId(Long botId) {
        Optional<AsrErrorCorrectionDetailPO> optional = getByBotId(botId);
        return optional.map(AsrErrorCorrectionDetailPO::getModelId);
    }

    private Optional<BigDecimal> getAsrErrorCorrectionThreshold(Long botId) {
        Optional<AsrErrorCorrectionDetailPO> optional = getByBotId(botId);
        return optional.map(AsrErrorCorrectionDetailPO::getThreshold);
    }

    private String doErrorCorrection(AsrCorrectionKey asrCorrectionKey) {
        String modelId = asrCorrectionKey.getModelId();
        String userInput = asrCorrectionKey.getUserInput();
        log.info("执行asr文本纠错, modelId={}, userInput={}", modelId, userInput);

        Optional<ErrorCorrectionResultItem> resultOptional = requestErrCorrection(asrCorrectionKey);
        if (!resultOptional.isPresent() || StringUtils.isBlank(resultOptional.get().getWord())) {
            return userInput;
        }

        ErrorCorrectionResultItem result = resultOptional.get();
        // 检查下标是否越界
        if (result.endPos <= result.startPos || result.startPos >= userInput.length() || result.endPos > userInput.length()) {
            log.error("asr纠错响应信息下标异常, startPos={}, endPos={}", result.startPos, result.endPos);
            return userInput;
        }

        String finalUserInput = String.format("%s%s%s", userInput.substring(0, result.getStartPos()), result.getWord(), userInput.substring(result.endPos));
        log.info("执行asr文本纠错获取结果, modelId={}, userInput={}, finalUserInput={}", modelId, userInput, finalUserInput);
        return finalUserInput;
    }

    private Mono<String> doErrorCorrectionAsync(AsrCorrectionKey asrCorrectionKey) {
        String modelId = asrCorrectionKey.getModelId();
        String userInput = asrCorrectionKey.getUserInput();
        log.info("执行asr文本纠错, modelId={}, userInput={}", modelId, userInput);
        return requestErrCorrectionAsync(asrCorrectionKey)
                .filter(result -> StringUtils.isNotBlank(result.getWord()))
                .map(result -> {
                    if (result.endPos <= result.startPos || result.startPos >= userInput.length() || result.endPos > userInput.length()) {
                        log.error("asr纠错响应信息下标异常, startPos={}, endPos={}", result.startPos, result.endPos);
                        return userInput;
                    }
                    String finalUserInput = String.format("%s%s%s", userInput.substring(0, result.getStartPos()), result.getWord(), userInput.substring(result.endPos));
                    log.info("执行asr文本纠错获取结果, modelId={}, userInput={}, finalUserInput={}", modelId, userInput, finalUserInput);
                    return  finalUserInput;
                }).switchIfEmpty(Mono.just(userInput))
                .onErrorResume(e -> {
                    log.error("执行asr纠错异常", e);
                    return Mono.just(userInput);
                });
    }

    private Optional<ErrorCorrectionResultItem> requestErrCorrection(AsrCorrectionKey asrCorrectionKey) {
        double doubleValue = DEFAULT_THRESHOLD;
        String userInput = asrCorrectionKey.getUserInput();
        BigDecimal threshold = asrCorrectionKey.getThreshold();
        if (Objects.nonNull(threshold)) {
            doubleValue = threshold.setScale(2, RoundingMode.HALF_UP).doubleValue();
        }

        Map<String, Object> postMap = new HashMap<>();
        postMap.put("tenantId", 0L);
        postMap.put("robotId", asrCorrectionKey.getModelId());
        postMap.put("topk", 1);
        postMap.put("threshold", doubleValue);
        postMap.put("text", userInput);
        postMap.put("trainType", AlgorithmTrainTypeEnum.ASR_ERROR_CORRECTION.name());
        postMap.put("snapshotType", SnapshotTypeEnum.PUBLISHED.name());
        postMap.put("environment", ApplicationConstant.ALGORITHM_REGISTER_ENV);

        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add("Content-Type", "application/json");
            HttpEntity<String> requestEntity = new HttpEntity<>(JsonUtils.object2String(postMap), httpHeaders);
            log.info("远程调用算法asr纠错模型, url={}  param={}", ApplicationConstant.ASR_ERROR_CORRECT_URL, JsonUtils.object2String(postMap));
            ResponseEntity<String> response = lowLatencyRestTemplate.exchange(ApplicationConstant.ASR_ERROR_CORRECT_URL, HttpMethod.POST, requestEntity, String.class);
            String body = String.valueOf(response.getBody());
            log.info("远程调用算法asr纠错模型, response={}", body);
            return parseCorrectionResult(body);
        } catch (Exception e) {
            log.error("[LogHub_Warn]远程调用算法asr纠错模型失败, 用户输入={}, 失败原因={}", userInput, e.getMessage(), e);
        }

        return Optional.empty();
    }

    private Optional<ErrorCorrectionResultItem> parseCorrectionResult(String body) {
        if (StringUtils.isBlank(body) || "[]".equals(body)) {
            return Optional.empty();
        }

        JSONArray jsonArray = JSONObject.parseArray(body);
        if (jsonArray.isEmpty()) {
            return Optional.empty();
        }

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            Double score = jsonObject.getDouble("score");
            String word = jsonObject.getString("word");
            Integer start = jsonObject.getInteger("start_pos");
            Integer end = jsonObject.getInteger("end_pos");
            if (Objects.nonNull(score) && score >= DEFAULT_THRESHOLD
                        && StringUtils.isNotBlank(word)
                        && Objects.nonNull(start)
                        && Objects.nonNull(end)) {

                ErrorCorrectionResultItem res = new ErrorCorrectionResultItem();
                // 算法返回的是左闭右开区间, 我们处理的是左闭右闭
                res.setEndPos(end);
                res.setStartPos(start);
                res.setWord(word);
                return Optional.of(res);
            }
        }
        return Optional.empty();
    }

    private Mono<ErrorCorrectionResultItem> requestErrCorrectionAsync(AsrCorrectionKey asrCorrectionKey) {
        double doubleValue = DEFAULT_THRESHOLD;
        String userInput = asrCorrectionKey.getUserInput();
        BigDecimal threshold = asrCorrectionKey.getThreshold();
        if (Objects.nonNull(threshold)) {
            doubleValue = threshold.setScale(2, RoundingMode.HALF_UP).doubleValue();
        }

        Map<String, Object> postMap = new HashMap<>();
        postMap.put("tenantId", 0L);
        postMap.put("robotId", asrCorrectionKey.getModelId());
        postMap.put("topk", 1);
        postMap.put("threshold", doubleValue);
        postMap.put("text", userInput);
        postMap.put("trainType", AlgorithmTrainTypeEnum.ASR_ERROR_CORRECTION.name());
        postMap.put("snapshotType", SnapshotTypeEnum.PUBLISHED.name());
        postMap.put("environment", ApplicationConstant.ALGORITHM_REGISTER_ENV);

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Content-Type", "application/json");
        HttpEntity<String> requestEntity = new HttpEntity<>(JsonUtils.object2String(postMap), httpHeaders);
        log.info("远程调用算法asr纠错模型, url={}  param={}", ApplicationConstant.ASR_ERROR_CORRECT_URL, JsonUtils.object2String(postMap));
        long start = System.currentTimeMillis();
        return webClient.method(HttpMethod.POST)
                .uri(ApplicationConstant.ASR_ERROR_CORRECT_URL)
                .body(BodyInserters.fromValue(JsonUtils.object2String(postMap)))
                .retrieve()
                .bodyToMono(String.class)
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .doOnNext(body -> log.info("远程调用算法asr纠错模型, response={}, 耗时={}ms", body, System.currentTimeMillis() - start))
                .map(this::parseCorrectionResult)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .onErrorResume(e -> {
                    if (e instanceof ReadTimeoutException) {
                        log.warn("[LogHub_Warn]调用算法asr纠错接口异常, 用户输入={}", userInput, e);
                    } else {
                        log.warn("[LogHub_Warn]调用算法asr纠错接口异常, 用户输入={}, 失败原因={}", userInput, e.getMessage(), e);
                    }
                    return Mono.empty();
                });
    }

    @Data
    public static class ErrorCorrectionResultItem {
        String word;
        // 左闭右开
        int startPos;
        int endPos;
    }

    @Override
    public AsrErrorCorrectionDetailPO get(String asrErrorCorrectionDetailId) {
        if (StringUtils.isBlank(asrErrorCorrectionDetailId)) {
            return null;
        }
        try {
            return mongoTemplate.findById(asrErrorCorrectionDetailId, AsrErrorCorrectionDetailPO.class);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public void save(AsrErrorCorrectionDetailVO asrErrorCorrectionDetailVO) {
        AsrErrorCorrectionDetailPO updatePO = new AsrErrorCorrectionDetailPO();
        BeanUtils.copyProperties(asrErrorCorrectionDetailVO, updatePO);

        // 名称校验
        if (nameExists(updatePO.getAsrErrorCorrectionDetailId(), updatePO.getName())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "名称已存在");
        }

        // 语料去重
        updatePO.setCorpusList(Lists.newArrayList(Sets.newLinkedHashSet(updatePO.getCorpusList())));
        updatePO.setWhiteList(Lists.newArrayList(Sets.newLinkedHashSet(updatePO.getWhiteList())));

        boolean update = false;
        boolean hasChanged = false;
        if (StringUtils.isNotEmpty(asrErrorCorrectionDetailVO.getAsrErrorCorrectionDetailId())) {
            update = true;
            AsrErrorCorrectionDetailPO oldPO = get(asrErrorCorrectionDetailVO.getAsrErrorCorrectionDetailId());
            // 是否需要重新训练
            hasChanged = MyCollectionUtils.hasChanged(oldPO.getCorpusList(), updatePO.getCorpusList());
            if (hasChanged) {
                updatePO.setTrainStatus(AsrErrorCorrectionTrainStatusEnum.NOT_TRAIN);
            }
        } else {
            // 新建
            updatePO.setTrainStatus(AsrErrorCorrectionTrainStatusEnum.NOT_TRAIN);
            updatePO.setCreateUserId(updatePO.getUpdateUserId());
            updatePO.setCreateTime(LocalDateTime.now());
        }

        updatePO.setUpdateTime(LocalDateTime.now());
        mongoTemplate.save(updatePO);

        if (BooleanUtils.isTrue(asrErrorCorrectionDetailVO.getIsNeedTrain()) && (!update || hasChanged)) {
            // TODO 暂时只有OPE能改
            startTrain(updatePO.getAsrErrorCorrectionDetailId(), asrErrorCorrectionDetailVO.getUpdateUserId(), SystemEnum.OPE);
        }
    }

    @Override
    public void unbindBot(AsrErrorCorrectionDetailVO asrErrorCorrectionDetailVO) {
        if (Objects.isNull(asrErrorCorrectionDetailVO.getBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "要解绑的bot不能为空");
        }
        if (StringUtils.isEmpty(asrErrorCorrectionDetailVO.getAsrErrorCorrectionDetailId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "模型id不能为空");
        }
        botService.unBindAsrErrorCorrection(asrErrorCorrectionDetailVO.getBotId(), asrErrorCorrectionDetailVO.getAsrErrorCorrectionDetailId(), asrErrorCorrectionDetailVO.getUpdateUserId());
    }

    @Override
    public void bindBot(AsrErrorCorrectionDetailVO asrErrorCorrectionDetailVO) {
        if (Objects.isNull(asrErrorCorrectionDetailVO.getBotIdList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "要绑定的bot不能为空");
        }
        if (StringUtils.isEmpty(asrErrorCorrectionDetailVO.getAsrErrorCorrectionDetailId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "模型id不能为空");
        }
        botService.bindAsrErrorCorrection(asrErrorCorrectionDetailVO.getBotIdList(), asrErrorCorrectionDetailVO.getAsrErrorCorrectionDetailId(), asrErrorCorrectionDetailVO.getUpdateUserId());
    }

    @Override
    public void delete(String asrErrorCorrectionDetailId) {
        // 校验
        List<BotPO> botList = getBotList(Lists.newArrayList(asrErrorCorrectionDetailId), null);
        if (CollectionUtils.isNotEmpty(botList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "不可删除已绑定话术的模型");
        }
        Query query = Query.query(Criteria.where("_id").is(asrErrorCorrectionDetailId));
        mongoTemplate.remove(query, AsrErrorCorrectionDetailPO.class);
    }

    @Override
    public List<BotPO> getBotList(List<String> asrErrorCorrectionDetailId, String botInfo) {
        if (CollectionUtils.isEmpty(asrErrorCorrectionDetailId)) {
            return Collections.emptyList();
        }
        Example example = new Example(BotPO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("asrErrorCorrectionDetailId", asrErrorCorrectionDetailId);
        if (StringUtils.isNotEmpty(botInfo)) {
            criteria.andLike("name", botInfo);
            if (StringUtils.isNumeric(botInfo)) {
                criteria.orEqualTo("botId", botInfo);
            }
        }
        return botService.selectListByExample(example);
    }

    @Override
    public PageResultObject<BotPO> getBotList(String asrErrorCorrectionDetailId, String botInfo, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        return PageResultObject.of(getBotList(Lists.newArrayList(asrErrorCorrectionDetailId), botInfo));
    }

    @Override
    public PageResultObject<AsrErrorCorrectionDetailVO> list(String name, AsrErrorCorrectionTrainStatusEnum status, String botInfo, Integer pageNum, Integer pageSize) {
        Query query = new Query();
        if (StringUtils.isNotEmpty(name)) {
            query.addCriteria(Criteria.where("name").regex(ParseUtil.regex(name)));
        }

        if (Objects.nonNull(status)) {
            query.addCriteria(Criteria.where("trainStatus").is(status));
        }

        if (StringUtils.isNotEmpty(botInfo)) {
            Example example = new Example(BotPO.class);
            Example.Criteria criteria = example.createCriteria().andLike("name", botInfo);
            if (StringUtils.isNumeric(botInfo)) {
                criteria.orEqualTo("botId", botInfo);
            }
            List<BotPO> botPOList = botService.selectListByExample(example);
            if (CollectionUtils.isNotEmpty(botPOList)) {
                List<String> collect = botPOList.stream().map(BotPO::getAsrErrorCorrectionDetailId).collect(Collectors.toList());
                query.addCriteria(Criteria.where("_id").in(collect));
            }
        }

        long totalCount = mongoTemplate.count(query, AsrErrorCorrectionDetailPO.COLLECTION_NAME);
        query.with(PageRequest.of(pageNum - 1, pageSize));
        query.with(Sort.by(Sort.Direction.DESC, "updateTime"));

        List<AsrErrorCorrectionDetailVO> resultList = mongoTemplate.find(query, AsrErrorCorrectionDetailVO.class, AsrErrorCorrectionDetailPO.COLLECTION_NAME);

        // 获取纠错模型-话术的绑定关系
        List<String> asrErrorCorrectionDetailIdList = MyCollectionUtils.listToConvertList(resultList, AsrErrorCorrectionDetailVO::getAsrErrorCorrectionDetailId);
        List<BotPO> botList = getBotList(asrErrorCorrectionDetailIdList, null);
        Map<String, Long> map = MyCollectionUtils.listToMap(botList, BotPO::getAsrErrorCorrectionDetailId, BotPO::getBotId);

        resultList.forEach(result -> {
            if (Objects.nonNull(result.getUpdateUserId())) {
                UserPO user = userService.getUserById(result.getUpdateUserId());
                if (Objects.nonNull(user)) {
                    result.setUpdateUserName(user.getName());
                }
            }

            // 是否绑定话术
            result.setIsBound(map.containsKey(result.getAsrErrorCorrectionDetailId()));

            // 清理训练超时的模型
            if (AsrErrorCorrectionTrainStatusEnum.TRAINING.equals(result.getTrainStatus())
                        && Objects.nonNull(result.getLastTrainTime())
                        && result.getLastTrainTime().isBefore(LocalDateTime.now().minusMinutes(10))) {
                result.setTrainStatus(AsrErrorCorrectionTrainStatusEnum.NOT_TRAIN);
                AsrErrorCorrectionDetailPO detailPO = MyBeanUtils.copy(result, AsrErrorCorrectionDetailPO.class);
                detailPO.setUpdateTime(LocalDateTime.now());
                mongoTemplate.save(detailPO);
            }
        });
        return PageResultObject.of(resultList, pageNum, pageSize, (int) totalCount);
    }

    @Override
    public PageResultObject<IdNamePair<String, String>> idNamePairList(String name, Integer pageNum, Integer pageSize) {
        Query query = new Query();
        if (StringUtils.isNotEmpty(name)) {
            query.addCriteria(Criteria.where("name").regex(ParseUtil.regex(name)));
        }

        long totalCount = mongoTemplate.count(query, AsrErrorCorrectionDetailPO.COLLECTION_NAME);
        query.with(PageRequest.of(pageNum - 1, pageSize));

        List<AsrErrorCorrectionDetailPO> resultList = mongoTemplate.find(query, AsrErrorCorrectionDetailPO.class);
        List<IdNamePair<String, String>> idNamePairList = resultList.stream().map(detailPO -> new IdNamePair<>(detailPO.getAsrErrorCorrectionDetailId(), detailPO.getName())).collect(Collectors.toList());
        return PageResultObject.of(idNamePairList, pageNum, pageSize, (int) totalCount);
    }

    @Override
    public Optional<AsrErrorCorrectionDetailPO> getByBotId(Long botId) {
        BotPO botPO = botService.getById(botId);
        Assert.notNull(botPO, "bot不存在");
        if (StringUtils.isEmpty(botPO.getAsrErrorCorrectionDetailId())) {
            return Optional.empty();
        }
        return Optional.of(get(botPO.getAsrErrorCorrectionDetailId()));
    }

    @Override
    public AsrErrorCorrectionDetailPO getByModelId(String modelId) {
        ModelVersionPO modelVersionPO = modelVersionService.get(modelId);
        Assert.notNull(modelVersionPO, "modelVersion不存在");
        String refId = modelVersionPO.getRefId();
        return get(refId);
    }

    @Override
    public boolean nameExists(String asrErrorCorrectionDetailId, String name) {
        Query query = Query.query(Criteria.where("name").is(name));
        if (StringUtils.isNotEmpty(asrErrorCorrectionDetailId)) {
            query.addCriteria(Criteria.where("_id").ne(asrErrorCorrectionDetailId));
        }
        return mongoTemplate.exists(query, AsrErrorCorrectionDetailPO.class);
    }

    @Data
    public static class AsrCorrectionKey {
        String modelId;
        String userInput;

        BigDecimal threshold;

        public AsrCorrectionKey(String modelId, String userInput, BigDecimal threshold) {
            this.modelId = modelId;
            this.userInput = userInput;
            this.threshold = threshold;
        }
    }
}