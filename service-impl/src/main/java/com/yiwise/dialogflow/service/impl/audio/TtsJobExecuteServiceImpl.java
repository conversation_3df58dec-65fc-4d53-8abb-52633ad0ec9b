package com.yiwise.dialogflow.service.impl.audio;

import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.base.model.websocket.BasicMsg;
import com.yiwise.middleware.redis.service.RedisOpsService;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.OssKeyCenter;
import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.entity.bo.TtsJobBO;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import com.yiwise.dialogflow.entity.po.BotAudioConfigPO;
import com.yiwise.dialogflow.entity.po.TtsJobPO;
import com.yiwise.dialogflow.entity.po.TtsVoiceConfigPO;
import com.yiwise.dialogflow.entity.vo.audio.AudioUploadResultVO;
import com.yiwise.dialogflow.entity.vo.audio.TtsComposeProgressVO;
import com.yiwise.dialogflow.entity.vo.audio.TtsComposeResultVO;
import com.yiwise.dialogflow.entity.vo.audio.request.TtsComposeRequestVO;
import com.yiwise.dialogflow.helper.AliMessageQueueHelper;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.operationlog.AnswerAudioOperationLogService;
import com.yiwise.dialogflow.service.remote.TtsComposeService;
import com.yiwise.dialogflow.thread.DynamicDataSourceApplicationExecutorHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class TtsJobExecuteServiceImpl implements TtsJobExecuteService {

    /**
     * 每个答案都刷新, 一个答案处理超时了, 就快速超时
     */
    private static final long TTS_JOB_INTERVAL_TIMEOUT = 60;

    /**
     * 连续多少个答案都超时了, 则直接结束任务
     */
    private static final int CONTINUOUS_ERROR_COUNT = 5;

    private static final int MAX_TRY_COUNT = 3;

    @Resource
    private BotConfigService botConfigService;

    @Resource
    private AnswerAudioMappingService answerAudioMappingService;

    @Resource
    private RedisOpsService redisOpsService;

    @Resource
    private TtsComposeService ttsComposeService;

    @Resource
    private TtsJobService ttsJobService;

    @Resource
    private AnswerAudioOperationLogService answerAudioOperationLogService;

    @Override
    public void submit(TtsJobPO job) {
        TtsJobBO ttsJob = new TtsJobBO();
        ttsJob.setBotId(job.getBotId());
        ttsJob.setOriginJob(job);

        ttsJob.setUniqueKey(generateFullBotTtsJobUniqueKey(job.getBotId()));
        ttsJob.setUniqueValue(job.getId());
        execCompose(ttsJob);
    }

    private void updateTtsJobProgress(TtsJobPO job, Set<String> completedSet, Set<String> failSet, boolean finish) {
        // 更新tts合成进度
        // 更新任务信息
        // 发送推送通知
        int totalCount = job.getTotalCount();
        int manMadeCompletedCount = job.getManMadeCompletedCount();
        int alreadyCompletedCount = totalCount - job.getTextCount();
        int completedCount = completedSet.size();
        int allCompletedCount = alreadyCompletedCount + completedCount;
        int ttsCompletedCount = allCompletedCount - manMadeCompletedCount;
        int percent = allCompletedCount * 100 / totalCount;
        int manMadePercent = manMadeCompletedCount * 100 / totalCount;
        int ttsPercent = ttsCompletedCount * 100 / totalCount;

        TtsComposeProgressVO progress = new TtsComposeProgressVO();
        progress.setRunning(!finish);
        progress.setFailed(finish && CollectionUtils.isNotEmpty(failSet));
        progress.setPercent(percent);
        progress.setManMadePercent(manMadePercent);
        progress.setTtsPercent(ttsPercent);
        progress.setBotId(job.getBotId());
        progress.setTotalCount(totalCount);
        progress.setCompletedCount(completedCount);
        progress.setBotName(job.getBotName());
        sendProgressMsg(progress);

        ttsJobService.updateProgress(job.getBotId(), job.getId(), new HashSet<>(completedSet), new HashSet<>(failSet), finish);
    }

    private void sendProgress(Long botId, String jobId, Long userId, int total, int completedCount, int percent, boolean finish) {
        TtsComposeProgressVO progress = new TtsComposeProgressVO();
        progress.setRunning(!finish);
        progress.setPercent(percent);
        progress.setBotId(botId);
        progress.setTotalCount(total);
        progress.setCompletedCount(completedCount);
        sendProgressMsg(progress);
    }

    private void sendProgressMsg(TtsComposeProgressVO progress) {
        try {
            BasicMsg<TtsComposeProgressVO> msg = new BasicMsg<>();
            msg.setInfo(progress);
            msg.setMsg("tts合成进度更新");
            log.info("推送tts合成进度, msg={}", JsonUtils.object2String(progress));
            AliMessageQueueHelper.sendWebSocketMessage(ApplicationConstant.WEBSOCKET_TTS_JOB_SUBSCRIPT_URL, msg, "TTS");
        } catch (Exception e) {
            log.warn("推送tts合成进度失败", e);
        }
    }

    private void execCompose(TtsJobBO composeJob) {
        flushJobTimeout(composeJob.getUniqueKey(), composeJob.getUniqueValue());

        final RequestAttributes attributes = RequestContextHolder.getRequestAttributes();

        // 提交异步任务到线程池
        Runnable task = () -> {
            answerAudioOperationLogService.ttsComposeLog(composeJob.getBotId(), "开始合成音频", composeJob.getOriginJob().getUserId());
            RequestContextHolder.setRequestAttributes(attributes);
            String logId = composeJob.getOriginJob().getLogId();
            if (StringUtils.isNotBlank(logId)) {
                MDC.put(ApplicationConstant.MDC_LOG_ID, logId);
            }
            Set<String> completeSet = new HashSet<>();
            Set<String> failSet = new HashSet<>();
            TtsJobPO originJob = composeJob.getOriginJob();
            try {
                boolean cancel = false;
                String voice = originJob.getVoice();
                Float speed = originJob.getSpeed();
                Float volume = originJob.getVolume();
                int continuousErrorCount = 0;
                for (String answerText : originJob.getTextSet()) {
                    try {
                        updateTtsJobProgress(originJob, completeSet, failSet, false);
                        if (!cancel && checkJobIsCancel(composeJob.getUniqueKey(), composeJob.getUniqueValue())) {
                            log.info("当前任务已经被取消了, 退出合成, 总任务{}句, 目前已合成{}句, ", originJob.getTextCount(), completeSet.size());
                            cancel = true;
                            break;
                        }
                        flushJobTimeout(composeJob.getUniqueKey(), composeJob.getUniqueValue());
                        if (!answerText.trim().isEmpty()) {
                            AudioUploadResultVO result = doComposeWithRetry(composeJob.getBotId(), answerText, voice, speed, volume, MAX_TRY_COUNT);
                            answerAudioMappingService.upsertAudioMapping(composeJob.getBotId(), 0L, AudioTypeEnum.COMPOSE, answerText, result.getUrl(), result.getVolume());
                        }
                        completeSet.add(answerText);
                        continuousErrorCount = 0;
                    } catch (Exception e) {
                        log.warn("合成过程中异常", e);
                        failSet.add(answerText);
                        continuousErrorCount++;
                        if (continuousErrorCount >= CONTINUOUS_ERROR_COUNT) {
                            String msg = String.format("合成过程中出现连续%s次异常, 取消合成任务", CONTINUOUS_ERROR_COUNT);
                            log.warn(msg);
                            cancel = true;
                            answerAudioOperationLogService.ttsComposeLog(composeJob.getBotId(), msg, composeJob.getOriginJob().getUserId());
                        }
                    }
                }
                if (!cancel) {
                    answerAudioOperationLogService.ttsComposeLog(composeJob.getBotId(), "完成音频合成", composeJob.getOriginJob().getUserId());
                }
            } finally {
                redisOpsService.delete(composeJob.getUniqueKey());
                updateTtsJobProgress(originJob, completeSet, failSet, true);
                RequestContextHolder.resetRequestAttributes();
            }
        };

        DynamicDataSourceApplicationExecutorHolder.execute("执行tts合成", task);
    }


    private void flushJobTimeout(String uniqueKey, String uniqueValue) {
        redisOpsService.set(uniqueKey, uniqueValue, TTS_JOB_INTERVAL_TIMEOUT);
    }

    private AudioUploadResultVO doCompose(Long botId, String answerText, String voice, Float speed, Float volume) {
        TtsComposeRequestVO request = new TtsComposeRequestVO();
        request.setVoice(voice);
        request.setSpeed(speed);
        request.setVolume(volume);
        request.setAnswerText(answerText);
        request.setFilePath(generateOssKey(botId, answerText));
        try {
            TtsComposeResultVO composeResult = ttsComposeService.composeTextByConfig(request);
            if (Objects.isNull(composeResult) || StringUtils.isBlank(composeResult.getRelativeUrl())) {
                throw new ComException(ComErrorCode.UNKNOWN_ERROR, "调用tts合成服务出错, 返回路径为空");
            }
            log.info("tts 合成完成, answer={}, url={}", answerText, composeResult.getFullUrl());

            AudioUploadResultVO result = new AudioUploadResultVO();
            result.setFullUrl(composeResult.getFullUrl());
            result.setVolume(volume.intValue());
            result.setBotId(botId);
            result.setUrl(composeResult.getRelativeUrl());
            return result;
        } catch (Exception e) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "调用tts合成服务出错" + e);
        }
    }
    private AudioUploadResultVO doComposeWithRetry(Long botId, String answerText, String voice, Float speed, Float volume, int leftRetry) {
        try {
            return doCompose(botId, answerText, voice, speed, volume);
        } catch (Exception e) {
            log.warn("tts合成失败, 剩余重试次数{}", leftRetry, e);
            if (leftRetry > 0) {
                return doComposeWithRetry(botId, answerText, voice, speed, volume, leftRetry - 1);
            } else {
                throw new ComException(ComErrorCode.UNKNOWN_ERROR, "调用tts合成服务出错" + e);
            }
        }
    }

    private String generateOssKey(Long botId, String answerText) {
        return OssKeyCenter.getBotTtsAudioPath(botId, generateFileName());
    }

    private String generateFileName() {
        return String.format("%s.wav", new ObjectId().toHexString());
    }

    @Override
    public TtsComposeResultVO composeTextByBotConfig(Long botId, String text) {
        BotAudioConfigPO audioConfig = botConfigService.getAudioConfig(botId);

        TtsVoiceConfigPO ttsConfig = audioConfig.getTtsConfig();
        TtsComposeRequestVO request = new TtsComposeRequestVO();
        request.setSpeed(ttsConfig.getTtsSpeed());
        request.setVolume(ttsConfig.getTtsVolume());
        request.setAnswerText(text);
        request.setVoice(ttsConfig.getTtsVoice());

        return ttsComposeService.composeTextByConfig(request);
    }

    @Override
    public boolean jobIsRunning(TtsJobPO job) {
        if (Objects.isNull(job)) {
            return false;
        }
        String uniqueKey = generateFullBotTtsJobUniqueKey(job.getBotId());
        String runningJobId = redisOpsService.get(uniqueKey);
        return job.getId().equals(runningJobId);
    }

    @Override
    public boolean botIsComposing(Long botId) {
        String uniqueKey = generateFullBotTtsJobUniqueKey(botId);
        String runningJobId = redisOpsService.get(uniqueKey);
        return StringUtils.isNotBlank(runningJobId);
    }

    private String generateFullBotTtsJobUniqueKey(Long botId) {
        return RedisKeyCenter.getTtsJobUniqueKey(botId);
    }

    private boolean checkJobIsCancel(String key, String expectValue) {
        String newestValue = redisOpsService.get(key);
        return StringUtils.isBlank(newestValue) || !StringUtils.equals(expectValue, newestValue);
    }
}
