package com.yiwise.dialogflow.service.impl;

import com.google.common.collect.Lists;
import com.opencsv.CSVReader;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.enums.GroupTypeEnum;
import com.yiwise.dialogflow.entity.enums.IntentPropertiesEnum;
import com.yiwise.dialogflow.entity.enums.SyncModeEnum;
import com.yiwise.dialogflow.entity.po.DomainIntentPO;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.query.DomainIntentQuery;
import com.yiwise.dialogflow.entity.vo.BotVO;
import com.yiwise.dialogflow.entity.vo.DomainIntentVO;
import com.yiwise.dialogflow.entity.vo.SyncResultVO;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.DomainIntentService;
import com.yiwise.dialogflow.service.GroupService;
import com.yiwise.dialogflow.service.intent.IntentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/2/18
 */
@Slf4j
@Service
public class DomainIntentServiceImpl implements DomainIntentService {

    @Resource
    private MongoTemplate primaryMongoTemplate;

    @Resource
    private IntentService intentService;

    @Resource
    private GroupService groupService;

    @Resource
    private BotService botService;

    @Override
    public PageResultObject<DomainIntentVO> list(DomainIntentQuery intentQuery) {
        Query query = toQuery(intentQuery);
        long count = primaryMongoTemplate.count(query, DomainIntentPO.class);
        if (intentQuery.getWithPage()) {
            query.with(PageRequest.of(intentQuery.getPageNum() - 1, intentQuery.getPageSize()));
        }
        List<DomainIntentPO> domainIntentPOList = primaryMongoTemplate.find(query, DomainIntentPO.class);
        domainIntentPOList.forEach(this::processDomainIntent);
        return PageResultObject.of(buildIsUse(domainIntentPOList, intentQuery), intentQuery.getPageNum(), intentQuery.getPageNum(), (int) count);
    }

    private List<DomainIntentVO> buildIsUse(List<DomainIntentPO> domainIntentPOList, DomainIntentQuery intentQuery) {
        BotVO botVO = null;
        if (Objects.nonNull(intentQuery.getBotId())) {
            botVO = botService.detail(intentQuery.getBotId());
            if (StringUtils.isNotEmpty(botVO.getDomainName()) && !botVO.getDomainName().equals(intentQuery.getDomainName())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "当前bot已调用过其他类型的内置意图");
            }
        }
        List<DomainIntentVO> domainIntentVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(domainIntentPOList)) {
            List<IntentPO> buildInIntentPOList = new ArrayList<>();
            List<IntentPO> customizedIntentPOList = new ArrayList<>();
            if (Objects.nonNull(botVO)) {
                Query query = Query.query(Criteria.where("botId").is(botVO.getBotId()).and("corpusType").is("BUILD_IN"));
                buildInIntentPOList = primaryMongoTemplate.find(query, IntentPO.class);
                Query query1 = Query.query(Criteria.where("botId").is(botVO.getBotId()).and("corpusType").is("CUSTOMIZED"));
                customizedIntentPOList = primaryMongoTemplate.find(query1, IntentPO.class);
            }
            List<String> buildInNameList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(buildInIntentPOList)) {
                buildInNameList = buildInIntentPOList.stream().map(IntentPO::getName).collect(Collectors.toList());
            }
            List<String> customizedNameList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(customizedIntentPOList)) {
                customizedNameList = customizedIntentPOList.stream().map(IntentPO::getName).collect(Collectors.toList());
            }
            List<String> finalBuildInNameList = buildInNameList;
            List<String> finalCustomizedNameList = customizedNameList;
            domainIntentPOList.forEach(domainIntentPO -> {
                DomainIntentVO domainIntentVO = MyBeanUtils.copy(domainIntentPO, DomainIntentVO.class);
                if (CollectionUtils.isNotEmpty(finalBuildInNameList) && finalBuildInNameList.contains(domainIntentPO.getName())) {
                    domainIntentVO.setIsUse(true);
                }
                if (CollectionUtils.isNotEmpty(finalCustomizedNameList) && finalCustomizedNameList.contains(domainIntentPO.getName())) {
                    domainIntentVO.setIsSame(true);
                }
                domainIntentVOList.add(domainIntentVO);
            });


        }
        return domainIntentVOList;
    }

    @Override
    public List<DomainIntentPO> listAll() {
        Query query = new Query();
        List<DomainIntentPO> domainIntentPOList = primaryMongoTemplate.find(query, DomainIntentPO.class);
        return domainIntentPOList;
    }

    /**
     * 第一遍前端不传SyncMode，如果没有重复返回false，同步成功
     * 如果有重复返回true，前端再传一遍参数，带上SyncMode
     */
    @Override
    public SyncResultVO sync(DomainIntentQuery intentQuery) {
        SyncResultVO syncResultVO = new SyncResultVO();
        syncResultVO.setRepeated(false);

        Query query = toQuery(intentQuery);
        List<DomainIntentPO> domainIntentPOList = primaryMongoTemplate.find(query, DomainIntentPO.class);
        if (CollectionUtils.isNotEmpty(domainIntentPOList) && CollectionUtils.isNotEmpty(intentQuery.getExcludeIntentIdList())) {
            domainIntentPOList = domainIntentPOList.stream().filter(domainIntentPO -> !intentQuery.getExcludeIntentIdList().contains(domainIntentPO.getId())).collect(Collectors.toList());
        }
        List<String> nameList = MyCollectionUtils.listToConvertList(domainIntentPOList, DomainIntentPO::getName);

        List<Long> botIdList = intentQuery.getBotIdList();
        if (CollectionUtils.isEmpty(botIdList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "bot列表不能为空");
        }
        if (botIdList.size() == 1) {
            Assert.isTrue(StringUtils.isNotBlank(intentQuery.getTargetGroupId()), "目标分组不能为空");
            Assert.notNull(groupService.selectOne(intentQuery.getTargetGroupId(), GroupTypeEnum.SINGLE_INTENT, botIdList.get(0)), "目标分组不存在");
        } else {
            intentQuery.setTargetGroupId(null);
        }
        SyncModeEnum syncMode = intentQuery.getSyncMode();
        Long currentUserId = intentQuery.getCurrentUserId();

        if (Objects.isNull(syncMode)) {
            Long repeatCount = intentService.nameExistsCount(nameList, botIdList.get(0));
            if (repeatCount > 0) {
                syncResultVO.setRepeated(true);
                syncResultVO.setTotalCount(domainIntentPOList.size());
                syncResultVO.setRepeatedCount(Integer.valueOf(repeatCount.toString()));
                return syncResultVO;
            }
        }

        // 对同名意图进行更新，非同名意图进行创建
        intentService.batchInsert(botIdList, domainIntentPOList, syncMode, currentUserId, intentQuery.getTargetGroupId());

        return syncResultVO;
    }

    private Query toQuery(DomainIntentQuery intentQuery) {
        Query query = new Query();

        if (CollectionUtils.isNotEmpty(intentQuery.getIntentIdList())) {
            query.addCriteria(Criteria.where("_id").in(intentQuery.getIntentIdList()));
        } else {
            if (StringUtils.isNotEmpty(intentQuery.getDomainName())) {
                query.addCriteria(Criteria.where("domainName").is(intentQuery.getDomainName()));
            }
            if (StringUtils.isNotEmpty(intentQuery.getKeyword())) {
                query.addCriteria(Criteria.where("").orOperator(
                        Criteria.where("name").regex(Pattern.compile(intentQuery.getKeyword())),
                        Criteria.where("corpusList").regex(Pattern.compile(intentQuery.getKeyword())),
                        Criteria.where("regexList").regex(Pattern.compile(intentQuery.getKeyword()))
                ));
            }
        }

        return query;
    }

    /**
     * 清空内置问法和正则
     */
    private void processDomainIntent(DomainIntentPO domainIntentPO) {
        List<String> builtInCorpusList = domainIntentPO.getBuiltInCorpusList();
        if (CollectionUtils.isNotEmpty(builtInCorpusList) && builtInCorpusList.size() > 1) {
            builtInCorpusList = Lists.newArrayList(builtInCorpusList.subList(0, 2));
        } else if (CollectionUtils.isNotEmpty(builtInCorpusList)) {
            builtInCorpusList = Lists.newArrayList(builtInCorpusList.subList(0, 1));
        }
        domainIntentPO.setCorpusList(builtInCorpusList);
        domainIntentPO.setBuiltInCorpusList(null);

        List<String> builtInRegexList = domainIntentPO.getBuiltInRegexList();
        if (CollectionUtils.isNotEmpty(builtInRegexList) && builtInRegexList.size() > 1) {
            builtInRegexList = Lists.newArrayList(builtInRegexList.subList(0, 2));
        } else if (CollectionUtils.isNotEmpty(builtInRegexList)) {
            builtInRegexList = Lists.newArrayList(builtInRegexList.subList(0, 1));
        }
        domainIntentPO.setRegexList(builtInRegexList);
        domainIntentPO.setBuiltInRegexList(null);
    }

    @Override
    public void importFromFile(String domainName, MultipartFile multipartFile, Boolean isCover) {
        CSVReader csvReader = null;
        boolean isCsv = Objects.requireNonNull(multipartFile.getOriginalFilename()).endsWith(".csv");
        try (BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(multipartFile.getInputStream()))) {
            MultiValueMap<String, String> corpusMap = new LinkedMultiValueMap<>();
            Map<String, IntentPropertiesEnum> propertiesMap = new HashMap<>();
            if (isCsv) {
                csvReader = new CSVReader(bufferedReader);
                // 第一行是表头
                csvReader.readNext();

                String[] next;
                while ((next = csvReader.readNext()) != null) {
                    // 语料
                    corpusMap.add(next[0], next[1]);
                    // 意图属性
                    try {
                        propertiesMap.put(next[0], IntentPropertiesEnum.fromNameOrDesc(next[2]));
                    } catch (Exception e) {
                        propertiesMap.put(next[0], IntentPropertiesEnum.NEUTRAL);
                    }
                }
            } else {
                List<String> lines = bufferedReader.lines().collect(Collectors.toList());
                lines.forEach(line -> {
                    String[] split = line.split("\\|,\\|");
                    corpusMap.add(split[1], split[0]);
                });
            }

            if (BooleanUtils.isTrue(isCover)) {
                Query query = Query.query(Criteria.where("domainName").is(domainName));
                primaryMongoTemplate.remove(query, DomainIntentPO.class);
            }

            List<DomainIntentPO> domainIntentPOList = Lists.newArrayList();
            corpusMap.forEach((k, v) -> {
                Query query = Query.query(Criteria.where("domainName").is(domainName).and("name").is(k));
                DomainIntentPO domainIntentPO = primaryMongoTemplate.findOne(query, DomainIntentPO.class);
                if (Objects.isNull(domainIntentPO)) {
                    domainIntentPO = new DomainIntentPO();
                    domainIntentPO.setDomainName(domainName);
                    domainIntentPO.setName(k);
                    domainIntentPO.setIntentProperties(propertiesMap.get(k));
                    domainIntentPO.setBuiltInCorpusList(v);
                    domainIntentPO.setCreateTime(LocalDateTime.now());
                    domainIntentPO.setUpdateTime(LocalDateTime.now());
                    domainIntentPOList.add(domainIntentPO);
                } else {
                    Query updateQuery = Query.query(Criteria.where("_id").is(domainIntentPO.getId()));
                    Update update = Update.update("builtInCorpusList", v);
                    update.set("updateTime", LocalDateTime.now());
                    primaryMongoTemplate.updateFirst(updateQuery, update, DomainIntentPO.class);
                }
            });
            primaryMongoTemplate.insertAll(domainIntentPOList);
        } catch (Exception e) {
            log.error("读取文件错误", e);
        } finally {
            if (Objects.nonNull(csvReader)) {
                try {
                    csvReader.close();
                } catch (IOException e) {
                    log.error("关闭csv错误", e);
                }
            }
        }
    }
}
