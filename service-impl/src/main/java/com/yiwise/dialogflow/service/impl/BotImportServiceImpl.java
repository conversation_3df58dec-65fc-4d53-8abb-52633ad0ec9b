package com.yiwise.dialogflow.service.impl;

import cn.hutool.core.io.FileUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.file.ZipUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import com.yiwise.dialogflow.api.enums.V3BotTypeEnum;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.OssKeyCenter;
import com.yiwise.dialogflow.common.TempFilePathKeyCenter;
import com.yiwise.dialogflow.entity.dto.BotGenerateCreateResultDTO;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.enums.llm.RagDocumentStatusEnum;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.llm.RagDocumentPO;
import com.yiwise.dialogflow.entity.vo.*;
import com.yiwise.dialogflow.entity.vo.audio.AudioUploadResultVO;
import com.yiwise.dialogflow.entity.vo.audio.request.AnswerUpdateAudioRequestVO;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.botgenerate.BotGenerateRecordService;
import com.yiwise.dialogflow.service.llm.LlmStepConfigService;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BotImportServiceImpl implements BotImportService {

    @Resource
    private BotService botService;
    @Resource
    private KnowledgeService knowledgeService;
    @Resource
    private StepService stepService;
    @Resource
    private StepNodeService stepNodeService;
    @Resource
    private SpecialAnswerConfigService specialAnswerConfigService;
    @Resource
    private BotResourceSequenceService botResourceSequenceService;
    @Resource
    private BotGenerateRecordService botGenerateRecordService;
    @Resource
    private LlmStepConfigService llmStepConfigService;
    @Resource
    private AnswerAudioManagerService answerAudioManagerService;

    @Resource
    private AudioUploadService audioUploadService;

    @Resource
    private ObjectStorageHelper objectStorageHelper;

    private static final String NON_SUPPORT = "[生成失败] 暂时没有支持";

    private static final String NUM_ERROR = "[生成失败] 要求文本数量与生成结果不一致,生成结果有";


    private Long importBySnapshotJson(RobotSnapshotPO snapshot, Long intentLevelTagId, Long recorderId, Long userId) {

        // 处理一些客户相关的数据
        // 1. 录音师信息
        // 2. 清空所有的动作配置
        BotConfigPO botConfig = snapshot.getBotConfig();
        if (!AudioTypeEnum.COMPOSE.equals(botConfig.getAudioConfig().getAudioType())
                && Objects.isNull(recorderId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "真人录音 bot, 录音师不能为空");
        }
        botConfig.getAudioConfig().setRecordUserId(recorderId);

        BotPO bot = snapshot.getBot();

        // 基本信息
        bot.setIntentLevelTagId(intentLevelTagId);
        bot.setCreateUserId(userId);
        bot.setUpdateUserId(userId);
        bot.setCreateTenantId(ApplicationConstant.OPE_TENANT_ID);
        bot.setCreateTime(LocalDateTime.now());
        bot.setUpdateTime(LocalDateTime.now());
        bot.setPublished(false);
        bot.setQrCodeId(null);
        bot.setQrCodeName(null);

        // asr相关配置
        bot.setAsrErrorCorrectionDetailId(null);
        bot.setAsrSelfLearningDetailId(null);
        bot.setAsrVocabId(null);

        // 背景音设置
        botConfig.getAudioConfig().setEnableBackground(false);
        botConfig.getAudioConfig().setBackgroundList(Collections.emptyList());
        Long newBotId = botService.createFromSnapshot(snapshot);

        // 重置所有的标签
        botResourceSequenceService.cleanAllSequence(newBotId);
        stepService.resetAllStepLabel(newBotId);
        knowledgeService.resetAllKnowledgeLabel(newBotId);
        specialAnswerConfigService.resetAllSpecialAnswerLabel(newBotId);

        // 重新生成资源引用(意图和变量)
        stepService.resetResourceReferenceInfo(newBotId);
        stepNodeService.resetResourceReferenceInfo(newBotId);
        knowledgeService.resetResourceReferenceInfo(newBotId);
        specialAnswerConfigService.resetResourceReferenceInfo(newBotId);
        llmStepConfigService.resetResourceReferenceInfo(newBotId);
        return newBotId;
    }

    @Override
    public void importFromJson(BotAutoImportVO botAutoImportVO) {
        //复制bot
        BotVO detail = botService.detail(botAutoImportVO.getBotId());
        if (Objects.isNull(detail)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "bot不存在, botId=" + botAutoImportVO.getBotId());
        }
        BotCreateRequestVO botCreateRequest = new BotCreateRequestVO();
        botCreateRequest.setSrcBotId(botAutoImportVO.getBotId());
        botCreateRequest.setName(botAutoImportVO.getBotName());
        botCreateRequest.setUserId(detail.getCreateUserId());
        botCreateRequest.setIsAutoCreate(true);
        botCreateRequest.setTenantId(0L);
        botCreateRequest.setCreateSource(BotCreateSourceEnum.GENERATE);

        botGenerateRecordService.getById(botAutoImportVO.getJob_id())
                .ifPresent(record -> botCreateRequest.setUserId(record.getCreateUserId()));

        BotPO newBot = botService.create(botCreateRequest);
        //替换节点答案
        replaceNodeAnswer(botAutoImportVO, newBot.getBotId());
        //替换知识答案
        replaceKnowledgeAnswer(botAutoImportVO, newBot.getBotId());
        //替换特殊语境答案
        replaceSpecialAnswer(botAutoImportVO, newBot.getBotId());

        botGenerateRecordService.complete(botAutoImportVO.getJob_id(), newBot.getBotId(), botAutoImportVO.getComplete_rate());
    }

    @Override
    public CommonFileUploadResult uploadZipFile(MultipartFile file) {
        String localZipPath = TempFilePathKeyCenter.getBotImportZipDir();
        log.info("开始导入zip文件, 保存路径: {}", localZipPath);
        File dir = new File(localZipPath);
        if (!dir.exists()) {
            if (!dir.mkdirs()) {
                throw new ComException(ComErrorCode.UNKNOWN_ERROR, "创建文件夹失败");
            }
        }
        String filePath = localZipPath + file.getOriginalFilename();
        try {
            file.transferTo(new File(filePath));
            String ossKey = OssKeyCenter.getBotImportZipOssKey(file.getOriginalFilename());
            String url = objectStorageHelper.upload(ossKey, new File(filePath));
            return CommonFileUploadResult.builder()
                    .key(ossKey)
                    .url(url)
                    .build();
        } catch (IOException e) {
            log.warn("上传文件失败", e);
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "上传文件失败", e);
        }
    }

    @Override
    public Long importFromZipFile(BotZipImportRequestVO request) {
        V3BotTypeEnum botType = request.getBotType();
        Long intentLevelTagId = request.getIntentLevelTagId();
        Long audioRecorderId = request.getAudioRecorderId();
        String ossKey = request.getOssKey();
        Long userId = request.getUserId();

        if (Objects.isNull(botType)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botType不能为空");
        }
        if (Objects.isNull(intentLevelTagId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "intentLevelTagId不能为空");
        }
        if (StringUtils.isBlank(ossKey)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "ossKey不能为空");
        }

        String localWorkDir = TempFilePathKeyCenter.getBotImportZipDir();
        log.info("开始导入zip文件, 保存路径: {}", localWorkDir);
        File dir = new File(localWorkDir);
        if (!dir.exists() && !dir.mkdirs()) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "创建文件夹失败");
        }

        String localZipFilePath = localWorkDir + "import.zip";
        // 下载到本地

        objectStorageHelper.downloadToFile(ossKey, localZipFilePath);

        // 解压缩
        ZipUtils.unzip(localZipFilePath, localWorkDir);

        // 读取 snapshotJson
        String snapshotJson = readSnapshotJson(localWorkDir);
        if (StringUtils.isBlank(snapshotJson)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "未找到snapshot.json");
        }
        RobotSnapshotPO snapshot = JsonUtils.string2Object(snapshotJson, RobotSnapshotPO.class);

        // 进行校验
        if (!botType.equals(snapshot.getBot().getType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "bot类型不匹配, 无法导入, 允许的类型: " + botType.getDesc() + ", 导入的类型:" + snapshot.getBot().getType().getDesc());
        }

        uploadRagDocAndUpdateSnapshot(localWorkDir, snapshot);

        Long newBotId = importBySnapshotJson(snapshot, intentLevelTagId, audioRecorderId, userId);

        uploadAudio(audioRecorderId, localWorkDir, newBotId);

        // 删除临时文件
        try {
            FileUtils.deleteDirectory(new File(localWorkDir));
        } catch (IOException e) {
            log.warn("删除文件失败: {}", localWorkDir);
        }

        return newBotId;
    }

    private void uploadRagDocAndUpdateSnapshot(String localZipPath, RobotSnapshotPO snapshot) {
        String ragDocMappingFilePath = localZipPath + ApplicationConstant.BOT_EXPORT_RAG_DOC_MAPPING_FILE_NAME;
        String ragDocMappingJson = readJsonFromFile(ragDocMappingFilePath);
        if (StringUtils.isBlank(ragDocMappingJson)) {
            log.warn("未找到ragDocMapping.json");
        } else {
            Map<String, String> docOssKey2PathMap = JsonUtils.string2Object(ragDocMappingJson, new TypeReference<Map<String, String>>(){});
            List<RagDocumentPO> docList = snapshot.getRagDocumentList();
            if (CollectionUtils.isEmpty(docList)) {
                return;
            }
            for (RagDocumentPO doc : docList) {
                String fileOssKey = doc.getFileOssKey();
                if (!docOssKey2PathMap.containsKey(fileOssKey)) {
                    continue;
                }
                String docPath = localZipPath + docOssKey2PathMap.get(fileOssKey);
                try {
                    String ossKey = OssKeyCenter.getBotRagDocUploadOssKey(0L, FileUtil.getName(fileOssKey));
                    objectStorageHelper.upload(ossKey, new File(docPath));
                    doc.setFileOssKey(ossKey);
                    doc.setStatus(RagDocumentStatusEnum.PARSING);
                } catch (Exception e) {
                    log.error("上传文档失败, docPath={}", docPath);
                }
            }
        }
    }

    private void uploadAudio(Long audioRecorderId, String localZipPath, Long newBotId) {
        if (Objects.nonNull(audioRecorderId)) {
            // 更新录音师信息
            log.info("开始处理录音上传");

            String audioMappingJson = readAudioMappingJson(localZipPath);
            if (StringUtils.isBlank(audioMappingJson)) {
                log.warn("未找到audioMapping.json");
            } else {
                Map<String, AnswerAudioMappingPO> audioMappingMap = JsonUtils.string2MapObject(audioMappingJson, String.class, AnswerAudioMappingPO.class);
                // 读取录音文件
                // 上传
                for (Map.Entry<String, AnswerAudioMappingPO> entry : audioMappingMap.entrySet()) {
                    AnswerAudioMappingPO audioMapping = entry.getValue();
                    String audioPath = localZipPath + entry.getKey() + ".wav";
                    log.info("开始上传录音: {}", audioPath);
                    File audioFile = new File(audioPath);
                    if (audioFile.exists()) {
                        AudioUploadResultVO audioUploadResult = audioUploadService.uploadAudio(newBotId, audioFile, false);
                        log.info("上传录音结果: {}", audioUploadResult);
                        if (Objects.nonNull(audioUploadResult) && StringUtils.isNotBlank(audioUploadResult.getUrl())) {
                            AnswerUpdateAudioRequestVO request = new AnswerUpdateAudioRequestVO();
                            request.setBotId(newBotId);
                            request.setVolume(audioUploadResult.getVolume());
                            request.setDuration(audioUploadResult.getDuration());
                            request.setUrl(audioUploadResult.getUrl());
                            request.setFullUrl(audioUploadResult.getFullUrl());
                            request.setAnswerText(audioMapping.getText());
                            answerAudioManagerService.updateAnswerAudio(request, audioRecorderId);
                        }
                    }
                }
            }
        }
    }

    private String readAudioMappingJson(String localZipPath) {
        String audioMappingJsonFilePath = localZipPath + ApplicationConstant.BOT_EXPORT_AUDIO_MAPPING_FILE_NAME;
        log.info("开始读取录音映射文件: {}", audioMappingJsonFilePath);
        return readJsonFromFile(audioMappingJsonFilePath);
    }

    private static String readJsonFromFile(String audioMappingJsonFilePath) {
        File snapshotJsonFile = new File(audioMappingJsonFilePath);
        if (snapshotJsonFile.exists()) {
            try (FileInputStream fis = new FileInputStream(snapshotJsonFile)) {
                StringWriter writer = new StringWriter();
                IOUtils.copy(fis, writer, StandardCharsets.UTF_8.name());
                return writer.toString();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    private String readSnapshotJson(String localZipPath) {
        String snapshotJsonFilePath = localZipPath + ApplicationConstant.BOT_EXPORT_SNAPSHOT_FILE_NAME;
        return readJsonFromFile(snapshotJsonFilePath);
    }

    private void replaceSpecialAnswer(BotAutoImportVO botAutoImportVO, Long botId) {
        List<SpecialAnswerConfigPO> byBotId = specialAnswerConfigService.getByBotId(botId);
        List<BotAutoImportVO.SpecialVO> specialVOList = botAutoImportVO.getSpecialList();
        if (CollectionUtils.isNotEmpty(specialVOList) && CollectionUtils.isNotEmpty(byBotId)) {
            Map<String, SpecialAnswerConfigPO> collect = byBotId.stream().collect(Collectors.toMap(SpecialAnswerConfigPO::getName, SpecialAnswerConfigPO -> SpecialAnswerConfigPO));
            specialVOList.forEach(specialVO -> {
                if (collect.containsKey(specialVO.getName())) {
                    List<KnowledgeAnswer> answerList = collect.get(specialVO.getName()).getAnswerList();
                    for (int i = 0; i < answerList.size(); i++) {
                        if (CollectionUtils.isNotEmpty(specialVO.getAnswer()) && answerList.size() == specialVO.getAnswer().size()) {
                            answerList.get(i).setText(specialVO.getAnswer().get(i).getText());
                        } else {
                            answerList.get(i).setText(NUM_ERROR + specialVO.getAnswer().size() + "条");
                        }
                    }
                    specialAnswerConfigService.updateAnswerList(collect.get(specialVO.getName()));
                    collect.remove(specialVO.getName());
                }
            });
            if (Objects.nonNull(collect)) {
                collect.keySet().forEach(k -> {
                    List<KnowledgeAnswer> answerList = collect.get(k).getAnswerList();
                    if (CollectionUtils.isNotEmpty(answerList)) {
                        answerList.forEach(answer -> {
                            answer.setText(NON_SUPPORT);
                        });
                        specialAnswerConfigService.updateAnswerList(collect.get(k));
                    }
                });
            }
        }
    }

    private void replaceKnowledgeAnswer(BotAutoImportVO botAutoImportVO, Long botId) {
        List<KnowledgePO> oldKnowledgeList = knowledgeService.getAllListByBotId(botId);
        List<BotAutoImportVO.KnowledgeVO> newKnowledgeList = botAutoImportVO.getKnowledgeList();
        if (CollectionUtils.isNotEmpty(oldKnowledgeList) && CollectionUtils.isNotEmpty(newKnowledgeList)) {
            Map<String, BotAutoImportVO.KnowledgeVO> newKnowledgeMap = newKnowledgeList.stream().collect(Collectors.toMap(BotAutoImportVO.KnowledgeVO::getName, KnowledgeVO -> KnowledgeVO));
            oldKnowledgeList.forEach(oldKnowledgePO -> {
                if (newKnowledgeMap.containsKey(oldKnowledgePO.getName())) {
                    BotAutoImportVO.KnowledgeVO newKnowledge = newKnowledgeMap.get(oldKnowledgePO.getName());
                    for (int i = 0; i < oldKnowledgePO.getAnswerList().size(); i++) {
                        if (CollectionUtils.isNotEmpty(newKnowledge.getAnswer()) && newKnowledge.getAnswer().size() == oldKnowledgePO.getAnswerList().size()) {
                            oldKnowledgePO.getAnswerList().get(i).setText(newKnowledge.getAnswer().get(i).getText());
                        } else {
                            oldKnowledgePO.getAnswerList().get(i).setText(NUM_ERROR + newKnowledge.getAnswer().size() + "条");
                        }
                    }
                    knowledgeService.updateAnswerList(oldKnowledgePO);
                } else {
                    List<KnowledgeAnswer> answerList = oldKnowledgePO.getAnswerList();
                    if (CollectionUtils.isNotEmpty(answerList)) {
                        answerList.forEach(answer -> {
                            answer.setText(NON_SUPPORT);
                        });
                        knowledgeService.updateAnswerList(oldKnowledgePO);
                    }
                }
            });
        }
    }

    private void replaceNodeAnswer(BotAutoImportVO botAutoImportVO, Long botId) {
        List<DialogBaseNodePO> nodeList = stepNodeService.getAllListByBotId(botId);
        List<BotAutoImportVO.NodeVO> importNodeList = botAutoImportVO.getNodeList();
        if (CollectionUtils.isNotEmpty(nodeList) && CollectionUtils.isNotEmpty(importNodeList)) {
            Map<String, BotAutoImportVO.NodeVO> importNodeMap = importNodeList.stream().collect(Collectors.toMap(BotAutoImportVO.NodeVO::getLabel, NodeVO -> NodeVO));
            nodeList.forEach(node -> {
                if (importNodeMap.containsKey(node.getLabel())) {
                    List<NodeAnswer> answerList = node.getAnswerList();
                    List<BotAutoImportVO.AnswerVO> importAnswerList = importNodeMap.get(node.getLabel()).getAnswer();
                    for (int i = 0; i < answerList.size(); i++) {
                        if (CollectionUtils.isNotEmpty(importAnswerList) && answerList.size() == importAnswerList.size()) {
                            answerList.get(i).setText(importAnswerList.get(i).getText());
                        } else {
                            answerList.get(i).setText(NUM_ERROR + importAnswerList.size() + "条");
                        }
                    }
                    stepNodeService.updateAnswerList(node);
                } else {
                    List<NodeAnswer> answerList = node.getAnswerList();
                    if (CollectionUtils.isNotEmpty(answerList)) {
                        answerList.forEach(answer -> {
                            answer.setText(NON_SUPPORT);
                        });
                    }
                    stepNodeService.updateAnswerList(node);
                }
            });
        }
    }

    @Override
    public void importFromJsonFile(MultipartFile file) {
        try (InputStream is = file.getInputStream()) {
            StringWriter writer = new StringWriter();
            IOUtils.copy(is, writer, StandardCharsets.UTF_8.name());
            String str = writer.toString();
            BotGenerateCreateResultDTO snapshot = JsonUtils.string2Object(str, BotGenerateCreateResultDTO.class);

            Long newBotId = botService.createFromSnapshot(snapshot);
            // 重置所有的标签
            botResourceSequenceService.cleanAllSequence(newBotId);
            stepService.resetAllStepLabel(newBotId);
            knowledgeService.resetAllKnowledgeLabel(newBotId);
            specialAnswerConfigService.resetAllSpecialAnswerLabel(newBotId);

            // 重新生成资源引用(意图和变量)
            stepService.resetResourceReferenceInfo(newBotId);
            stepNodeService.resetResourceReferenceInfo(newBotId);
            knowledgeService.resetResourceReferenceInfo(newBotId);
            specialAnswerConfigService.resetResourceReferenceInfo(newBotId);

            // 删除忽略的知识
            if (CollectionUtils.isNotEmpty(snapshot.getIgnoreKnowledgeList())) {
                log.info("删除忽略的知识: {}", snapshot.getIgnoreKnowledgeList());
                knowledgeService.deleteByNameList(newBotId, snapshot.getIgnoreKnowledgeList());
            }

            if (CollectionUtils.isNotEmpty(snapshot.getIgnoreSpecialList())) {
                log.info("删除忽略的特殊回答: {}, 但是特殊语境暂不支持删除", snapshot.getIgnoreSpecialList());
                // todo 特殊语境是不支持删除的

//                specialAnswerConfigService.deleteByNameList(newBotId, snapshot.getIgnoreSpecialList());
            }

        } catch (Exception e) {
            log.error("读取Json文件错误", e);
            throw new ComException(ComErrorCode.VALIDATE_ERROR, e);
        }
    }

}
