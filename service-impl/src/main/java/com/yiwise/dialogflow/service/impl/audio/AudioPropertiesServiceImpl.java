package com.yiwise.dialogflow.service.impl.audio;

import com.yiwise.base.common.audio.AudioHandleUtils;
import com.yiwise.base.common.utils.PropertyLoaderUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.entity.bo.AudioPropertiesBO;
import com.yiwise.dialogflow.entity.po.AudioPropertiesPO;
import com.yiwise.dialogflow.service.AudioPropertiesService;
import javaslang.collection.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AudioPropertiesServiceImpl implements AudioPropertiesService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public AudioPropertiesPO getByUrl(String url) {
        Query query = new Query();
        query.addCriteria(new Criteria("url").is(url));
        return mongoTemplate.findOne(query, AudioPropertiesPO.class, AudioPropertiesPO.COLLECTION_NAME);
    }

    @Override
    public void upsert(String url, Integer volume, Integer duration) {
        if (StringUtils.isBlank(url)) {
            return;
        }
        if (Objects.isNull(volume) && Objects.isNull(duration)) {
            return;
        }
        Query query = new Query();
        query.addCriteria(new Criteria("url").is(url));
        Update update = new Update();
        if (Objects.nonNull(volume)) {
            update.set("volume", volume);
        }
        if (Objects.nonNull(duration)) {
            update.set("duration", duration);
        }
        mongoTemplate.upsert(query, update, AudioPropertiesPO.class, AudioPropertiesPO.COLLECTION_NAME);
    }

    @Override
    public AudioPropertiesBO calculateAudioProperties(File waveFile) {
        AudioPropertiesBO result = new AudioPropertiesBO();
        result.setVolume(calculateVolume(waveFile));
        result.setDuration(calculateDuration(waveFile));
        return result;
    }

    @Override
    public List<Short> generateWaveform(File waveFile) {
        if (Objects.isNull(waveFile)) {
            return Collections.emptyList();
        }
        return doGenerateWaveform(waveFile);
    }

    // 为音频文件生成简略版的波形图, 默认文件格式为8K 16bit
    private List<Short> doGenerateWaveform(File audioFile) {
        if (Objects.isNull(audioFile)) {
            return Collections.emptyList();
        }
        // 1秒有8000个short值,
        int frameLength = 8000 / 10; // 每帧的100毫秒, 每秒10帧
        List<Short> resultList = new LinkedList<>();

        long startTime = System.currentTimeMillis();
        try (FileInputStream fileInputStream = new FileInputStream(audioFile);
             BufferedInputStream bufferedInputStream = new BufferedInputStream(fileInputStream)) {

            if (bufferedInputStream.available() < frameLength) {
                return Collections.emptyList();
            }

            while (bufferedInputStream.available() >= frameLength) {
                byte[] buffer = new byte[frameLength];
                bufferedInputStream.read(buffer);
                short[] shorts = getShorts(buffer);
                long totalVolume = Stream.ofAll(shorts).sum().longValue();
                short avgVolume = (short) (totalVolume / shorts.length);
                resultList.add(avgVolume);
            }

            log.debug("音频波形生成耗时=" + (System.currentTimeMillis() - startTime));
            return resultList;
        } catch (Exception e) {
            throw new RuntimeException("获取音量值错误", e);
        }
    }

    private static short[] getShorts(byte[] b) {
        return getShorts(b, 0, b.length);
    }

    private static short[] getShorts(byte[] b, int offset, int len) {
        short[] s = new short[len >> 1];
        int start = offset;
        int end = offset + len;
        for (int i = start; i < end; i += 2) {
            s[(i - start) >> 1] = (short) (((b[i + 1] & 0x00FF) << 8) | (0x00FF & b[i]));
        }
        return s;
    }

    @Override
    public int calculateVolume(File file) {
        if (Objects.isNull(file)) {
            return 0;
        }

        short fs;
        if (PropertyLoaderUtils.getBooleanProperty("enable.new.volume.calculate")) {
            return getAverageVolume(file);
        } else {
            fs = AudioHandleUtils.getAverageVolume(file, ApplicationConstant.AUDIO_AVERAGE_VOLUME_RATE);
            double db = 20 * (Math.log10(fs) - Math.log10(65536));
            return (int) (100 + Math.round(db));
        }
    }

    private int getAverageVolume(File file) {
        try {
            List<String> command = new ArrayList<>();
            command.add("ffmpeg");
            command.add("-i");
            command.add(file.getAbsolutePath());
            command.add("-filter_complex");
            command.add("volumedetect");
            command.add("-c:v");
            command.add("copy");
            command.add("-f");
            command.add("null");
            command.add("/dev/null");
            ProcessBuilder builder = new ProcessBuilder(command);
            Process process = builder.start();
            InputStream errorStream = process.getErrorStream();
            InputStreamReader inputStreamReader = new InputStreamReader(errorStream);
            BufferedReader br = new BufferedReader(inputStreamReader);
            String db = "";
            while ((db = br.readLine()) != null) {
                if (db.contains("mean_volume")) {
                    db = db.split(": ")[1];
                    db = db.split(" ")[0];
                    break;
                }
            }
            br.close();
            inputStreamReader.close();
            errorStream.close();
            if (Objects.nonNull(db)) {
                double volume = Double.parseDouble(db);
                return (int) Math.abs(100 + volume);
            }
            return 0;
        } catch (Exception e) {
            log.error("计算音频文件音量失败，错误原因:{}", e.getMessage());
            throw new RuntimeException("获取音量值错误", e);
        }
    }

    private int calculateDuration(File file) {
        if (Objects.isNull(file)) {
            return 0;
        }
        // 前端又要求向下取整了, 所以结果不要做变更
        return AudioHandleUtils.getAudioFileDuration(file);
    }
}
