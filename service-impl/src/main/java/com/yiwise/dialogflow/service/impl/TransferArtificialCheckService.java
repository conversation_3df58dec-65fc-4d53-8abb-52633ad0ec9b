package com.yiwise.dialogflow.service.impl;

import com.yiwise.dialogflow.entity.bo.SnapshotInvalidFailItemMsg;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.enums.BotResourceTypeEnum;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.DefaultRobotValidateServiceAdaptor;
import com.yiwise.dialogflow.service.StepNodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class TransferArtificialCheckService extends DefaultRobotValidateServiceAdaptor {

    @Resource
    private BotService botService;

    @Resource
    private StepNodeService stepNodeService;

    @Override
    public void validateResource(RobotResourceContext context) {
        Long botId = context.getSrcBotId();
        if (stepNodeService.containsSwitchToHumanServiceNodeByBotId(botId) && botService.checkEnableHumanInterventionByBotId(botId)) {
            SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                    .builder()
                    .resourceType(BotResourceTypeEnum.TRANSFER_ARTIFICIAL).resourceName(BotResourceTypeEnum.TRANSFER_ARTIFICIAL.getDesc())
                    .failMsg("不能同时配置【转人工】和【启用人工介入】，请检查")
                    .build();
            context.getInvalidMsgList().add(msg);
        }
    }
}