package com.yiwise.dialogflow.service.impl.llm;

import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.po.llm.RagDocumentPO;
import com.yiwise.dialogflow.entity.po.llm.RagDocumentSegmentPO;
import com.yiwise.dialogflow.entity.vo.llm.RagDocumentSegmentVO;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.llm.AlgorithmRagDocService;
import com.yiwise.dialogflow.service.llm.RagDocumentSegmentService;
import com.yiwise.dialogflow.service.llm.RagDocumentService;
import com.yiwise.dialogflow.service.operationlog.RagDocumentOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class RagDocumentSegmentServiceImpl implements RagDocumentSegmentService {

    @Resource
    private BotService botService;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private RagDocumentService ragDocumentService;

    @Resource
    private AlgorithmRagDocService algorithmRagDocService;

    @Resource
    private RagDocumentOperationLogService ragDocumentOperationLogService;

    @Override
    public List<RagDocumentSegmentVO> getByDocId(Long botId, String docId) {
        return mongoTemplate.find(Query.query(Criteria.where("botId").is(botId).and("ragDocumentId").is(docId)), RagDocumentSegmentVO.class, RagDocumentSegmentVO.COLLECTION_NAME);
    }

    @Override
    public List<RagDocumentSegmentPO> getByIdList(List<String> idList) {
        return mongoTemplate.find(Query.query(Criteria.where("_id").in(idList)), RagDocumentSegmentPO.class, RagDocumentSegmentPO.COLLECTION_NAME);
    }

    private RagDocumentSegmentPO getById(String docId, String segmentId) {
        return mongoTemplate.findOne(Query.query(Criteria.where("ragDocumentId").is(docId).and("_id").is(segmentId)), RagDocumentSegmentPO.class, RagDocumentSegmentPO.COLLECTION_NAME);
    }

    @Override
    public void update(RagDocumentSegmentVO request, Long userId) {
        String docId = request.getRagDocumentId();
        String segmentId = request.getId();
        String content = request.getContent();
        RagDocumentPO doc = ragDocumentService.getById(docId);
        RagDocumentSegmentPO segment = getById(docId, segmentId);
        if (Objects.isNull(segment) || Objects.isNull(doc)) {
            return;
        }
        if (!algorithmRagDocService.updateSegment(docId, segmentId, content)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "编辑失败");
        }
        String oldContent = segment.getContent();
        segment.setContent(content);
        mongoTemplate.save(segment, RagDocumentSegmentPO.COLLECTION_NAME);

        ragDocumentService.changeUpdateTimeToNow(docId);
        ragDocumentOperationLogService.updateSegment(doc.getBotId(), doc.getDocName(), oldContent, content, userId);
        botService.onUpdateBotResource(doc.getBotId());
    }

    @Override
    public void delete(String docId, String id, Long userId) {
        RagDocumentPO doc = ragDocumentService.getById(docId);
        RagDocumentSegmentPO segment = getById(docId, id);
        if (Objects.isNull(segment) || Objects.isNull(doc)) {
            return;
        }
        if (!algorithmRagDocService.deleteSegment(id)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "删除失败");
        }
        mongoTemplate.remove(Query.query(Criteria.where("_id").is(id)), RagDocumentSegmentPO.COLLECTION_NAME);

        ragDocumentService.changeUpdateTimeToNow(docId);
        ragDocumentOperationLogService.deleteSegment(doc.getBotId(), doc.getDocName(), segment.getContent(), userId);
        botService.onUpdateBotResource(doc.getBotId());
    }

    @Override
    public void deleteByDocId(String docId) {
        mongoTemplate.remove(Query.query(Criteria.where("ragDocumentId").is(docId)), RagDocumentSegmentPO.COLLECTION_NAME);
    }

    @Override
    public List<RagDocumentSegmentPO> getByBotId(Long botId) {
        return mongoTemplate.find(Query.query(Criteria.where("botId").is(botId)), RagDocumentSegmentPO.class, RagDocumentSegmentPO.COLLECTION_NAME);
    }
}