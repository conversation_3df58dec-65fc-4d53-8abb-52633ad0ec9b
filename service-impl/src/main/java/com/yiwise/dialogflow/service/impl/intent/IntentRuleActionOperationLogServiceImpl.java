package com.yiwise.dialogflow.service.impl.intent;

import com.yiwise.dialogflow.entity.bo.ActionNameResourceBO;
import com.yiwise.dialogflow.entity.bo.algorithm.ResourceId2NameBO;
import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum;
import com.yiwise.dialogflow.entity.po.intent.IntentRuleActionPO;
import com.yiwise.dialogflow.entity.po.intent.IntentRuleConditionPO;
import com.yiwise.dialogflow.entity.po.intent.RuleActionParam;
import com.yiwise.dialogflow.service.OperationLogService;
import com.yiwise.dialogflow.service.intent.IntentRuleActionOperationLogService;
import com.yiwise.dialogflow.utils.IntentRuleContentRenderUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/9/22
 * @class <code>IntentRuleActionOperationLogServiceImpl</code>
 * @see
 * @since JDK1.8
 */
@Slf4j
@Service
public class IntentRuleActionOperationLogServiceImpl implements IntentRuleActionOperationLogService {

    @Resource
    private OperationLogService operationLogService;

    @Override
    public void addOperationLog(IntentRuleActionPO oldRuleAction, IntentRuleActionPO newRuleAction,
                                ResourceId2NameBO resourceMap, ActionNameResourceBO actionResourceMap, Long botId, Long userId) {
        //新增操作
        if (Objects.isNull(oldRuleAction)) {
            String ruleContent = buildConditionContent(newRuleAction.getConditionList(), resourceMap);
            String actionContent = buildActionContent(newRuleAction.getActionList(), actionResourceMap);
            String detail = String.format("添加动作:【%s:%s】", actionContent, ruleContent);
            operationLogService.save(botId, OperationLogTypeEnum.RULE_CONFIG,
                    OperationLogResourceTypeEnum.INTENT_RULE_ACTION_CONFIG, detail, userId);
        } else if (Objects.isNull(newRuleAction)) {
            String ruleContent = buildConditionContent(oldRuleAction.getConditionList(), resourceMap);
            String actionContent = buildActionContent(oldRuleAction.getActionList(), actionResourceMap);
            String detail = String.format("删除动作:【%s:%s】", actionContent, ruleContent);
            operationLogService.save(botId, OperationLogTypeEnum.RULE_CONFIG,
                    OperationLogResourceTypeEnum.INTENT_RULE_ACTION_CONFIG, detail, userId);
        } else {
            String oldRuleContent = buildConditionContent(oldRuleAction.getConditionList(), resourceMap);
            String oldActionContent = buildActionContent(oldRuleAction.getActionList(), actionResourceMap);
            String newRuleContent = buildConditionContent(newRuleAction.getConditionList(), resourceMap);
            String newActionContent = buildActionContent(newRuleAction.getActionList(), actionResourceMap);
            if (!oldRuleContent.equals(newRuleContent) || !oldActionContent.equals(newActionContent)) {
                String detail = String.format("编辑动作:【%s:%s】,修改为【%s:%s】", oldActionContent, oldRuleContent,
                        newActionContent, newRuleContent);
                operationLogService.save(botId, OperationLogTypeEnum.RULE_CONFIG,
                        OperationLogResourceTypeEnum.INTENT_RULE_ACTION_CONFIG, detail, userId);
            }
        }
    }

    private String buildActionContent(List<RuleActionParam> actionList, ActionNameResourceBO actionResourceMap) {
        if (CollectionUtils.isEmpty(actionList)) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        actionList.forEach(actionParam -> {
            String actionDetail = buildActionDetail(actionParam, actionResourceMap);
            String msg = String.format("%s(%s)", actionParam.getActionType().getDesc(), actionDetail);
            sb.append(msg).append(" \n");
        });
        return sb.toString();
    }

    private String buildActionDetail(RuleActionParam actionParam, ActionNameResourceBO actionResourceMap) {
        if (CollectionUtils.isEmpty(actionParam.getSourceIdList())) {
            return "";
        }
        List<String> sourceNameList = actionParam.getSourceIdList().stream().map(item -> {
            switch (actionParam.getActionType()) {
                case ADD_TAG:
                    return actionResourceMap.getTagId2NameMap().getOrDefault(item.getId(), null);
                case SEND_SMS:
                    return actionResourceMap.getSmsTempId2NameMap().getOrDefault(item.getId(), null);
                case WHITE_LIST:
                    return actionResourceMap.getGroupId2NameMap().getOrDefault(item.getId(), null);
            }
            return null;
        }).collect(Collectors.toList());
        return String.join(",", sourceNameList);
    }

    private String buildConditionContent(List<IntentRuleConditionPO> conditionList, ResourceId2NameBO resourceMap) {
        return IntentRuleContentRenderUtils.renderIntentRuleConditionContent(conditionList,
                resourceMap.getStepNode2NameMap(),
                resourceMap.getSpecialAnswerId2NameMap(),
                resourceMap.getKnowledgeMap(),
                resourceMap.getIntentLevelIdNameMap(),
                resourceMap.getEntityId2NameMap(),
                resourceMap.getIntentId2NameMap(),
                resourceMap.getVarId2NameMap(),
                resourceMap.getLlmLabeId2NameMap());
    }
}
