package com.yiwise.dialogflow.service.impl;

import com.yiwise.base.common.utils.file.MyFileUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.common.TempFilePathKeyCenter;
import com.yiwise.dialogflow.entity.bo.AudioPropertiesBO;
import com.yiwise.dialogflow.entity.enums.BotGenerateTemplateStatusEnum;
import com.yiwise.dialogflow.entity.enums.GroupTypeEnum;
import com.yiwise.dialogflow.entity.enums.VariableTypeEnum;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.botgenerate.BotGenerateTemplatePO;
import com.yiwise.dialogflow.entity.po.remote.CustomerScenePO;
import com.yiwise.dialogflow.entity.po.remote.CustomerTrackTypePO;
import com.yiwise.dialogflow.entity.query.BotQuery;
import com.yiwise.dialogflow.entity.vo.BotVO;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.remote.CustomerSceneService;
import com.yiwise.dialogflow.service.remote.CustomerTrackTypeService;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import com.yiwise.middleware.tts.enums.VarInterpretTypeEnum;
import javaslang.Tuple;
import javaslang.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.yiwise.dialogflow.service.impl.VariableServiceImpl.DEFAULT_SYSTEM_VARIABLE_NAME_DESC_MAP;

/**
 * <AUTHOR>
 * @date 2022/7/29
 */
@Slf4j
@Service
public class DataFixServiceImpl implements DataFixService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private AudioPropertiesService audioPropertiesService;

    @Resource
    private ObjectStorageHelper objectStorageHelper;

    @Resource
    private BotConfigService botConfigService;

    @Resource
    private BotService botService;

    @Resource
    private GroupService groupService;

    @Resource
    private CustomerTrackTypeService customerTrackTypeService;

    @Resource
    private CustomerSceneService customerSceneService;

    @Resource
    private VariableService variableService;

    @Override
    public void updateBackgroundVolume(Long botId) {
        BotQuery botQuery = new BotQuery();
        botQuery.setBotId(botId);
        List<BotVO> botList = botService.queryListWithoutPage(botQuery);
        int size = botList.size();
        AtomicInteger index = new AtomicInteger(0);
        for (BotVO bot : botList) {
            try {
                log.info("start process {}/{}", index.incrementAndGet(), size);
                List<BackgroundAudioConfigPO> backgroundAudioConfigList = Optional.ofNullable(botConfigService.getAudioConfig(bot.getBotId())).map(BotAudioConfigPO::getBackgroundList).orElse(Collections.emptyList());
                boolean changed = false;

                for (BackgroundAudioConfigPO backgroundAudioConfig : backgroundAudioConfigList) {
                    String url = objectStorageHelper.getKeyFromUrl(backgroundAudioConfig.getUrl());
                    AudioPropertiesPO audioProperties = audioPropertiesService.getByUrl(url);
                    Integer volume;
                    if (Objects.nonNull(audioProperties)) {
                        volume = audioProperties.getVolume();
                    } else if (StringUtils.equals(backgroundAudioConfig.getSource(), "办公室谈话声") && (
                            Objects.equals(backgroundAudioConfig.getVolume(), 5) ||
                                    Objects.equals(backgroundAudioConfig.getVolume(), 70))) {
                        volume = 70;
                    } else {
                        String fileName = String.format("%s%s", new ObjectId().toHexString(), url.substring(url.lastIndexOf(".")));
                        String localPath = TempFilePathKeyCenter.getOSSDownloadTempAudioFilePath(fileName);
                        objectStorageHelper.downloadToFile(url, localPath);
                        File localFile = new File(localPath);
                        AudioPropertiesBO properties = audioPropertiesService.calculateAudioProperties(localFile);
                        volume = properties.getVolume();
                        MyFileUtils.deleteFileByPath(localPath);
                        audioPropertiesService.upsert(url, properties.getVolume(), properties.getDuration());
                    }

                    if (!Objects.equals(backgroundAudioConfig.getVolume(), volume)) {
                        changed = true;
                        log.info("bot:{} source:{} volume change from {} to {}", bot.getBotId(), backgroundAudioConfig.getSource(), backgroundAudioConfig.getVolume(), volume);
                        backgroundAudioConfig.setVolume(volume);
                    }
                }

                if (changed) {
                    mongoTemplate.updateFirst(Query.query(Criteria.where("_id").is(bot.getBotId())), Update.update("audioConfig.backgroundList", backgroundAudioConfigList), BotConfigPO.class);
                }
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        }
    }

    @Override
    public void initAllAudioGroupIfNotExists() {
        GroupPO allAudioGroup = groupService.selectOne(0L, "全部音频", GroupTypeEnum.PUBLIC_AUDIO, 0, null);
        if (Objects.isNull(allAudioGroup)) {
            LocalDateTime now = LocalDateTime.now();
            allAudioGroup = new GroupPO();
            allAudioGroup.setId(null);
            allAudioGroup.setName("全部音频");
            allAudioGroup.setType(GroupTypeEnum.PUBLIC_AUDIO);
            allAudioGroup.setParentId(null);
            allAudioGroup.setLevel(0);
            allAudioGroup.setBotId(0L);
            allAudioGroup.setPath("全部音频");
            allAudioGroup.setCreateTime(now);
            allAudioGroup.setUpdateTime(now);
            mongoTemplate.insert(allAudioGroup);
        }
    }

    @Override
    public void updateTtsVolume() {
        BotQuery botQuery = new BotQuery();
        List<BotVO> botList = botService.queryListWithoutPage(botQuery);
        int size = botList.size();
        AtomicInteger index = new AtomicInteger(0);
        for (BotVO botVO : botList) {
            log.info("start process {}/{}", index.incrementAndGet(), size);
            Query query = new Query();
            query.addCriteria(Criteria.where("botId").is(botVO.getBotId()));
            BotConfigPO configPO = mongoTemplate.findOne(query, BotConfigPO.class, BotConfigPO.COLLECTION_NAME);
            if (Objects.nonNull(configPO)) {
                TtsVoiceConfigPO ttsConfig = configPO.getAudioConfig().getTtsConfig();
                if (Objects.nonNull(ttsConfig) && (ttsConfig.getTtsVolume() == 5f || ttsConfig.getTtsVolume() == 10f)) {
                    ttsConfig.setTtsVolume(70f);
                    mongoTemplate.updateFirst(Query.query(Criteria.where("_id").is(botVO.getBotId())), Update.update("audioConfig.ttsConfig", ttsConfig), BotConfigPO.class);
                }
            }
        }
    }

    @Override
    public void fixVar() {
        // "客户名"和"客户名_总"修正为自定义变量
        mongoTemplate.updateMulti(
                Query.query(Criteria.where("name").in("客户名", "客户名_总")),
                Update.update("type", VariableTypeEnum.CUSTOM),
                VariablePO.COLLECTION_NAME);

        // 变量库的历史数据中interpretType为空的填充DEFAULT
        mongoTemplate.updateMulti(
                Query.query(Criteria.where("interpretType").is(null)),
                Update.update("interpretType", VarInterpretTypeEnum.DEFAULT),
                VariablePO.COLLECTION_NAME
        );

        // 新增内置变量"姓名"和“姓名_总”
        BotQuery botQuery = new BotQuery();
        List<BotVO> botList = botService.queryListWithoutPage(botQuery);
        Map<String, String> varNameDescMap = new HashMap<>(2);
        varNameDescMap.put("姓名", "将用户基本信息中“姓名”作为变量被话术调用。");
        varNameDescMap.put("姓名_总", "使用该变量，需在录音小程序对<百家姓录音>进行完整的录音。话术调用该变量，当上传的姓名中的姓氏不存在对应的录音，则会使用“哎”作为称呼。如“张三”播报为“张总“，若无“张总”录音，则播报为“哎”。");
        for (BotVO bot : botList) {
            varNameDescMap.forEach((name, desc) -> {
                mongoTemplate.upsert(
                        Query.query(Criteria.where("botId").is(bot.getBotId()).and("name").is(name)),
                        Update.update("botId", bot.getBotId()).set("name", name).set("type", VariableTypeEnum.SYSTEM)
                                .set("interpretType", VarInterpretTypeEnum.DEFAULT).set("createTime", LocalDateTime.now())
                                .set("desc", desc).set("updateTime", LocalDateTime.now()),
                        VariablePO.COLLECTION_NAME);
            });
            log.info("BOT={}变量库修复完成", bot.getBotId());
        }
    }

    @Override
    public void fixSystemVar() {
        //查询话术列表
        int page = 1;
        int pageSize = 50;
        BotQuery botQuery = new BotQuery();
        botQuery.setPageNum(page);
        botQuery.setPageSize(pageSize);
        PageResultObject<BotVO> botPage = botService.getPage(botQuery);

        //映射关系处理
        //场景数据处理
        int pages = botPage.getPages();
        List<BotVO> handleList = new ArrayList<>();
        while (page <= pages) {
            if (page == 1) {
                handleList = botPage.getContent();
            } else {
                botQuery.setPageNum(page);
                handleList = botService.getPage(botQuery).getContent();
            }
            page ++;
            log.debug("处理进度:{}/{}", page, pages);

            List<VariablePO> list = new ArrayList<>();

            for (BotVO bot : handleList) {

                List<VariablePO> oldVariableList = variableService.getListByBotId(bot.getBotId());
                Set<String> oldNameSet = oldVariableList.stream().map(VariablePO::getName).collect(Collectors.toSet());

                DEFAULT_SYSTEM_VARIABLE_NAME_DESC_MAP.forEach((name, desc) -> {
                    if (!oldNameSet.contains(name)) {
                        VariablePO v = new VariablePO();
                        v.setBotId(bot.getBotId());
                        v.setName(name);
                        v.setDesc(desc);
                        v.setCreateTime(LocalDateTime.of(2020, 1, 1, 10, 0, 0));
                        v.setUpdateTime(LocalDateTime.of(2020, 1, 1, 10, 0, 0));
                        v.setType(VariableTypeEnum.BUILT_IN_SYSTEM);
                        v.setInterpretType(VarInterpretTypeEnum.DEFAULT);
                        list.add(v);
                    }
                });
            }
            mongoTemplate.insert(list, VariablePO.COLLECTION_NAME);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void fixBot() {
        //资源加载：新行业数据，场景数据
        List<CustomerTrackTypePO> customerTrackTypeList = customerTrackTypeService.getCustomerTrackList();
        Map<Integer, String> id2NameMap = customerTrackTypeList.stream().collect(Collectors.toMap(CustomerTrackTypePO::getCustomerTrackType, CustomerTrackTypePO::getCustomerTrackTypeName));
        Map<Tuple2<String, Integer>, CustomerTrackTypePO> name2IdMap = customerTrackTypeList.stream().filter(x -> x.getCustomerTrackParentType() != null).collect(Collectors.toMap(x -> Tuple.of(x.getCustomerTrackTypeName(), x.getCustomerTrackParentType()), x -> x));
        Map<String, CustomerTrackTypePO> parentName2IdMap = customerTrackTypeList.stream().filter(x -> x.getCustomerTrackParentType() == null).collect(Collectors.toMap(CustomerTrackTypePO::getCustomerTrackTypeName, x -> x));

        //场景数据
        List<CustomerScenePO> scenePOList = customerSceneService.getCustomerSceneList(null, true);
        Map<String, Integer> sceneMap = scenePOList.stream().collect(Collectors.toMap(CustomerScenePO::getCustomerSceneName, CustomerScenePO::getCustomerSceneId));

        //查询话术列表
        int page = 1;
        int pageSize = 1000;
        BotQuery botQuery = new BotQuery();
        botQuery.setPageNum(page);
        botQuery.setPageSize(pageSize);
        PageResultObject<BotVO> botPage = botService.getPage(botQuery);

        //映射关系处理
        //场景数据处理
        int pages = botPage.getPages();
        List<BotVO> handleList = new ArrayList<>();
        while (page <= pages) {
            if (page == 1) {
                handleList = botPage.getContent();
            } else {
                botQuery.setPageNum(page);
                handleList = botService.getPage(botQuery).getContent();
            }
            List<BotVO> updateList = new ArrayList<>();
            List<BotVO> updateTrackList = new ArrayList<>();
            for (BotVO botVO : handleList) {
                //场景数据处理
                if (botVO.getSubIndustry() != null) {
                    switch (botVO.getSubIndustry()) {
                        case DEBT_COLLECTION:
                        case CUSTOMER_DEBT_COLLECTION:
                        case BANK_DEBT_COLLECTION:
                            updateBotScene(sceneMap.get("催收"), botVO, updateList);
                            break;
                        case ACCOUNT_INVESTMENT:
                            updateBotScene(sceneMap.get("开户邀请"), botVO, updateList);
                            break;
                        case INSURANCE_NOTIFY:
                        case HEALTH_NOTIFY:
                        case EDUCATION_NOTIFY:
                        case AUTO_NOTIFY:
                        case CUSTOMER_INTERVIEW:
                        case E_COMMERCE_SURVEY:
                        case BANK_NOTIFY:
                        case CALLBACK:
                            updateBotScene(sceneMap.get("回访"), botVO, updateList);
                            break;
                        case EDUCATION_ONLINE:
                        case OFFLINE_BLUE_WATER:
                            updateBotScene(sceneMap.get("公海激活"), botVO, updateList);
                            break;
                        case EDUCATION_REMIND:
                            updateBotScene(sceneMap.get("催到课"), botVO, updateList);
                            break;
                        case EDUCATION_OTHER:
                        case AUTO_OTHER:
                        case CUSTOMER_OTHER:
                        case E_COMMERCE_OTHER:
                        case BANK_OTHER:
                        case COMMUNICATION_OTHER:
                        case GAME_OTHER:
                        case EDUCATION_STRANGER_VISIT:
                        case OTHER:
                            updateBotScene(sceneMap.get("其他"), botVO, updateList);
                            break;
                        case AUTO_INVITE:
                            updateBotScene(sceneMap.get("试驾邀约"), botVO, updateList);
                            break;
                        case GOVERNMENT_NOTIFY:
                        case NOTIFY_NO_SAY:
                            updateBotScene(sceneMap.get("通知"), botVO, updateList);
                            break;
                        case E_COMMERCE_DIVERSION:
                            updateBotScene(sceneMap.get("私域引流"), botVO, updateList);
                            break;
                        case PROMOTION_NOTIFY:
                            updateBotScene(sceneMap.get("活动通知"), botVO, updateList);
                            break;
                        case INVITE_ZAN:
                            updateBotScene(sceneMap.get("邀好评"), botVO, updateList);
                            break;
                        case STOCK_OUT:
                            updateBotScene(sceneMap.get("缺/发货提醒"), botVO, updateList);
                            break;
                        case REMIND_PAY:
                            updateBotScene(sceneMap.get("催付"), botVO, updateList);
                            break;
                        case GAME_RECALL:
                            updateBotScene(sceneMap.get("玩家召回"), botVO, updateList);
                            break;
                    }
                }
                //赛道数据处理
                Tuple2<String, Integer> key;
                if (botVO.getCustomerTrackType() != null) {
                    if (id2NameMap.containsKey(botVO.getCustomerTrackType())) {
                        String name = id2NameMap.get(botVO.getCustomerTrackType());
                        String subName = id2NameMap.get(botVO.getCustomerTrackSubType());
                        switch (name) {
                            case "幼儿教育":
                            case "素质教育":
                            case "少儿英语":
                            case "成人教育":
                            case "K12":
                            case "STEAM":
                            case "教育其他":
                            case "游戏":
                            case "其他":
                                key = Tuple.of(name, botVO.getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case "超市":
                            case "百货商场":
                                key = Tuple.of(name, parentName2IdMap.get("线下零售").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case "数据方":
                                key = Tuple.of("其他", parentName2IdMap.get("其他").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case "家清个护":
                                if (subName != null && subName.equals("鲜花")) {
                                    key = Tuple.of("超市", parentName2IdMap.get("线下零售").getCustomerTrackType());
                                    updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                }
                                if (subName != null) {
                                    if (subName.equals("居家日用")
                                            || subName.equals("个护")) {
                                        key = Tuple.of("百货商场", parentName2IdMap.get("线下零售").getCustomerTrackType());
                                        updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                    }
                                }
                                break;
                            case "电商其他":
                                if (subName != null && subName.equals("家纺类")) {
                                    key = Tuple.of("家纺类", parentName2IdMap.get("家清个护").getCustomerTrackType());
                                    updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                }
                                break;
                            case "平台类":
                                key = Tuple.of("其他", botVO.getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case "线下餐饮":
                                key = Tuple.of("线下餐饮", parentName2IdMap.get("食品").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                        }
                    }
                } else {
                    //行业数据处理
                    if (botVO.getSubIndustry() != null) {
                        switch (botVO.getSubIndustry()) {
                            case LOAN:
                            case BANK_LOAN:
                            case BANK_DEBT_COLLECTION:
                                key = Tuple.of("贷款", parentName2IdMap.get("金融").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case INVESTMENT:
                            case STOCK:
                            case BANK_SPREAD:
                                key = Tuple.of("投资理财", parentName2IdMap.get("金融").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case DEBT_COLLECTION:
                            case FINANCE_OTHER:
                            case ACCOUNT_INVESTMENT:
                            case BANK_OTHER:
                            case BANK_NOTIFY:
                                key = Tuple.of("其他", parentName2IdMap.get("金融").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case INSURANCE_RECOMMEND:
                            case INSURANCE_NOTIFY:
                            case INSURANCE_OTHER:
                                key = Tuple.of("保险", parentName2IdMap.get("金融").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case DECORATION:
                            case BUILDING_RECOMMEND:
                            case HOUSE_LEASE:
                            case REALTY_OTHER:
                                key = Tuple.of("其他", parentName2IdMap.get("房地产").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case LEGAL_SERVICE:
                                key = Tuple.of("法律服务", parentName2IdMap.get("企业服务").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case FINANCE_SERVICE:
                                key = Tuple.of("财税服务", parentName2IdMap.get("企业服务").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case SOFTWARE_SERVICE:
                                key = Tuple.of("软件服务", parentName2IdMap.get("企业服务").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case PROPERTY_RIGHT:
                                key = Tuple.of("产权/资质", parentName2IdMap.get("企业服务").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case MARKETING_PLAN:
                                key = Tuple.of("营销策划", parentName2IdMap.get("企业服务").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case HUMAN_RESOURCE:
                                key = Tuple.of("人力资源", parentName2IdMap.get("企业服务").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case COMPANY_OTHER:
                            case CALLBACK:
                            case NOTIFY_NO_SAY:
                            case BUSINESS_RECOMMEND:
                            case COMMUNICATION_OTHER:
                                key = Tuple.of("其他", parentName2IdMap.get("企业服务").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case HEALTH_NOTIFY:
                            case HEALTH_PRODUCT_RECOMMEND:
                            case HEALTH_ALLIANCE:
                            case HEALTH_OTHER:
                                key = Tuple.of("其他", parentName2IdMap.get("健康保健").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case RETAIL_ALLIANCE:
                            case PRODUCT_SALE:
                            case RETAIL_NOTIFY:
                            case RETAIL_OTHER:
                                key = Tuple.of("其他", parentName2IdMap.get("线下零售").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case EDUCATION_ONLINE:
                            case EDUCATION_OFFLINE:
                            case EDUCATION_REMIND:
                            case OFFLINE_BLUE_WATER:
                            case EDUCATION_NOTIFY:
                            case EDUCATION_STRANGER_VISIT:
                            case EDUCATION_OTHER:
                                key = Tuple.of("教育其他", parentName2IdMap.get("教育其他").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case AUTO_SALES:
                            case AUTO_MAINTAIN:
                            case AUTO_INSURANCE_SALE:
                            case AUTO_NOTIFY:
                            case AUTO_LOAN:
                            case AUTO_OTHER:
                                key = Tuple.of("其他", parentName2IdMap.get("汽车").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case AUTO_INVITE:
                                key = Tuple.of("车前", parentName2IdMap.get("汽车").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case GOVERNMENT_NOTIFY:
                            case CUSTOMER_INTERVIEW:
                            case CUSTOMER_DEBT_COLLECTION:
                            case CUSTOMER_OTHER:
                                key = Tuple.of("政府机构", parentName2IdMap.get("政府机构").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case E_COMMERCE_DIVERSION:
                            case PROMOTION_NOTIFY:
                            case INVITE_ZAN:
                            case STOCK_OUT:
                            case E_COMMERCE_SURVEY:
                            case REMIND_PAY:
                            case E_COMMERCE_OTHER:
                                key = Tuple.of("其他", parentName2IdMap.get("其他").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case GAME_NEW:
                            case GAME_RECALL:
                            case GAME_OTHER:
                                key = Tuple.of("游戏", parentName2IdMap.get("游戏").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                            case OTHER:
                                key = Tuple.of("其他", parentName2IdMap.get("其他").getCustomerTrackType());
                                updateBotTrackType(botVO, name2IdMap.get(key), updateTrackList);
                                break;
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                //更新场景
                botService.batchUpdate(updateList);
            }
            if (CollectionUtils.isNotEmpty(updateTrackList)) {
                //更新行业
                botService.batchUpdate(updateTrackList);
            }
            page++;
        }
    }

    private void updateBotTrackType(BotVO botVO, CustomerTrackTypePO customerTrackTypePO, List<BotVO> updateTrackList) {
        if (Objects.isNull(customerTrackTypePO)) {
            return;
        }
        botVO.setCustomerTrackSubType(customerTrackTypePO.getCustomerTrackType());
        botVO.setCustomerTrackType(customerTrackTypePO.getCustomerTrackParentType());
        updateTrackList.add(botVO);
    }

    private void updateBotScene(Integer sceneId, BotVO botVO, List<BotVO> botList) {
        if (sceneId == null) {
            return;
        }
        botVO.setCustomerSceneId(sceneId);
        botList.add(botVO);
    }

    @Override
    public void fixAiRepeatName() {
        // 特殊语境模板中的“AI重复上一句”修改为“AI重复上句语音”
        mongoTemplate.updateFirst(
                Query.query(Criteria.where("name").is("AI重复上一句")),
                Update.update("name", SpecialAnswerConfigPO.AI_REPEAT),
                SpecialAnswerConfigPO.TEMPLATE_COLLECTION_NAME
        );

        // 历史数据中的“AI重复上一句”修改为“AI重复上句语音”
        mongoTemplate.updateMulti(
                Query.query(Criteria.where("name").is("AI重复上一句")),
                Update.update("name", SpecialAnswerConfigPO.AI_REPEAT),
                SpecialAnswerConfigPO.COLLECTION_NAME
        );
    }

    @Override
    public void fixBotGenerateTemplateStatus() {
        // 有完成度的都算成功，其余的都是未生成
        mongoTemplate.updateMulti(
                Query.query(Criteria.where("lastCompletePercent").ne(null)),
                Update.update("status", BotGenerateTemplateStatusEnum.SUCCESS),
                BotGenerateTemplatePO.COLLECTION_NAME
        );
        mongoTemplate.updateMulti(
                Query.query(Criteria.where("lastCompletePercent").is(null)),
                Update.update("status", BotGenerateTemplateStatusEnum.CREATED),
                BotGenerateTemplatePO.COLLECTION_NAME
        );
    }
}
