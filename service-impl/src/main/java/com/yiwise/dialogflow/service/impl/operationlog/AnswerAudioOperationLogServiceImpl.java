package com.yiwise.dialogflow.service.impl.operationlog;

import com.yiwise.dialogflow.entity.dto.OperationLogDTO;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.vo.audio.AudioUploadResultVO;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.OperationLogService;
import com.yiwise.dialogflow.service.operationlog.AnswerAudioOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AnswerAudioOperationLogServiceImpl implements AnswerAudioOperationLogService {

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private BotService botService;

    @Override
    public void uploadAnswerAudio(Long botId, List<String> answerList, Long userId) {
        if (CollectionUtils.isEmpty(answerList)) {
            return;
        }
        List<String> logs = answerList.stream()
                .map(item -> String.format("上传【%s】话术音频", item))
                .collect(Collectors.toList());
        save(botId, logs, userId);
    }

    @Override
    public void resetAnswerAudio(Long botId, List<String> answerList, Long userId) {
        if (CollectionUtils.isEmpty(answerList)) {
            return;
        }
        List<String> logs = answerList.stream()
                .map(item -> String.format("重置【%s】话术音频", item))
                .collect(Collectors.toList());
        save(botId, logs, userId);
    }

    @Override
    public void replaceAnswerAudio(Long botId, List<String> answerList, Long userId) {

    }

    @Override
    public void searchAndReplaceAnswer(Long botId, List<String> detailList, Long userId, boolean replaceVar) {
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }
        String detail = String.format("%s替换: %s, 共%s条", replaceVar ? "变量" : "话术", String.join("; ", detailList), detailList.size());
        OperationLogDTO log = OperationLogDTO.builder()
                .operatorId(userId)
                .botId(botId)
                .detail(detail)
                .type(OperationLogTypeEnum.SEARCH_REPLACE)
                .resourceType(OperationLogResourceTypeEnum.REPLACE)
                .build();
        operationLogService.batchSave(Collections.singletonList(log));
    }

    @Override
    public void resetAllAnswerAudio(Long botId, AudioTypeEnum audioType, Long userId) {
        save(botId, Collections.singletonList("重置全部答案"), userId);
    }

    @Override
    public void copyAnswerAudio(Long fromBotId, Long toBotId, Set<String> fromAnswerSet, Long userId) {
        if (CollectionUtils.isEmpty(fromAnswerSet)) {
            return;
        }
        String sourceBotName = "botId:" + fromBotId;
        BotPO bot = botService.selectByKey(fromBotId);
        if (Objects.nonNull(bot)) {
            sourceBotName = bot.getName();
        }
        // 将答案之前添加递增序号
        AtomicInteger index = new AtomicInteger(0);
        List<String> answerList = fromAnswerSet.stream()
                .map(item -> String.format("%s. %s", index.incrementAndGet(), item))
                .collect(Collectors.toList());
        String detail = String.format("同步录音:【%s】同步至当前bot, 成功%s条: %s", sourceBotName, fromAnswerSet.size(),
                String.join("; ", answerList));
        save(toBotId, Collections.singletonList(detail), userId);
    }

    @Override
    public void ttsComposeLog(Long botId, String logInfo, Long userId) {
        if (StringUtils.isBlank(logInfo)) {
            return;
        }
        save(botId, Collections.singletonList(logInfo), userId);
    }

    @Override
    public void batchUpload(Long botId, Map<String, AudioUploadResultVO> audioUploadResultMap, Long userId) {
        if (MapUtils.isEmpty(audioUploadResultMap)) {
            return;
        }
        String answerList = audioUploadResultMap.keySet().stream()
                .map(item -> String.format("【%s】", item))
                .collect(Collectors.joining("、"));
        String detail = String.format("批量上传音频: %s, 共%s条", answerList, audioUploadResultMap.size());
        save(botId, Collections.singletonList(detail), userId);
    }

    @Override
    public void batchAdjustVolume(Long botId, List<String> adjustSuccessTextList, Integer volume, Long userId) {
        if (CollectionUtils.isEmpty(adjustSuccessTextList)) {
            return;
        }

        String answerList = adjustSuccessTextList.stream()
                .distinct()
                .map(item -> String.format("【%s】", item))
                .collect(Collectors.joining("、"));
        String detail = String.format("音量调节: 文本%s, 音量调整至【%s】", answerList, volume);
        save(botId, Collections.singletonList(detail), userId);
    }

    private void save(Long botId, List<String> logDetailList, Long userId) {
        if (CollectionUtils.isEmpty(logDetailList)) {
            return;
        }
        List<OperationLogDTO> logs = logDetailList.stream()
                .map(detail -> OperationLogDTO.builder()
                        .operatorId(userId)
                        .detail(detail)
                        .botId(botId)
                        .type(OperationLogTypeEnum.AUDIO)
                        .resourceType(OperationLogResourceTypeEnum.AUDIO_LIST)
                        .build()
                ).collect(Collectors.toList());
        operationLogService.batchSave(logs);
    }
}
