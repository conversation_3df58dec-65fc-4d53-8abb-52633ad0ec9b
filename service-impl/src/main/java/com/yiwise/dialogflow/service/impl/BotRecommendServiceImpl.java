package com.yiwise.dialogflow.service.impl;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.MongoCollectionNameCenter;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.vo.*;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.utils.BotStatsUtil;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class BotRecommendServiceImpl implements BotRecommendService {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private StepNodeService stepNodeService;

    @Resource
    private KnowledgeService knowledgeService;

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    static class Result implements Serializable {

        String status;

        String message;

        public boolean isSuccess() {
            return "success".equals(status);
        }
    }

    @Override
    public boolean textParsing(Long botId) {
        try {
            String url = ApplicationConstant.ALGORITHM_TEXT_BANK_PARSING_URL;
            Map<String, Object> params = new HashMap<>(4);
            params.put("bot_id", botId);
            params.put("ope_domain", ApplicationConstant.ALGORITHM_SERVICE_OPE_DOMAIN);
            params.put("namespace", ApplicationConstant.ALGORITHM_SERVICE_NAMESPACE);
            String body = JsonUtils.object2String(params);

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add("Content-Type", "application/json");
            HttpEntity<String> requestEntity = new HttpEntity<>(body, httpHeaders);

            log.info("调用算法话术解析接口, url={}, body={}", url, body);
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
            log.info("调用算法话术解析接口结果为:response={}", response);

            Result result = JsonUtils.string2Object(response.getBody(), Result.class);
            if (!result.isSuccess()) {
                log.error("[LogHub_Warn]调用算法话术解析接口失败, botId={}, result={}", botId, result);
            }
            return result.isSuccess();
        } catch (Exception e) {
            log.error("[LogHub_Warn]调用算法话术解析接口失败,botId={}", botId, e);
            return false;
        }
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    static class BotCallCountStatistics implements Serializable {

        Long botId;

        Long count;
    }

    @Override
    public void textParsingByRangeTime(LocalDateTime startTime, LocalDateTime endTime, Long minCallCount) {
        long beginEpochHour = BotStatsUtil.toEpochHour(startTime);
        long endEpochHour = BotStatsUtil.toEpochHour(endTime);
        List<AggregationOperation> aggregationOperationList = new ArrayList<>(4);
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.group("botId").sum("totalCallCount").as("count").first("botId").as("botId"));
        aggregationOperationList.add(Aggregation.match(Criteria.where("count").gte(minCallCount)));
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
        List<BotCallCountStatistics> botCallCountStatisticsList = mongoTemplate.aggregate(aggregation,
                MongoCollectionNameCenter.BOT_RECEPTION_STATS, BotCallCountStatistics.class).getMappedResults();
        log.info("botCallCountStatisticsList={}", JsonUtils.object2String(botCallCountStatisticsList));
        if (CollectionUtils.isEmpty(botCallCountStatisticsList)) {
            return;
        }
        botCallCountStatisticsList.stream().map(BotCallCountStatistics::getBotId).forEach(this::textParsing);
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @FieldDefaults(level = AccessLevel.PRIVATE)
    static class SearchResult extends Result implements Serializable {

        List<BotTextSearchResultVO> data;
    }

    @Override
    public List<BotTextSearchResultVO> search(BotRecommendSearchRequestVO request) {
        String url = ApplicationConstant.ALGORITHM_TEXT_BANK_SEARCH_URL;
        Map<String, Object> params = new HashMap<>(4);
        params.put("sentence", request.getSearch());
        params.put("k", request.getCount());
        params.put("ope_domain", ApplicationConstant.ALGORITHM_SERVICE_OPE_DOMAIN);
        params.put("namespace", ApplicationConstant.ALGORITHM_SERVICE_NAMESPACE);
        if (StringUtils.isNotBlank(request.getCategory())) {
            params.put("category", request.getCategory());
        }
        String body = JsonUtils.object2String(params);

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Content-Type", "application/json");
        HttpEntity<String> requestEntity = new HttpEntity<>(body, httpHeaders);

        log.info("调用算法相似句接口, url={}, body={}", url, body);
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        log.info("调用算法相似句接口结果为:response={}", response);

        SearchResult result = JsonUtils.string2Object(response.getBody(), SearchResult.class);
        if (!result.isSuccess()) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "调用算法相似句接口失败");
        }
        return result.getData();
    }

    private static final Table<String, String, Comparator<BotTextSearchResultVO>> SORT_TABLE = HashBasedTable.create();

    static {
        SORT_TABLE.put("hangupRate", ApplicationConstant.SORT_DIRECTION_ASC, Comparator.comparing(BotTextSearchResultVO::getHangupRate, Comparator.nullsLast(Double::compareTo)));
        SORT_TABLE.put("hangupRate", ApplicationConstant.SORT_DIRECTION_DESC, Comparator.comparing(BotTextSearchResultVO::getHangupRate, Comparator.nullsFirst(Double::compareTo)).reversed());

        SORT_TABLE.put("negativeRate", ApplicationConstant.SORT_DIRECTION_ASC, Comparator.comparing(BotTextSearchResultVO::getNegativeRate, Comparator.nullsLast(Double::compareTo)));
        SORT_TABLE.put("negativeRate", ApplicationConstant.SORT_DIRECTION_DESC, Comparator.comparing(BotTextSearchResultVO::getNegativeRate, Comparator.nullsFirst(Double::compareTo)).reversed());

        SORT_TABLE.put("positiveRate", ApplicationConstant.SORT_DIRECTION_ASC, Comparator.comparing(BotTextSearchResultVO::getPositiveRate, Comparator.nullsLast(Double::compareTo)));
        SORT_TABLE.put("positiveRate", ApplicationConstant.SORT_DIRECTION_DESC, Comparator.comparing(BotTextSearchResultVO::getPositiveRate, Comparator.nullsFirst(Double::compareTo)).reversed());

        SORT_TABLE.put("score", ApplicationConstant.SORT_DIRECTION_ASC, Comparator.comparing(BotTextSearchResultVO::getScore, Comparator.nullsLast(Double::compareTo)));
        SORT_TABLE.put("score", ApplicationConstant.SORT_DIRECTION_DESC, Comparator.comparing(BotTextSearchResultVO::getScore, Comparator.nullsFirst(Double::compareTo)).reversed());

        SORT_TABLE.put("similarity", ApplicationConstant.SORT_DIRECTION_ASC, Comparator.comparing(BotTextSearchResultVO::getSimilarity, Comparator.nullsLast(Double::compareTo)));
        SORT_TABLE.put("similarity", ApplicationConstant.SORT_DIRECTION_DESC, Comparator.comparing(BotTextSearchResultVO::getSimilarity, Comparator.nullsFirst(Double::compareTo)).reversed());
    }

    @Override
    public List<BotTextSearchResultVO> sort(BotRecommendSearchResultSortRequestVO request) {
        List<BotTextSearchResultVO> resultList = request.getResultList();
        String orderBy = request.getOrderBy();
        String direction = request.getDirection();
        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }
        if (StringUtils.isBlank(orderBy) || StringUtils.isBlank(direction)) {
            return resultList;
        }
        Comparator<BotTextSearchResultVO> comparator = SORT_TABLE.get(orderBy, direction);
        if (Objects.isNull(comparator)) {
            return resultList;
        }
        return resultList.stream().sorted(comparator).collect(Collectors.toList());
    }

    @Override
    public Map<String, List<String>> rephrase(BotRecommendRephraseRequestVO request) {
        Map<String, List<String>> historyMap = request.getHistoryMap();
        Map<String, List<String>> resultMap = new HashMap<>(historyMap.size());
        historyMap.forEach((referenceSentence, historyList) ->
                resultMap.put(referenceSentence, rephrase(request.getUserSentence(), referenceSentence, request.getCount(), historyList)));
        return resultMap;
    }

    @Override
    public Map<String, List<BotRecommendRephraseRecordPO>> botAnswerRephrase(BotAnswerRephraseRequestVO request, Long userId) {
        Map<String, List<String>> historyMap = request.getHistoryMap();
        Map<String, List<BotRecommendRephraseRecordPO>> resultMap = new HashMap<>(historyMap.size());
        historyMap.forEach((referenceSentence, historyList) ->
                resultMap.put(referenceSentence, rephraseAndSave(request, referenceSentence, request.getCount(), historyList, userId)));
        return resultMap;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @FieldDefaults(level = AccessLevel.PRIVATE)
    static class RephraseResult extends Result implements Serializable {

        List<String> result;
    }

    private List<String> rephrase(String userSentence, String referenceSentence, Integer count, List<String> historyList) {
        try {
            String url = ApplicationConstant.ALGORITHM_TEXT_BANK_REPHRASE_URL;
            Map<String, Object> params = new HashMap<>(4);
            params.put("user_sentence", userSentence);
            params.put("reference_sentence", referenceSentence);
            params.put("count", count);
            if (CollectionUtils.isNotEmpty(historyList)) {
                params.put("history", historyList);
            }
            String body = JsonUtils.object2String(params);

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add("Content-Type", "application/json");
            HttpEntity<String> requestEntity = new HttpEntity<>(body, httpHeaders);

            log.info("调用算法文本重写接口, url={}, body={}", url, body);
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
            log.info("调用算法文本重写接口结果为:response={}", response);

            RephraseResult result = JsonUtils.string2Object(response.getBody(), RephraseResult.class);
            if (!result.isSuccess()) {
                log.error("[LogHub_Warn]调用算法文本重写接口失败,result={}", result);
                return Collections.emptyList();
            }
            return result.getResult();
        } catch (Exception e) {
            log.error("[LogHub_Warn]调用算法文本重写接口失败", e);
            return Collections.emptyList();
        }
    }

    private List<BotRecommendRephraseRecordPO> rephraseAndSave(BotAnswerRephraseRequestVO request, String referenceSentence, Integer count, List<String> historyList, Long userId) {
        List<String> rephrasedSentenceList = rephrase(request.getUserSentence(), referenceSentence, count, historyList);
        if (CollectionUtils.isEmpty(rephrasedSentenceList)) {
            return null;
        }
        LocalDateTime now = LocalDateTime.now();
        List<BotRecommendRephraseRecordPO> resultList = new ArrayList<>(rephrasedSentenceList.size());
        for (String rephrasedSentence : rephrasedSentenceList) {
            BotRecommendRephraseRecordPO record = new BotRecommendRephraseRecordPO();
            record.setBotId(request.getBotId());
            record.setAnswerSource(request.getAnswerSource());
            record.setStepId(request.getStepId());
            record.setNodeId(request.getNodeId());
            record.setKnowledgeId(request.getKnowledgeId());
            record.setUserSentence(request.getUserSentence());
            record.setReferenceSentence(referenceSentence);
            record.setRephrasedSentence(rephrasedSentence);
            record.setCreateTime(now);
            record.setUpdateTime(now);
            record.setCreateUserId(userId);
            record.setUpdateUserId(userId);
            mongoTemplate.save(record, BotRecommendRephraseRecordPO.COLLECTION_NAME);
            resultList.add(record);
        }
        return resultList;
    }

    @Override
    public List<BotRecommendRephraseRecordPO> queryRephraseRecordList(BotAnswerRephraseRequestVO request) {
        AnswerSourceEnum answerSource = request.getAnswerSource();
        Query query = Query.query(Criteria.where("botId").is(request.getBotId()).and("answerSource").is(answerSource));
        if (AnswerSourceEnum.STEP.equals(answerSource)) {
            query.addCriteria(Criteria.where("stepId").is(request.getStepId()).and("nodeId").is(request.getNodeId()));
        } else if (AnswerSourceEnum.KNOWLEDGE.equals(answerSource)) {
            query.addCriteria(Criteria.where("knowledgeId").is(request.getKnowledgeId()));
        }
        query.with(Sort.by(Sort.Direction.DESC, "_id"));
        return mongoTemplate.find(query, BotRecommendRephraseRecordPO.class, BotRecommendRephraseRecordPO.COLLECTION_NAME);
    }

    @Override
    public void updateRephrasedSentence(String id, String rephrasedSentence, Long userId) {
        Query query = Query.query(Criteria.where("_id").is(id));
        Update update = Update.update("rephrasedSentence", rephrasedSentence).set("updateTime", LocalDateTime.now()).set("updateUserId", userId);
        mongoTemplate.updateFirst(query, update, BotRecommendRephraseRecordPO.COLLECTION_NAME);
    }

    @Override
    public void starred(String id, Long userId) {
        Query query = Query.query(Criteria.where("_id").is(id));
        Update update = Update.update("starred", true).set("updateTime", LocalDateTime.now()).set("updateUserId", userId);
        mongoTemplate.updateFirst(query, update, BotRecommendRephraseRecordPO.COLLECTION_NAME);
    }

    @Override
    public void deleteRephrasedRecord(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        mongoTemplate.remove(Query.query(Criteria.where("_id").in(idList)), BotRecommendRephraseRecordPO.COLLECTION_NAME);
    }

    @Override
    public List<Long> queryBotIdListByRangeTime(LocalDateTime startTime, LocalDateTime endTime) {
        Query query = Query.query(Criteria.where("createTime").gte(startTime).lte(endTime));
        return mongoTemplate.findDistinct(query, "botId", BotRecommendRephraseRecordPO.COLLECTION_NAME, BotRecommendRephraseRecordPO.class, Long.class);
    }

    @Override
    public List<BotRecommendRephraseRecordVO> queryAnswerStarredRephraseRecordList(Long botId) {
        List<BotRecommendRephraseRecordVO> botRecommendRephraseRecordList = mongoTemplate.find(Query.query(Criteria.where("botId").is(botId)), BotRecommendRephraseRecordVO.class, BotRecommendRephraseRecordPO.COLLECTION_NAME);
        if (CollectionUtils.isEmpty(botRecommendRephraseRecordList)) {
            return Collections.emptyList();
        }

        List<DialogBaseNodePO> nodeList = stepNodeService.getAllListByBotId(botId);
        Map<String, String> nodeIdAnswerMap = new HashMap<>();
        for (DialogBaseNodePO node : nodeList) {
            if (Objects.nonNull(node) && CollectionUtils.isNotEmpty(node.getAnswerList())) {
                String text = node.getAnswerList().get(0).getText();
                nodeIdAnswerMap.put(node.getId(), text);
            }
        }

        List<KnowledgePO> knowledgeList = knowledgeService.getAllListByBotId(botId);
        Map<String, String> knowledgeIdAnswerMap = new HashMap<>();
        for (KnowledgePO knowledge : knowledgeList) {
            if (Objects.nonNull(knowledge) && CollectionUtils.isNotEmpty(knowledge.getAnswerList())) {
                String text = knowledge.getAnswerList().get(0).getText();
                knowledgeIdAnswerMap.put(knowledge.getId(), text);
            }
        }

        List<BotRecommendRephraseRecordVO> resultList = new ArrayList<>();
        for (BotRecommendRephraseRecordVO record : botRecommendRephraseRecordList) {
            if (BooleanUtils.isNotTrue(record.getStarred())) {
                continue;
            }
            AnswerSourceEnum answerSource = record.getAnswerSource();
            String answer = null;
            if (AnswerSourceEnum.STEP.equals(answerSource)) {
                answer = nodeIdAnswerMap.get(record.getNodeId());
            } else if (AnswerSourceEnum.KNOWLEDGE.equals(answerSource)) {
                answer = knowledgeIdAnswerMap.get(record.getKnowledgeId());
            }
            if (StringUtils.isNotBlank(answer)) {
                record.setFinalAnswer(answer);
                resultList.add(record);
            }
        }
        return resultList;
    }
}