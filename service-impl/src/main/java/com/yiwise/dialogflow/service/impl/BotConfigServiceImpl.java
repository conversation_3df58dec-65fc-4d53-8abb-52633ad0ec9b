package com.yiwise.dialogflow.service.impl;

import com.google.common.collect.Lists;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.OssKeyCenter;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.entity.bo.AsrMetaData;
import com.yiwise.dialogflow.entity.bo.BotConfigSyncDTO;
import com.yiwise.dialogflow.entity.bo.SnapshotInvalidFailItemMsg;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import com.yiwise.dialogflow.entity.enums.llm.LlmGuideAnswerConfigEnum;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.asrmodel.*;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrUpdateVO;
import com.yiwise.dialogflow.entity.vo.audio.BotAudioConfigVO;
import com.yiwise.dialogflow.entity.vo.sync.BasicSyncVO;
import com.yiwise.dialogflow.entity.vo.sync.BotSyncResultVO;
import com.yiwise.dialogflow.mapper.BotPOMapper;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.asrmodel.*;
import com.yiwise.dialogflow.service.operationlog.AudioConfigOperationLogService;
import com.yiwise.dialogflow.service.remote.UserService;
import com.yiwise.dialogflow.utils.SensitivityLevelUtil;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import com.yiwise.middleware.tts.enums.TtsVoiceEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 只负责机器人配置的查询和存储以及初始化工作, 配置校验逻辑在各种的子配置服务中, 比如BotAudioConfigService
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BotConfigServiceImpl implements BotConfigService, RobotResourceService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Lazy
    @Resource
    private BotService botService;

    @Resource
    private AudioUploadService audioUploadService;

    @Lazy
    @Resource
    private AnswerAudioMappingService answerAudioMappingService;

    @Resource
    private ObjectStorageHelper objectStorageHelper;

    @Resource
    private UserService userService;

    @Resource
    private AudioConfigOperationLogService audioConfigOperationLogService;

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private AsrProviderService asrProviderService;

    @Resource
    private AsrVocabService asrVocabService;

    @Resource
    private AsrSelfLearningService asrSelfLearningService;

    @Resource
    private AsrErrorCorrectionService asrErrorCorrectionService;

    @Resource
    private BotSyncOperationLogService botSyncOperationLogService;

    @Lazy
    @Resource
    private SpecialAnswerConfigService specialAnswerConfigService;

    @Resource
    private BotPOMapper botPOMapper;

    @Override
    public void initOnCreateBot(Long botId) {
        log.info("initBotConfig botId={}", botId);
        BotConfigPO config = new BotConfigPO();
        config.setBotId(botId);
        config.setAudioConfig(generateDefaultAudioConfig(botId));
        config.setSpeechConfig(generateDefaultSpeechConfig(botId));
        mongoTemplate.save(config, BotConfigPO.COLLECTION_NAME);
    }

    @Override
    public BotAudioConfigPO getAudioConfig(Long botId) {
        BotAudioConfigPO result = getConfig(botId).getAudioConfig();
        if (Objects.nonNull(result)) {
            result.setBotId(botId);
        }
        return result;
    }

    @Override
    public BotAudioConfigVO getAudioConfigVO(Long botId) {
        BotAudioConfigPO po = getAudioConfig(botId);
        if (po == null) {
            return null;
        }
        BotAudioConfigVO vo = MyBeanUtils.copy(po, BotAudioConfigVO.class);
        if (!AudioTypeEnum.COMPOSE.equals(vo.getAudioType()) && Objects.nonNull(vo.getRecordUserId())) {
            try {
                UserPO user = userService.getUserById(vo.getRecordUserId());
                if (Objects.nonNull(user)) {
                    vo.setRecordUserName(user.getName());
                }
            } catch (Exception e) {
                log.warn("查询用户信息出错", e);
            }
        }
        return vo;
    }

    @Override
    public BotAudioConfigPO saveAudioConfig(Long botId, BotAudioConfigPO tmpConfig, Long userId) {
        if (Objects.isNull(tmpConfig.getBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "机器人id不能为空");
        }
        // 如果tts配置变更, 则重新合成所有音频
        BotAudioConfigPO oldConfig = getAudioConfig(tmpConfig.getBotId());
        BotAudioConfigPO newConfig = mergeNotNullProperties(tmpConfig, oldConfig);

        // 校验背景音配置
        adjustBackgroundVolume(oldConfig, newConfig);

        if (isTtsConfigChanged(oldConfig, newConfig)) {
            log.info("tts合成配置变更, 删除所有已合成录音");
            answerAudioMappingService.deleteAllComposeAudio(newConfig.getBotId());
        }
        updateRecordUserIdIfChanged(oldConfig, newConfig);
        newConfig.setBotId(botId);
        saveSubConfig(botId, "audioConfig", newConfig);
        audioConfigOperationLogService.compareAudioConfigAndCreateOperationLog(botId, oldConfig, newConfig, userId);
        return getAudioConfig(botId);
    }

    private void updateRecordUserIdIfChanged(BotAudioConfigPO oldConfig, BotAudioConfigPO newConfig) {
        Long oldRecordUserId = AudioTypeEnum.COMPOSE.equals(oldConfig.getAudioType()) ? null : oldConfig.getRecordUserId();
        Long newRecordUserId = AudioTypeEnum.COMPOSE.equals(newConfig.getAudioType()) ? null : newConfig.getRecordUserId();
        if (!Objects.equals(oldRecordUserId, newRecordUserId)) {
            botPOMapper.updateRecordUserId(newConfig.getBotId(), newRecordUserId);
        }
    }

    @Override
    public BotSpeechConfigPO getSpeechConfig(Long botId) {
        BotSpeechConfigPO result = getConfig(botId).getSpeechConfig();
        if (Objects.nonNull(result)) {
            result.setBotId(botId);
        }
        return result;
    }

    @Override
    public BotConfigPO getByBotId(Long botId) {
        return getConfig(botId);
    }

    @Override
    public void saveSpeechConfig(Long botId, BotSpeechConfigPO speechConfig, Long currentUserId) {
        if (Objects.isNull(speechConfig.getBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "机器人id不能为空");
        }
        if (Objects.isNull(speechConfig.getUserSilenceThreshold())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "阈值设置不能为空");
        }
        if (speechConfig.getUserSilenceThreshold() < 0) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "阈值设置不能小于0");
        }
        if (!Objects.isNull(speechConfig.getEnableToneInterrupt()) && speechConfig.getEnableToneInterrupt()) {
            if (Objects.isNull(speechConfig.getToneInterruptPercent()) || Objects.isNull(speechConfig.getToneWordList())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "播报进度或语气词不能为空");
            }
        }
        BotSpeechConfigPO oldConfig = getSpeechConfig(botId);
        speechConfig.setBotId(botId);
        saveSubConfig(botId, "speechConfig", speechConfig);

        buildLogAndSave(oldConfig, speechConfig, currentUserId);
    }

    @Override
    public AsrMetaData getAsrMetaData(Long botId) {
        AsrMetaData asrMetaData = new AsrMetaData();
        BotPO bot = botService.getById(botId);
        asrMetaData.setBotPO(bot);
        asrMetaData.setAsrProviderPO(asrProviderService.get(bot.getAsrProviderId()));
        asrMetaData.setAsrVocabDetailPO(asrVocabService.getById(bot.getAsrVocabId()));
        asrMetaData.setAsrSelfLearningDetailPO(asrSelfLearningService.getById(bot.getAsrSelfLearningDetailId()));
        asrMetaData.setAsrErrorCorrectionDetailPO(asrErrorCorrectionService.get(bot.getAsrErrorCorrectionDetailId()));
        asrMetaData.setMaxSentenceSilenceLevel(SensitivityLevelUtil.valueToLevel(bot.getMaxSentenceSilence(), SensitivityLevelEnum.maxSentenceSilence));
        asrMetaData.setEnableAsrDelayStart(BooleanUtils.isTrue(bot.getEnableAsrDelayStart()));
        asrMetaData.setAsrDelayStartSeconds(bot.getAsrDelayStartSeconds());
        return asrMetaData;
    }

    private void buildLogAndSave(BotPO oldBot, BotPO newBot, Long currentUserId) {
        List<String> detailList = Lists.newArrayList();
        if (!Objects.equals(oldBot.getMaxSentenceSilence(), newBot.getMaxSentenceSilence())) {
            detailList.add(String.format("反应灵敏度【%s】修改为【%s】",
                    SensitivityLevelUtil.valueToLevel(oldBot.getMaxSentenceSilence(), SensitivityLevelEnum.maxSentenceSilence),
                    SensitivityLevelUtil.valueToLevel(newBot.getMaxSentenceSilence(), SensitivityLevelEnum.maxSentenceSilence)));
        }
        if (!Objects.equals(oldBot.getAsrLanguageId(), newBot.getAsrLanguageId())) {
            Map<Long, String> languageMap = MyCollectionUtils.listToMap(asrProviderService.languageList(), AsrLanguagePO::getAsrLanguageId, AsrLanguagePO::getLanguageName);
            detailList.add(String.format("ASR语言类型【%s】修改为【%s】",
                    languageMap.getOrDefault(oldBot.getAsrLanguageId(), ""), languageMap.getOrDefault(newBot.getAsrLanguageId(), "")));
        }
        if (!Objects.equals(oldBot.getAsrProviderId(), newBot.getAsrProviderId())) {
            Map<Long, String> asrProviderMap = Stream.of(oldBot.getAsrProviderId(), newBot.getAsrProviderId()).filter(Objects::nonNull).map(asrProviderService::get)
                    .collect(Collectors.toMap(AsrProviderPO::getAsrProviderId, AsrProviderPO::getName));
            detailList.add(String.format("ASR供应商【%s】修改为【%s】",
                    asrProviderMap.getOrDefault(oldBot.getAsrProviderId(), ""), asrProviderMap.getOrDefault(newBot.getAsrProviderId(), "")));
        }
        if (!Objects.equals(oldBot.getAsrSelfLearningDetailId(), newBot.getAsrSelfLearningDetailId())) {
            Map<Long, String> asrSelfLearningDetailMap = Stream.of(oldBot.getAsrSelfLearningDetailId(), newBot.getAsrSelfLearningDetailId())
                    .filter(Objects::nonNull).map(asrSelfLearningService::getById)
                    .collect(Collectors.toMap(AsrSelfLearningDetailPO::getAsrSelfLearningDetailId, AsrSelfLearningDetailPO::getName));
            detailList.add(String.format("ASR自学习模型【%s】修改为【%s】",
                    asrSelfLearningDetailMap.getOrDefault(oldBot.getAsrSelfLearningDetailId(), ""), asrSelfLearningDetailMap.getOrDefault(newBot.getAsrSelfLearningDetailId(), "")));
        }
        if (!Objects.equals(oldBot.getAsrVocabId(), newBot.getAsrVocabId())) {
            Map<Long, String> asrVocabDetailMap = Stream.of(oldBot.getAsrVocabId(), newBot.getAsrVocabId()).filter(Objects::nonNull).map(asrVocabService::getById)
                    .collect(Collectors.toMap(AsrVocabDetailPO::getAsrVocabId, AsrVocabDetailPO::getName));
            detailList.add(String.format("ASR热词组【%s】修改为【%s】",
                    asrVocabDetailMap.getOrDefault(oldBot.getAsrVocabId(), ""), asrVocabDetailMap.getOrDefault(newBot.getAsrVocabId(), "")));
        }
        if (!Objects.equals(oldBot.getAsrErrorCorrectionDetailId(), newBot.getAsrErrorCorrectionDetailId())) {
            Map<String, String> asrErrorCorrectionDetailMap = Stream.of(oldBot.getAsrErrorCorrectionDetailId(), newBot.getAsrErrorCorrectionDetailId())
                    .filter(StringUtils::isNotBlank).map(asrErrorCorrectionService::get)
                    .collect(Collectors.toMap(AsrErrorCorrectionDetailPO::getAsrErrorCorrectionDetailId, AsrErrorCorrectionDetailPO::getName));
            detailList.add(String.format("ASR纠错模型【%s】修改为【%s】",
                    asrErrorCorrectionDetailMap.getOrDefault(oldBot.getAsrErrorCorrectionDetailId(), ""), asrErrorCorrectionDetailMap.getOrDefault(newBot.getAsrErrorCorrectionDetailId(), "")));
        }
        String oldAsrDelayDesc = getAsrDelayStartDescription(oldBot);
        String newAsrDelayDesc = getAsrDelayStartDescription(newBot);
        if (!StringUtils.equals(oldAsrDelayDesc, newAsrDelayDesc)) {
            String log = String.format("ASR延迟启动时间设置【%s】修改为【%s】", oldAsrDelayDesc, newAsrDelayDesc);
            detailList.add(log);
        }
        operationLogService.save(newBot.getBotId(), OperationLogTypeEnum.BOT_CONFIG, OperationLogResourceTypeEnum.ASR_CONFIG, detailList, currentUserId);
    }

    @Override
    public void updateAsrMetaData(AsrUpdateVO asrUpdateVO) {
        BotPO oldBotPO = botService.getById(asrUpdateVO.getBotId());
        // 更新botPO
        BotPO updatePO = MyBeanUtils.copy(oldBotPO, BotPO.class);
        updatePO.setAsrLanguageId(asrUpdateVO.getAsrLanguageId());
        if (Objects.nonNull(asrUpdateVO.getAsrProviderId())) {
            updatePO.setAsrProviderId(asrUpdateVO.getAsrProviderId());
        } else if (Objects.nonNull(asrUpdateVO.getAsrLanguageId())) {
            updatePO.setAsrProviderId(asrProviderService.getByLanguageId(asrUpdateVO.getAsrLanguageId()).getAsrProviderId());
        }
        updatePO.setAsrVocabId(asrUpdateVO.getAsrVocabDetailId());
        updatePO.setAsrSelfLearningDetailId(asrUpdateVO.getAsrSelfLearningDetailId());
        updatePO.setAsrErrorCorrectionDetailId(asrUpdateVO.getAsrErrorCorrectionDetailId());
        updatePO.setAuditStatus(AuditStatusEnum.DRAFT);
        updatePO.setUpdateUserId(asrUpdateVO.getCurrentUserId());
        updatePO.setMaxSentenceSilence(SensitivityLevelUtil.levelToValue(asrUpdateVO.getMaxSentenceSilence(), SensitivityLevelEnum.maxSentenceSilence));
        updatePO.setEnableAsrDelayStart(BooleanUtils.isTrue(asrUpdateVO.getEnableAsrDelayStart()));
        updatePO.setAsrDelayStartSeconds(asrUpdateVO.getAsrDelayStartSeconds());
        botService.updateAll(updatePO);
        asrSelfLearningService.trainSelfLearning(asrUpdateVO.getTenantId(), asrUpdateVO.getCurrentUserId(), asrUpdateVO.getAsrSelfLearningDetailId());

        // 添加操作日志
        buildLogAndSave(oldBotPO, updatePO, asrUpdateVO.getCurrentUserId());
    }

    private String getAsrDelayStartDescription(BotPO bot) {
        if (BooleanUtils.isNotTrue(bot.getEnableAsrDelayStart())) {
            return "未启用";
        }
        return String.format("%s秒", bot.getAsrDelayStartSeconds());
    }

    private void buildLogAndSave(BotSpeechConfigPO oldConfig, BotSpeechConfigPO newConfig, Long currentUserId) {
        List<String> detailList = Lists.newArrayList();
        if (!Objects.equals(oldConfig.getUserSilenceThreshold(), newConfig.getUserSilenceThreshold())) {
            detailList.add(String.format("客户无应答时长设置【%s】修改为【%s】", oldConfig.getUserSilenceThreshold(), newConfig.getUserSilenceThreshold()));
        }
        if (!Objects.equals(oldConfig.getEnableToneInterrupt(), newConfig.getEnableToneInterrupt())) {
            detailList.add(String.format("%s语气词过滤", BooleanUtils.isTrue(newConfig.getEnableToneInterrupt()) ? "开启" : "关闭"));
        }
        if (BooleanUtils.isTrue(oldConfig.getEnableToneInterrupt()) && BooleanUtils.isTrue(newConfig.getEnableToneInterrupt()) &&
                !Objects.equals(oldConfig.getToneInterruptPercent(), newConfig.getToneInterruptPercent())) {
            detailList.add(String.format("语气词过滤：调整音频播报进度【%s】，修改为【%s】", oldConfig.getToneInterruptPercent(), newConfig.getToneInterruptPercent()));
        }
        List<String> oldToneWordList = Optional.ofNullable(oldConfig.getToneWordList()).orElse(Collections.emptyList());
        List<String> newToneWordList = Optional.ofNullable(newConfig.getToneWordList()).orElse(Collections.emptyList());
        if (!CollectionUtils.isEqualCollection(oldToneWordList, newToneWordList)) {
            Collection<String> deletedList = CollectionUtils.subtract(oldToneWordList, newToneWordList);
            Collection<String> addList = CollectionUtils.subtract(newToneWordList, oldToneWordList);
            if (CollectionUtils.isNotEmpty(addList)) {
                detailList.add(String.format("语气词过滤：新增语气词%s", addList.stream().map(s -> "【" + s + "】").collect(Collectors.joining("、"))));
            }
            if (CollectionUtils.isNotEmpty(deletedList)) {
                detailList.add(String.format("语气词过滤：删除语气词%s", deletedList.stream().map(s -> "【" + s + "】").collect(Collectors.joining("、"))));
            }
        }

        boolean oldEnableInputMerge = BooleanUtils.isNotFalse(oldConfig.getEnableInputMerge());
        boolean newEnableInputMerge = BooleanUtils.isNotFalse(newConfig.getEnableInputMerge());
        if (oldEnableInputMerge != newEnableInputMerge) {
            detailList.add(String.format("%s断句补齐设置", newEnableInputMerge ? "开启" : "关闭"));
        }

        LlmGuideAnswerConfigEnum oldGuideAnswerConfig = oldConfig.getLlmGuideAnswerConfig();
        LlmGuideAnswerConfigEnum newGuideAnswerConfig = newConfig.getLlmGuideAnswerConfig();
        if (!Objects.equals(oldGuideAnswerConfig, newGuideAnswerConfig)) {
            detailList.add(String.format("大模型设置: %s 更新为 %s ", oldGuideAnswerConfig.getDesc(), newGuideAnswerConfig.getDesc()));
        }

        if (BooleanUtils.isTrue(oldConfig.getDisablePromptAudioCheck())
                != BooleanUtils.isTrue(newConfig.getDisablePromptAudioCheck())) {
            Function<Boolean, String> conv = (disable) -> BooleanUtils.isTrue(disable) ? "开启" : "关闭";
            detailList.add(String.format("禁用虚拟号提示音检测: %s 更新为 %s ",
                    conv.apply(oldConfig.getDisablePromptAudioCheck()),
                    conv.apply(newConfig.getDisablePromptAudioCheck())));
        }

        operationLogService.save(newConfig.getBotId(), OperationLogTypeEnum.BOT_CONFIG, OperationLogResourceTypeEnum.VOICE_CONFIG, detailList, currentUserId);
    }

    @Override
    public List<Long> queryBotIdListByRecordUserId(Long callRecordId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("audioConfig.audioType").in(Arrays.asList(AudioTypeEnum.MAN_MADE, AudioTypeEnum.MIXTURE)));
        query.addCriteria(Criteria.where("audioConfig.recordUserId").is(callRecordId));
        // todo 仅查询botId字段
        List<BotConfigPO> configList = mongoTemplate.find(query, BotConfigPO.class, BotConfigPO.COLLECTION_NAME);
        return configList.stream()
                .map(BotConfigPO::getBotId)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<BotConfigPO> getBotConfigListByBotId(List<Long> botIdList) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(botIdList));
        return mongoTemplate.find(query, BotConfigPO.class, BotConfigPO.COLLECTION_NAME);
    }

    @Override
    public BotSyncResultVO sync(BasicSyncVO syncVO) {
        Long srcBotId = syncVO.getSrcBotId();
        List<Long> targetBotIdList = syncVO.getTargetBotIdList();
        Assert.notNull(srcBotId, "源BotId不能为空");
        Assert.notNull(targetBotIdList, "目标botId不能为空");
        BotSpeechConfigPO srcBotSpeechConfig = getSpeechConfig(srcBotId);
        AtomicInteger successNum = new AtomicInteger();
        AtomicInteger failNum = new AtomicInteger();
        List<String> failBotNameList = new ArrayList<>();
        List<BotPO> targetBotVOList = botService.getByIdList(targetBotIdList);
        Map<Long, String> targetBotIdToNameMap = targetBotVOList.stream().collect(Collectors.toMap(BotPO::getBotId, BotPO::getName));

        List<Long> successBotIdList = new ArrayList<>();
        List<Long> failBotIdList = new ArrayList<>();

        targetBotIdList.forEach(targetBotId -> {
            try {
                BotConfigSyncDTO botConfigSyncDTO = BotConfigSyncDTO
                        .builder()
                        .targetBotId(targetBotId)
                        .sourceBotSpeechConfig(srcBotSpeechConfig)
                        .currentUserId(syncVO.getCurrentUserId())
                        .syncVO(syncVO)
                        .build();
                singleSync(botConfigSyncDTO);
                successNum.incrementAndGet();
                successBotIdList.add(targetBotId);
            } catch (Exception e) {
                log.error("同步语音设置到botId:{}失败:{}", targetBotId, e);
                failNum.incrementAndGet();
                failBotNameList.add(targetBotIdToNameMap.get(targetBotId));
                failBotIdList.add(targetBotId);
            }
        });

        botSyncOperationLogService.speechConfigSync(syncVO, successBotIdList, failBotIdList);
        return BotSyncResultVO.builder().successNum(successNum.intValue()).failNum(failNum.intValue()).failBotNameList(failBotNameList).build();
    }

    @Override
    public List<BotConfigPO> queryByBotIdList(List<Long> botIdList) {
        if (CollectionUtils.isEmpty(botIdList)) {
            return Collections.emptyList();
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(botIdList));
        return mongoTemplate.find(query, BotConfigPO.class, BotConfigPO.COLLECTION_NAME);
    }

    private void singleSync(BotConfigSyncDTO botConfigSyncDTO) {
        Long targetBotId = botConfigSyncDTO.getTargetBotId();
        BotSpeechConfigPO targetBotSpeechConfig = getSpeechConfig(targetBotId);
        BotSpeechConfigPO sourceBotSpeechConfig = botConfigSyncDTO.getSourceBotSpeechConfig();
        BasicSyncVO syncVO = botConfigSyncDTO.getSyncVO();
        Set<SyncScopeEnum> syncScopeEnumSet = syncVO.getSyncScopeEnumSet();
        if (CollectionUtils.isNotEmpty(syncScopeEnumSet)) {
            //无应答时长
            if (syncScopeEnumSet.contains(SyncScopeEnum.USER_SILENCE)) {
                targetBotSpeechConfig.setUserSilenceThreshold(sourceBotSpeechConfig.getUserSilenceThreshold());
            }

            //语气词过滤
            if (syncScopeEnumSet.contains(SyncScopeEnum.MODAL_PARTICLE)) {
                targetBotSpeechConfig.setEnableToneInterrupt(sourceBotSpeechConfig.getEnableToneInterrupt());
                targetBotSpeechConfig.setToneWordList(sourceBotSpeechConfig.getToneWordList());
                targetBotSpeechConfig.setToneInterruptPercent(sourceBotSpeechConfig.getToneInterruptPercent());
            }

            // 断句补齐
            if (syncScopeEnumSet.contains(SyncScopeEnum.INPUT_MERGE)) {
                targetBotSpeechConfig.setEnableInputMerge(BooleanUtils.isNotFalse(sourceBotSpeechConfig.getEnableInputMerge()));
            }
            targetBotSpeechConfig.setDisablePromptAudioCheck(BooleanUtils.isTrue(sourceBotSpeechConfig.getDisablePromptAudioCheck()));
            targetBotSpeechConfig.setLlmGuideAnswerConfig(sourceBotSpeechConfig.getLlmGuideAnswerConfig());
            saveSpeechConfig(targetBotId, targetBotSpeechConfig, botConfigSyncDTO.getCurrentUserId());
        }

    }

    private BotConfigPO getConfig(Long botId) {
        Query query = getQuery(botId);
        BotConfigPO result = mongoTemplate.findOne(query, BotConfigPO.class, BotConfigPO.COLLECTION_NAME);
        if (Objects.isNull(result)) {
            initOnCreateBot(botId);
            result = mongoTemplate.findOne(query, BotConfigPO.class, BotConfigPO.COLLECTION_NAME);
        } else {
            if (Objects.isNull(result.getAudioConfig())) {
                result.setAudioConfig(generateDefaultAudioConfig(botId));
            }
            if (Objects.isNull(result.getSpeechConfig())) {
                result.setSpeechConfig(generateDefaultSpeechConfig(botId));
            }
            if (CollectionUtils.isEmpty(result.getAudioConfig().getBackgroundList())) {
                initBackground(result.getAudioConfig());
                saveSubConfig(botId, "audioConfig", result.getAudioConfig());
            }
        }

        return result;
    }

    private BotAudioConfigPO mergeNotNullProperties(BotAudioConfigPO src, BotAudioConfigPO dest) {
        BotAudioConfigPO result = MyBeanUtils.copy(dest, BotAudioConfigPO.class);
        MyBeanUtils.copyNonNullProperties(src, result);
        return result;
    }

    private TtsVoiceConfigPO getTtsConfig(BotAudioConfigPO botAudioConfig) {
        if (Objects.isNull(botAudioConfig) || Objects.isNull(botAudioConfig.getAudioType())) {
            return null;
        }
        if (AudioTypeEnum.COMPOSE.equals(botAudioConfig.getAudioType())) {
            return botAudioConfig.getTtsConfig();
        } else {
            return botAudioConfig.getSpliceTtsConfig();
        }
    }

    private boolean isTtsConfigChanged(BotAudioConfigPO oldConfig, BotAudioConfigPO newConfig) {
        TtsVoiceConfigPO newTtsConfig = getTtsConfig(newConfig);
        TtsVoiceConfigPO oldTtsConfig = getTtsConfig(oldConfig);

        if (Objects.isNull(newTtsConfig) || Objects.isNull(oldTtsConfig)) {
            return true;
        }

        return !Objects.equals(newTtsConfig.getTtsVolume(), oldTtsConfig.getTtsVolume())
                || !Objects.equals(newTtsConfig.getTtsSpeed(), oldTtsConfig.getTtsSpeed())
                || !StringUtils.equals(newTtsConfig.getTtsVoice(), oldTtsConfig.getTtsVoice());
    }

    private void adjustBackgroundVolume(BotAudioConfigPO oldConfig, BotAudioConfigPO newConfig) {
        if (BooleanUtils.isNotTrue(newConfig.getEnableBackground())) {
            return;
        }
        if (CollectionUtils.isEmpty(newConfig.getBackgroundList())) {
            return;
        }

        newConfig.getBackgroundList().forEach(background -> {
            if (needAdjustVolume(oldConfig, background) && Objects.nonNull(background.getVolume())) {
                log.info("背景音配置需要调整音量, {}", background);
                // 办公室谈话声保留了原始音频，每次调节音量都是从原始音频进行调节
                if (BooleanUtils.isTrue(background.getIsBuiltIn())) {
                    background.setUrl(copyBackgroundAudioKey(newConfig.getBotId(), background.getOriginUrl()));
                } else {
                    background.setUrl(copyBackgroundAudioKey(newConfig.getBotId(), background.getUrl()));
                }
                log.info("调整音量前需复制出一份音频, {}", background);
                audioUploadService.adjustVolume(oldConfig.getBotId(), background.getUrl(), background.getVolume());
            }
        });
    }


    private boolean needAdjustVolume(BotAudioConfigPO oldConfig, BackgroundAudioConfigPO backgroundConfig) {
        if (Objects.isNull(backgroundConfig) || BooleanUtils.isNotTrue(backgroundConfig.getEnable())) {
            return false;
        }
        Optional<BackgroundAudioConfigPO> oldBackgroundConfig = Optional.empty();
        if (CollectionUtils.isNotEmpty(oldConfig.getBackgroundList())) {
            oldBackgroundConfig = oldConfig.getBackgroundList().stream()
                    .filter(item -> StringUtils.equals(backgroundConfig.getSource(), item.getSource()))
                    .findAny();
        }

        return oldBackgroundConfig.map(old -> {
            if (!StringUtils.equals(old.getUrl(), backgroundConfig.getUrl())) {
                return true;
            }
            return !Objects.equals(old.getVolume(), backgroundConfig.getVolume());
        }).orElse(true);
    }


    private void saveSubConfig(Long botId, String fieldName, Object config) {
        Query query = getQuery(botId);
        Update update = new Update();
        update.set(fieldName, config);
        mongoTemplate.updateFirst(query, update, BotConfigPO.COLLECTION_NAME);
        botService.updateAuditStatus(botId, AuditStatusEnum.DRAFT);
    }

    private Query getQuery(Long botId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(botId));
        return query;
    }

    private BotSpeechConfigPO generateDefaultSpeechConfig(Long botId) {
        BotSpeechConfigPO config = new BotSpeechConfigPO();
        config.setBotId(botId);
        config.setUserSilenceThreshold(7.0);
        return config;
    }

    private BotAudioConfigPO generateDefaultAudioConfig(Long botId) {
        BotAudioConfigPO config = new BotAudioConfigPO();
        config.setBotId(botId);
        config.setAudioType(AudioTypeEnum.MAN_MADE);

        TtsVoiceConfigPO ttsConfig = new TtsVoiceConfigPO();
        ttsConfig.setTtsSpeed(5.0f);
        ttsConfig.setTtsVolume(70.0f);
        ttsConfig.setTtsVoice(TtsVoiceEnum.WOMEN_ZHIYI.name());
        ttsConfig.setTtsVoiceName(TtsVoiceEnum.WOMEN_ZHIYI.getDesc());
        config.setTtsConfig(ttsConfig);

        TtsVoiceConfigPO spliceTtsConfig = new TtsVoiceConfigPO();
        spliceTtsConfig.setTtsSpeed(5.0f);
        spliceTtsConfig.setTtsVolume(70.0f);
        config.setSpliceTtsConfig(spliceTtsConfig);

        initBackground(config);
        return config;
    }

    private void initBackground(BotAudioConfigPO config) {
        if (CollectionUtils.isNotEmpty(config.getBackgroundList())) {
            return;
        }
        BackgroundAudioConfigPO po = new BackgroundAudioConfigPO();
        po.setEnable(false);
        po.setSource("办公室谈话声");
        po.setVolume(70);
        po.setIsBuiltIn(true);

        //下载上传
        String fileKey = objectStorageHelper.getKeyFromUrl(ApplicationConstant.BOT_BACKGROUND_SOUND_URL);
        String audioUrl = OssKeyCenter.getBotUploadAudioPath(config.getBotId(), String.format("%s/办公室谈话声.wav", System.currentTimeMillis()));
        String backUrl = objectStorageHelper.copyObject(fileKey, audioUrl);
        po.setUrl(audioUrl);
        po.setOriginUrl(fileKey);
        config.setEnableBackground(false);
        config.setBackgroundList(Collections.singletonList(po));
    }

    private String copyBackgroundAudioKey(Long botId, String originUrl) {
        String fileKey = objectStorageHelper.getKeyFromUrl(originUrl);
        String fileName = fileKey.substring(fileKey.lastIndexOf("/") + 1);
        String newKey = OssKeyCenter.getBotUploadAudioPath(botId, String.format("%s/%s", System.currentTimeMillis(), fileName));
        objectStorageHelper.copyObject(fileKey, newKey);
        return newKey;
    }

    @Override
    public void saveToSnapshot(RobotResourceContext context) {
        context.getSnapshot().setBotConfig(getConfig(context.getSrcBotId()));
    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {
        if (!context.isCopy()) {
            return;
        }
        BotConfigPO config = context.getSnapshot().getBotConfig();
        if (Objects.nonNull(config)) {
            config.setBotId(context.getTargetBotId());
            config.getAudioConfig().setBotId(context.getTargetBotId());
            config.getSpeechConfig().setBotId(context.getTargetBotId());

            // 处理背景音的复制
            copyBackgroundAudio(config.getAudioConfig(), context.getSrcBotId(), context.getTargetBotId());
            mongoTemplate.insert(config, BotConfigPO.COLLECTION_NAME);
        }
    }

    private void copyBackgroundAudio(BotAudioConfigPO audioConfig, long srcBotId, Long targetBotId) {
        if (CollectionUtils.isEmpty(audioConfig.getBackgroundList())) {
            initBackground(audioConfig);
        } else {
            audioConfig.getBackgroundList().forEach(background -> {
                if (BooleanUtils.isTrue(background.getIsBuiltIn())) {
                    // 复制时脏数据处理
                    String fileKey = objectStorageHelper.getKeyFromUrl(ApplicationConstant.BOT_BACKGROUND_SOUND_URL);
                    background.setOriginUrl(fileKey);
                }
            });
        }
    }

    private String copyAudioUrl(String url, long srcBotId, Long targetBotId) {
        if (StringUtils.isBlank(url)) {
            return url;
        }

        String dstRelativeUrl = url.replace(String.format("/%s/", srcBotId), String.format("/%s/", targetBotId));
        objectStorageHelper.copyObject(url, dstRelativeUrl);
        log.info("copy audio file, fromUrl={}, fromBotId={}, toUrl={} toBotId={}", url, srcBotId, dstRelativeUrl, targetBotId);
        return dstRelativeUrl;
    }


    @Override
    public void validateResource(RobotResourceContext context) {
        RobotSnapshotPO snapshot = context.getSnapshot();
        if (BooleanUtils.isTrue(snapshot.getBotConfig().getSpeechConfig().getEnableToneInterrupt())
                && CollectionUtils.isEmpty(snapshot.getBotConfig().getSpeechConfig().getToneWordList())) {
            SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                    .builder()
                    .resourceType(BotResourceTypeEnum.SPEECH_CONFIG)
                    .failMsg("开启语气词过滤时语气词不能为空")
                    .build();
            context.getInvalidMsgList().add(msg);
        }
        validTtsConfig(context, snapshot);
        validAudioRecorder(context, snapshot);
    }

    private void validAudioRecorder(RobotResourceContext context, RobotSnapshotPO snapshot) {
        if (BooleanUtils.isNotTrue(context.getValidateConfig().getRequireValidAudio())
                || AudioTypeEnum.COMPOSE.equals(snapshot.getBotConfig().getAudioConfig().getAudioType())) {
            return;
        }
        if (Objects.isNull(snapshot.getBotConfig().getAudioConfig().getRecordUserId())) {
            SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                    .builder()
                    .resourceType(BotResourceTypeEnum.AUDIO_CONFIG)
                    .failMsg("当前话术未绑定录音师")
                    .isWarning(true)
                    .build();
            context.getInvalidMsgList().add(msg);
        }

        // 获取录音师信息

        Optional<UserPO> userInfo = userService.getUserByIdIfPresent(snapshot.getBotConfig().getAudioConfig().getRecordUserId());
        if (!userInfo.isPresent()) {
            SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                    .builder()
                    .resourceType(BotResourceTypeEnum.AUDIO_CONFIG)
                    .failMsg("录音师不存在")
                    .isWarning(true)
                    .build();
            context.getInvalidMsgList().add(msg);
            return;
        }

        boolean useVariable = checkUsingVariable(snapshot);
        boolean enableLLMChat = specialAnswerConfigService.checkEnableLLMChat(snapshot.getSpecialAnswerConfigList());
        boolean useLlmStep = checkUsingLlmStep(snapshot);


        BotAudioConfigPO audioConfig = snapshot.getBotConfig().getAudioConfig();
        com.yiwise.middleware.tts.enums.TtsVoiceEnum ttsVoiceEnum = null;
        if (Objects.nonNull(audioConfig.getSpliceTtsConfig())
                && StringUtils.isNotBlank(audioConfig.getSpliceTtsConfig().getTtsVoice())) {
            try {
                ttsVoiceEnum = com.yiwise.middleware.tts.enums.TtsVoiceEnum.valueOf(audioConfig.getSpliceTtsConfig().getTtsVoice());
            } catch (Exception e) {
                log.warn("tts音色不存在, ttsVoice={}", audioConfig.getSpliceTtsConfig().getTtsVoice(), e);
                SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                        .builder()
                        .resourceType(BotResourceTypeEnum.AUDIO_CONFIG)
                        .failMsg("音色不存在" + audioConfig.getSpliceTtsConfig().getTtsVoice())
                        .build();
                context.getInvalidMsgList().add(msg);
            }
        }
        // 校验录音师的性别和tts音色的性别是否一致
        boolean ttsIsWomen = Objects.nonNull(ttsVoiceEnum) && ttsVoiceEnum.isFemale();
        boolean recorderIsWomen = "FEMALE".equals(userInfo.get().getGender());
        // 启用变量或启用大模型对话, 都需要设置 tts 音色
        if (useVariable || enableLLMChat || useLlmStep) {
            if (Objects.isNull(audioConfig.getSpliceTtsConfig())
                    || StringUtils.isBlank(audioConfig.getSpliceTtsConfig().getTtsVoice())) {
                SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                        .builder()
                        .resourceType(BotResourceTypeEnum.AUDIO_CONFIG)
                        .failMsg("未配置TTS音色，请检查")
                        .build();
                context.getInvalidMsgList().add(msg);
            } else if (ttsIsWomen != recorderIsWomen) {
                SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                        .builder()
                        .resourceType(BotResourceTypeEnum.AUDIO_CONFIG)
                        .failMsg("TTS音色和录音师性别不符，请检查")
                        .build();
                context.getInvalidMsgList().add(msg);
            }
        } else {
            if (Objects.nonNull(audioConfig.getSpliceTtsConfig())
                    && StringUtils.isNotBlank(audioConfig.getSpliceTtsConfig().getTtsVoice())) {
                SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                        .builder()
                        .resourceType(BotResourceTypeEnum.AUDIO_CONFIG)
                        .failMsg("当前话术内容无变量, 请清空配置的TTS音色")
                        .isWarning(true)
                        .build();
                context.getInvalidMsgList().add(msg);
            }
        }
    }

    private static boolean checkUsingLlmStep(RobotSnapshotPO snapshot) {
        List<StepPO> stepList = snapshot.getStepList();
        return CollectionUtils.isNotEmpty(stepList) && stepList.stream().anyMatch(StepPO::isLlmStep);
    }

    private static boolean checkUsingVariable(RobotSnapshotPO snapshot) {
        // 判断答案中是否使用变量
        List<BaseAnswerContent> allAnswerList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(snapshot.getKnowledgeList())) {
            allAnswerList.addAll(snapshot.getKnowledgeList().stream().map(KnowledgePO::getAnswerList)
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(snapshot.getNodeList())) {
            allAnswerList.addAll(snapshot.getNodeList().stream().map(DialogBaseNodePO::getAnswerList)
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(snapshot.getSpecialAnswerConfigList())) {
            allAnswerList.addAll(snapshot.getSpecialAnswerConfigList().stream().map(SpecialAnswerConfigPO::getAnswerList)
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList()));
        }

        return allAnswerList.stream()
                .anyMatch(answer -> {
                    if (StringUtils.isNotBlank(answer.getText())) {
                        AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(answer.getText(), false);
                        return CollectionUtils.isNotEmpty(splitter.getPlaceholderList());
                    }
                    return false;
                });
    }

    private static void validTtsConfig(RobotResourceContext context, RobotSnapshotPO snapshot) {
        if (BooleanUtils.isTrue(context.getValidateConfig().getRequireValidAudio())) {
            BotAudioConfigPO audioConfig = snapshot.getBotConfig().getAudioConfig();
            if (AudioTypeEnum.COMPOSE.equals(audioConfig.getAudioType())) {
                TtsVoiceConfigPO ttsConfig = audioConfig.getTtsConfig();
                if (Objects.isNull(ttsConfig)
                        || StringUtils.isBlank(ttsConfig.getTtsVoice())
                        || Objects.isNull(ttsConfig.getTtsSpeed())
                        || Objects.isNull(ttsConfig.getTtsVolume())) {
                    SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                            .builder()
                            .resourceType(BotResourceTypeEnum.AUDIO_CONFIG)
                            .failMsg("合成音色配置不完整")
                            .build();
                    context.getInvalidMsgList().add(msg);
                }
            } else {
                TtsVoiceConfigPO ttsConfig = audioConfig.getSpliceTtsConfig();
                // 保存的时候, 仅在设置了音色的情况下才校验完整性, 整个配置的完整性, 在发布审核的时候, 根据是否答案中包含变量来判断
                if (Objects.nonNull(ttsConfig) && StringUtils.isNotBlank(ttsConfig.getTtsVoice())) {
                    if (Objects.isNull(ttsConfig.getTtsSpeed()) || Objects.isNull(ttsConfig.getTtsVolume())) {
                        SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                                .builder()
                                .resourceType(BotResourceTypeEnum.AUDIO_CONFIG)
                                .failMsg("拼接音色配置不完整")
                                .build();
                        context.getInvalidMsgList().add(msg);
                    }
                }
            }
        }
    }

    @Override
    public void batchUpdateRecordUserId() {
        MatchOperation match = Aggregation.match(Criteria.where("audioConfig.audioType").ne("COMPOSE").and("audioConfig.recordUserId").ne(null));
        GroupOperation group = Aggregation.group("audioConfig.botId")
                .first("audioConfig.botId").as("botId")
                .first("audioConfig.recordUserId").as("recordUserId");

        Aggregation aggregation = Aggregation.newAggregation(match, group);
        for (BotIdRecordUserId botIdRecordUserId : mongoTemplate.aggregate(aggregation, BotConfigPO.COLLECTION_NAME, BotIdRecordUserId.class).getMappedResults()) {
            log.info("botId: {}, recordUserId: {}", botIdRecordUserId.getBotId(), botIdRecordUserId.getRecordUserId());
            botPOMapper.updateRecordUserId(botIdRecordUserId.getBotId(), botIdRecordUserId.getRecordUserId());
        }
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    private static class BotIdRecordUserId implements Serializable {

        Long botId;

        Long recordUserId;
    }
}
