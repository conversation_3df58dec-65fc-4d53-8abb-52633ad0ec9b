package com.yiwise.dialogflow.service.impl;

import com.google.common.collect.Maps;
import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.customer.data.platform.rpc.api.service.dto.CustomerAttributeMetaDataDTO;
import com.yiwise.dialogflow.api.dto.response.variable.VariableInfoDTO;
import com.yiwise.dialogflow.api.enums.V3BotTypeEnum;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.entity.bo.SnapshotInvalidFailItemMsg;
import com.yiwise.dialogflow.entity.bo.SourceRefBO;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.dto.VariableLogDTO;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.magic.MagicActivityConfigPO;
import com.yiwise.dialogflow.entity.query.RobotSnapshotQuery;
import com.yiwise.dialogflow.entity.query.VariableQuery;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.UserVariableNameResult;
import com.yiwise.dialogflow.entity.vo.VariableBindInfoVO;
import com.yiwise.dialogflow.entity.vo.VariableVO;
import com.yiwise.dialogflow.entity.vo.sync.VariableSyncRequestVO;
import com.yiwise.dialogflow.entity.vo.sync.VariableSyncResultVO;
import com.yiwise.dialogflow.exceptions.VarNameRepeatException;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.intent.IntentCorpusService;
import com.yiwise.dialogflow.service.intent.IntentService;
import com.yiwise.dialogflow.service.llm.LlmStepConfigService;
import com.yiwise.dialogflow.service.magic.MagicActivityConfigService;
import com.yiwise.dialogflow.service.operationlog.VariableOperationLogService;
import com.yiwise.dialogflow.service.remote.customer.CustomerAttributeService;
import com.yiwise.dialogflow.utils.AnswerTextUtils;
import com.yiwise.middleware.tts.enums.VarInterpretTypeEnum;
import javaslang.Tuple;
import javaslang.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class VariableServiceImpl implements VariableService, RobotResourceService {


    private static final Map<String, String> DEFAULT_VARIABLE_NAME_DESC_MAP = new LinkedHashMap<>();
    public static final Map<String, String> DEFAULT_SYSTEM_VARIABLE_NAME_DESC_MAP = new LinkedHashMap<>();

    static {
        DEFAULT_SYSTEM_VARIABLE_NAME_DESC_MAP.put("任务id", "内置变量，系统自动填入当前正在外呼的客户所在任务id作为变量值;");
        DEFAULT_SYSTEM_VARIABLE_NAME_DESC_MAP.put("任务描述", "内置变量，系统自动填入当前正在外呼的客户所在任务描述作为变量值;");
        DEFAULT_VARIABLE_NAME_DESC_MAP.put("姓名", "将用户基本信息中“姓名”作为变量被话术调用。");
        DEFAULT_VARIABLE_NAME_DESC_MAP.put("联系电话", "将用户基本信息中的“联系电话”作为变量被话术调用。");
        DEFAULT_VARIABLE_NAME_DESC_MAP.put("性别", "将用户基本信息中的“性别”作为变量被话术调用。话术内调用该变量时，当上传性别为”男“时，“张三{性别}“播报为“张三先生“。");
        DEFAULT_VARIABLE_NAME_DESC_MAP.put("姓名_总", "使用该变量，需在录音小程序对<百家姓录音>进行完整的录音。话术调用该变量，当上传的姓名中的姓氏不存在对应的录音，则会使用“哎”作为称呼。如“张三”播报为“张总“，若无“张总”录音，则播报为“哎”。");
    }

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private BotRefService botRefService;

    @Lazy
    @Resource
    private AnswerManagerService answerManagerService;

    @Resource
    private SourceRefService sourceRefService;

    @Resource
    private BotService botService;

    @Lazy
    @Resource
    private RobotSnapshotService robotSnapshotService;

    @Resource
    private CustomerAttributeService customerAttributeService;

    @Resource
    private VariableOperationLogService variableOperationLogService;

    @Resource
    private MagicActivityConfigService magicActivityConfigService;


    @Lazy
    @Resource
    private StepNodeService stepNodeService;

    @Lazy
    @Resource
    private IntentCorpusService intentCorpusService;

    @Lazy
    @Resource
    private LlmStepConfigService llmStepConfigService;


    private static final Pattern VAR_NAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_\\u4e00-\\u9fa5]+$");

    @Override
    public List<VariablePO> recoverySystemVariable(Long botId) {
        deleteSystemVariable(botId);
        return initOnCreateBot(botId);
    }

    private void deleteSystemVariable(Long botId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("type").is(VariableTypeEnum.SYSTEM.name()));
        mongoTemplate.remove(query, VariablePO.COLLECTION_NAME);
    }

    @Override
    public List<VariablePO> initOnCreateBot(Long botId) {
        List<VariablePO> defaultList = generateDefault(botId);
        mongoTemplate.insert(defaultList, VariablePO.COLLECTION_NAME);
        return defaultList;
    }

    @Override
    public PageResultObject<VariableVO> getPageByBotId(VariableQuery variableQuery) {
        if (variableQuery.getBotId() == null) {
            return null;
        }
        Query query = new Query();
        query.with(Sort.by(Sort.Direction.ASC, "createTime"));
        query.addCriteria(Criteria.where("botId").is(variableQuery.getBotId()));
        if (CollectionUtils.isNotEmpty(variableQuery.getTypeList())) {
            query.addCriteria(Criteria.where("type").in(variableQuery.getTypeList()));
        }
        if (StringUtils.isNotBlank(variableQuery.getSearch())) {
            // 这里需要搜索 name, desc, defaultValue, prompt, templateSentence 字段
            // 创建基础搜索条件（所有类型都需要搜索的字段）
            String searchKeyword = variableQuery.getSearch().trim();
            String regexPattern = ".*" + Pattern.quote(searchKeyword) + ".*";
            List<Criteria> searchCriteriaList = new ArrayList<>();
            searchCriteriaList.add(Criteria.where("name").regex(regexPattern));
            searchCriteriaList.add(Criteria.where("desc").regex(regexPattern));
            searchCriteriaList.add(Criteria.where("defaultValue").regex(regexPattern));

            // 构建最终的搜索条件
            Criteria finalCriteria = new Criteria().orOperator(
                    // 通用字段搜索
                    new Criteria().orOperator(searchCriteriaList.toArray(new Criteria[0])),
                    // 模板提示词
                    new Criteria().andOperator(
                            Criteria.where("type").is(VariableTypeEnum.TEMPLATE),
                            Criteria.where("templateVariableConfigType").is(TemplateVariableConfigTypeEnum.PROMPT),
                            new Criteria().orOperator(
                                    Criteria.where("prompt").regex(regexPattern),
                                    Criteria.where("templateSentence").regex(regexPattern)
                            )
                    ),
                    // 模板默认值
                    new Criteria().andOperator(
                            Criteria.where("type").is(VariableTypeEnum.TEMPLATE),
                            Criteria.where("templateVariableConfigType").is(TemplateVariableConfigTypeEnum.DEFAULT_VALUE),
                            Criteria.where("defaultValue").regex(regexPattern)
                    )
            );
            query.addCriteria(finalCriteria);
        }
        long count = mongoTemplate.count(query, VariablePO.COLLECTION_NAME);
        query.skip((long) (variableQuery.getPageNum() - 1) * variableQuery.getPageSize());
        query.limit(variableQuery.getPageSize());
        List<VariablePO> variablePOList = mongoTemplate.find(query, VariablePO.class, VariablePO.COLLECTION_NAME);
        //获取所有变量的引用路径
        List<SourceRefPO> sourceRefList = sourceRefService.getListBySourceType(variableQuery.getBotId(), SourceTypeEnum.VARIABLE);
        Map<String, List<String>> sourceRefPOMap = sourceRefList.stream()
                .collect(Collectors.toMap(SourceRefPO::getSourceId, source -> Arrays.stream(StringUtils.split(source.getRefLabel(), ","))
                        .filter(StringUtils::isNotEmpty).collect(toList()), (List<String> oldList, List<String> newList) -> {
                    oldList.addAll(newList);
                    return oldList;
                }));

        // 获取所有动态变量保存的客户属性id
        List<Long> customerAttributeIdList = variablePOList.stream().filter(p -> VariableTypeEnum.DYNAMIC.equals(p.getType()))
                .map(VariablePO::getCustomerAttributeId).filter(Objects::nonNull).distinct().collect(toList());
        Long tenantId = botRefService.getTenantIdByBotId(variableQuery.getBotId());
        // <客户属性id,name>
        Map<Long, String> customerAttributeIdNameMap = MyCollectionUtils.listToConvertMap(customerAttributeService.getCustomerAttributeByIdList(tenantId, customerAttributeIdList),
                CustomerAttributeMetaDataDTO::getMcCustomerAttributeMetaDataId, CustomerAttributeMetaDataDTO::getAttributeName);

        List<VariableVO> variableVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(variablePOList)) {
            variableVOList = variablePOList.stream()
                    .map(po -> {
                        VariableVO variableVO = new VariableVO();
                        BeanUtils.copyProperties(po, variableVO);
                        variableVO.setRefLabelList(sourceRefPOMap.getOrDefault(po.getId(), null));
                        Optional.ofNullable(variableVO.getCustomerAttributeId()).map(customerAttributeIdNameMap::get)
                                .ifPresent(variableVO::setCustomerAttributeName);
                        return variableVO;
                    }).collect(toList());
        }
        return PageResultObject.of(variableVOList,
                variableQuery.getPageNum(), variableQuery.getPageSize(), (int) count);
    }

    @Override
    public List<VariablePO> getListByBotId(Long botId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId));
        return mongoTemplate.find(query, VariablePO.class, VariablePO.COLLECTION_NAME);
    }

    @Override
    public List<VariablePO> getByIdList(Collection<String> variableIdList) {
        if (CollectionUtils.isEmpty(variableIdList)) {
            return Collections.emptyList();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(variableIdList));
        return mongoTemplate.find(query, VariablePO.class, VariablePO.COLLECTION_NAME);
    }

    @Override
    public Map<String, String> getNameIdMapByBotId(Long botId) {
        return MyCollectionUtils.listToConvertMap(getListByBotId(botId), VariablePO::getName, VariablePO::getId);
    }

    @Override
    public VariablePO update(VariablePO variable, Long userId) {
        return doUpdate(variable, userId, true);
    }

    private VariablePO doUpdate(VariablePO variable, Long userId, boolean createOpLog) {
        checkCanExecuteOperation(variable.getBotId(), variable.getType());
        VariablePO old = getById(variable.getBotId(), variable.getId());
        if (VariableTypeEnum.SYSTEM.equals(old.getType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "系统内置变量不能编辑");
        }
        variable.setCreateUserId(userId);
        variable.setUpdateUserId(userId);
        //校验变量修改的名称是否会与其他变量重名
        validVariable(variable, false, true);
        variable.setUpdateTime(LocalDateTime.now());
        variable.setCreateTime(old.getCreateTime());

        if (VariableTypeEnum.isTemplateVariable(old.getType())
                || VariableTypeEnum.isTemplateVariable(variable.getType())) {
            processDependVar(variable, createOpLog);
        }

        mongoTemplate.save(variable, VariablePO.COLLECTION_NAME);
        VariableLogDTO variableLogDTO = new VariableLogDTO();
        BeanUtils.copyProperties(old, variableLogDTO);
        variableLogDTO.setUpdateUserId(userId);

        if (createOpLog) {
            variableOperationLogService.updateVar(old, variable);
        }

        if (!StringUtils.equals(old.getName(), variable.getName())) {
            // 查询所有的依赖, 然后更新其中的变量名称
            // 这里变量改名的操作应该是低频的, 所以在改名的时候, 把所有的依赖进行更新, 没有采用所有资源在被查询出来的时候检查是否变量名称变更了
            updateDependVariableName(variable.getBotId(), variable.getId(), old.getName(), variable.getName(), userId);
        }
        botService.updateAuditStatus(variable.getBotId(), AuditStatusEnum.DRAFT);
        return variable;
    }

    private void validVariable(VariablePO variable, boolean ignoreNameExistsCheck, boolean ignoreCustomerAttributeCheck) {
        // 变量名称仅支持输入中文、英文、数字
        if (!isValidVarName(variable.getName())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "变量名称不可输入特殊符号");
        }
        // 动态变量且开启了保存到客户属性，校验保存模式和客户属性id
        if (!ignoreCustomerAttributeCheck && VariableTypeEnum.isDynamicVariable(variable.getType()) && BooleanUtils.isTrue(variable.getEnableSave())) {
            if (Objects.isNull(variable.getCustomerAttributeId())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "保存字段不能为空");
            }
            if (Objects.isNull(variable.getSaveMode())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "对话中多次采集配置不能为空");
            }
        }
        // 模板变量默认值必填
        if (VariableTypeEnum.isTemplateVariable(variable.getType())) {
            if (TemplateVariableConfigTypeEnum.PROMPT.equals(variable.getTemplateVariableConfigType())) {
                if (StringUtils.isBlank(variable.getPrompt()) && StringUtils.isBlank(variable.getTemplateSentence())) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "提示词和模板句不可同时为空");
                }
            }
        }
        if (!ignoreNameExistsCheck && nameExists(variable)) {
            throw new VarNameRepeatException();
        }
    }

    private boolean nameExists(VariablePO variable) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(variable.getBotId()))
                .addCriteria(Criteria.where("name").is(variable.getName()));
        if (variable.getId() != null) {
            query.addCriteria(Criteria.where("_id").ne(variable.getId()));
        }
        List<VariablePO> variablePOList = mongoTemplate.find(query, VariablePO.class, VariablePO.COLLECTION_NAME);
        return CollectionUtils.isNotEmpty(variablePOList);
    }

    private void updateDependVariableName(Long botId, String variableId, String oldVariableName, String newVariableName, Long userId) {
        answerManagerService.updateAnswerOnVariableRename(botId, variableId, oldVariableName, newVariableName, userId);
        stepNodeService.updateQueryNodeOnVariableRename(botId, variableId, oldVariableName, newVariableName, userId);
        intentCorpusService.updateIntentOnVariableRename(botId, variableId, oldVariableName, newVariableName, userId);
        llmStepConfigService.updateStepOnVariableRename(botId, variableId, oldVariableName, newVariableName, userId);
        updateTemplateVariableOnVariableRename(botId, variableId, oldVariableName, newVariableName, userId);
        sourceRefService.updateRefLabelByRefId(botId, variableId, newVariableName);
    }

    private void updateTemplateVariableOnVariableRename(Long botId, String variableId, String oldVariableName, String newVariableName, Long userId) {
        List<VariablePO> templateVariableList = getTemplateVariableByBotIdList(Collections.singletonList(botId));
        if (CollectionUtils.isEmpty(templateVariableList)) {
            return;
        }

        List<VariablePO> updateList = new ArrayList<>();
        for (VariablePO variable : templateVariableList) {
            boolean updated = false;
            if (StringUtils.isNotBlank(variable.getPrompt())) {
                String newPrompt = AnswerTextUtils.convertTemplateOnVariableRename(variable.getPrompt(), oldVariableName, newVariableName);
                if (!variable.getPrompt().equals(newPrompt)) {
                    variable.setPrompt(newPrompt);
                    updated = true;
                }
            }
            if (StringUtils.isNotBlank(variable.getTemplateSentence())) {
                String newTemplateSentence = AnswerTextUtils.convertTemplateOnVariableRename(variable.getTemplateSentence(), oldVariableName, newVariableName);
                if (!variable.getTemplateSentence().equals(newTemplateSentence)) {
                    variable.setTemplateSentence(newTemplateSentence);
                    updated = true;
                }
            }
            if (updated) {
                updateList.add(variable);
            }
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            updateList.forEach(variable -> {
                mongoTemplate.save(variable, VariablePO.COLLECTION_NAME);
            });
        }
    }

    @Override
    public VariablePO getById(Long botId, String id) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("_id").is(id));
        return mongoTemplate.findOne(query, VariablePO.class, VariablePO.COLLECTION_NAME);
    }

    /**
     * 被场景关联的模板话术，不允许新增、删除、修改模板变量
     */
    private void checkCanExecuteOperation(Long botId, VariableTypeEnum variableType) {
        if (!VariableTypeEnum.isTemplateVariable(variableType)) {
            return;
        }
        List<String> dependentSceneNameList = botService.getDependentSceneNameList(botId);
        if (CollectionUtils.isNotEmpty(dependentSceneNameList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("该话术已关联【%s】场景，请取消关联后修改", String.join(",", dependentSceneNameList)));
        }
    }

    @Override
    public VariablePO create(VariablePO variable) {
        checkCanExecuteOperation(variable.getBotId(), variable.getType());
        validVariable(variable, false, true);
        return doCreate(variable, true);
    }

    @Override
    public boolean isValidVarName(String varName) {
        if (StringUtils.isBlank(varName)) {
            return false;
        }
        return VAR_NAME_PATTERN.matcher(varName).matches();
    }

    @Override
    public Map<String, String> autoCreateIfNotExists(Long botId, List<String> varNameList, Long userId) {
        if (CollectionUtils.isEmpty(varNameList)) {
            return Collections.emptyMap();
        }
        List<VariablePO> existsVariableList = getListByBotId(botId);
        // 已经存在的变量名称集合
        List<String> existsVarNameList = MyCollectionUtils.listToConvertList(existsVariableList, VariablePO::getName);
        // 需要创建的变量集合
        List<String> createVarNameList = varNameList.stream().filter(v -> !existsVarNameList.contains(v)).distinct().collect(toList());

        List<VariablePO> allVariableList = new ArrayList<>(existsVariableList);
        if (CollectionUtils.isNotEmpty(createVarNameList)) {
            LocalDateTime now = LocalDateTime.now();
            List<VariablePO> variableList = new ArrayList<>();
            for (String varName : createVarNameList) {
                VariablePO var = new VariablePO();
                var.setBotId(botId);
                var.setName(varName);
                var.setType(VariableTypeEnum.CUSTOM);
                var.setDesc("");
                var.setInterpretType(VarInterpretTypeEnum.DEFAULT);
                var.setEnableSave(false);
                var.setCreateTime(now);
                var.setUpdateTime(now);
                var.setCreateUserId(userId);
                var.setUpdateUserId(userId);
                variableList.add(var);
            }
            mongoTemplate.insert(variableList, VariablePO.COLLECTION_NAME);

            // 日志
            variableOperationLogService.batchCreateVar(botId, createVarNameList, userId);

            allVariableList.addAll(variableList);
        }

        return allVariableList.stream().filter(v -> varNameList.contains(v.getName())).collect(Collectors.toMap(VariablePO::getName, VariablePO::getId));
    }

    private VariablePO doCreate(VariablePO variable, boolean createOpLog) {
        variable.setCreateTime(LocalDateTime.now());
        variable.setUpdateTime(LocalDateTime.now());
        variable.setId(new ObjectId().toHexString());

        // 如果是模板变量, 则判断是否引用了自定义变量, 则需要更新引用关系
        if (VariableTypeEnum.isTemplateVariable(variable.getType())) {
            processDependVar(variable, createOpLog);
        }

        mongoTemplate.save(variable, VariablePO.COLLECTION_NAME);
        if (createOpLog) {
            variableOperationLogService.addVar(variable);
        }

        return variable;
    }

    private void processDependVar(VariablePO variable, boolean createOpLog) {
        Set<String> dependVariableIdSet = new HashSet<>();
        Set<String> dependVariableNameSet = new HashSet<>();

        if (VariableTypeEnum.isTemplateVariable(variable.getType())) {
            if (TemplateVariableConfigTypeEnum.PROMPT.equals(variable.getTemplateVariableConfigType())) {
                if (StringUtils.isNotBlank(variable.getPrompt())) {
                    dependVariableNameSet.addAll(new AnswerPlaceholderSplitter(variable.getPrompt(), false).getPlaceholderSet());
                }
                if (StringUtils.isNotBlank(variable.getTemplateSentence())) {
                    dependVariableNameSet.addAll(new AnswerPlaceholderSplitter(variable.getTemplateSentence(), false).getPlaceholderSet());
                }
                // 查看当前所有的
            }

            if (CollectionUtils.isNotEmpty(dependVariableNameSet)) {
                List<VariablePO> allVariableList = getListByBotId(variable.getBotId());
                Map<String, String> varName2IdMap = MyCollectionUtils.listToConvertMap(allVariableList, VariablePO::getName, VariablePO::getId);
                for (String dependVarName : dependVariableNameSet) {
                    if (varName2IdMap.containsKey(dependVarName)) {
                        dependVariableIdSet.add(varName2IdMap.get(dependVarName));
                    } else {
                        // 创建自定义变量
                        VariablePO createPO = new VariablePO();
                        createPO.setName(dependVarName);
                        createPO.setDesc("通过模板变量" + variable.getName() +"引用自动创建");
                        createPO.setCreateUserId(variable.getUpdateUserId());
                        createPO.setUpdateUserId(variable.getUpdateUserId());
                        createPO.setCreateTime(LocalDateTime.now());
                        createPO.setUpdateTime(LocalDateTime.now());
                        createPO.setType(VariableTypeEnum.CUSTOM);
                        createPO.setId(new ObjectId().toHexString());
                        createPO.setBotId(variable.getBotId());
                        mongoTemplate.save(createPO, VariablePO.COLLECTION_NAME);
                        if (createOpLog) {
                            variableOperationLogService.addVar(createPO);
                        }
                        dependVariableIdSet.add(createPO.getId());
                    }
                }
            }
        }

        updateDependResourceRef(variable, dependVariableIdSet);
    }


    private void updateDependResourceRef(VariablePO templateVariable, Set<String> dependVariableIdSet) {
        // 先删除, 再添加
        sourceRefService.deleteSourceByRefId(templateVariable.getBotId(), templateVariable.getId());
        if (CollectionUtils.isNotEmpty(dependVariableIdSet)) {
            sourceRefService.saveSourceRef(buildParam(templateVariable, dependVariableIdSet));
        }
    }

    private SourceRefBO buildParam(VariablePO templateVariable, Set<String> sourceIdSet) {
        SourceRefBO sourceRefBO = new SourceRefBO();
        sourceRefBO.setBotId(templateVariable.getBotId());
        sourceRefBO.setSourceType(SourceTypeEnum.VARIABLE);
        sourceRefBO.setSourceIdSet(sourceIdSet);
        sourceRefBO.setRefId(templateVariable.getId());
        sourceRefBO.setRefLabel(templateVariable.getName());
        sourceRefBO.setRefType(IntentRefTypeEnum.TEMPLATE_VARIABLE);
        return sourceRefBO;
    }

    @Override
    public Boolean autoCreateIfNotExists(Long botId, String text, Long userId) {
        return autoCreateIfNotExists(botId, Collections.singletonList(text), VariableTypeEnum.CUSTOM, userId)._1();
    }

    @Override
    public Tuple2<Boolean, Map<String, String>> autoCreateIfNotExists(Long botId, List<String> textList, VariableTypeEnum defaultType, Long userId) {
        if (CollectionUtils.isEmpty(textList)) {
            return Tuple.of(false, Collections.emptyMap());
        }
        Set<String> requireVarNameSet = textList.stream()
                .flatMap(text -> new AnswerPlaceholderSplitter(text, false)
                        .getVariableSet().stream())
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(requireVarNameSet)) {
            return Tuple.of(false, Collections.emptyMap());
        }

        Map<String, String> variableNameIdMap = getNameIdMapByBotId(botId);
        boolean createNewVar = false;
        Map<String, String> resultMap = new HashMap<>();
        for (String varName : requireVarNameSet) {
            if (variableNameIdMap.containsKey(varName)) {
                resultMap.put(varName, variableNameIdMap.get(varName));
            } else {
                createNewVar = true;
                VariablePO po = new VariablePO();
                po.setBotId(botId);
                po.setName(varName);
                po.setType(defaultType);
                po.setDesc("");
                po.setInterpretType(VarInterpretTypeEnum.DEFAULT);
                po.setCreateUserId(userId);
                po.setUpdateUserId(userId);
                try {
                    resultMap.put(varName, create(po).getId());
                } catch (VarNameRepeatException ignore) {
                    // 忽略变量名重复校验错误
                }
            }
        }
        return Tuple.of(createNewVar, resultMap);
    }

    @Override
    public VariablePO deleteById(Long botId, String variableId, Long currentUserId) {
        VariablePO old = getById(botId, variableId);
        if (Objects.isNull(old)) {
            log.warn("变量已被删除, botId:{}, variableId:{}, userId:{}", botId, variableId, currentUserId);
            return null;
        }
        if (VariableTypeEnum.SYSTEM.equals(old.getType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "内置变量不能删除");
        }

        checkCanExecuteOperation(botId, old.getType());

        // 检查所有答案依赖的
        checkDependVariable(botId, variableId);

        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("_id").is(variableId));
        VariablePO variablePO = mongoTemplate.findAndRemove(query, VariablePO.class, VariablePO.COLLECTION_NAME);
        if (Objects.nonNull(variablePO)) {
            variableOperationLogService.deleteVar(variablePO, currentUserId);
            updateDependResourceRef(variablePO, Collections.emptySet());
        }

        return old;
    }

    @Override
    public Set<String> getRealUsedVariableNameSet(Long botId) {
        return getRealUsedVariableNameSet(botId, null, false, false);
    }

    private List<VariablePO> getAllUsedVariableList(Long botId) {
        List<VariablePO> variableList = getListByBotIdList(Collections.singletonList(botId));
        // 查询所有的变量依赖数据
        List<SourceRefPO> sourceRefList = sourceRefService.getVariableRefListByBotIdList(Collections.singletonList(botId));
        Set<String> usedVariableIdSet = new HashSet<>();
        for (SourceRefPO sourceRef : sourceRefList) {
            usedVariableIdSet.add(sourceRef.getSourceId());
        }
        return variableList.stream()
                .filter(item -> usedVariableIdSet.contains(item.getId()))
                .collect(toList());
    }

    @Override
    public Set<String> getRealUsedVariableNameSet(Long botId, Long callJobId,
                                                  boolean containsDynamicVar,
                                                  boolean containsOptionalVar) {
        BotPO bot = botService.getById(botId);
        if (Objects.isNull(bot)) {
            return Collections.emptySet();
        }

        List<VariablePO> variableList = getListByBotIdList(Collections.singletonList(botId)).stream()
                .filter(variable -> containsDynamicVar || VariableTypeEnum.isNotDynamicVariable(variable.getType()))
                .filter(variable -> VariableTypeEnum.isNotBuiltInSystem(variable.getType()))
                .filter(v -> VariableTypeEnum.isNotTemplateVariable(v.getType()))
                .collect(toList());
        if (CollectionUtils.isEmpty(variableList)) {
            return Collections.emptySet();
        }

        // 需要查询 MagicActivityConfig 中的配置, 获取用户选择的可选变量名称集合, 并将其添加到结果中
        Set<String> selectedVariableNameSet = Collections.emptySet();
        if (!containsOptionalVar && V3BotTypeEnum.isMagicTemplate(bot.getType()) && Objects.nonNull(callJobId)) {
            Optional<MagicActivityConfigPO> magicActivityConfigOptional = magicActivityConfigService.getByBotIdAndCallJobId(botId, callJobId);
            if (magicActivityConfigOptional.isPresent()) {
                MagicActivityConfigPO magicActivityConfig = magicActivityConfigOptional.get();
                if (CollectionUtils.isNotEmpty(magicActivityConfig.getSelectedVariableNameList())) {
                    // 这里需要校验一下这些变量是否还存在
                    Set<String> existVariableNameSet = variableList.stream().map(VariablePO::getName).collect(toSet());
                    selectedVariableNameSet = magicActivityConfig.getSelectedVariableNameList().stream()
                            .filter(existVariableNameSet::contains)
                            .collect(toSet());
                }
            }
        }

        // 查询所有的变量依赖数据
        List<SourceRefPO> sourceRefList = sourceRefService.getVariableRefListByBotIdList(Collections.singletonList(botId));
        Set<String> usedVariableIdSet = new HashSet<>();
        Set<String> usedVariableNameSet = new HashSet<>();
        for (SourceRefPO sourceRef : sourceRefList) {
            usedVariableIdSet.add(sourceRef.getSourceId());
        }

        for (VariablePO variable : variableList) {
            if (usedVariableIdSet.contains(variable.getId())
                    && (containsOptionalVar || selectedVariableNameSet.contains(variable.getName()) || BooleanUtils.isNotTrue(variable.getIsOptional()))
            ) {
                usedVariableNameSet.add(variable.getName());
            }
        }
        return usedVariableNameSet;
    }

    @Override
    public UserVariableNameResult getRealUsedVariableNameResult(Long botId, Long callJobId, boolean containsDynamicVar, boolean containsOptionalVar) {
        Set<String> allNameSet = getRealUsedVariableNameSet(botId, callJobId, containsDynamicVar, containsOptionalVar);
        Set<String> requireNameSet = new HashSet<>();
        Set<String> optionalNameSet = new HashSet<>();

        Set<String> allOptionalNameSet = getListByBotIdList(Collections.singletonList(botId)).stream()
                .filter(item -> BooleanUtils.isTrue(item.getIsOptional()))
                .map(VariablePO::getName)
                .collect(toSet());

        for (String name : allNameSet) {
            if (allOptionalNameSet.contains(name)) {
                optionalNameSet.add(name);
            } else {
                requireNameSet.add(name);
            }
        }
        UserVariableNameResult result = new UserVariableNameResult();
        result.setRequireNameSet(requireNameSet);
        result.setOptionalNameSet(optionalNameSet);

        return result;
    }

    @Override
    public Map<Long, Set<String>> getRealUsedVariableNameSetByBotIdList(List<Long> botIdList) {
        return getRealUsedVariableNameSetByBotIdList(botIdList, false);
    }

    @Override
    public Map<Long, Set<String>> getRealUsedVariableNameSetByBotIdList(List<Long> botIdList, boolean containsDynamicVar) {
        List<VariablePO> variableList = getListByBotIdList(botIdList).stream()
                .filter(variable -> containsDynamicVar || VariableTypeEnum.isNotDynamicVariable(variable.getType()))
                .filter(variable -> VariableTypeEnum.isNotBuiltInSystem(variable.getType()))
                .filter(v -> VariableTypeEnum.isNotTemplateVariable(v.getType()))
                .collect(toList());
        if (CollectionUtils.isEmpty(variableList)) {
            return Collections.emptyMap();
        }

        // 查询所有的变量依赖数据
        List<SourceRefPO> sourceRefList = sourceRefService.getVariableRefListByBotIdList(botIdList);
        Set<String> usedKey = new HashSet<>();
        for (SourceRefPO sourceRef : sourceRefList) {
            usedKey.add(String.format("botId%s_varId%s", sourceRef.getBotId(), sourceRef.getSourceId()));
        }

        Map<Long, Set<String>> result = new HashMap<>();

        for (VariablePO variable : variableList) {
            Long botId = variable.getBotId();
            Set<String> usedVariableNameSet = result.computeIfAbsent(botId, k -> new HashSet<>());
            String key = String.format("botId%s_varId%s", botId, variable.getId());
            if (usedKey.contains(key) && BooleanUtils.isNotTrue(variable.getIsOptional())) {
                usedVariableNameSet.add(variable.getName());
            }
        }
        return result;
    }

    @Override
    public List<VariablePO> getListByBotIdList(List<Long> botIdList) {
        if (CollectionUtils.isEmpty(botIdList)) {
            return Collections.emptyList();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").in(botIdList));
        return mongoTemplate.find(query, VariablePO.class, VariablePO.COLLECTION_NAME);
    }

    @Override
    public List<VariableBindInfoVO> getUsedVariableBindInfoList(Long botId, Long tenantId) {
        List<VariablePO> variableList = getListByBotId(botId);
        Set<String> usedVariableNameSet = getRealUsedVariableNameSet(botId);
        return variableList.stream()
                .filter(item -> usedVariableNameSet.contains(item.getName()))
                .filter(item -> VariableTypeEnum.isNotDynamicVariable(item.getType()))
                .map(item -> MyBeanUtils.copy(item, VariableBindInfoVO.class))
                .collect(toList());
    }

    @Override
    public List<IdNamePair<String, String>> getDynamicVariableIdNamePairList(Long botId) {
        return getListByBotId(botId)
                .stream()
                .filter(item -> VariableTypeEnum.isDynamicVariable(item.getType()))
                .map(item -> IdNamePair.of(item.getId(), item.getName()))
                .collect(toList());
    }

    @Override
    public Set<String> getLastSnapshotUsedVariableNameSet(Long botId, RobotSnapshotUsageTargetEnum snapshotType) {
        RobotSnapshotQuery query = new RobotSnapshotQuery();
        query.setBotId(botId);
        query.setUsageTarget(snapshotType);
        Optional<RobotSnapshotPO> robotSnapshotOptional = robotSnapshotService.queryLastSnapshotByCondition(query);
        if (robotSnapshotOptional.isPresent()) {
            RobotSnapshotPO snapshot = robotSnapshotOptional.get();
            if (Objects.nonNull(snapshot.getUsedVariableNameSet())) {
                return snapshot.getUsedVariableNameSet();
            }
        }
        return getRealUsedVariableNameSet(botId);
    }

    @Override
    public Set<String> getLastSnapshotUsedVariableNameSetByDialogFlowId(Long dialogFlowId, RobotSnapshotUsageTargetEnum snapshotType) {
        Long botId = botRefService.getBotId(dialogFlowId);
        if (Objects.isNull(botId)) {
            return Collections.emptySet();
        }
        return getLastSnapshotUsedVariableNameSet(botId, snapshotType);
    }

    @Override
    public Set<String> getUsedVariableNameSetByDialogFlowId(Long dialogFlowId, Long callJobId) {
        Long botId = botRefService.getBotId(dialogFlowId);
        if (Objects.isNull(botId)) {
            return Collections.emptySet();
        }
        return getRealUsedVariableNameSet(botId, callJobId, false, false);
    }

    @Override
    public void deleteByIdList(Long botId, List<String> idList, boolean selectAll, Long userId) {
        if (!selectAll && CollectionUtils.isEmpty(idList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "变量id列表不能为空");
        }
        if (selectAll) {
            List<VariablePO> variablePOList = getListByBotId(botId);
            if (CollectionUtils.isEmpty(variablePOList)) {
                return;
            }
            idList = variablePOList.stream().filter(var -> !VariableTypeEnum.SYSTEM.equals(var.getType())).map(VariablePO::getId).collect(toList());
        } else {
            //去除内置变量
            Query query = new Query();
            query.addCriteria(Criteria.where("botId").is(botId))
                    .addCriteria(Criteria.where("type").is(VariableTypeEnum.SYSTEM));
            List<VariablePO> systemVarList = mongoTemplate.find(query, VariablePO.class, VariablePO.COLLECTION_NAME);
            if (CollectionUtils.isNotEmpty(systemVarList)) {
                List<String> sysIdList = systemVarList.stream().map(VariablePO::getId).collect(toList());
                idList.removeAll(sysIdList);
            }
        }

        // 被场景关联的BOT中的模板变量不允许删除
        if (CollectionUtils.isNotEmpty(botService.getDependentSceneNameList(botId))) {
            List<String> templateVarIdList = MyCollectionUtils.listToConvertList(getTemplateVariableByBotIdList(Collections.singletonList(botId)), VariablePO::getId);
            if (CollectionUtils.isNotEmpty(templateVarIdList)) {
                idList.removeAll(templateVarIdList);
            }
        }

        List<SourceRefPO> sourceRefList = sourceRefService.getBySourceIdList(botId, idList, SourceTypeEnum.VARIABLE);
        if (CollectionUtils.isNotEmpty(sourceRefList)) {
            Set<String> sourceIdSet = sourceRefList.stream().map(SourceRefPO::getSourceId).collect(toSet());
            idList.removeAll(sourceIdSet);
        }
        if (CollectionUtils.isEmpty(idList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "所选变量均已被引用，无法删除");
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("_id").in(idList));
        List<VariablePO> allRemovedVarList = mongoTemplate.findAllAndRemove(query, VariablePO.class, VariablePO.COLLECTION_NAME);
        if (CollectionUtils.isNotEmpty(allRemovedVarList)) {
            for (VariablePO variablePO : allRemovedVarList) {
                updateDependResourceRef(variablePO, Collections.emptySet());
            }
        }
        variableOperationLogService.batchDeleteVar(botId, allRemovedVarList, userId);
    }

    private void checkDependVariable(Long botId, String variableId) {
        List<SourceRefPO> dependInfoList = sourceRefService.getBySourceIdList(botId, Collections.singletonList(variableId), SourceTypeEnum.VARIABLE);
        if (CollectionUtils.isNotEmpty(dependInfoList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "变量已被引用，无法删除");
        }
    }

    public List<VariablePO> generateDefault(Long botId) {
        List<VariablePO> result = new ArrayList<>();

        DEFAULT_SYSTEM_VARIABLE_NAME_DESC_MAP.forEach((name, desc) -> {
            VariablePO v = new VariablePO();
            v.setBotId(botId);
            v.setName(name);
            v.setDesc(desc);
            v.setCreateTime(LocalDateTime.now());
            v.setUpdateTime(LocalDateTime.now());
            v.setType(VariableTypeEnum.BUILT_IN_SYSTEM);
            v.setInterpretType(VarInterpretTypeEnum.DEFAULT);
            result.add(v);
        });

        DEFAULT_VARIABLE_NAME_DESC_MAP.forEach((name, desc) -> {
            VariablePO v = new VariablePO();
            v.setBotId(botId);
            v.setName(name);
            v.setDesc(desc);
            v.setCreateTime(LocalDateTime.now());
            v.setUpdateTime(LocalDateTime.now());
            v.setType(VariableTypeEnum.SYSTEM);
            v.setInterpretType(VarInterpretTypeEnum.DEFAULT);
            result.add(v);
        });

        return result;
    }

    @Override
    public void saveToSnapshot(RobotResourceContext context) {
        List<VariablePO> variableList = getListByBotId(context.getSrcBotId());
        context.getSnapshot().setVariableList(variableList);
        context.getSnapshot().setUsedVariableNameSet(getRealUsedVariableNameSet(context.getSrcBotId(), null, true, true));
    }

    @Override
    public void validateResource(RobotResourceContext context) {
        if(context.isMagicTemplateBot()) {
            validVariableUsingForMagicTemplateBot(context);
        }
        for (VariablePO variablePO : context.getSnapshot().getVariableList()) {
            try {
                validVariable(variablePO, true, false);
            } catch (ComException e) {
                context.getInvalidMsgList().add(
                        SnapshotInvalidFailItemMsg.builder()
                                .resourceType(BotResourceTypeEnum.VARIABLE)
                                .resourceId(variablePO.getId())
                                .resourceName(variablePO.getName())
                                .resourceLabel(variablePO.getName())
                                .failMsg(String.format("变量[%s]:%s", variablePO.getName(), e.getMessage()))
                                .build()
                );
            }
        }
    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {
        copyVariable(context);
    }

    private void copyVariable(RobotResourceContext context) {
        List<VariablePO> variableList = context.getSnapshot().getVariableList();
        Map<String, String> variableIdMapping = context.getResourceCopyReferenceMapping().getVariableIdMapping();
        if (context.isCopy()) {
            if (CollectionUtils.isNotEmpty(variableList)) {
                variableList.forEach(variable -> {
                    String oldId = variable.getId();
                    variable.setBotId(context.getTargetBotId());
                    variable.setId(new ObjectId().toString());
                    // bot复制时清空动态变量保存的客户属性
                    if (Objects.nonNull(context.getSrcTenantId()) && Objects.equals(context.getTargetTenantId(), context.getSrcTenantId())) {
                        log.debug("复制 bot 时, 目标租户和当前租户一致, 不清空客户属性, srcBotId:{}, targetBotId:{} srcTenantId:{}, targetTenantId:{}",
                                context.getSrcBotId(), context.getTargetBotId(), context.getSrcTenantId(), context.getTargetTenantId());
                    } else {
                        variable.setCustomerAttributeId(null);
                    }

                    variableIdMapping.put(oldId, variable.getId());
                });
            }
            mongoTemplate.insert(variableList, VariablePO.COLLECTION_NAME);
        }
    }

    @Override
    public List<Class<? extends RobotResourceService>> dependsOn() {
        return RobotResourceService.super.dependsOn();
    }

    @Override
    public void clearCustomerAttribute(Long botId) {
        Query query = Query.query(
                Criteria.where("botId").is(botId).and("customerAttributeId").ne(null)
        );
        Update update = Update.update("customerAttributeId", null);
        mongoTemplate.updateMulti(query, update, VariablePO.COLLECTION_NAME);
    }

    @Override
    public String copyVariableByNameIfAbsent(Long srcBotId, Long targetBotId, String srcVariableId, Long userId) {
        VariablePO srcVariable = getById(srcBotId, srcVariableId);
        if (Objects.isNull(srcVariable)) {
            return null;
        }
        VariablePO targetVariable = getByName(targetBotId, srcVariable.getName());
        if (Objects.isNull(targetVariable)) {
            targetVariable = MyBeanUtils.copy(srcVariable, VariablePO.class);
            targetVariable.setId(null);
            targetVariable.setBotId(targetBotId);
            targetVariable.setCreateTime(LocalDateTime.now());
            targetVariable.setUpdateTime(LocalDateTime.now());
            targetVariable.setCustomerAttributeId(null);
            targetVariable.setCreateUserId(userId);
            targetVariable.setUpdateUserId(userId);
            create(targetVariable);
        }
        return targetVariable.getId();
    }

    @Override
    public List<VariableInfoDTO> getUsedVariableListByDialogFlowId(Long dialogFlowId) {
        Long botId = botRefService.getBotId(dialogFlowId);
        if (Objects.isNull(botId)) {
            return Collections.emptyList();
        }
        Set<String> useNameSet = getRealUsedVariableNameSet(botId, null, false, true);
        if (CollectionUtils.isEmpty(useNameSet)) {
            return Collections.emptyList();
        }

        List<VariablePO> variableList = getListByBotId(botId);
        return variableList.stream()
                .filter(item -> useNameSet.contains(item.getName()))
                .map(item -> {
                    VariableInfoDTO r = new VariableInfoDTO();
                    r.setId(item.getId());
                    r.setName(item.getName());
                    r.setType(item.getType().name());
                    r.setDesc(item.getDesc());
                    r.setIsOptional(BooleanUtils.isTrue(item.getIsOptional()));
                    return r;
                }).collect(toList());
    }

    @Override
    public List<VariableInfoDTO> getUsingTemplateVariableListByDialogFlowId(Long dialogFlowId) {
        Long botId = botRefService.getBotId(dialogFlowId);
        if (Objects.isNull(botId)) {
            return Collections.emptyList();
        }

        List<VariablePO> variableList = getAllUsedVariableList(botId);

        return variableList.stream()
                .filter(item -> VariableTypeEnum.isTemplateVariable(item.getType()))
                .map(item -> {
                    VariableInfoDTO r = new VariableInfoDTO();
                    r.setId(item.getId());
                    r.setName(item.getName());
                    r.setType(item.getType().name());
                    r.setDesc(item.getDesc());
                    r.setIsOptional(BooleanUtils.isTrue(item.getIsOptional()));
                    r.setPrompt(item.getPrompt());
                    r.setTemplateSentence(item.getTemplateSentence());
                    r.setTemplateVariableConfigType(item.getTemplateVariableConfigType().name());
                    return r;
                }).collect(toList());
    }

    private VariablePO getByName(Long botId, String variableName) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("name").is(variableName));
        return mongoTemplate.findOne(query, VariablePO.class, VariablePO.COLLECTION_NAME);
    }

    @Override
    public List<String> listAllDynamicVariableNameByDialogFlowId(Long dialogFlowId) {
        if (Objects.isNull(dialogFlowId)) {
            return Collections.emptyList();
        }
        Long botId = botRefService.getBotId(dialogFlowId);
        if (Objects.isNull(botId)) {
            return Collections.emptyList();
        }
        return getListByBotId(botId).stream().
                filter(v -> VariableTypeEnum.isDynamicVariable(v.getType()))
                .map(VariablePO::getName)
                .distinct()
                .collect(toList());
    }

    @Override
    public List<VariablePO> getTemplateVariableByBotIdList(List<Long> botIdList) {
        if (CollectionUtils.isEmpty(botIdList)) {
            return Collections.emptyList();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").in(botIdList))
                .addCriteria(Criteria.where("type").is(VariableTypeEnum.TEMPLATE.name()));
        return mongoTemplate.find(query, VariablePO.class, VariablePO.COLLECTION_NAME);
    }

    @Override
    public List<VariablePO> getUsedTemplateVarListByBotId(Long botId) {
        if (Objects.isNull(botId)) {
            return Collections.emptyList();
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("type").is(VariableTypeEnum.TEMPLATE.name()));
        List<VariablePO> varList = mongoTemplate.find(query, VariablePO.class, VariablePO.COLLECTION_NAME);

        // 查询所有的变量依赖数据
        List<SourceRefPO> sourceRefList = sourceRefService.getVariableRefListByBotIdList(Collections.singletonList(botId));
        Set<String> usedVariableIdSet = new HashSet<>();
        for (SourceRefPO sourceRef : sourceRefList) {
            usedVariableIdSet.add(sourceRef.getSourceId());
        }

        return varList.stream()
                .filter(item -> usedVariableIdSet.contains(item.getId()))
                .collect(toList());
    }

    @Override
    public boolean existsTemplateVariable(Long botId) {
        return mongoTemplate.exists(
                Query.query(Criteria.where("type").is(VariableTypeEnum.TEMPLATE).and("botId").is(botId)),
                VariablePO.COLLECTION_NAME
        );
    }

    public void validVariableUsingForMagicTemplateBot(RobotResourceContext context) {
        RobotSnapshotPO snapshot = context.getSnapshot();

        List<String> varIdList = snapshot.getSourceRefList().stream().filter(s -> SourceTypeEnum.VARIABLE.equals(s.getSourceType()))
                .map(SourceRefPO::getSourceId).distinct().collect(toList());

        List<VariablePO> varList = snapshot.getVariableList().stream().filter(v -> varIdList.contains(v.getId())).collect(toList());

        if (CollectionUtils.isEmpty(varList)) {
            context.getInvalidMsgList().add(
                    SnapshotInvalidFailItemMsg.builder()
                            .resourceType(BotResourceTypeEnum.VARIABLE)
                            .failMsg("未使用模板变量，请检查")
                            .isWarning(true)
                            .build());
            return;
        }

        // 是否使用了模板变量
        boolean containsTemplateVar = varList.stream().anyMatch(v -> VariableTypeEnum.isTemplateVariable(v.getType()));

        if (!containsTemplateVar) {
            context.getInvalidMsgList().add(
                    SnapshotInvalidFailItemMsg.builder()
                            .resourceType(BotResourceTypeEnum.VARIABLE)
                            .failMsg("未使用模板变量，请检查")
                            .isWarning(true)
                            .build());
        }
    }

    @Override
    public Map<String, String> syncDependVariable(List<VariablePO> variablePOList, Long targetBotId, Long currentUserId) {
        // 添加变量
        Map<String, String> variableIdMapping = Maps.newHashMap();
        Map<String, VariablePO> variablePOMap = MyCollectionUtils.listToMap(variablePOList, VariablePO::getName);
        // 这是同步过来的变量Mapping
        Map<String, String> variableNameIdMapping = MyCollectionUtils.listToMap(variablePOList, VariablePO::getName, VariablePO::getId);
        // 这是当前Bot已存在的变量Mapping
        Map<String, String> nameIdMapByBotId = getNameIdMapByBotId(targetBotId);
        // 如果变量已存在，直接复用ID
        variableNameIdMapping.keySet().forEach(name -> {
            if (nameIdMapByBotId.containsKey(name)) {
                variableIdMapping.put(variableNameIdMapping.get(name), nameIdMapByBotId.get(name));
                variablePOMap.remove(name);
            }
        });
        // 如果变量不存在，则新建
        variablePOMap.forEach((name, variablePO) -> {
            variablePO.setId(null);
            variablePO.setBotId(targetBotId);
            variablePO.setCreateUserId(currentUserId);
            variablePO.setUpdateUserId(currentUserId);
            variablePO.setCustomerAttributeId(null);
            create(variablePO);
            variableIdMapping.put(variableNameIdMapping.get(name), variablePO.getId());
        });
        return variableIdMapping;
    }

    @Override
    public VariableSyncResultVO sync(VariableSyncRequestVO syncRequest) {
        if (CollectionUtils.isEmpty(syncRequest.getSrcVariableIdList()) && BooleanUtils.isNotTrue(syncRequest.getSelectAll())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "待同步变量不能为空");
        }

        if (CollectionUtils.isEmpty(syncRequest.getTargetBotIdList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "目标 bot 不能为空");
        }

        if (Objects.isNull(syncRequest.getSyncMode())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "同步方式不能为空");
        }

        List<VariablePO> allVariableList = getListByBotId(syncRequest.getSrcBotId());
        List<VariablePO> srcVariableList;
        if (BooleanUtils.isTrue(syncRequest.getSelectAll())) {
            srcVariableList = getListByBotId(syncRequest.getSrcBotId()).stream()
                    .filter(item -> !VariableTypeEnum.isSystemVariable(item.getType()))
                    .collect(toList());
        } else {
            srcVariableList = getByIdList(syncRequest.getSrcVariableIdList());
        }

        if (CollectionUtils.isEmpty(srcVariableList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "待同步变量为空");
        }

        BotPO srcBot = botService.getById(syncRequest.getSrcBotId());
        if (Objects.isNull(srcBot)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "源 bot 不存在");
        }

        int successCount = 0;
        int failCount = 0;

        Map<String, VariablePO> variableNameMap = MyCollectionUtils.listToMap(allVariableList, VariablePO::getName);
        List<VariablePO> finalSrcVariableList = new ArrayList<>();
        Set<VariablePO> parsedVarSet = new HashSet<>();
        for (VariablePO variable : srcVariableList) {
            VariablePO tmpVar = variableNameMap.get(variable.getName());
            finalSrcVariableList.add(tmpVar);
        }

        for (VariablePO variable : srcVariableList) {
            loopParseDependVar(parsedVarSet, finalSrcVariableList, variableNameMap, variable);
        }

        finalSrcVariableList = finalSrcVariableList.stream().distinct().collect(toList());
        log.debug("finalSrcVariableList:{}", finalSrcVariableList);

        List<BotPO> targetBotList = new ArrayList<>();
        for (Long targetBotId : syncRequest.getTargetBotIdList()) {
            try {
                BotPO targetBot = botService.getById(targetBotId);
                if (Objects.isNull(targetBot)) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "目标 bot 不存在");
                }
                syncToOneBot(finalSrcVariableList, targetBotId, syncRequest.getSyncMode(), syncRequest.getCurrentUserId());
                targetBotList.add(targetBot);
                successCount ++;
            } catch (Exception e) {
                log.warn("同步变量至{}失败", targetBotId, e);
                failCount ++;
            }
        }

        variableOperationLogService.syncVariable(srcBot, srcVariableList, targetBotList, syncRequest.getSyncMode(), syncRequest.getCurrentUserId());

        VariableSyncResultVO result = new VariableSyncResultVO();
        result.setFailBotCount(failCount);
        result.setSuccessBotCount(successCount);
        return result;
    }

    private void loopParseDependVar(Set<VariablePO> parsedVarSet, List<VariablePO> dependVarList, Map<String, VariablePO> variableNameMap, VariablePO variable) {
        if (parsedVarSet.contains(variable)) {
            return;
        }
        parsedVarSet.add(variable);
        if (VariableTypeEnum.isTemplateVariable(variable.getType())
                && TemplateVariableConfigTypeEnum.PROMPT.equals(variable.getTemplateVariableConfigType())) {
            if (StringUtils.isNotBlank(variable.getPrompt())) {
                AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(variable.getPrompt(), false);
                if (CollectionUtils.isNotEmpty(splitter.getPlaceholderSet())) {
                    for (String name : splitter.getPlaceholderSet()) {
                        VariablePO tmp = variableNameMap.get(name);
                        if (Objects.nonNull(tmp)) {
                            dependVarList.add(0, tmp);
                            loopParseDependVar(parsedVarSet, dependVarList, variableNameMap, tmp);
                        }
                    }
                }
            }
            if (StringUtils.isNotBlank(variable.getTemplateSentence())) {
                AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(variable.getTemplateSentence(), false);
                if (CollectionUtils.isNotEmpty(splitter.getPlaceholderSet())) {
                    for (String name : splitter.getPlaceholderSet()) {
                        VariablePO tmp = variableNameMap.get(name);
                        if (Objects.nonNull(tmp)) {
                            dependVarList.add(0, tmp);
                            loopParseDependVar(parsedVarSet, dependVarList, variableNameMap, tmp);
                        }
                    }
                }
            }
        }
    }

    private void syncToOneBot(List<VariablePO> srcVariableList, Long targetBotId, SyncModeEnum syncMode, Long userId) {
        List<VariablePO> targetVariableList = getListByBotId(targetBotId);
        Map<String, VariablePO> targetVariableNameMap = MyCollectionUtils.listToMap(targetVariableList, VariablePO::getName);
        for (VariablePO srcVar : srcVariableList) {
            VariablePO targetVar = targetVariableNameMap.get(srcVar.getName());
            if (Objects.isNull(targetVar)) {
                // 创建
                targetVar = MyBeanUtils.copy(srcVar, VariablePO.class);
                targetVar.setId(null);
                targetVar.setCustomerAttributeId(null);
                targetVar.setBotId(targetBotId);
                targetVar.setCreateTime(LocalDateTime.now());
                targetVar.setUpdateTime(LocalDateTime.now());
                targetVar.setCreateUserId(userId);
                targetVar.setUpdateUserId(userId);
                doCreate(targetVar, false);
            } else if (targetVar.getType().equals(srcVar.getType()) && SyncModeEnum.COVER.equals(syncMode)) {
                targetVar.setUpdateUserId(userId);
                targetVar.setDesc(srcVar.getDesc());
                targetVar.setInterpretType(srcVar.getInterpretType());
                targetVar.setEnableSave(srcVar.getEnableSave());
                targetVar.setSaveMode(srcVar.getSaveMode());
                targetVar.setEnableDeduplicate(srcVar.getEnableDeduplicate());
                targetVar.setDefaultValue(srcVar.getDefaultValue());
                targetVar.setEnableAutoAlign(srcVar.getEnableAutoAlign());
                targetVar.setTemplateVariableConfigType(srcVar.getTemplateVariableConfigType());
                targetVar.setPrompt(srcVar.getPrompt());
                targetVar.setTemplateSentence(srcVar.getTemplateSentence());
                targetVar.setIsOptional(srcVar.getIsOptional());
                doUpdate(targetVar, userId, false);
            }
            targetVariableNameMap = MyCollectionUtils.listToMap(getListByBotId(targetBotId), VariablePO::getName);
        }
    }


}
