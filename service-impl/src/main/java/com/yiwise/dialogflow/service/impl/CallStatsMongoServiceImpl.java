package com.yiwise.dialogflow.service.impl;

import com.yiwise.middleware.redis.service.RedisOpsService;
import com.yiwise.dialogflow.common.MongoCollectionNameCenter;
import com.yiwise.dialogflow.entity.bo.MongoWriteCacheInfo;
import com.yiwise.dialogflow.service.CallStatsMongoService;
import com.yiwise.dialogflow.service.ShutdownListener;
import com.yiwise.dialogflow.utils.MongoCacheUtil;
import javaslang.control.Try;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.scripting.support.ResourceScriptSource;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.yiwise.dialogflow.utils.MongoCacheUtil.CACHE_PREFIX;
import static com.yiwise.dialogflow.utils.MongoCacheUtil.LOCK_KEY_PREFIX;

@Service
public class CallStatsMongoServiceImpl implements CallStatsMongoService, ShutdownListener {
    private static Logger logger = LoggerFactory.getLogger(CallStatsMongoServiceImpl.class);

    @Resource
    private RedisOpsService redisOpsService;

    @Resource
    private MongoTemplate mongoTemplate;

    // 用于实现自定义的redis操作
    DefaultRedisScript<Long> hdecrbyScript;

    // mongo表的缓存刷新周期，默认300秒，并使用内存存储聚合数据，有特殊刷新要求需要按下面的方法设置一下
    // 设置包括是否启用redis，以及刷新周期，由于redis读写性能比内存更差，请谨慎使用
    // 1-如果数据不能聚合，慎用redis
    // 2-缓存数据，占用的是redis的DBIndex=20的空间
    Map<String, MongoWriteCacheInfo> flushIntervalMap = new HashMap<>();

    ConcurrentHashMap<String, ConcurrentHashMap<Query, ConcurrentHashMap<String, AtomicLong>>> cachedMongoData = new ConcurrentHashMap<>();

    List<Timer> timers = new ArrayList<>();

    @PostConstruct
    void init() {
        flushIntervalMap.put(MongoCollectionNameCenter.BOT_RECEPTION_STATS, new MongoWriteCacheInfo(false, 1000L));
        flushIntervalMap.put(MongoCollectionNameCenter.STEP_NODE_JUMP_STATS, new MongoWriteCacheInfo(false, 1000L));
        flushIntervalMap.put(MongoCollectionNameCenter.STEP_STATS, new MongoWriteCacheInfo(false, 1000L));
        flushIntervalMap.put(MongoCollectionNameCenter.INTENT_TRIGGER_STATS, new MongoWriteCacheInfo(false, 1000L));
        flushIntervalMap.put(MongoCollectionNameCenter.INTENT_RULE_STATS, new MongoWriteCacheInfo(false, 1000L));
        flushIntervalMap.put(MongoCollectionNameCenter.ANSWER_STATS, new MongoWriteCacheInfo(false, 1000L));

        hdecrbyScript = new DefaultRedisScript<>();
        hdecrbyScript.setScriptSource(new ResourceScriptSource(new ClassPathResource("redis-scripts/hdecrby.lua")));
        hdecrbyScript.setResultType(Long.class);

        Timer timerDefault = new Timer();
        timerDefault.schedule(new TimerTask() {
            @Override
            public void run() {
                Try.run(() -> {
                    flushMemoryToMongoForDefault();
                }).onFailure(e -> logger.error("[LogHub_Warn]刷新缓存到mongo错误", e));
            }
        }, 300 * 1000L, 300 * 1000L);
        timers.add(timerDefault);

        for (Map.Entry<String, MongoWriteCacheInfo> entry : flushIntervalMap.entrySet()) {
            String collectionName = entry.getKey();
            MongoWriteCacheInfo cacheInfo = entry.getValue();
            Long flushInterval = cacheInfo.getFlushInterval();
            boolean isRedis = cacheInfo.isRedis();
            createTimer(collectionName, isRedis, flushInterval);
        }
    }

    void destory() {
        for (Timer timer : timers) {
            timer.cancel();
        }
    }

    @Override
    public void setCollectionCacheInfo(String collectionName, boolean isRedis, long flushInterval) {
        flushIntervalMap.put(collectionName, new MongoWriteCacheInfo(isRedis, flushInterval));
        createTimer(collectionName, isRedis, flushInterval);
    }

    private void createTimer(String collectionName, boolean isRedis, long flushInterval) {
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                Try.run(() -> {
                    if (isRedis) {
                        flushRedisToMongo(collectionName);
                    } else {
                        flushMemoryToMongo(collectionName);
                    }
                }).onFailure(e -> logger.error("[LogHub_Warn]刷新缓存到mongo错误", e));
            }
        }, flushInterval, flushInterval);
        timers.add(timer);
    }

    @Override
    public void flushToMongo() {
        if (!cachedMongoData.isEmpty()) {
            logger.info("刷新内存缓存到mongo...");
        }
        Set<String> collectionNames = cachedMongoData.keySet();
        for (String collectionName : collectionNames) {
            flushMemoryToMongo(collectionName);
        }
    }

    private void flushMemoryToMongoForDefault() {
        Set<String> collectionNames = cachedMongoData.keySet().stream()
                .filter(collectionName -> !flushIntervalMap.containsKey(collectionName))
                .collect(Collectors.toSet());
        if (!collectionNames.isEmpty()) {
            logger.info("刷新默认内存缓存到mongo...");
        }
        for (String collectionName : collectionNames) {
            flushMemoryToMongo(collectionName);
        }
    }

    @Override
    public void flushMemoryToMongo(String collectionName) {
        if (!cachedMongoData.containsKey(collectionName)) {
            return;
        }
        ConcurrentHashMap<Query, ConcurrentHashMap<String, AtomicLong>> collection = cachedMongoData.remove(collectionName);
        if (collection == null || collection.isEmpty()) {
            return;
        }
        long start = System.currentTimeMillis();
        for (Map.Entry<Query, ConcurrentHashMap<String, AtomicLong>> entry : collection.entrySet()) {
            Query query = entry.getKey();
            ConcurrentHashMap<String, AtomicLong> toAddValues = entry.getValue();
            if(toAddValues.isEmpty()) {
                continue;
            }
            Update update = new Update();
            for (Map.Entry<String, AtomicLong> entry1 : toAddValues.entrySet()) {
                update.inc(entry1.getKey(), entry1.getValue());
            }
            mongoTemplate.upsert(query, update, collectionName);
        }
        long used = System.currentTimeMillis() - start;
        logger.info("刷新mongo 内存缓存{}数据条数={}, 耗时={}", collectionName, collection.size(), used);
    }

    @Override
    public void flushRedisToMongo(String collectionName) {
        MongoWriteCacheInfo cacheInfo = flushIntervalMap.get(collectionName);
        long interval = cacheInfo.getFlushInterval();

        String timeKey = MongoCacheUtil.getTimeRedisKey(collectionName);
        String lastFlushTimeStr = redisOpsService.get(timeKey);
        long lastFlushTime = -1L;
        if (StringUtils.isNotEmpty(lastFlushTimeStr)) {
            lastFlushTime = Long.valueOf(lastFlushTimeStr);
        }
        if (System.currentTimeMillis() - lastFlushTime < interval) {
            return;
        }

        int redisFlushConcurrency = cacheInfo.getRedisFlushConcurrency();
        String cLockKey = null;
        for (int i=0; i<redisFlushConcurrency; ++i) {
            String testLockKey = MongoCacheUtil.getCLockRedisKey(collectionName, i);
            boolean lock = redisOpsService.setIfAbsent(testLockKey, 1, 600);
            if (lock) {
                cLockKey = testLockKey;
                break;
            }
        }
        if (cLockKey == null) {
            return;
        }
        try {
            String queryKey = MongoCacheUtil.getCollectionScanKey(collectionName);
            long ts1 = System.currentTimeMillis();
            Set<Object> keys = redisOpsService.scan(queryKey);
            long ts2 = System.currentTimeMillis();
            logger.info("scan {} costs {} ms", queryKey, ts2 - ts1);
            if (CollectionUtils.isEmpty(keys)) {
                return;
            }
            long start = System.currentTimeMillis();
            AtomicInteger updateCnt = new AtomicInteger(0);
            keys.parallelStream().forEach(key -> {
                boolean updated = flushRedisToMongoForKey(key);
                if (updated) {
                    updateCnt.incrementAndGet();
                }
            });
            long used = System.currentTimeMillis() - start;
            if (updateCnt.get() > 0) {
                logger.info("刷新mongo redis缓存{}数据条数={}, 耗时={}", collectionName, updateCnt.get(), used);
            }
        } finally {
            redisOpsService.delete(cLockKey);
            redisOpsService.set(timeKey, System.currentTimeMillis(), 3600);
        }
    }

    private boolean flushRedisToMongoForKey(Object key) {
        // try lock
        String kLockKey = LOCK_KEY_PREFIX + ((String)key).substring(CACHE_PREFIX.length());
        boolean lock = redisOpsService.setIfAbsent(kLockKey, 1, 600);
        if (!lock) {
            return false;
        }
        RedisTemplate redisTemplate = redisOpsService.getRedisTemplate();
        try {
            Map<Object, Object> m = redisTemplate.opsForHash().entries(key);
            boolean needUpdate = false;
            Update update = new Update();
            for (Map.Entry<Object, Object> entry1 : m.entrySet()) {
                String key1 = (String) entry1.getKey();
                long value1 = Long.valueOf((String) entry1.getValue());
                redisTemplate.execute(hdecrbyScript, Arrays.asList(key, key1), value1);
                if (value1 != 0) {
                    needUpdate = true;
                    update.inc(key1.substring(CACHE_PREFIX.length()), value1);
                }
            }
            if (needUpdate) {
                String collectionName = MongoCacheUtil.getCollectionNameFromRedisKey((String) key);
                Query query = MongoCacheUtil.getQueryFromRedisKey((String) key);
                mongoTemplate.upsert(query, update, collectionName);
            }
            return needUpdate;
        } finally {
            redisOpsService.delete(kLockKey);
        }
    }

    /**
     * mongo某个key新增1
     * @param collectionName mongo集合名词
     * @param key mongo集合列名称
     * @param query  修改的条件
     */
    @Override
    public void incrementValue(String collectionName, Query query, String key) {
        incrementValue(collectionName, query, key, 1L);
    }
    /**
     * mongo某个key新增1
     * @param collectionName mongo集合名词
     * @param key mongo集合列名称
     * @param query  修改的条件
     * @param value 修改的值
     */
    @Override
    public void incrementValue(String collectionName, Query query, String key, Long value) {
        // mongo修改
        Update update = new Update();
        update.inc(key, value);

        updateMongoData(collectionName, query, update);
    }

    /**
     * 修改mongo数据，
     */
    @Override
    public synchronized void updateMongoData(String collectionName, Query query, Update update) {
        // TODO upsert非原子性，最好用rlock、zk做个分布式锁
        Assert.notNull(update, "Update must not be null!");
        if (update.getUpdateObject().isEmpty()) {
            logger.warn("[LogHub]更新统计数据异常, update内容为空");
            return;
        }
        mongoTemplate.upsert(query, update, collectionName);
    }

    public void updateMongoDataUsingCache(String collectionName, Query query, Update update) {
        MongoWriteCacheInfo cacheInfo = flushIntervalMap.get(collectionName);
        if (cacheInfo == null || !cacheInfo.isRedis()) {
            updateMongoDataUsingMemoryCache(collectionName, query, update);
        } else {
            long ts1 = System.currentTimeMillis();
            updateMongoDataUsingRedisCache(collectionName, query, update);
            long ts2 = System.currentTimeMillis();
            logger.info("updateUseRedisCache costs {} ms.", ts2 - ts1);
        }
    }

    public void updateMongoDataUsingMemoryCache(String collectionName, Query query, Update update) {
        ConcurrentHashMap<Query, ConcurrentHashMap<String, AtomicLong>> collection = cachedMongoData.get(collectionName);
        if (collection == null) {
            collection = new ConcurrentHashMap<>();
            ConcurrentHashMap<Query, ConcurrentHashMap<String, AtomicLong>> preCollection = cachedMongoData.putIfAbsent(collectionName, collection);
            if (preCollection != null) {
                collection = preCollection;
            }
        }
        ConcurrentHashMap<String, AtomicLong> mergeMap = collection.get(query);
        if (mergeMap == null) {
            mergeMap = new ConcurrentHashMap<>();
            ConcurrentHashMap<String, AtomicLong> preMergeMap = collection.putIfAbsent(query, mergeMap);
            if (preMergeMap != null) {
                mergeMap = preMergeMap;
            }
        }

        Document document = (Document)update.getUpdateObject().get("$inc");
        for (Map.Entry<String, Object> entry : document.entrySet()) {
            String key = entry.getKey();
            Object objectValue = entry.getValue();
            long value = 0;
            if (objectValue instanceof Integer) {
                value = ((Integer) objectValue).longValue();
            } else {
                value = (long)objectValue;
            }
            AtomicLong mergeValue = mergeMap.get(key);
            if (mergeValue == null) {
                mergeValue = new AtomicLong(0);
                AtomicLong preMergeValue = mergeMap.putIfAbsent(key, mergeValue);
                if (preMergeValue != null) {
                    mergeValue = preMergeValue;
                }
            }
            mergeValue.getAndAdd(value);
        }
    }

    public void updateMongoDataUsingRedisCache(String collectionName, Query query, Update update) {
        String key1 = MongoCacheUtil.generateRedisKey(collectionName, query);
        Document document = (Document)update.getUpdateObject().get("$inc");
        for (Map.Entry<String, Object> entry : document.entrySet()) {
            String key = entry.getKey();
            Object objectValue = entry.getValue();
            long value = 0;
            if (objectValue instanceof Integer) {
                value = ((Integer) objectValue).longValue();
            } else {
                value = (long)objectValue;
            }
            if (value == 0) {
                continue;
            }
            redisOpsService.getRedisTemplate().opsForHash().increment(key1, CACHE_PREFIX + key, value);
        }
        redisOpsService.expire(key1, 1, TimeUnit.HOURS);
    }

    /**
     * mongo某个key新增value
     *
     * @param tenantId       客户ID
     * @param callStatsId    任务/话术ID
     * @param collectionName mongo集合名词
     * @param key            mongo集合列名称
     * @param value          修改的值
     */
    @Override
    public void incrementTotalTask(Long tenantId, Long callStatsId, Long dialogFlowId, String collectionName, String key, int value) {
        Query query = new Query();
        query.addCriteria(Criteria.where("tenantId").is(tenantId));
        query.addCriteria(Criteria.where("callStatsId").is(callStatsId));
        query.addCriteria(Criteria.where("dialogFlowId").is(dialogFlowId));
        // mongo修改
        Update update = new Update();
        update.inc(key, value);

        mongoTemplate.upsert(query, update, collectionName);
    }

    @Override
    public boolean needFlushToMongo() {
        return this.cachedMongoData.size() > 0;
    }

    @Override
    public void beforeShutdown() {
        destory();
    }
}
