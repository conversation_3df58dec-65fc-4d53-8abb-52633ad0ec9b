package com.yiwise.dialogflow.service.impl.llm;

import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.MdcLogIdLifterTransformer;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.entity.bo.*;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.enums.llm.*;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.reactor.Accumulator;
import com.yiwise.dialogflow.reactor.FluxAccumulator;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.impl.StepServiceImpl;
import com.yiwise.dialogflow.service.impl.VariableServiceImpl;
import com.yiwise.dialogflow.service.llm.LlmStepConfigService;
import com.yiwise.dialogflow.service.operationlog.LlmStepConfigOperationLogService;

import com.yiwise.dialogflow.utils.AnswerTextUtils;
import java_cup.production;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.yiwise.dialogflow.service.impl.StepServiceImpl.MAIN_FIRST_ORDER_NUM;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LlmStepConfigServiceImpl implements LlmStepConfigService, RobotResourceService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private StepService stepService;

    @Resource
    private LlmStepConfigOperationLogService llmStepConfigOperationLogService;

    @Resource
    private DependResourceService dependResourceService;

    @Resource
    private SourceRefService sourceRefService;

    @Resource
    private BotService botService;

    @Resource
    private VariableService variableService;

    @Resource(name = "llmChatClient")
    private WebClient webClient;

    private DependentResourceBO prepareResource(Long botId) {
        DependentResourceBO.Condition condition = new DependentResourceBO.Condition(botId);
        condition.step().variable().knowledge().specialAnswerConfig();
        return dependResourceService.generateByCondition(condition);
    }

    private void autoCreateVarIfNotExists(LlmStepConfigPO config, Long userId) {
        List<String> textList = new ArrayList<>();
        if (StringUtils.isNotBlank(config.getRoleDesc())) {
            textList.add(config.getRoleDesc());
        }
        if (StringUtils.isNotBlank(config.getBackground())) {
            textList.add(config.getBackground());
        }
        if (CollectionUtils.isNotEmpty(config.getCollectTaskConfigList())) {
            for (LlmStepCollectTaskConfigPO collectTaskConfig : config.getCollectTaskConfigList()) {
                if (StringUtils.isNotBlank(collectTaskConfig.getDesc())) {
                    textList.add(collectTaskConfig.getDesc());
                }
                if (StringUtils.isNotBlank(collectTaskConfig.getGuideAnswer())) {
                    textList.add(collectTaskConfig.getGuideAnswer());
                }
            }
        }
        if (StringUtils.isNotBlank(config.getPrompt())) {
            textList.add(config.getPrompt());
        }
        if (CollectionUtils.isNotEmpty(textList)) {
            variableService.autoCreateIfNotExists(config.getBotId(), textList, VariableTypeEnum.CUSTOM, userId);
        }
    }

    private void validateParam(StepPO step, LlmStepConfigPO entity, DependentResourceBO resource) {
        LlmStepTypeEnum llmStepType = step.getLlmStepType();
        if (LlmStepTypeEnum.isCollectTask(llmStepType)) {
            if (StringUtils.isBlank(entity.getRoleDesc())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "角色描述不能为空");
            }
            if (CollectionUtils.isEmpty(entity.getCollectTaskConfigList())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "采集任务配置不能为空");
            }
            int index = 1;
            for (LlmStepCollectTaskConfigPO config : entity.getCollectTaskConfigList()) {
                if (StringUtils.isBlank(config.getDesc())) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("%s描述不能为空", "采集任务" + index));
                }
                config.validateVariableAssign("采集任务" + index, resource);
                index++;
            }
        }
        if (LlmStepTypeEnum.isFreeConfig(llmStepType) && StringUtils.isBlank(entity.getPrompt())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "提示词不能为空");
        }
        entity.validateVariableAssign("", resource);
        List<String> errMsgList = entity.validMismatchAndInterruptConflict(String.format("%s[%s]", step.getName(), step.getLabel()), resource);
        if (CollectionUtils.isNotEmpty(errMsgList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, String.join(";", errMsgList));
        }
        if (step.isLlmStep() && Objects.nonNull(step.getOrderNum()) && step.getOrderNum() <= MAIN_FIRST_ORDER_NUM) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "大模型流程不能作为第一个流程");
        }
        if (BooleanUtils.isTrue(entity.getEnableUserSilence())) {
            if (Objects.isNull(entity.getCustomUserSilenceSecond())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "无应答时长不能为空");
            }
            if (entity.getCustomUserSilenceSecond() < 0.1 || entity.getCustomUserSilenceSecond() > 10) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "无应答时长范围为0.1-10秒");
            }
        }
    }

    private void fixEntity(LlmStepConfigPO config, StepPO step) {
        if (LlmStepTypeEnum.isCollectTask(step.getLlmStepType())) {
            config.setEnableAssign(false);
            config.setVariableAssignConfigList(Collections.emptyList());
            config.setPrompt(null);
        }
        if (LlmStepTypeEnum.isFreeConfig(step.getLlmStepType())) {
            config.setRoleDesc(null);
            config.setBackground(null);
            config.setCollectTaskConfigList(Collections.emptyList());
        }
    }

    @Override
    public LlmStepConfigPO save(LlmStepConfigPO config, Long userId) {
        Long botId = config.getBotId();
        String stepId = config.getStepId();
        StepPO step = stepService.getPOById(botId, stepId);
        if (Objects.isNull(step)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程不存在");
        }
        if (!step.isLlmStep()) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "仅支持大模型流程配置");
        }
        fixEntity(config, step);
        DependentResourceBO resource = prepareResource(botId);
        validateParam(step, config, resource);

        autoCreateVarIfNotExists(config, userId);

        LocalDateTime now = LocalDateTime.now();
        LlmStepConfigPO oldConfig = getByStepId(botId, stepId);
        if (Objects.nonNull(oldConfig)) {
            config.setId(oldConfig.getId());
            config.setUpdateTime(now);
            config.setUpdateUserId(userId);
            mongoTemplate.save(config, LlmStepConfigPO.COLLECTION_NAME);
            deleteSourceRef(botId, oldConfig.getId());
            llmStepConfigOperationLogService.update(step.getName(), oldConfig, config, userId, resource);
        } else {
            config.setId(null);
            config.setCreateTime(now);
            config.setUpdateTime(now);
            config.setCreateUserId(userId);
            config.setUpdateUserId(userId);
            mongoTemplate.save(config, LlmStepConfigPO.COLLECTION_NAME);
            llmStepConfigOperationLogService.create(botId, step.getName(), userId);
        }
        addSourceRef(step, config);
        botService.onUpdateBotResource(botId);
        return config;
    }

    private void addSourceRef(StepPO step, LlmStepConfigPO llmStepConfig) {
        Set<String> dependVariableIdSet = llmStepConfig.calDependVariableIdSet(dependResourceService.generateByCondition(new DependentResourceBO.Condition(llmStepConfig.getBotId()).variable()));
        if (CollectionUtils.isNotEmpty(dependVariableIdSet)) {
            sourceRefService.saveSourceRef(buildParam(step, llmStepConfig, dependVariableIdSet));
        }
    }

    private void updateSourceRef(StepPO step, LlmStepConfigPO llmStepConfig) {
        deleteSourceRef(llmStepConfig.getBotId(), llmStepConfig.getId());
        addSourceRef(step, llmStepConfig);
    }

    private void deleteSourceRef(Long botId, String id) {
        sourceRefService.deleteSourceByRefIdList(botId, Collections.singletonList(id), IntentRefTypeEnum.LLM_STEP_CONFIG);
    }

    private SourceRefBO buildParam(StepPO step, LlmStepConfigPO llmStepConfig, Set<String> sourceIdSet) {
        SourceRefBO sourceRefBO = new SourceRefBO();
        sourceRefBO.setSourceIdSet(sourceIdSet);
        sourceRefBO.setBotId(step.getBotId());
        sourceRefBO.setParentRefId(step.getId());
        sourceRefBO.setParentRefType(IntentRefTypeEnum.STEP);
        sourceRefBO.setRefLabel(step.getLabel());
        sourceRefBO.setSourceType(SourceTypeEnum.VARIABLE);
        sourceRefBO.setRefId(llmStepConfig.getId());
        sourceRefBO.setRefType(IntentRefTypeEnum.LLM_STEP_CONFIG);
        return sourceRefBO;
    }

    @Override
    public LlmStepConfigPO getByStepId(Long botId, String stepId) {
        return mongoTemplate.findOne(Query.query(Criteria.where("botId").is(botId).and("stepId").is(stepId)), LlmStepConfigPO.class);
    }

    @Override
    public void deleteByStepIdList(Long botId, List<String> stepIdList) {
        List<LlmStepConfigPO> llmStepConfigList = mongoTemplate.findAllAndRemove(Query.query(Criteria.where("botId").is(botId).and("stepId").in(stepIdList)), LlmStepConfigPO.class);
        if (CollectionUtils.isNotEmpty(llmStepConfigList)) {
            sourceRefService.deleteSourceByRefIdList(botId, llmStepConfigList.stream().map(LlmStepConfigPO::getId).collect(Collectors.toList()), IntentRefTypeEnum.LLM_STEP_CONFIG);
        }
    }

    @Override
    public void copyInBot(Long botId, String srcStepId, StepPO targetStep) {
        LlmStepConfigPO config = getByStepId(botId, srcStepId);
        if (Objects.nonNull(config)) {
            config.setId(null);
            config.setStepId(targetStep.getId());
            mongoTemplate.save(config, LlmStepConfigPO.COLLECTION_NAME);
            addSourceRef(targetStep, config);
        }
    }

    @Override
    public void sync(LlmStepConfigPO srcLlmStepConfig, StepPO targetStep, Map<String, String> stepIdMapping, Map<String, String> knowledgeIdMapping,
                     Map<String, String> specialAnswerIdMapping, Map<String, String> variableIdMapping) {
        String targetStepId = targetStep.getId();
        Long targetBotId = targetStep.getBotId();
        LlmStepConfigPO oldConfig = getByStepId(targetBotId, targetStepId);
        if (Objects.isNull(oldConfig)) {
            srcLlmStepConfig.setId(null);
        } else {
            srcLlmStepConfig.setId(oldConfig.getId());
        }
        srcLlmStepConfig.setBotId(targetBotId);
        srcLlmStepConfig.setStepId(targetStepId);
        srcLlmStepConfig.mapMismatch(stepIdMapping, knowledgeIdMapping, specialAnswerIdMapping);
        srcLlmStepConfig.mapUninterrupted(stepIdMapping, knowledgeIdMapping, specialAnswerIdMapping, Collections.emptyMap());
        srcLlmStepConfig.mapLlmStepVariableAssign(variableIdMapping);
        mongoTemplate.save(srcLlmStepConfig, LlmStepConfigPO.COLLECTION_NAME);
        if (Objects.nonNull(oldConfig)) {
            deleteSourceRef(targetBotId, oldConfig.getId());
        }
        addSourceRef(targetStep, srcLlmStepConfig);
    }

    @Override
    public List<LlmStepConfigPO> getByBotId(Long botId) {
        return mongoTemplate.find(Query.query(Criteria.where("botId").is(botId)), LlmStepConfigPO.class);
    }

    @Override
    public void saveToSnapshot(RobotResourceContext context) {
        context.getSnapshot().setLlmStepConfigList(getByBotId(context.getSrcBotId()));
    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {
        List<LlmStepConfigPO> configList = context.getSnapshot().getLlmStepConfigList();
        if (CollectionUtils.isEmpty(configList)) {
            return;
        }
        ResourceCopyReferenceMappingBO mapping = context.getResourceCopyReferenceMapping();
        Map<String, String> stepIdMapping = mapping.getStepIdMapping();
        Map<String, String> llmStepConfigIdMapping = mapping.getLlmStepConfigIdMapping();
        for (LlmStepConfigPO config : configList) {
            String newId = new ObjectId().toString();
            llmStepConfigIdMapping.put(config.getId(), newId);
            config.setId(newId);
            config.setBotId(context.getTargetBotId());
            config.setStepId(stepIdMapping.get(config.getStepId()));
            mapping.mappingMismatch(config);
            mapping.mappingUninterrupted(config);
            mapping.mappingLlmStepVariableAssign(config);
            mongoTemplate.save(config, LlmStepConfigPO.COLLECTION_NAME);
        }
    }

    @Override
    public void validateResource(RobotResourceContext context) {
        DependentResourceBO dependentResource = context.getDependentResource();
        Map<String, StepPO> stepMap = dependentResource.getStepMap();
        List<LlmStepConfigPO> configList = context.getSnapshot().getLlmStepConfigList();
        if (CollectionUtils.isEmpty(configList)) {
            configList = Collections.emptyList();
        }
        Map<String, LlmStepConfigPO> configMap = MyCollectionUtils.listToMap(configList, LlmStepConfigPO::getStepId);
        for (Map.Entry<String, StepPO> entry : stepMap.entrySet()) {
            String stepId = entry.getKey();
            StepPO step = entry.getValue();
            if (step.isLlmStep()) {
                if (!configMap.containsKey(stepId)) {
                    SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                            .builder()
                            .resourceType(BotResourceTypeEnum.LLM_STEP)
                            .resourceId(step.getId())
                            .failMsg(String.format("流程[%s] %s", step.getName(), "大模型流程配置不存在"))
                            .resourceName(step.getName())
                            .resourceLabel(step.getLabel())
                            .build();
                    context.getInvalidMsgList().add(msg);
                    continue;
                }
                LlmStepConfigPO config = configMap.get(stepId);
                try {
                    validateParam(step, config, context.getDependentResource());
                } catch (ComException e) {
                    SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg
                            .builder()
                            .resourceType(BotResourceTypeEnum.LLM_STEP)
                            .resourceId(step.getId())
                            .failMsg(e.getDetailMsg())
                            .resourceName(step.getName())
                            .resourceLabel(step.getLabel())
                            .build();
                    context.getInvalidMsgList().add(msg);
                }
            }
        }
    }

    @Override
    public List<Class<? extends RobotResourceService>> dependsOn() {
        return Arrays.asList(StepServiceImpl.class, VariableServiceImpl.class);
    }

    @Override
    public Flux<ResultObject<String>> promptOptimize(String prompt) {
        String url = ApplicationConstant.ALGORITHM_LLM_PROMPT_OPTIMIZE_URL;
        if (StringUtils.isBlank(url) || StringUtils.isBlank(prompt)) {
            return Flux.empty();
        }

        Map<String, String> params = new HashMap<>(2);
        params.put("prompt", prompt);

        return webClient.post()
                .uri(url)
                .body(BodyInserters.fromValue(JsonUtils.object2String(params)))
                .accept(MediaType.TEXT_EVENT_STREAM)
                .retrieve()
                .bodyToFlux(String.class)
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .transformDeferred(flux -> FluxAccumulator.accumulator(flux, new ResponseAnswerAccumulator()))
                .map(ResultObject::success);
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    static class Res implements Serializable {

        Integer code;

        String prompt;

        String info;

        public boolean isSuccess() {
            return Objects.equals(0, code);
        }
    }

    public static class ResponseAnswerAccumulator implements Accumulator<String, String> {
        private volatile boolean complete = false;

        @Override
        public Optional<String> accumulate(String responseBody) {
            log.debug("responseBody: [{}]", responseBody);
            if (complete) {
                log.debug("Already completed, ignore response body: [{}]", responseBody);
                return Optional.empty();
            }
            if (StringUtils.isBlank(responseBody)) {
                return Optional.empty();
            }

            try {
                Res response = JsonUtils.string2Object(responseBody, Res.class);
                if (response != null && response.isSuccess()) {
                    return Optional.ofNullable(response.getPrompt());
                }
                return Optional.empty();
            } catch (Exception e) {
                log.error("Failed to parse response body: [{}]", responseBody, e);
            }
            return Optional.empty();
        }

        @Override
        public Optional<String> onComplete() {
            complete = true;
            return Optional.empty();
        }
    }

    @Override
    public void resetResourceReferenceInfo(Long botId) {
        List<LlmStepConfigPO> configList = getByBotId(botId);
        Map<String, StepPO> stepMap = MyCollectionUtils.listToMap(stepService.getAllListByBotId(botId), StepPO::getId);
        if (CollectionUtils.isEmpty(configList)) {
            return;
        }
        for (LlmStepConfigPO config : configList) {
            String stepId = config.getStepId();
            if (!stepMap.containsKey(stepId)) {
                continue;
            }
            updateSourceRef(stepMap.get(stepId), config);
        }
    }

    @Override
    public void updateStepOnVariableRename(Long botId, String variableId, String oldVariableName, String newVariableName, Long userId) {
        List<LlmStepConfigPO> configList = getByBotId(botId);
        if (CollectionUtils.isEmpty(configList)) {
            return;
        }

        List<LlmStepConfigPO> updateList = new ArrayList<>();

        for (LlmStepConfigPO config : configList) {
            boolean updated = false;
            if (StringUtils.isNotBlank(config.getRoleDesc())) {
                String newRoleDesc = AnswerTextUtils.convertTemplateOnVariableRename(config.getRoleDesc(), oldVariableName, newVariableName);
                if (!config.getRoleDesc().equals(newRoleDesc)) {
                    config.setRoleDesc(newRoleDesc);
                    updated = true;
                }
            }
            if (StringUtils.isNotBlank(config.getBackground())) {
                String newBackground = AnswerTextUtils.convertTemplateOnVariableRename(config.getBackground(), oldVariableName, newVariableName);
                if (!config.getBackground().equals(newBackground)) {
                    config.setBackground(newBackground);
                    updated = true;
                }
            }

            if (StringUtils.isNotBlank(config.getPrompt())) {
                String newPrompt = AnswerTextUtils.convertTemplateOnVariableRename(config.getPrompt(), oldVariableName, newVariableName);
                if (!config.getPrompt().equals(newPrompt)) {
                    config.setPrompt(newPrompt);
                    updated = true;
                }
            }

            if (CollectionUtils.isNotEmpty(config.getCollectTaskConfigList())) {
                for (LlmStepCollectTaskConfigPO taskConfig : config.getCollectTaskConfigList()) {
                    if (StringUtils.isNotBlank(taskConfig.getDesc())) {
                        String newDesc = AnswerTextUtils.convertTemplateOnVariableRename(taskConfig.getDesc(), oldVariableName, newVariableName);
                        if (!taskConfig.getDesc().equals(newDesc)) {
                            taskConfig.setDesc(newDesc);
                            updated = true;
                        }
                    }
                    if (StringUtils.isNotBlank(taskConfig.getGuideAnswer())) {
                        String newGuideAnswer = AnswerTextUtils.convertTemplateOnVariableRename(taskConfig.getGuideAnswer(), oldVariableName, newVariableName);
                        if (!taskConfig.getGuideAnswer().equals(newGuideAnswer)) {
                            taskConfig.setGuideAnswer(newGuideAnswer);
                            updated = true;
                        }
                    }
                    if (CollectionUtils.isNotEmpty(taskConfig.getVariableAssignConfigList())) {
                        for (LlmStepVariableAssignConfigPO assignConfig : taskConfig.getVariableAssignConfigList()) {
                            if (StringUtils.isNotBlank(assignConfig.getDesc())) {
                                String newDesc = AnswerTextUtils.convertTemplateOnVariableRename(assignConfig.getDesc(), oldVariableName, newVariableName);
                                if (!StringUtils.equals(assignConfig.getDesc(), newDesc)) {
                                    assignConfig.setDesc(newDesc);
                                    updated = true;
                                }
                            }
                        }
                    }
                }
            }

            if (updated) {
                updateList.add(config);
            }
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            updateList.forEach(config -> {
                mongoTemplate.save(config, LlmStepConfigPO.COLLECTION_NAME);
            });
        }
    }

}