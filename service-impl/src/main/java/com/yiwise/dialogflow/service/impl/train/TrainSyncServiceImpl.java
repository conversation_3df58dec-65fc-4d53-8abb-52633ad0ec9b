package com.yiwise.dialogflow.service.impl.train;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yiwise.base.common.helper.EnvironmentConstants;
import com.yiwise.base.common.utils.PropertyLoaderUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.entity.bo.algorithm.ModelTrainKey;
import com.yiwise.dialogflow.entity.bo.algorithm.TrainItemBO;
import com.yiwise.dialogflow.entity.enums.AlgorithmTrainTypeEnum;
import com.yiwise.dialogflow.entity.enums.SnapshotTypeEnum;
import com.yiwise.dialogflow.entity.po.TrainResultPO;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrErrorCorrectionDetailPO;
import com.yiwise.dialogflow.entity.po.intent.IntentCorpusPO;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.query.IntentCorpusQuery;
import com.yiwise.dialogflow.entity.query.IntentQuery;
import com.yiwise.dialogflow.entity.vo.NameDescVO;
import com.yiwise.dialogflow.entity.vo.TrainDataVO;
import com.yiwise.dialogflow.service.asrmodel.AsrErrorCorrectionService;
import com.yiwise.dialogflow.service.intent.IntentCorpusService;
import com.yiwise.dialogflow.service.intent.IntentService;
import com.yiwise.dialogflow.service.train.ModelVersionService;
import com.yiwise.dialogflow.service.train.TrainService;
import com.yiwise.dialogflow.service.train.TrainSyncService;
import com.yiwise.middleware.redis.service.ObjectRedisTemplate;
import com.yiwise.middleware.redis.service.RedisOpsService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.net.URI;
import java.security.Key;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 当前算法训练逻辑：
 * 1. 后端应用启动时向算法注册当前的环境（算法端环境是隔离的）
 * 2. 页面点击训练后将key存入redis
 * 3. 算法定时轮询redisKeyList，获取待训练的key的列表
 * 4. 算法获取key列表后根据单个key调用queryTrainData获取训练数据
 * 5. 训练完成后调用callback接口，更新训练状态
 *
 * <AUTHOR>
 * @date 2019/6/19 11:42 AM
 **/
@Service
public class TrainSyncServiceImpl implements TrainSyncService {

    private static final Logger logger = LoggerFactory.getLogger(TrainSyncServiceImpl.class);

    @Resource
    private ObjectRedisTemplate objectRedisTemplate;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private IntentService intentService;

    @Resource
    private IntentCorpusService intentCorpusService;

    @Resource
    private ModelVersionService modelVersionService;

    @Resource
    private TrainService trainService;

    @Resource
    private RedisOpsService redisOpsService;

    @Resource
    private AsrErrorCorrectionService asrErrorCorrectionService;

    @PostConstruct
    public void init() {
        // 初始化，注册算法接口
        if (EnvironmentConstants.CURR_ENV.isNotLocal()) {
            Optional<Boolean> register = register();
            register.ifPresent(result -> {
                if (Boolean.TRUE.equals(result)) {
                    logger.info("注册算法端口成功");
                } else {
                    logger.error("注册算法端口失败");
                }
            });
        }
    }

    /**
     * 向算法提供待训练的keyList
     */
    @Override
    public List<TrainDataVO> redisKeyList() {
        String key = RedisKeyCenter.getTrainDataSignalRedisKey();
        Object first = objectRedisTemplate.opsForList().leftPop(key);
        if (Objects.nonNull(first)) {
            String json = first.toString();
            TrainDataVO trainData = JsonUtils.string2Object(json, TrainDataVO.class);
            if (Objects.nonNull(trainData)) {
                String lockKey = RedisKeyCenter.getTrainDataSignalLock(trainData.getTenantId(), trainData.getRobotId(), trainData.getSnapshotType(), trainData.getTrainType());
                logger.info("获取到训练数据，TID={}，RID={}，lockKey={}", trainData.getTenantId(), trainData.getRobotId(), lockKey);
                redisOpsService.delete(lockKey);
                return Lists.newArrayList(trainData);
            }
        }
        return Lists.newArrayList();
    }

    /**
     * 查询训练数据
     */
    @Override
    public TrainDataVO queryTrainData(Long tenantId, String refId, AlgorithmTrainTypeEnum trainType) {
        logger.info("算法模型请求训练数据，TID={}，RID={}", tenantId, refId);
        TrainDataVO trainDataVO = new TrainDataVO();
        trainDataVO.setEnvironment(ApplicationConstant.ALGORITHM_REGISTER_ENV);
        trainDataVO.setTenantId(tenantId);
        trainDataVO.setRobotId(refId);
        trainDataVO.setTrainType(AlgorithmTrainTypeEnum.INTENT);
        trainDataVO.setSnapshotType(SnapshotTypeEnum.PUBLISHED);

        String modelId = null;
        List<TrainItemBO> corpusList = Lists.newArrayList();
        List<TrainItemBO> keywords = Lists.newArrayList();

        switch (trainType) {
            case INTENT:
                processIntent(Long.valueOf(refId), corpusList, keywords);
                // 添加模型历史
                TrainResultPO trainResultPO = trainService.earliestUntrained(ModelTrainKey.of(tenantId, refId, trainType));
                if (Objects.nonNull(trainResultPO)) {
                    modelId = trainResultPO.getModelId();
                }
                if (StringUtils.isEmpty(modelId)) {
                    modelId = modelVersionService.create(tenantId, Long.valueOf(refId), trainType);
                }
                trainDataVO.setModelId(modelId);
                break;
            case ASR_ERROR_CORRECTION:
                AsrErrorCorrectionDetailPO asrErrorCorrectionDetailPO = asrErrorCorrectionService.getByModelId(refId);
                if (CollectionUtils.isNotEmpty(asrErrorCorrectionDetailPO.getCorpusList())) {
                    corpusList = asrErrorCorrectionDetailPO.getCorpusList().stream()
                                         .map(item -> {
                                             TrainItemBO bo = new TrainItemBO();
                                             bo.setText(item);
                                             bo.setRelationId(String.valueOf(refId));
                                             bo.setRelationName(String.valueOf(refId));
                                             return bo;
                                         }).collect(Collectors.toList());
                }
                trainDataVO.setModelId(refId);
                break;
        }

        trainDataVO.setContent(corpusList);
        trainDataVO.setKeywords(keywords);

        return trainDataVO;
    }

    private void processIntent(Long botId, List<TrainItemBO> corpusList, List<TrainItemBO> keywords) {
        // 语料查询
        IntentQuery intentQuery = IntentQuery.builder().botId(botId).build();
        // 关闭分页查询
        intentQuery.setWithPage(false);
        List<IntentPO> intentPOList = intentService.getIntentPOList(intentQuery);
        Map<String, String> intentNameMap = MyCollectionUtils.listToMap(intentPOList, IntentPO::getId, IntentPO::getName);
        List<IntentCorpusPO> intentCorpusPOList = intentCorpusService.scroll(IntentCorpusQuery.builder().botId(botId).build());
        intentCorpusPOList.forEach(intentCorpusPO -> {
            String intentName = intentNameMap.get(intentCorpusPO.getIntentId());
            if (StringUtils.isBlank(intentName)) {
                logger.warn("意图语料数据错误, 意图ID={}不存在, botId={}, name={}", intentCorpusPO.getIntentId(), intentCorpusPO.getBotId(), intentCorpusPO.getName());
                return;
            }
            if (CollectionUtils.isNotEmpty(intentCorpusPO.getCorpusList())) {
                intentCorpusPO.getCorpusList().forEach(corpus -> {
                    TrainItemBO trainItemBO = new TrainItemBO();
                    trainItemBO.setRelationId(intentCorpusPO.getIntentId());
                    trainItemBO.setRelationName(intentNameMap.getOrDefault(intentCorpusPO.getIntentId(), ""));
                    trainItemBO.setText(corpus);
                    corpusList.add(trainItemBO);
                });
            }
            if (CollectionUtils.isNotEmpty(intentCorpusPO.getBuildInCorpusList())) {
                intentCorpusPO.getBuildInCorpusList().forEach(corpus -> {
                    TrainItemBO trainItemBO = new TrainItemBO();
                    trainItemBO.setRelationId(intentCorpusPO.getIntentId());
                    trainItemBO.setRelationName(intentNameMap.getOrDefault(intentCorpusPO.getIntentId(), ""));
                    trainItemBO.setText(corpus);
                    corpusList.add(trainItemBO);
                });
            }
            if (CollectionUtils.isNotEmpty(intentCorpusPO.getRegexList())) {
                intentCorpusPO.getRegexList().forEach(corpus -> {
                    TrainItemBO trainItemBO = new TrainItemBO();
                    trainItemBO.setRelationId(intentCorpusPO.getIntentId());
                    trainItemBO.setRelationName(intentNameMap.getOrDefault(intentCorpusPO.getIntentId(), ""));
                    trainItemBO.setText(corpus);
                    keywords.add(trainItemBO);
                });
            }
            if (CollectionUtils.isNotEmpty(intentCorpusPO.getBuildInRegexList())) {
                intentCorpusPO.getBuildInRegexList().forEach(corpus -> {
                    TrainItemBO trainItemBO = new TrainItemBO();
                    trainItemBO.setRelationId(intentCorpusPO.getIntentId());
                    trainItemBO.setRelationName(intentNameMap.getOrDefault(intentCorpusPO.getIntentId(), ""));
                    trainItemBO.setText(corpus);
                    keywords.add(trainItemBO);
                });
            }
        });
    }

    /**
     * 发送训练信号
     */
    @Override
    public void postTrainSignal(TrainDataVO trainDataVO) {
        Long tenantId = trainDataVO.getTenantId();
        String robotId = trainDataVO.getRobotId();
        AlgorithmTrainTypeEnum trainType = trainDataVO.getTrainType();
        SnapshotTypeEnum snapshotType = trainDataVO.getSnapshotType();
        String domainName = trainDataVO.getDomainName();
        logger.info("发送算法模型训练信号，TID={}，RID={}，训练类型为{}，发布版本为{}, domainName:{}", tenantId, robotId, trainType, snapshotType, domainName);
        String key = RedisKeyCenter.getTrainDataSignalRedisKey();
        String hashKey = RedisKeyCenter.getTrainDataSignalLock(tenantId, robotId, snapshotType, trainType);
        // 去重, 即任务已经在队列中了, 就不需要再次入队了
        Boolean lock = redisOpsService.setIfAbsent(hashKey, 1,  600);
        if (BooleanUtils.isTrue(lock)) {
            objectRedisTemplate.opsForList().rightPush(key, JsonUtils.object2String(trainDataVO));
            objectRedisTemplate.opsForList().getOperations().expire(key, 7, TimeUnit.DAYS);
        } else {
            logger.info("当前任务已经在队列中了, 无需再次入队, hashKey:{}", hashKey);
        }
    }

    /**
     * 领域模型列表
     */
    @Override
    public List<NameDescVO> domainList() {
        String domainList = PropertyLoaderUtils.getProperty("algorithm.domain.list.url");
        if (StringUtils.isNotEmpty(domainList)) {
            try {
                ResponseEntity<String> response = restTemplate.getForEntity(URI.create(domainList), String.class);
                if (response.getStatusCodeValue() == 200) {
                    String body = response.getBody();
                    JSONObject jsonObject = JSON.parseObject(body);
                    String string = jsonObject.getString("domain_names");
                    List<NameDescVO> nameDescVOList = Lists.newArrayList(new NameDescVO("CUSTOMIZED", "自定义模型"));
                    List<NameDescVO> list = JSONArray.parseArray(string, NameDescVO.class);
                    if (CollectionUtils.isNotEmpty(list)) {
                        nameDescVOList.addAll(list);
                    }
                    return nameDescVOList;
                }
            } catch (Exception e) {
                logger.error("获取领域模型列表错误", e);
            }
        }
        return Lists.newArrayList();
    }

    /**
     * 向算法平台注册
     */
    private Optional<Boolean> register() {
        String registerUrl = PropertyLoaderUtils.getProperty("algorithm.register.url");
        String redisKeyUrl = PropertyLoaderUtils.getProperty("algorithm.redisKey.url");
        String dataUrl = PropertyLoaderUtils.getProperty("algorithm.data.url");
        String callbackUrl = PropertyLoaderUtils.getProperty("algorithm.callback.url");
        if (StringUtils.isNotEmpty(registerUrl)) {
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);

                Map<String, Object> params = new HashMap<>();
                params.put("environment", ApplicationConstant.ALGORITHM_REGISTER_ENV);
                params.put("updateKey", redisKeyUrl);
                params.put("dataUrl", dataUrl);
                params.put("callbackUrl", callbackUrl);
                HttpEntity<String> entity = new HttpEntity<>(JsonUtils.object2String(params), headers);

                ResponseEntity<String> response = restTemplate.exchange(registerUrl, HttpMethod.POST, entity, new ParameterizedTypeReference<String>() {
                });
                if (response.getStatusCodeValue() == 200) {
                    return Optional.of(true);
                }
            } catch (Exception e) {
                logger.error("注册算法端口错误", e);
                return Optional.of(false);
            }
        }

        return Optional.of(false);
    }
}
