package com.yiwise.dialogflow.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.batch.enums.PlatformTypeEnum;
import com.yiwise.batch.model.dto.SheetInfoDTO;
import com.yiwise.batch.model.po.SpringBatchJobTypePO;
import com.yiwise.batch.model.vo.JobStartResultVO;
import com.yiwise.batch.service.BasicBatchService;
import com.yiwise.batch.service.SpringBatchJobTypeService;
import com.yiwise.dialogflow.common.BatchConstant;
import com.yiwise.dialogflow.entity.po.callout.CallDetailPO;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.entity.po.semantic.analysis.SemanticAnalysisConditionPO;
import com.yiwise.dialogflow.entity.po.semantic.analysis.SemanticAnalysisTemplatePO;
import com.yiwise.dialogflow.entity.vo.SemanticAnalysisTemplateVO;
import com.yiwise.dialogflow.mapper.CallDetailPOMapper;
import com.yiwise.dialogflow.service.SemanticAnalysisService;
import com.yiwise.dialogflow.service.remote.UserService;
import com.yiwise.middleware.mysql.aop.annotation.TargetDataSource;
import com.yiwise.middleware.mysql.common.DataSourceEnum;
import javaslang.Tuple;
import javaslang.Tuple3;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.batch.core.Job;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 自定义语义分析工具
 *
 * <AUTHOR>
 * @date 2022/11/28
 */
@Service
public class SemanticAnalysisServiceImpl implements SemanticAnalysisService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private CallDetailPOMapper callDetailPOMapper;

    @Resource
    private SpringBatchJobTypeService springBatchJobTypeService;

    @Resource
    private BasicBatchService basicBatchService;

    @Resource
    private Job semanticAnalysisExportJob;

    @Resource
    private UserService userService;

    @Override
    public void save(SemanticAnalysisTemplatePO templatePO) {
        if (StringUtils.isEmpty(templatePO.getTemplateId())) {
            templatePO.setCreateTime(LocalDateTime.now());
            templatePO.setCreateUserId(templatePO.getUpdateUserId());
        }

        if (CollectionUtils.isNotEmpty(templatePO.getConditionList())) {
            templatePO.getConditionList().forEach(conditionPO -> {
                if (Objects.isNull(conditionPO.getCreateTime())) {
                    conditionPO.setCreateTime(LocalDateTime.now());
                }
                conditionPO.setUpdateTime(LocalDateTime.now());
            });
        }

        templatePO.setUpdateTime(LocalDateTime.now());
        mongoTemplate.save(templatePO);
    }

    @Override
    public void copy(String templateId, Long currentUserId) {
        SemanticAnalysisTemplatePO templatePO = get(templateId);
        SemanticAnalysisTemplatePO updatePO = MyBeanUtils.copy(templatePO, SemanticAnalysisTemplatePO.class);
        updatePO.setTemplateId(null);
        updatePO.setTemplateName(updatePO.getTemplateName() + "-复制");
        updatePO.setCreateTime(LocalDateTime.now());
        updatePO.setUpdateTime(LocalDateTime.now());
        updatePO.setCreateUserId(currentUserId);
        updatePO.setUpdateUserId(currentUserId);
        mongoTemplate.save(updatePO);
    }

    @Override
    public void delete(String templateId) {
        Query query = Query.query(Criteria.where("_id").is(templateId));
        mongoTemplate.remove(query, SemanticAnalysisTemplatePO.class);
    }

    @Override
    public PageResultObject<SemanticAnalysisTemplateVO> list(Integer pageNum, Integer pageSize) {
        Query query = new Query();
        // 减少数据量
        query.fields().exclude("callDetailIdList");
        long count = mongoTemplate.count(query, SemanticAnalysisTemplatePO.class);
        query.with(Sort.by(Sort.Direction.DESC, "_id"));
        query.with(PageRequest.of(pageNum - 1, pageSize));
        List<SemanticAnalysisTemplateVO> list = mongoTemplate.find(query, SemanticAnalysisTemplateVO.class);
//        list.stream()
//                .filter(templatePO -> CollectionUtils.isNotEmpty(templatePO.getConditionList()))
//                .forEach(templatePO -> templatePO.setTotalCount(templatePO.getConditionList().stream().map(this::corpusSize).reduce(Integer::sum).orElse(0)));
        List<Long> userIdList = list.stream().map(SemanticAnalysisTemplatePO::getCreateUserId).distinct().collect(Collectors.toList());
        Map<Long, String> userIdNameMap = MyCollectionUtils.listToConvertMap(userService.getUserByIdList(userIdList), UserPO::getUserId, UserPO::getName);
        list.forEach(p -> p.setCreateUserName(userIdNameMap.get(p.getCreateUserId())));
        return PageResultObject.of(list, pageNum, pageSize, (int) count);
    }

    @Override
    public void clearCorpus(String templateId, Long currentUserId) {
        SemanticAnalysisTemplatePO templatePO = get(templateId);
        templatePO.setConditionList(Lists.newArrayList());
        templatePO.setCallDetailIdSet(Sets.newHashSet());
        templatePO.setTotalCount(0);
        templatePO.setUpdateTime(LocalDateTime.now());
        templatePO.setUpdateUserId(currentUserId);
        mongoTemplate.save(templatePO);
    }

    /**
     * 分析并导出
     */
    @Override
    public JobStartResultVO exportCorpus(String templateId, Long currentUserId) {
        Long tenantId = 0L;
        SemanticAnalysisTemplatePO templatePO = get(templateId);
        Assert.notNull(templatePO, "templatePO不能为空");
        Boolean exportDetail = templatePO.getExportDetail();

        List<Tuple3<String, String, Object>> param = Lists.newArrayList(
                Tuple.of("TEMPLATE_PO", "string", JsonUtils.object2String(templatePO)),
                Tuple.of("QUERY_ID", "string", "com.yiwise.dialogflow.mapper.CallDetailPOMapper.selectRecords"),
                Tuple.of("EXPORT_DETAIL", "string", String.valueOf(exportDetail)),
                Tuple.of("MDC_LOG_ID", "string", MDC.get("MDC_LOG_ID"))
        );

        SpringBatchJobTypePO jobType = springBatchJobTypeService.getByName("SEMANTIC_ANALYSIS_DETAIL_EXPORT_STEP");
        SystemEnum systemType = SystemEnum.OPE;
        Job job = semanticAnalysisExportJob;
        List<SheetInfoDTO> sheetInfoDTOList = BatchConstant.SheetInfoList.SEMANTIC_ANALYSIS_STAT_TUPLE_LIST;
        if (BooleanUtils.isTrue(exportDetail)) {
            sheetInfoDTOList = BatchConstant.SheetInfoList.SEMANTIC_ANALYSIS_DETAIL_TUPLE_LIST;
        }

        return basicBatchService.exportWithQuery(DataSourceEnum.BASE_ADB, tenantId, currentUserId, systemType, job, jobType, "自定义语义分析-" + templatePO.getTemplateName(),
                param, sheetInfoDTOList, false, PlatformTypeEnum.AICC);
    }

    @TargetDataSource(value = DataSourceEnum.BASE_ADB)
    @Override
    public List<CallDetailPO> selectRecords(SemanticAnalysisConditionPO conditionPO) {
        conditionPO.setEndDate(conditionPO.getEndDate().plusDays(1));
        return callDetailPOMapper.selectRecords(conditionPO);
    }

    @Override
    public SemanticAnalysisTemplatePO get(String templateId) {
        return mongoTemplate.findById(templateId, SemanticAnalysisTemplatePO.class);
    }

    @Override
    public Integer corpusSize(SemanticAnalysisConditionPO conditionPO) {
        conditionPO.setEndDate(conditionPO.getEndDate().plusDays(1));
        return callDetailPOMapper.countRecords(conditionPO);
    }
}
