package com.yiwise.dialogflow.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.yiwise.base.common.helper.ServerInfoConstants;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.websocket.BasicMsg;
import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum;
import com.yiwise.dialogflow.service.OperationLogService;
import com.yiwise.middleware.redis.service.ObjectRedisTemplate;
import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.entity.bo.BotResourceLockInfo;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.service.BotResourceModifyLockService;
import com.yiwise.dialogflow.service.remote.UserService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

import static com.yiwise.dialogflow.common.ApplicationConstant.WEBSOCKET_BOT_RESOURCE_LOCK_SUBSCRIPT_URL;

@Slf4j
@Service
public class BotResourceModifyLockServiceImpl implements BotResourceModifyLockService {
    private static final long HEARTBEAT_TIMEOUT_SECOND = 35;

    @Resource
    private ObjectRedisTemplate objectRedisTemplate;

    @Lazy
    @Resource
    private SimpMessagingTemplate simpMessagingTemplate;

    @Resource
    private UserService userService;

    @Resource
    private OperationLogService operationLogService;

    private final ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(1);

    private final Cache<String, BotResourceLockInfo> SESSION_CACHE = CacheBuilder
            .newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(10, java.util.concurrent.TimeUnit.MINUTES)
            .build();

    @PostConstruct
    public void init() {
        scheduledExecutorService.scheduleAtFixedRate(this::processAll, 10, 5, java.util.concurrent.TimeUnit.SECONDS);
    }

    @Override
    public void onActive(String sessionId, Long userId) {
        BotResourceLockInfo botResourceLockInfo = SESSION_CACHE.getIfPresent(sessionId);
        if (Objects.isNull(botResourceLockInfo)) {
            botResourceLockInfo = new BotResourceLockInfo();
        }
        botResourceLockInfo.setBotId(0L);
        botResourceLockInfo.setUserId(userId);
        Optional<UserPO> userInfo = userService.getUserByIdIfPresent(userId);
        if (userInfo.isPresent()) {
            botResourceLockInfo.setUserName(userInfo.get().getName());
        }
        botResourceLockInfo.setSessionId(sessionId);
        botResourceLockInfo.setConnectTime(LocalDateTime.now());
        botResourceLockInfo.setLastHeartbeatTime(LocalDateTime.now());
        botResourceLockInfo.setDisconnectTime(null);
        SESSION_CACHE.put(sessionId, botResourceLockInfo);
    }

    @Override
    public void onInactive(String sessionId) {
        BotResourceLockInfo botResourceLockInfo = SESSION_CACHE.getIfPresent(sessionId);
        if (Objects.nonNull(botResourceLockInfo)) {
            botResourceLockInfo.setDisconnectTime(LocalDateTime.now());
            processAll();
            operationLogService.save(botResourceLockInfo.getBotId(), OperationLogTypeEnum.PAGE_STAY, OperationLogResourceTypeEnum.EXIT_PAGE, "退出当前话术的页面", botResourceLockInfo.getUserId());
        } else {
            log.warn("onInactive, botResourceLockInfo is null, sessionId: {}", sessionId);
        }
    }

    @Override
    public void onHeartbeat(String sessionId) {
        BotResourceLockInfo botResourceLockInfo = SESSION_CACHE.getIfPresent(sessionId);
        if (Objects.isNull(botResourceLockInfo)) {
            log.warn("onHeartbeat, botResourceLockInfo is null, sessionId: {}", sessionId);
            botResourceLockInfo = new BotResourceLockInfo();
            botResourceLockInfo.setBotId(0L);
            botResourceLockInfo.setUserId(0L);
            botResourceLockInfo.setSessionId(sessionId);
            botResourceLockInfo.setConnectTime(LocalDateTime.now());
        }
        botResourceLockInfo.setLastHeartbeatTime(LocalDateTime.now());
        botResourceLockInfo.setDisconnectTime(null);
        SESSION_CACHE.put(sessionId, botResourceLockInfo);
    }

    @Override
    public void onSubscribe(String sessionId, Long botId) {
        BotResourceLockInfo botResourceLockInfo = SESSION_CACHE.getIfPresent(sessionId);
        if (Objects.nonNull(botResourceLockInfo)) {
            Long oldBotId = botResourceLockInfo.getBotId();
            if (Objects.nonNull(oldBotId) && oldBotId > 0) {
                // 切换了bot
                botRemoveSession(oldBotId, sessionId);
                sendMsg(oldBotId, Collections.singleton(sessionId));
                operationLogService.save(oldBotId, OperationLogTypeEnum.PAGE_STAY, OperationLogResourceTypeEnum.EXIT_PAGE, "退出当前话术的页面", botResourceLockInfo.getUserId());
            }
            botResourceLockInfo.setBotId(botId);
            botResourceLockInfo.setLastHeartbeatTime(LocalDateTime.now());
            botResourceLockInfo.setDisconnectTime(null);
            SESSION_CACHE.put(sessionId, botResourceLockInfo);
            operationLogService.save(botId, OperationLogTypeEnum.PAGE_STAY, OperationLogResourceTypeEnum.ENTER_PAGE, "进入当前话术的页面", botResourceLockInfo.getUserId());
            processAll();
        } else {
            log.warn("onSubscribe, botResourceLockInfo is null, sessionId: {}, botId: {}", sessionId, botId);
        }
    }

    private void botRemoveSession(Long botId, String sessionId) {
        String redisKey = RedisKeyCenter.getBotResourceLockKey(botId);
        deleteSessionId(redisKey, sessionId);
    }

    /**
     * 刷新到redis中, 并推送消息
     */
    private void processAll() {
        try {
            Set<String> sessionIdSet = SESSION_CACHE.asMap().keySet();
            if (sessionIdSet.isEmpty()) {
                log.info("flushAndSendMsg, sessionIdSet is empty");
                return;
            }
            Set<Long> botIdSet = new HashSet<>();
            Map<Long, Set<String>>  botIdSessionIdMap = new HashMap<>();
            for (String sessionId : sessionIdSet) {
                process(sessionId).ifPresent(botId -> {
                    botIdSet.add(botId);
                    botIdSessionIdMap.computeIfAbsent(botId, k -> new HashSet<>()).add(sessionId);
                });
            }
            botIdSessionIdMap.forEach(this::sendMsg);
        } catch (Exception e) {
            log.warn("asyncProcess error", e);
        }
    }

    /**
     */
    private Optional<Long> process(String sessionId) {
        BotResourceLockInfo info = SESSION_CACHE.getIfPresent(sessionId);
        if (Objects.isNull(info) || Objects.isNull(info.getBotId()) || info.getBotId() <= 0) {
            return Optional.empty();
        }
        String redisKey = RedisKeyCenter.getBotResourceLockKey(info.getBotId());
        setExpire(redisKey);
        if (Objects.nonNull(info.getDisconnectTime()) || isTimeout(info)) {
            // 断开连接了
            SESSION_CACHE.invalidate(sessionId);
            deleteSessionId(redisKey, sessionId);
        } else {
            pushLockInfo(redisKey, sessionId, info);
            return Optional.of(info.getBotId());
        }
        return Optional.empty();
    }

    private void setExpire(String redisKey) {
        objectRedisTemplate.expire(redisKey, 10, java.util.concurrent.TimeUnit.MINUTES);
    }

    private void deleteSessionId(String redisKey, String sessionId) {
        objectRedisTemplate.opsForHash().delete(redisKey, sessionId);
    }

    private void pushLockInfo(String redisKey, String sessionId, BotResourceLockInfo info) {
        objectRedisTemplate.opsForHash().put(redisKey, sessionId, info);
    }

    private boolean isTimeout(BotResourceLockInfo info) {
        return isTimeout(info.getLastHeartbeatTime());
    }

    private boolean isTimeout(LocalDateTime heartbeatTime) {
        return Objects.isNull(heartbeatTime)
                || heartbeatTime.plusSeconds(HEARTBEAT_TIMEOUT_SECOND).isBefore(LocalDateTime.now());
    }

    private boolean isTimeout(long heartbeatTimestamp) {
        long currentTimestamp = System.currentTimeMillis();
        return heartbeatTimestamp + HEARTBEAT_TIMEOUT_SECOND * 1000 < currentTimestamp;
    }

    /**
     * 推送某个bot当前同时编辑的信息
     * 从redis中获取数据
     */
    private void sendMsg(Long botId, Set<String> sessionIdSet) {
        if (Objects.isNull(botId) || botId <= 0 || CollectionUtils.isEmpty(sessionIdSet)) {
            log.info("sendMsg, botId is null or sessionIdSet is empty, botId: {}, sessionIdSet: {}", botId, sessionIdSet);
            return;
        }
        String redisKey = RedisKeyCenter.getBotResourceLockKey(botId);
        Map<Object, Object> allSessionMap = objectRedisTemplate.opsForHash().entries(redisKey);
        Set<String> userNameSet = new HashSet<>();
        allSessionMap.forEach((k, v) -> {
            if (v instanceof String) {
                String infoJson = (String) v;
                try {
                    BotResourceLockInfo info = JsonUtils.string2Object(infoJson, BotResourceLockInfo.class);
                    if (isTimeout(info.getLastHeartbeatTime())) {
                        deleteSessionId(redisKey, k.toString());
                    } else {
                        if (StringUtils.isBlank(info.getUserName())) {
                            try {
                                userService.getUserByIdIfPresent(info.getUserId())
                                        .ifPresent(user -> info.setUserName(user.getName()));
                            } catch (Exception e) {
                                log.warn("sendMsg error, infoJson: {}", infoJson, e);
                            }
                        }
                        userNameSet.add(info.getUserName());
                    }
                } catch (Exception e) {
                    log.warn("sendMsg error, infoJson: {}", infoJson, e);
                }
            }
        });
        BasicMsg<MsgPayload> msg = new BasicMsg<>();
        MsgPayload payload = new MsgPayload();
        payload.setUserNameList(new ArrayList<>(userNameSet));
        payload.setBotId(botId);
        payload.setServerHostName(ServerInfoConstants.SERVER_HOSTNAME);
        msg.setInfo(payload);
        msg.setTime(new Date());
        msg.setMsg("多人同时编辑");
        for (String sessionId : sessionIdSet) {
            try {
                // todo 临时兼容, websocket哪里配置有问题, 通过convertAndSendToUser发送过去的和注册上来的destination不一样
                String dest = String.format("%s-user%s", WEBSOCKET_BOT_RESOURCE_LOCK_SUBSCRIPT_URL, sessionId);
                simpMessagingTemplate.convertAndSend(dest, msg);
                log.debug("[LogHub] 用户:{} 正在查看话术:{}", userNameSet, botId);
            } catch (Exception e) {
                log.warn("sendMsg error, sessionId: {}", sessionId, e);
            }
        }
    }

    @Data
    public static class MsgPayload {
        long botId;
        List<String> userNameList;
        String serverHostName;
    }
}
