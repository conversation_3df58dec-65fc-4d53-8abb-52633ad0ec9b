package com.yiwise.dialogflow.service.impl.audio;

import com.google.common.collect.Lists;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import com.yiwise.dialogflow.entity.enums.AuditStatusEnum;
import com.yiwise.dialogflow.entity.enums.SyncModeEnum;
import com.yiwise.dialogflow.entity.po.AnswerAudioMappingPO;
import com.yiwise.dialogflow.entity.po.AudioPropertiesPO;
import com.yiwise.dialogflow.entity.po.BotAudioConfigPO;
import com.yiwise.dialogflow.entity.vo.audio.AnswerAudioMappingVO;
import com.yiwise.dialogflow.helper.AddOssPrefixSerializer;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.impl.BotConfigServiceImpl;
import com.yiwise.dialogflow.service.operationlog.AnswerAudioOperationLogService;
import com.yiwise.dialogflow.thread.DynamicDataSourceApplicationExecutorHolder;
import com.yiwise.dialogflow.utils.AnswerTextUtils;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AnswerAudioMappingServiceImpl implements AnswerAudioMappingService, RobotResourceService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private ObjectStorageHelper objectStorageHelper;

    @Lazy
    @Resource
    private AnswerAudioManagerService answerAudioManagerService;

    @Resource
    private AudioRecordLogService audioRecordLogService;

    @Resource
    private AudioPropertiesService audioPropertiesService;

    @Lazy
    @Resource
    private BotService botService;

    @Resource
    private BotConfigService botConfigService;

    @Resource
    private AnswerAudioOperationLogService answerAudioOperationLogService;

    @Override
    public List<AnswerAudioMappingPO> getAllByBotId(Long botId, Long recordUserId, AudioTypeEnum audioType) {
        if (Objects.isNull(botId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空");
        }
        if (Objects.isNull(audioType)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "audioType不能为空");
        }
        if (AudioTypeEnum.isExtendManMade(audioType) && Objects.isNull(recordUserId)) {
            return new ArrayList<>();
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("audioType").is(selectStorageType(audioType).name()));
        if (AudioTypeEnum.isExtendManMade(audioType)) {
            query.addCriteria(Criteria.where("recordUserId").is(recordUserId));
        }
        return mongoTemplate.find(query, AnswerAudioMappingPO.class, AnswerAudioMappingPO.COLLECTION_NAME);
    }

    /**
     * 真人录音和整句合成在存储上是一样的, 所以统一按照真人录音来存储
     */
    private AudioTypeEnum selectStorageType(AudioTypeEnum audioType) {
        if (Objects.isNull(audioType)) {
            return audioType;
        }
        return AudioTypeEnum.COMPOSE.equals(audioType) ? audioType : AudioTypeEnum.MAN_MADE;
    }

    @Override
    public List<AnswerAudioMappingVO> listAllAvailableAudioVO(Long botId, Long recordUserId, AudioTypeEnum audioType) {
        return wrapVOList(listAllAvailableAudio(botId, recordUserId, audioType, null));
    }

    @Override
    public List<AnswerAudioMappingPO> listAllAvailableAudio(Long botId, Long recordUserId, AudioTypeEnum audioType, List<String> answerTextList) {
        // 全语音合成模式下直接查询全部的合成音频即可
        // 真人录音和含变量短句合成模式下，要查询全部音频，包括真人和TTS音频，同一文案的音频优先返回真人录音数据
        if (Objects.isNull(botId) || Objects.isNull(audioType)) {
            return Collections.emptyList();
        }
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("botId").is(botId));
        if (CollectionUtils.isNotEmpty(answerTextList)) {
            criteriaList.add(Criteria.where("text").in(answerTextList));
        }
        if (AudioTypeEnum.COMPOSE.equals(audioType)) {
            criteriaList.add(Criteria.where("audioType").is(AudioTypeEnum.COMPOSE));
        } else {
            criteriaList.add(
                    new Criteria().orOperator(
                            Criteria.where("audioType").is(AudioTypeEnum.COMPOSE),
                            Criteria.where("audioType").is(AudioTypeEnum.MAN_MADE)
                                    .and("recordUserId").is(recordUserId)
                    )
            );
        }
        List<AnswerAudioMappingPO> audioList = mongoTemplate.find(
                Query.query(new Criteria().andOperator(criteriaList.toArray(new Criteria[]{}))),
                AnswerAudioMappingPO.class, AnswerAudioMappingPO.COLLECTION_NAME);
        return mergeAudioList(audioList);
    }

    private List<AnswerAudioMappingPO> mergeAudioList(List<AnswerAudioMappingPO> audioList) {
        if (CollectionUtils.isEmpty(audioList)) {
            return audioList;
        }
        Map<String, AnswerAudioMappingPO> map = new HashMap<>();
        for (AnswerAudioMappingPO mapping : audioList) {
            String text = mapping.getText();
            AnswerAudioMappingPO existsMapping = map.get(text);
            if (Objects.isNull(existsMapping) || AudioTypeEnum.COMPOSE.equals(existsMapping.getAudioType())) {
                map.put(text, mapping);
            }
        }
        return new ArrayList<>(map.values());
    }

    private List<AnswerAudioMappingVO> wrapVOList(List<AnswerAudioMappingPO> poList) {
        return poList.stream()
                .map(item -> MyBeanUtils.copy(item, AnswerAudioMappingVO.class))
                .peek(vo -> {
                    vo.setFullUrl(AddOssPrefixSerializer.getAddOssPrefixUrl(vo.getUrl()));
                }).collect(Collectors.toList());
    }

    @Override
    public AnswerAudioMappingPO upsertAudioMapping(Long botId, Long userId, AudioTypeEnum type, String text, String url, Integer volume) {
        return upsertAudioMapping(botId, userId, type, text, url, volume, null);
    }

    @Override
    public AnswerAudioMappingPO upsertAudioMapping(Long botId, Long userId, AudioTypeEnum type, String text, String url, Integer volume, Integer duration) {
        if (AudioTypeEnum.isExtendManMade(type) && Objects.isNull(userId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "录音师id不能为空");
        }
        String answerText = answerTrim(text);
        Query query = generateQuery(botId, userId, type, answerText);
        Update update = new Update();
        update.set("url", url);
        AudioPropertiesPO properties = audioPropertiesService.getByUrl(url);
        if (Objects.nonNull(volume)) {
            update.set("volume", volume);
        } else if (Objects.nonNull(properties)) {
            update.set("volume", properties.getVolume());
        }
        if (Objects.nonNull(duration)) {
            update.set("duration", duration);
        } else if (Objects.nonNull(properties)) {
            update.set("duration", properties.getDuration());
        }
        update.set("createTime", LocalDateTime.now());
        update.set("updateTime", LocalDateTime.now());
        mongoTemplate.upsert(query, update, AnswerAudioMappingPO.COLLECTION_NAME);
        answerAudioManagerService.clearAudioCompleteProgressCache(botId);
        updateBotToDraft(botId);
        if (AudioTypeEnum.isExtendManMade(type)) {
            audioRecordLogService.create(botId, userId, text, url);
        }
        return getUniqueAnswerAudio(botId, userId, type, answerText);
    }

    private void updateBotToDraft(Long botId) {
        botService.updateAuditStatus(botId, AuditStatusEnum.DRAFT);
    }

    private AnswerAudioMappingPO getUniqueAnswerAudio(Long botId, Long userId, AudioTypeEnum type, String text) {
        String answerText = answerTrim(text);
        Query query = generateQuery(botId, userId, type, answerText);
        return mongoTemplate.findOne(query, AnswerAudioMappingPO.class, AnswerAudioMappingPO.COLLECTION_NAME);
    }

    private Query generateQuery(Long botId, Long userId, AudioTypeEnum type, String answerText) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("recordUserId").is(AudioTypeEnum.COMPOSE.equals(type) ? 0L : userId))
                .addCriteria(Criteria.where("audioType").is(selectStorageType(type).name()))
                .addCriteria(Criteria.where("text").is(answerText));
        return query;
    }

    @Override
    public List<AnswerAudioMappingPO> copyAudioMapping(Long botId, AudioTypeEnum type, Long fromUserId, Long toUserId, Set<String> includeAnswerSet) {
        if (CollectionUtils.isEmpty(includeAnswerSet)) {
            return Collections.emptyList();
        }
        List<AnswerAudioMappingPO> fromList = getAllByBotId(botId, fromUserId, type);

        List<AnswerAudioMappingPO> toList = getAllByBotId(botId, toUserId, type);

        // 默认不覆盖的策略
        Set<String> targetExistAnswer = new HashSet<>();
        toList.forEach(mapping -> {
            if (StringUtils.isNotBlank(mapping.getUrl())) {
                targetExistAnswer.add(StringUtils.trimToEmpty(mapping.getText()));
            } else {
                // 什么时候为空呢?
            }
        });

        List<AnswerAudioMappingPO> copyList = new ArrayList<>(fromList.size() - targetExistAnswer.size());
        fromList.forEach(mapping -> {
            String answerText = StringUtils.trimToEmpty(mapping.getText());
            if (!targetExistAnswer.contains(answerText)) {
                AnswerAudioMappingPO newCopy = MyBeanUtils.copy(mapping, AnswerAudioMappingPO.class);
                newCopy.setId(null);
                newCopy.setIsByCopy(true);
                newCopy.setBotId(botId);
                newCopy.setRecordUserId(toUserId);
                newCopy.setCreateTime(LocalDateTime.now());
                newCopy.setUpdateTime(LocalDateTime.now());
            }
        });

        if (CollectionUtils.isNotEmpty(copyList)) {
            mongoTemplate.insert(copyList, AnswerAudioMappingPO.COLLECTION_NAME);
        }

        answerAudioManagerService.clearAudioCompleteProgressCache(botId);
        return copyList;
    }

    @Override
    public Integer copyAllOnCopyBot(Long fromBotId, Long fromUserId, Long toBotId, Long toUserId, Set<String> includeAnswerSet, Long operatorId, SyncModeEnum syncMode) {
        if (CollectionUtils.isEmpty(includeAnswerSet)) {
            return 0;
        }
        // 只复制真人录音
        List<AnswerAudioMappingPO> fromList = getAllByBotId(fromBotId, fromUserId, AudioTypeEnum.MAN_MADE);
        List<AnswerAudioMappingPO> oldList = getAllByBotId(toBotId, toUserId, AudioTypeEnum.MAN_MADE);
        Map<String, String> oldMap = MyCollectionUtils.listToConvertMap(oldList, mapping -> answerTrim(mapping.getText()), AnswerAudioMappingPO::getUrl);
        List<AnswerAudioMappingPO> readyList = fromList.stream()
                .filter(item -> includeAnswerSet.contains(item.getText()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(readyList)) {
            log.info("copyAllOnCopyBot, fromBotId={}, fromUserId={}, toBotId={}, toUserId={}, readyList.size={}", fromBotId, fromUserId, toBotId, toUserId, readyList.size());
            return readyList.size();
        }

        boolean isSkip = SyncModeEnum.SKIP.equals(syncMode);

        // sharding list
        List<List<AnswerAudioMappingPO>> shardingList = Lists.partition(readyList, 10);

        CountDownLatch latch = new CountDownLatch(shardingList.size());
        for (int i = 0; i < shardingList.size(); i++) {
            final List<AnswerAudioMappingPO> list = shardingList.get(i);
            String taskName = "复制录音-" + i;
            DynamicDataSourceApplicationExecutorHolder.execute(taskName, () -> {
                try {
                    for (AnswerAudioMappingPO item : list) {
                        String text = answerTrim(item.getText());
                        if (isSkip && oldMap.containsKey(text)) {
                            continue;
                        }

                        item.setId(null);
                        item.setBotId(toBotId);
                        item.setRecordUserId(toUserId);
                        item.setCreateTime(LocalDateTime.now());
                        item.setUpdateTime(LocalDateTime.now());
                        String oldUrl = item.getUrl();
                        String newUrl = copyFile(oldUrl, fromBotId, toBotId);
                        if (StringUtils.isBlank(newUrl)) {
                            // 复制失败?
                            log.warn("复制录音异常, oldUrl = {}, text={}", oldUrl, item.getText());
                        } else {
                            upsertAudioMapping(toBotId, toUserId, AudioTypeEnum.MAN_MADE, text, newUrl, item.getVolume(), item.getDuration());
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("复制录音异常", e);
        }
        answerAudioManagerService.clearAudioCompleteProgressCache(toBotId);
        answerAudioOperationLogService.copyAnswerAudio(fromBotId, toBotId,
                readyList.stream().map(AnswerAudioMappingPO::getText).filter(StringUtils::isNotBlank).collect(Collectors.toSet()),
                operatorId);
        return readyList.size();
    }

    @Override
    public void deleteAllComposeAudio(Long botId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("audioType").is(AudioTypeEnum.COMPOSE.name()));
        mongoTemplate.remove(query, AnswerAudioMappingPO.class);
        answerAudioManagerService.clearAudioCompleteProgressCache(botId);
    }

    @Override
    public void adjustVolume(Long botId, String url, Integer volume) {
        if (Objects.isNull(volume)) {
            return;
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("url").is(url));

        Update update = new Update();
        update.set("volume", volume);
        update.set("updateTime", LocalDateTime.now());

        mongoTemplate.updateMulti(query, update, AnswerAudioMappingPO.COLLECTION_NAME);
    }

    @Override
    public List<AnswerAudioMappingPO> queryByUrl(Long botId, String url) {
        if (Objects.isNull(botId) || StringUtils.isBlank(url)) {
            return Collections.emptyList();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("url").is(url));
        return mongoTemplate.find(query, AnswerAudioMappingPO.class);
    }

    @Override
    public Optional<AnswerAudioMappingPO> getLastMapping(Long botId, AudioTypeEnum audioType) {
        Query query = new Query();
        query.addCriteria(Criteria.where("audioType").is(selectStorageType(audioType).name()))
                .addCriteria(Criteria.where("botId").is(botId))
                .with(Sort.by(Sort.Order.desc("updateTime")));

        return Optional.ofNullable(mongoTemplate.findOne(query, AnswerAudioMappingPO.class, AnswerAudioMappingPO.COLLECTION_NAME));
    }

    @Override
    public void deleteAllByRecordUserId(Long botId, Long recordUserId) {
        if (Objects.isNull(botId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空");
        }
        if (Objects.isNull(recordUserId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "recordUserId不能为空");
        }
        log.info("删除机器人:{}下的录音师{}所有录音", botId, recordUserId);
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("recordUserId").is(recordUserId));
        List<AnswerAudioMappingPO> deleteList = mongoTemplate.findAllAndRemove(query,  AnswerAudioMappingPO.class, AnswerAudioMappingPO.COLLECTION_NAME);
        log.info("共删除:{}条", deleteList.size());
//        log.debug("删除内容:{}", JsonUtils.object2String(deleteList));
        answerAudioManagerService.clearAudioCompleteProgressCache(botId);
    }

    @Override
    public void deleteByAnswerList(Long botId, List<String> answerTextList) {
        if (Objects.isNull(botId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空");
        }
        if (CollectionUtils.isEmpty(answerTextList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "重置答案列表不能为空");
        }
        answerTextList = answerTextList.stream().map(this::answerTrim).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        log.info("删除指定答案录音文件,botId={}, 答案列表={}, ", botId, answerTextList);
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("text").in(answerTextList));
        List<AnswerAudioMappingPO> deleteList = mongoTemplate.findAllAndRemove(query,  AnswerAudioMappingPO.class, AnswerAudioMappingPO.COLLECTION_NAME);
        log.info("共删除:{}条", deleteList.size());
        answerAudioManagerService.clearAudioCompleteProgressCache(botId);
    }

    @Override
    public List<AnswerAudioMappingVO> getVOListByTextList(Long botId, Long recordUserId, AudioTypeEnum audioType, List<String> answerTextList) {
        if (CollectionUtils.isEmpty(answerTextList)) {
            return Collections.emptyList();
        }
        List<String> filterTextList = answerTextList.stream().map(this::answerTrim).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterTextList)) {
            return Collections.emptyList();
        }
        List<AnswerAudioMappingPO> poList = listAllAvailableAudio(botId, recordUserId, audioType, filterTextList);
        return wrapVOList(poList);
    }

    @Override
    public void maintainText() {
        // 循环查询所有的文本, 每次查询1000条
        String preId = "000000000000000000000000";
        List<AnswerAudioMappingPO> list = new ArrayList<>();
        do {
            Query query = new Query();
            query.addCriteria(Criteria.where("_id").gt(new ObjectId(preId)));
            query.with(Sort.by(Sort.Order.asc("_id")));
            query.limit(1000);
            list = mongoTemplate.find(query, AnswerAudioMappingPO.class, AnswerAudioMappingPO.COLLECTION_NAME);
            for (AnswerAudioMappingPO answerAudioMappingPO : list) {
                String oldText = answerAudioMappingPO.getText();
                String newText = answerTrim(oldText);
                if (!StringUtils.equals(oldText, newText)) {
                    answerAudioMappingPO.setText(newText);
                    mongoTemplate.save(answerAudioMappingPO, AnswerAudioMappingPO.COLLECTION_NAME);
                }
                preId = answerAudioMappingPO.getId();
            }
        } while (CollectionUtils.isNotEmpty(list));
    }

    private String copyFile(String relativeUrl, Long fromBotId, Long toBotId) {
        String dstRelativeUrl = relativeUrl.replace(String.format("/%s/", fromBotId), String.format("/%s/", toBotId));
        objectStorageHelper.copyObject(relativeUrl, dstRelativeUrl);
        log.info("copy audio file, fromUrl={}, fromBotId={}, toUrl={} toBotId={}", relativeUrl, fromBotId, dstRelativeUrl, toBotId);
        return dstRelativeUrl;
    }

    private String answerTrim(String text) {
        return AnswerTextUtils.removeAnswerPrefixAndSuffixSymbols(text);
    }

    @Override
    public void saveToSnapshot(RobotResourceContext context) {
        BotAudioConfigPO config = botConfigService.getAudioConfig(context.getSrcBotId());
        if (AudioTypeEnum.COMPOSE.equals(config.getAudioType()) && context.isCopy()) {
            return;
        }
        Long recordUserId = config.getRecordUserId();
        if (Objects.isNull(recordUserId)) {
            log.info("未设置录音师, 忽略录音复制");
            return;
        }
        List<AnswerAudioMappingPO> mappingList = listAllAvailableAudio(context.getSrcBotId(), recordUserId, config.getAudioType(), null);
        context.getSnapshot().setAnswerAudioMappingList(mappingList);
    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {
        List<AnswerAudioMappingPO> oldList = context.getSnapshot().getAnswerAudioMappingList();
        if (!context.isCopy() || CollectionUtils.isEmpty(oldList)) {
            return;
        }
//        if (BotCreateSourceEnum.GENERATE_REWRITE.equals(context.getBotCreateSource())) {
//            log.info("由改写模型创建的bot不复制录音");
//            return;
//        }
        BotAudioConfigPO config = context.getSnapshot().getBotAudioConfig();
        // 只复制真人录音
        List<List<AnswerAudioMappingPO>> shardingList = Lists.partition(oldList, 10);
        CountDownLatch latch = new CountDownLatch(shardingList.size());
        final List<AnswerAudioMappingPO> resultList = Collections.synchronizedList(new ArrayList<>());

        Map<String, String> answerUrlMapping = context.getResourceCopyReferenceMapping().getAnswerUrlMapping();
        for (int i = 0; i < shardingList.size(); i++) {
            final List<AnswerAudioMappingPO> partitionList = shardingList.get(i);
            String taskName = "复制录音-" + i;
            DynamicDataSourceApplicationExecutorHolder.execute(taskName, () -> {
                try {
                    for (AnswerAudioMappingPO item : partitionList) {
                        try {
                            item.setId(null);
                            item.setBotId(context.getTargetBotId());
                            item.setCreateTime(LocalDateTime.now());
                            item.setUpdateTime(LocalDateTime.now());
                            String oldUrl = item.getUrl();
                            String newUrl = copyFile(oldUrl, context.getSrcBotId(), context.getTargetBotId());
                            answerUrlMapping.put(oldUrl, newUrl);
                            if (StringUtils.isBlank(newUrl)) {
                                // 复制失败?
                                log.warn("复制录音异常, oldUrl = {}, text={}", oldUrl, item.getText());
                            } else {
                                item.setUrl(newUrl);
                                resultList.add(item);
                            }
                        } catch (Exception e) {
                            log.warn("复制录音异常, oldUrl = {}, text={}", item.getUrl(), item.getText(), e);
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        try {
            latch.await(5, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.warn("复制录音异常", e);
        }

        if (CollectionUtils.isNotEmpty(resultList)) {
            mongoTemplate.insert(resultList, AnswerAudioMappingPO.COLLECTION_NAME);
        }
    }

    @Override
    public List<Class<? extends RobotResourceService>> dependsOn() {
        return Lists.newArrayList(BotConfigServiceImpl.class);
    }
}
