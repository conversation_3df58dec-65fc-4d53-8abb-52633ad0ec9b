package com.yiwise.dialogflow.service.impl;

import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.entity.enums.ActionCategoryEnum;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.intent.IntentRuleActionPO;
import com.yiwise.dialogflow.entity.po.intent.RuleActionParam;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.service.BotRefService;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.OutsideResourceService;
import com.yiwise.dialogflow.service.RobotSnapshotService;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class OutsideResourceServiceImpl implements OutsideResourceService {

    @Resource
    private RobotSnapshotService robotSnapshotService;

    @Resource
    private BotService botService;

    @Resource
    private BotRefService botRefService;

    @Override
    public Map<Long, Set<Long>> queryCallOutSmsTemplateIdSetByDialogFlowIdList(List<Long> dialogFlowIdList) {
        if (CollectionUtils.isEmpty(dialogFlowIdList)) {
            return Collections.emptyMap();
        }
        List<BotRefPO> botRefList = botRefService.getByDialogFlowIdList(dialogFlowIdList);
        Map<Long, Set<Long>> result = new HashMap<>();
        Map<Long, Long> dialogFlowId2BotIdMap = MyCollectionUtils.listToConvertMap(botRefList, BotRefPO::getDialogFlowId, BotRefPO::getBotId);
        dialogFlowId2BotIdMap.forEach((dialogFlowId, botId) -> {
            result.put(dialogFlowId, getSmsTemplateIdsByBotId(botId));
        });
        return result;
    }

    private Set<Long> getSmsTemplateIdsByBotId(Long botId) {
        RobotSnapshotPO snapshot = robotSnapshotService.getLastPublishRobotSnapshot(botId);
        return getSmsTemplateIdsFromSnapshot(snapshot);
    }

    private Set<Long> getSmsTemplateIdsFromSnapshot(RobotSnapshotPO snapshot) {
        if (snapshot == null) {
            return Collections.emptySet();
        }
        Set<Long> smsTemplateIdSet = new HashSet<>();
        List<RuleActionParam> allActionList = new ArrayList<>();
        // 节点
        if (CollectionUtils.isNotEmpty(snapshot.getNodeList())) {
            for (DialogBaseNodePO node : snapshot.getNodeList()) {
                if (BooleanUtils.isTrue(node.getIsEnableAction()) && CollectionUtils.isNotEmpty(node.getActionList())) {
                    allActionList.addAll(node.getActionList());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(snapshot.getKnowledgeList())) {
            for (KnowledgePO knowledge : snapshot.getKnowledgeList()) {
                if (BooleanUtils.isTrue(knowledge.getIsEnableAction()) && CollectionUtils.isNotEmpty(knowledge.getActionList())) {
                    allActionList.addAll(knowledge.getActionList());
                }
            }
        }

        if (CollectionUtils.isNotEmpty(snapshot.getSpecialAnswerConfigList())) {
            for (SpecialAnswerConfigPO item : snapshot.getSpecialAnswerConfigList()) {
                if (BooleanUtils.isTrue(item.getIsEnableAction()) && CollectionUtils.isNotEmpty(item.getActionList())) {
                    allActionList.addAll(item.getActionList());
                }
            }
        }

        if (CollectionUtils.isNotEmpty(snapshot.getIntentRuleActionList())) {
            for (IntentRuleActionPO item : snapshot.getIntentRuleActionList()) {
                if ( CollectionUtils.isNotEmpty(item.getActionList())) {
                    allActionList.addAll(item.getActionList());
                }
            }
        }

        for (RuleActionParam item : allActionList) {
            if (ActionCategoryEnum.SEND_SMS.equals(item.getActionType())
                    && CollectionUtils.isNotEmpty(item.getSourceIdList())) {
                for (IdNamePair<Long, String> pair : item.getSourceIdList()) {
                    if (Objects.nonNull(pair.getId())) {
                        smsTemplateIdSet.add(pair.getId());
                    }
                }
            }
        }
        return smsTemplateIdSet;
    }

}
