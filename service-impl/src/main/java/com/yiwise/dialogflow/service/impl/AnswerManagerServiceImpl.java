package com.yiwise.dialogflow.service.impl;

import com.yiwise.base.common.text.TextPlaceholderElement;
import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.bo.SourceRefBO;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.query.AnswerQuery;
import com.yiwise.dialogflow.entity.vo.BotAnswerReplaceRequestItem;
import com.yiwise.dialogflow.entity.vo.audio.BaseAnswerContentVO;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.operationlog.AnswerAudioOperationLogService;
import com.yiwise.dialogflow.utils.AnswerLocateUtils;
import com.yiwise.middleware.tts.enums.VarInterpretTypeEnum;
import javaslang.Tuple;
import javaslang.Tuple2;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AnswerManagerServiceImpl implements AnswerManagerService {

    @Resource
    private StepService stepService;

    @Resource
    private StepNodeService stepNodeService;

    @Resource
    private KnowledgeService knowledgeService;

    @Resource
    private SpecialAnswerConfigService specialAnswerConfigService;

    @Resource
    private BotService botService;

    @Resource
    private VariableService variableService;

    @Resource
    private DependResourceService dependResourceService;

    @Resource
    private SourceRefService sourceRefService;

    @Resource
    private AnswerAudioOperationLogService answerAudioOperationLogService;

    @Resource
    private OperationLogService operationLogService;

    @Override
    public List<BaseAnswerContentVO> queryByCondition(AnswerQuery condition) {
        if (Objects.isNull(condition.getBotId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空");
        }
        int type = AnswerQuery.SEARCH_TYPE_ANSWER;
        if (Objects.nonNull(condition.getSearchType())) {
            type = condition.getSearchType();
        }
        boolean searchAnswer = type == AnswerQuery.SEARCH_TYPE_ANSWER;
        boolean searchVariable = type == AnswerQuery.SEARCH_TYPE_CUSTOM_VARIABLE;
        boolean searchDynamicVariable = type == AnswerQuery.SEARCH_TYPE_DYNAMIC_VARIABLE;
        Set<String> allowVariableNameSet = Collections.emptySet();
        if (type > 0) {
            if (StringUtils.isBlank(condition.getSearch())) {
                return Collections.emptyList();
            }
            allowVariableNameSet = getVariableNameSet(condition, searchDynamicVariable);
            if (!allowVariableNameSet.contains(condition.getSearch())) {
                // 变量不存在, 所以一定不会有结果
                log.info("搜索的变量不存在, 直接返回空结果");
                return Collections.emptyList();
            }
        }

        Long botId = condition.getBotId();
        List<StepPO> stepList = Collections.emptyList();
        List<DialogBaseNodePO> nodeList = Collections.emptyList();
        List<KnowledgePO> knowledgeList = Collections.emptyList();
        List<SpecialAnswerConfigPO> specialAnswerList = Collections.emptyList();
        boolean searchAllSource = CollectionUtils.isEmpty(condition.getAnswerSourceSet());
        // 查询所有的流程和节点
        if (searchAllSource || condition.getAnswerSourceSet().contains(AnswerSourceEnum.STEP)) {
            if (CollectionUtils.isNotEmpty(condition.getLabelledList())) {
                List<StepPO> copyStepList = new ArrayList<>();
                condition.getLabelledList().forEach(labelled -> {
                    copyStepList.add(MyBeanUtils.copy(labelled, StepPO.class));
                });
                stepList = copyStepList;
            } else {
                stepList = stepService.getAllListByBotId(botId);
                if (CollectionUtils.isNotEmpty(condition.getStepTypeList())) {
                    stepList = stepList.stream()
                            .filter(step -> condition.getStepTypeList().contains(step.getType()))
                            .collect(Collectors.toList());
                }
            }
            nodeList = stepNodeService.getListByStepIdList(botId, stepList.stream().map(StepPO::getId).collect(Collectors.toList()));
        }

        // 查询所有的问答知识
        if (searchAllSource || condition.getAnswerSourceSet().contains(AnswerSourceEnum.KNOWLEDGE)) {
            if (CollectionUtils.isNotEmpty(condition.getLabelledList())) {
                List<KnowledgePO> copyKnowledgeList = new ArrayList<>();
                condition.getLabelledList().forEach(labelled -> {
                    copyKnowledgeList.add(MyBeanUtils.copy(labelled, KnowledgePO.class));
                });
                knowledgeList = copyKnowledgeList;
            } else {
                knowledgeList = knowledgeService.getAllListByBotId(botId);
            }
        }

        // 特殊配置
        if (searchAllSource || condition.getAnswerSourceSet().contains(AnswerSourceEnum.SPECIAL_ANSWER)) {
            if (CollectionUtils.isNotEmpty(condition.getLabelledList())) {
                List<SpecialAnswerConfigPO> copySpecialAnswerList = new ArrayList<>();
                condition.getLabelledList().forEach(labelled -> {
                    copySpecialAnswerList.add(MyBeanUtils.copy(labelled, SpecialAnswerConfigPO.class));
                });
                specialAnswerList = copySpecialAnswerList;
            } else {
                specialAnswerList = specialAnswerConfigService.getByBotId(botId, condition.getSpecialAnswerConfigEnabledStatus());
            }
        }

        List<BaseAnswerContentVO> result = getAnswerContentVOList(stepList, nodeList, knowledgeList, specialAnswerList);

        boolean mergeWholeVariableSentence = BooleanUtils.isTrue(condition.getMergeWholeVariableSentence());
        if (StringUtils.isNotBlank(condition.getSearch())) {
            result = result.stream().filter(answer -> {
                // 不需要搜索到变量的内容
                AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(answer.getText(), mergeWholeVariableSentence);
                if (searchVariable || searchDynamicVariable) {
                    return splitter.getPlaceholderSet().contains(condition.getSearch());
                } else {
                    return splitter.getTextList().stream().anyMatch(placeholder -> StringUtils.containsIgnoreCase(placeholder, condition.getSearch()));
                }
            }).collect(Collectors.toList());
        }

        return result;
    }

    @Override
    public void updateAnswerOnVariableRename(Long botId, String variableId, String oldVariableName, String newVariableName, Long userId) {
        // 查询所有的流程和节点
        List<StepPO> stepList = stepService.getAllListByBotId(botId);
        List<DialogBaseNodePO> nodeList = stepNodeService.getListByStepIdList(botId, stepList.stream().map(StepPO::getId).collect(Collectors.toList()));

        // 查询所有的问答知识
        List<KnowledgePO> knowledgeList = knowledgeService.getAllListByBotId(botId);

        // 特殊配置
        List<SpecialAnswerConfigPO> specialAnswerList = specialAnswerConfigService.getByBotId(botId);

        Set<String> answerIdSet = new HashSet<>();
        nodeList.forEach(node -> {
            if (CollectionUtils.isNotEmpty(node.getAnswerList())) {
                answerIdSet.addAll(node.getAnswerList().stream().map(BaseAnswerContent::getUniqueId).collect(Collectors.toList()));
            }
        });
        knowledgeList.forEach(knowledge -> {
            if (CollectionUtils.isNotEmpty(knowledge.getAnswerList())) {
                answerIdSet.addAll(knowledge.getAnswerList().stream().map(BaseAnswerContent::getUniqueId).collect(Collectors.toList()));
            }
        });
        specialAnswerList.forEach(specialAnswer -> {
            if (CollectionUtils.isNotEmpty(specialAnswer.getAnswerList())) {
                answerIdSet.addAll(specialAnswer.getAnswerList().stream().map(BaseAnswerContent::getUniqueId).collect(Collectors.toList()));
            }
        });
        executeReplaceAndSave(botId, knowledgeList, nodeList, specialAnswerList, answerIdSet, oldVariableName, newVariableName, this::doReplaceAnswerPlaceholder, userId, NO_LOG);
    }

    private Set<String> getVariableNameSet(AnswerQuery condition, boolean dynamicVariable) {
        Set<String> allowVariableNameSet;
        List<VariablePO> variableList = variableService.getListByBotId(condition.getBotId());
        if (dynamicVariable) {
            allowVariableNameSet = variableList.stream()
                    .filter(item -> VariableTypeEnum.DYNAMIC.equals(item.getType()))
                    .map(VariablePO::getName).collect(Collectors.toSet());
        } else {
            allowVariableNameSet = variableList.stream()
                    .filter(item -> !VariableTypeEnum.DYNAMIC.equals(item.getType()))
                    .map(VariablePO::getName).collect(Collectors.toSet());
        }
        return allowVariableNameSet;
    }

    @Override
    public List<BaseAnswerContentVO> getAnswerFromSnapshot(RobotSnapshotPO snapshot) {
        if (Objects.isNull(snapshot)) {
            return Collections.emptyList();
        }

        return getAnswerContentVOList(snapshot.getStepList(),
                snapshot.getNodeList(),
                snapshot.getKnowledgeList(),
                snapshot.getSpecialAnswerConfigList());
    }

    @Override
    public BaseAnswerContentVO getAnswerContextByLocation(Long botId, AnswerLocateBO answerLocate) {
        if (Objects.isNull(botId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "机器人id不能为空");
        }
        if (Objects.isNull(answerLocate)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "答案位置不能为空");
        }
        if (Objects.isNull(answerLocate.getAnswerSource())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "答案来源不能为空");
        }

        // 获取原答案数据
        Optional<? extends BaseAnswerContent> answerContentOptional = Optional.empty();

        String nextAction = "";
        switch (answerLocate.getAnswerSource()) {
            case KNOWLEDGE:
                KnowledgePO knowledge = knowledgeService.getById(botId, answerLocate.getKnowledgeId());
                if (Objects.nonNull(knowledge)) {
                    answerContentOptional = filterAnswerById(knowledge.getAnswerList(), answerLocate.getAnswerId());
                }
                break;
            case STEP:
                DialogBaseNodePO node = stepNodeService.getById(botId, answerLocate.getStepId(), answerLocate.getNodeId());
                if (Objects.nonNull(node)) {
                    answerContentOptional = filterAnswerById(node.getAnswerList(), answerLocate.getAnswerId());
                    if (node instanceof DialogJumpNodePO) {
                        DialogJumpNodePO jumpNode = (DialogJumpNodePO) node;
                        nextAction = jumpNode.getJumpType().getDesc();
                    }
                }
                break;
            case SPECIAL_ANSWER:
                SpecialAnswerConfigPO specialAnswer = specialAnswerConfigService.getById(botId, answerLocate.getSpecialAnswerConfigId());
                if (Objects.nonNull(specialAnswer)) {
                    answerContentOptional = filterAnswerById(specialAnswer.getAnswerList(), answerLocate.getAnswerId());
                }
                break;
            default:
                break;
        }

        if (!answerContentOptional.isPresent()) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "未查询到答案数据");
        }

        BaseAnswerContent answer = answerContentOptional.get();
        if (answer instanceof KnowledgeAnswer) {
            KnowledgeAnswer knowledgeAnswer = (KnowledgeAnswer) answer;
            nextAction = Objects.nonNull(knowledgeAnswer.getPostAction()) ? knowledgeAnswer.getPostAction().getDesc() : "";
        }
        BaseAnswerContentVO vo = MyBeanUtils.copy(answer, BaseAnswerContentVO.class);
        vo.setLocate(answerLocate);
        vo.setAction(nextAction);
        return vo;
    }

    @Override
    public void replaceAnswerContent(Long botId,
                                     List<AnswerLocateBO> targetAnswerLocateList,
                                     String oldContent,
                                     String newContent,
                                     Long userId) {
        if (CollectionUtils.isEmpty(targetAnswerLocateList) || StringUtils.isBlank(oldContent)) {
            return;
        }

        try {
            executeReplaceAndSave(botId, targetAnswerLocateList, oldContent, newContent, this::doReplaceAnswerText, userId, REPLACE_ANSWER);
        } finally {
            botService.updateAuditStatus(botId, AuditStatusEnum.DRAFT);
        }
    }

    @Override
    public void updateAnswer(Long botId, List<? extends BotAnswerReplaceRequestItem> answerList, Long userId) {
        if (CollectionUtils.isEmpty(answerList)) {
            return;
        }

        Function<AnswerLocateBO, String> convert = (loc) -> {
            switch (loc.getAnswerSource()) {
                case STEP: return String.format("%s_%s", loc.getNodeId(), loc.getAnswerId());
                case SPECIAL_ANSWER: return String.format("%s_%s", loc.getSpecialAnswerConfigId(), loc.getAnswerId());
                case KNOWLEDGE: return String.format("%s_%s", loc.getKnowledgeId(), loc.getAnswerId());
                default: throw new ComException(ComErrorCode.VALIDATE_ERROR, "不支持的答案来源");
            }
        };
        Map<String, BotAnswerReplaceRequestItem> answerReplaceMap = new HashMap<>();
        for (BotAnswerReplaceRequestItem replaceResult : answerList) {
            if (Objects.nonNull(replaceResult.getAnswerLocate())) {
                answerReplaceMap.put(convert.apply(replaceResult.getAnswerLocate()), replaceResult);
            }
        }

        List<String> allAnswerTextList = new ArrayList<>();
        List<DialogBaseNodePO> updateNodeList = new ArrayList<>();
        List<KnowledgePO> updateKnowledgeList = new ArrayList<>();
        List<SpecialAnswerConfigPO> updateSpecialAnswerList = new ArrayList<>();

        String logFormat = "【%s】【%s】, 修改为【%s】";
        List<String> logList = new ArrayList<>();

        BiConsumer<AnswerContainer, Consumer<Boolean>> processor = (answerContainer, callback) -> {
              if (CollectionUtils.isNotEmpty(answerContainer.getAnswerList())) {
                  boolean update = false;
                  for (BaseAnswerContent nodeAnswer : answerContainer.getAnswerList()) {
                      String key = String.format("%s_%s", answerContainer.getId(), nodeAnswer.getUniqueId());
                      if (answerReplaceMap.containsKey(key)) {
                          String oldText = nodeAnswer.getText();
                          nodeAnswer.setText(answerReplaceMap.get(key).getReplaceResultText());
                          allAnswerTextList.add(nodeAnswer.getText());
                          logList.add(String.format(logFormat, answerContainer.getLabel(), oldText, nodeAnswer.getText()));
                          update = true;
                      }
                  }
                  if (update) {
                      callback.accept(true);
                  }
              }
        };

        // 处理流程
        List<String> nodeIdList = answerList.stream()
                .map(BotAnswerReplaceRequestItem::getAnswerLocate)
                .filter(Objects::nonNull)
                .map(AnswerLocateBO::getNodeId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        stepNodeService.getByIdList(botId, nodeIdList).forEach(node -> {
            // 更新节点答案
            processor.accept(node, (update) -> updateNodeList.add(node));
        });

        // 处理问答知识
        List<String> knowledgeIdList = answerList.stream()
                .map(BotAnswerReplaceRequestItem::getAnswerLocate)
                .filter(Objects::nonNull)
                .map(AnswerLocateBO::getKnowledgeId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        knowledgeService.getByIdList(botId, knowledgeIdList).forEach(knowledge -> {
            processor.accept(knowledge, (update) -> updateKnowledgeList.add(knowledge));
        });

        specialAnswerConfigService.getByBotId(botId).forEach(specialAnswerConfig -> {
            processor.accept(specialAnswerConfig, update -> updateSpecialAnswerList.add(specialAnswerConfig));
        });

        variableService.autoCreateIfNotExists(botId, allAnswerTextList, VariableTypeEnum.CUSTOM, userId);

        // 更新答案
        stepNodeService.updateAnswerListAndVariableRefInfo(botId, updateNodeList, userId);
        knowledgeService.updateAnswerListAndVariableRefInfo(botId, updateKnowledgeList, userId);
        specialAnswerConfigService.updateAnswerListAndVariableRefInfo(botId, updateSpecialAnswerList, userId);

        // 更新操作日志
        REPLACE_ANSWER.saveLog(botId, logList, userId);
    }

    @Override
    public void updateAnswerAndBotStatus(Long botId, List<? extends BotAnswerReplaceRequestItem> answerList, Long userId) {
        updateAnswer(botId, answerList, userId);
        botService.updateAuditStatus(botId, AuditStatusEnum.DRAFT);
    }

    @Override
    public void replaceAnswerVariable(Long botId, List<AnswerLocateBO> targetAnswerLocateList,
                                      String oldVariable, String newVariable, Long userId, BotTextSearchTypeEnum searchType) {
        if (CollectionUtils.isEmpty(targetAnswerLocateList) || StringUtils.isBlank(oldVariable)) {
            return;
        }

        Map<String, String> variableName2IdMap = variableService.getNameIdMapByBotId(botId);
        if (!variableName2IdMap.containsKey(oldVariable)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "变量名称不存在");
        }

        try {
            // 判断是否需要创建变量
            if (!variableName2IdMap.containsKey(newVariable) && StringUtils.isNotBlank(newVariable)) {
                log.info("替换答案中变量, 目标变量不存在, 进行创建: {}", newVariable);
                VariableTypeEnum varType = BotTextSearchTypeEnum.DYNAMIC_VARIABLE.equals(searchType) ? VariableTypeEnum.DYNAMIC : VariableTypeEnum.CUSTOM;
                VariablePO variable = new VariablePO();
                variable.setBotId(botId);
                variable.setName(newVariable);
                variable.setType(varType);
                variable.setDesc("通过查找替换功能自动创建");
                variable.setInterpretType(VarInterpretTypeEnum.DEFAULT);
                variable.setCreateUserId(userId);
                variable.setUpdateUserId(userId);
                variableService.create(variable);
                operationLogService.save(botId, OperationLogTypeEnum.SEARCH_REPLACE, OperationLogResourceTypeEnum.REPLACE,
                        String.format("变量替换：新增%s【%s】", varType.getDesc(), newVariable), userId);
            }

            executeReplaceAndSave(botId, targetAnswerLocateList, oldVariable, newVariable, this::doReplaceAnswerPlaceholder, userId, REPLACE_VAR);
        } finally {
            botService.updateAuditStatus(botId, AuditStatusEnum.DRAFT);
        }
    }


    private void executeReplaceAndSave(Long botId,
                                       List<AnswerLocateBO> targetAnswerLocateList,
                                       String oldVal,
                                       String newVal,
                                       ReplaceAnswerElementAction elementAction,
                                       Long userId,
                                       AnswerReplaceChangeDetailLogCollector changeDetailCollector) {

        List<String> nodeIdList = new ArrayList<>();
        List<String> knowledgeIdList = new ArrayList<>();
        List<String> specialAnswerIdList = new ArrayList<>();

        Set<String> answerIdSet = new HashSet<>(targetAnswerLocateList.size());
        targetAnswerLocateList.stream()
                .filter(item -> Objects.nonNull(item.getAnswerSource()))
                .forEach(item -> {
                    answerIdSet.add(item.getAnswerId());
                    switch (item.getAnswerSource()) {
                        case STEP:
                            nodeIdList.add(item.getNodeId());
                            break;
                        case KNOWLEDGE:
                            knowledgeIdList.add(item.getKnowledgeId());
                            break;
                        case SPECIAL_ANSWER:
                            specialAnswerIdList.add(item.getSpecialAnswerConfigId());
                            break;
                        default:
                            break;
                    }
                });

        List<DialogBaseNodePO> nodeList = new ArrayList<>();
        List<KnowledgePO> knowledgeList = new ArrayList<>();
        List<SpecialAnswerConfigPO> specialAnswerList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(nodeIdList)) {
            nodeList = stepNodeService.getByIdList(botId, nodeIdList);
        }
        if (CollectionUtils.isNotEmpty(knowledgeIdList)) {
            knowledgeList = knowledgeService.getByIdList(botId, knowledgeIdList);
        }
        if (CollectionUtils.isNotEmpty(specialAnswerIdList)) {
            specialAnswerList = specialAnswerConfigService.getByBotId(botId).stream()
                    .filter(item -> specialAnswerIdList.contains(item.getId()))
                    .collect(Collectors.toList());
        }

        executeReplaceAndSave(botId, knowledgeList, nodeList, specialAnswerList, answerIdSet, oldVal, newVal, elementAction, userId, changeDetailCollector);
    }

    private void executeReplaceAndSave(Long botId,
                                       List<KnowledgePO> knowledgeList,
                                       List<DialogBaseNodePO> nodeList,
                                       List<SpecialAnswerConfigPO> specialAnswerList,
                                       Set<String> answerIdSet,
                                       String oldVal,
                                       String newVal,
                                       ReplaceAnswerElementAction elementAction, Long userId,
                                       AnswerReplaceChangeDetailLogCollector changeDetailCollector) {

        List<String> answerChangeDetailList = new ArrayList<>();

        DependentResourceBO dependentResource = dependResourceService.generateByCondition(new DependentResourceBO.Condition(botId).variable());

        // 替换流程
        if (CollectionUtils.isNotEmpty(nodeList)) {
            nodeList.forEach(node -> {
                List<Tuple2<String, String>> answerReplacePairList = matchAnswerAndReplace(node.getAnswerList(), answerIdSet, oldVal, newVal, elementAction, changeDetailCollector);
                if (CollectionUtils.isNotEmpty(answerReplacePairList)) {
                    Set<String> newVariableIdSet = node.calDependVariableIdSet(dependentResource);
                    stepNodeService.updateAnswerList(node);
                    resetVariableRefInfo(botId, newVariableIdSet, node.getId(), node.getLabel(), node.getStepId(), IntentRefTypeEnum.NODE);
                    answerChangeDetailList.addAll(
                            answerReplacePairList.stream()
                                    .map(tuple -> String.format("【%s】【%s】, 修改为【%s】", node.getLabel(), tuple._1, tuple._2))
                                    .collect(Collectors.toList())
                    );
                }
            });
        }

        // 替换知识
        if (CollectionUtils.isNotEmpty(knowledgeList)) {
            knowledgeList.forEach(knowledge -> {
                List<Tuple2<String, String>> answerReplacePairList = matchAnswerAndReplace(knowledge.getAnswerList(), answerIdSet, oldVal, newVal, elementAction, changeDetailCollector);
                if (CollectionUtils.isNotEmpty(answerReplacePairList)) {
                    knowledgeService.updateAnswerList(knowledge);
                    Set<String> newVariableIdSet = knowledge.calDependVariableIdSet(dependentResource);
                    resetVariableRefInfo(botId, newVariableIdSet, knowledge.getId(), knowledge.getLabel(), null, IntentRefTypeEnum.KNOWLEDGE);
                    answerChangeDetailList.addAll(
                            answerReplacePairList.stream()
                                    .map(tuple -> String.format("【%s】【%s】, 修改为【%s】", knowledge.getLabel(), tuple._1, tuple._2))
                                    .collect(Collectors.toList())
                    );
                }
            });
        }

        // 替换特殊语境答案
        if (CollectionUtils.isNotEmpty(specialAnswerList)) {
            specialAnswerList.forEach(specialAnswer -> {
                List<Tuple2<String, String>> answerReplacePairList = matchAnswerAndReplace(specialAnswer.getAnswerList(), answerIdSet, oldVal, newVal, elementAction, changeDetailCollector);
                if (CollectionUtils.isNotEmpty(answerReplacePairList)) {
                    specialAnswerConfigService.updateAnswerList(specialAnswer);
                    Set<String> newVariableIdSet = specialAnswer.calDependVariableIdSet(dependentResource);
                    resetVariableRefInfo(botId, newVariableIdSet, specialAnswer.getId(), specialAnswer.getLabel(), null, IntentRefTypeEnum.SPECIAL_ANSWER);
                    answerChangeDetailList.addAll(
                            answerReplacePairList.stream()
                                    .map(tuple -> String.format("【%s】【%s】, 修改为【%s】", specialAnswer.getLabel(), tuple._1, tuple._2))
                                    .collect(Collectors.toList())
                    );
                }
            });
        }

        if (changeDetailCollector != null) {
            changeDetailCollector.saveLog(botId, answerChangeDetailList, userId);
        }
    }

    private void resetVariableRefInfo(Long botId,
                                      Set<String> variableIdSet,
                                      String refId,
                                      String refLabel,
                                      String stepId,
                                      IntentRefTypeEnum refType) {
        sourceRefService.deleteSourceByRefIdAndSourceTypeAndRefType(botId, refId, SourceTypeEnum.VARIABLE, refType);
        if (CollectionUtils.isNotEmpty(variableIdSet)) {
            SourceRefBO sourceRefBO = new SourceRefBO();
            sourceRefBO.setBotId(botId);
            sourceRefBO.setSourceIdSet(variableIdSet);
            sourceRefBO.setSourceType(SourceTypeEnum.VARIABLE);
            sourceRefBO.setRefId(refId);
            sourceRefBO.setRefType(refType);
            sourceRefBO.setRefLabel(refLabel);
            if (IntentRefTypeEnum.NODE.equals(refType)) {
                sourceRefBO.setParentRefId(stepId);
                sourceRefBO.setParentRefType(IntentRefTypeEnum.STEP);
                StepPO step = stepService.getPOById(botId, stepId);
                sourceRefBO.setParentRefLabel(step.getLabel());
            }
            sourceRefService.saveSourceRef(sourceRefBO);
        }
    }


    private List<Tuple2<String, String>> matchAnswerAndReplace(List<? extends BaseAnswerContent> answerList,
                                                               Set<String> matchAnswerIdSet,
                                                               String oldContent,
                                                               String newContent,
                                                               ReplaceAnswerElementAction replaceAnswerElementAction,
                                                               AnswerReplaceChangeDetailLogCollector changeDetailCollector) {
        if (CollectionUtils.isEmpty(answerList)
                || CollectionUtils.isEmpty(matchAnswerIdSet)
                || StringUtils.isBlank(oldContent)) {
            return Collections.emptyList();
        }
        List<Tuple2<String, String>> answerReplacePairList = new ArrayList<>();
        answerList.forEach(answer -> {
            if (matchAnswerIdSet.contains(answer.getUniqueId()) && StringUtils.isNotBlank(answer.getText())) {
                String oldAnswerContent = answer.getText();
                // 需要先切分之后再替换, 防止对变量中的值进行了替换, 变量有单独的处理逻辑
                AnswerPlaceholderSplitter splitter = new AnswerPlaceholderSplitter(answer.getText(), false);
                AtomicBoolean replace = new AtomicBoolean();
                splitter.getTextPlaceholderList().forEach(subElement -> {
                    if (replaceAnswerElementAction.doReplace(subElement, oldContent, newContent)) {
                        replace.set(true);
                    }
                });
                if (replace.get()) {
                    // 对答案进行重新合并
                    String newAnswerContent = splitter.getTextPlaceholderList().stream()
                            .map(item -> {
                                if (TextPlaceholderTypeEnum.TEXT.equals(item.getType()) || TextPlaceholderTypeEnum.SEPARATOR.equals(item.getType())) {
                                    return item.getValue();
                                }
                                if (StringUtils.isBlank(item.getValue())) {
                                    return "";
                                }
                                return String.format("${%s}", item.getValue());
                            }).collect(Collectors.joining());
                    answer.setText(newAnswerContent);
                    log.info("答案整体替换结果,{} => {}, 原答案={}, 替换后:{}, label={}",
                            oldContent, newContent, oldAnswerContent, newAnswerContent, answer.getLabel());
                    answerReplacePairList.add(changeDetailCollector.collect(oldContent,newContent, oldAnswerContent, newAnswerContent));
                }
            }
        });

        return answerReplacePairList;
    }

    private boolean doReplaceAnswerPlaceholder(TextPlaceholderElement element,
                                               String oldVariableName,
                                               String newVariableName) {
        if (Objects.isNull(element)) {
            return false;
        }
        if (TextPlaceholderTypeEnum.PLACE_HOLDER.equals(element.getType())
                && StringUtils.equals(element.getValue(), oldVariableName)) {
            element.setValue(newVariableName);
            return true;
        }
        return false;
    }

    private boolean doReplaceAnswerText(TextPlaceholderElement element, String oldContent, String newContent) {
        if (Objects.isNull(element)) {
            return false;
        }
        if (TextPlaceholderTypeEnum.TEXT.equals(element.getType())
                && StringUtils.containsIgnoreCase(element.getValue(), oldContent)) {
            String newValue = StringUtils.replaceIgnoreCase(element.getValue(), oldContent, newContent);
            log.info("对答案部分内容进行替换成功,{} => {}, 原答案={}, 替换后:{}",
                    oldContent, newContent, element.getValue(), newValue);
            element.setValue(newValue);
            return true;
        }
        return false;
    }


    private Optional<? extends BaseAnswerContent> filterAnswerById(List<? extends BaseAnswerContent> answerList, String answerId) {
        if (CollectionUtils.isEmpty(answerList)) {
            return Optional.empty();
        }
        return answerList.stream()
                .filter(item -> Objects.equals(item.getUniqueId(), answerId))
                .findFirst();
    }

    @Override
    public BaseAnswerContentVO convertToVO(BaseAnswerContent answerContent, AnswerLocateBO locate) {
        return convertToVO(answerContent, locate, null);
    }

    @Override
    public BaseAnswerContentVO convertToVO(BaseAnswerContent answerContent, AnswerLocateBO locate, DialogBaseNodePO node) {
        BaseAnswerContentVO vo = MyBeanUtils.copy(answerContent, BaseAnswerContentVO.class);
        vo.setLocate(locate);
        locate.setAnswerId(answerContent.getUniqueId());
        locate.setAnswerLabel(answerContent.getLabel());
        if (answerContent instanceof KnowledgeAnswer) {
            KnowledgeAnswer knowledgeAnswer = (KnowledgeAnswer) answerContent;
            if (Objects.nonNull(knowledgeAnswer.getPostAction())) {
                vo.setAction(knowledgeAnswer.getPostAction().getDesc());
            }
        }
        if (node instanceof DialogJumpNodePO) {
            DialogJumpNodePO jumpNode = (DialogJumpNodePO) node;
            if (Objects.nonNull(jumpNode.getJumpType())) {
                vo.setAction(jumpNode.getJumpType().getDesc());
            }
        }
        return vo;
    }

    private List<BaseAnswerContentVO> getAnswerContentVOList(List<StepPO> stepList,
                                                             List<DialogBaseNodePO> nodeList,
                                                             List<KnowledgePO> knowledgeList,
                                                             List<SpecialAnswerConfigPO> specialAnswerList) {
        List<BaseAnswerContentVO> resultList = new ArrayList<>();
        Map<String, StepPO> stepMap = MyCollectionUtils.listToMap(stepList, StepPO::getId);
        // 查询所有的流程和节点
        nodeList.forEach(node -> {
            if (CollectionUtils.isEmpty(node.getAnswerList())) {
                return;
            }
            StepPO step = stepMap.get(node.getStepId());
            if (Objects.nonNull(step)) {
                node.getAnswerList().stream()
                        .map(answer -> convertToVO(answer, AnswerLocateUtils.generate(step, node), node))
                        .forEach(resultList::add);
            } else {
                log.warn("step is null, stepId={}, nodeId={}", node.getStepId(), node.getId());
            }
        });

        // 查询所有的问答知识
        knowledgeList.forEach(knowledge -> {
            if (CollectionUtils.isEmpty(knowledge.getAnswerList())) {
                return;
            }
            knowledge.getAnswerList().stream()
                    .map(answer -> convertToVO(answer, AnswerLocateUtils.generate(knowledge)))
                    .forEach(resultList::add);
        });

        // 特殊配置
        specialAnswerList.forEach(special -> {
            if (CollectionUtils.isEmpty(special.getAnswerList())) {
                return;
            }
            special.getAnswerList().stream()
                    .map(answer -> convertToVO(answer, AnswerLocateUtils.generate(special)))
                    .forEach(resultList::add);
        });

        return resultList;
    }


    interface ReplaceAnswerAction {
        boolean doReplace(List<? extends BaseAnswerContent> answerList,
                          Set<String> answerIdSet,
                          String oldValue,
                          String newValue,
                          ReplaceAnswerElementAction replaceAnswerElementAction);
    }

    interface ReplaceAnswerElementAction {
        boolean doReplace(TextPlaceholderElement element,
                          String oldValue,
                          String newValue);
    }

    @AllArgsConstructor
    public class AnswerReplaceChangeDetailLogCollector {

        private boolean replaceVar;

        private boolean noLog;

        public Tuple2<String, String> collect(String oldContent, String newContent, String oldAnswerContent, String newAnswerContent) {
            return replaceVar ? Tuple.of(oldContent, newContent) : Tuple.of(oldAnswerContent, newAnswerContent);
        }

        public void saveLog(Long botId, List<String> detailList, Long userId) {
            if (!noLog) {
                answerAudioOperationLogService.searchAndReplaceAnswer(botId, detailList, userId, replaceVar);
            }
        }
    }

    private final AnswerReplaceChangeDetailLogCollector REPLACE_ANSWER = new AnswerReplaceChangeDetailLogCollector(false,false);
    private final AnswerReplaceChangeDetailLogCollector REPLACE_VAR = new AnswerReplaceChangeDetailLogCollector(true,false);
    private final AnswerReplaceChangeDetailLogCollector NO_LOG = new AnswerReplaceChangeDetailLogCollector(false,true);
}
