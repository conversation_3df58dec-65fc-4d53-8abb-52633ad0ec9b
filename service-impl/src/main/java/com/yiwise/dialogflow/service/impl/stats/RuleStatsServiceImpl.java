package com.yiwise.dialogflow.service.impl.stats;

import com.yiwise.dialogflow.common.MongoCollectionNameCenter;
import com.yiwise.dialogflow.entity.bo.stats.BotStatsAnalysisResult;
import com.yiwise.dialogflow.entity.enums.RuleActionSourceEnum;
import com.yiwise.dialogflow.entity.enums.RuleActionTypeEnum;
import com.yiwise.dialogflow.entity.po.stats.IntentRuleStatsPO;
import com.yiwise.dialogflow.entity.query.BaseStatsQuery;
import com.yiwise.dialogflow.service.CallStatsMongoService;
import com.yiwise.dialogflow.service.stats.RuleStatsService;
import com.yiwise.dialogflow.utils.BotStatsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class RuleStatsServiceImpl implements RuleStatsService {

    @Resource
    private MongoTemplate readMongoTemplate;

    @Resource
    private CallStatsMongoService callStatsMongoService;

    @Override
    public List<IntentRuleStatsPO> queryBotAllIntentLevelRuleStatsList(Long botId, BaseStatsQuery condition) {
        return queryBotAllIntentRuleStatsList(botId, RuleActionTypeEnum.INTENT_LEVEL, RuleActionSourceEnum.RULE, condition);
    }

    @Override
    public List<IntentRuleStatsPO> queryBotAllIntentLevelStatsList(Long botId, BaseStatsQuery condition) {
        return queryBotAllIntentRuleStatsList(botId, RuleActionTypeEnum.INTENT_LEVEL, null, condition);
    }

    @Override
    public List<IntentRuleStatsPO> queryBotAllIntentActionRuleStatsList(Long botId, BaseStatsQuery condition) {
        return queryBotAllIntentRuleStatsList(botId, RuleActionTypeEnum.INTENT_ACTION, RuleActionSourceEnum.RULE, condition);
    }

    @Override
    public List<IntentRuleStatsPO> queryBotAllIntentActionStatsList(Long botId, BaseStatsQuery condition) {
        return queryBotAllIntentRuleStatsList(botId, RuleActionTypeEnum.INTENT_ACTION, null, condition);
    }

    @Override
    public void saveRuleStats(BotStatsAnalysisResult analysisResult) {
        if (MapUtils.isEmpty(analysisResult.getRuleCountMap())) {
            return;
        }

        analysisResult.getRuleCountMap().forEach((k, v) -> {
            Query query = BotStatsUtil.generateCommonQuery(analysisResult);
            query.addCriteria(Criteria.where("ruleType").is(k.getRuleType().name()));
            query.addCriteria(Criteria.where("source").is(k.getSource().name()));
            if (StringUtils.isNotBlank(k.getRuleId())) {
                query.addCriteria(Criteria.where("ruleId").is(k.getRuleId()));
            }
            if (StringUtils.isNotBlank(k.getKnowledgeId())) {
                query.addCriteria(Criteria.where("knowledgeId").is(k.getKnowledgeId()));
            }
            if (StringUtils.isNotBlank(k.getSpecialAnswerConfigId())) {
                query.addCriteria(Criteria.where("specialAnswerConfigId").is(k.getSpecialAnswerConfigId()));
            }
            if (StringUtils.isNotBlank(k.getStepId())) {
                query.addCriteria(Criteria.where("stepId").is(k.getStepId()));
            }
            if (StringUtils.isNotBlank(k.getNodeId())) {
                query.addCriteria(Criteria.where("nodeId").is(k.getNodeId()));
            }
            Update update = new Update();
            update.inc("count", 1);
            callStatsMongoService.updateMongoDataUsingCache(MongoCollectionNameCenter.INTENT_RULE_STATS, query, update);
        });
    }

    private List<IntentRuleStatsPO> queryBotAllIntentRuleStatsList(Long botId, RuleActionTypeEnum type, RuleActionSourceEnum source, BaseStatsQuery condition) {
        long beginEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getBeginDateTimeOrDefault(condition.getBeginTime()));
        long endEpochHour = BotStatsUtil.toEpochHour(BotStatsUtil.getEndDateTimeOrDefault(condition.getEndTime()));
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(BotStatsUtil.generateCommonEpochHourCondition(beginEpochHour, endEpochHour));
        aggregationOperationList.add(Aggregation.match(Criteria.where("botId").is(botId)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("ruleType").is(type.name())));
        if (Objects.nonNull(source)) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("source").is(source.name())));
        }
        BotStatsUtil.generateTenantCondition(condition).ifPresent(aggregationOperationList::add);
        BotStatsUtil.generateCallJobCondition(condition).ifPresent(aggregationOperationList::add);


        AggregationOperation groupOperation = Aggregation.group( "ruleType", "ruleId", "source", "knowledgeId", "specialAnswerConfigId", "stepId", "nodeId")
                .sum("count").as("count")
                .first("botId").as("botId")
                .first("ruleType").as("type")
                .first("source").as("source")
                .first("stepId").as("stepId")
                .first("nodeId").as("nodeId")
                .first("specialAnswerConfigId").as("specialAnswerConfigId")
                .first("knowledgeId").as("knowledgeId")
                .first("ruleId").as("ruleId");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        return readMongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.INTENT_RULE_STATS, IntentRuleStatsPO.class)
                .getMappedResults();
    }
}
