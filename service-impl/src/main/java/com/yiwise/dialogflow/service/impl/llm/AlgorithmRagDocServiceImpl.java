package com.yiwise.dialogflow.service.impl.llm;

import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.entity.enums.llm.DocumentSplitMethodEnum;
import com.yiwise.dialogflow.entity.enums.llm.DocumentSplitTypeEnum;
import com.yiwise.dialogflow.entity.enums.llm.DocumentTypeEnum;
import com.yiwise.dialogflow.entity.po.llm.DocumentSplitConfigPO;
import com.yiwise.dialogflow.entity.po.llm.RagDocumentPO;
import com.yiwise.dialogflow.entity.po.llm.RagDocumentSegmentPO;
import com.yiwise.dialogflow.entity.vo.llm.RagDocumentCallbackVO;
import com.yiwise.dialogflow.entity.vo.llm.RagDocumentSegmentVO;
import com.yiwise.dialogflow.helper.AddOssPrefixSerializer;
import com.yiwise.dialogflow.service.llm.AlgorithmRagDocService;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AlgorithmRagDocServiceImpl implements AlgorithmRagDocService {

    @Resource
    private RestTemplate restTemplate;

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    static class Result implements Serializable {

        Integer code;

        String info;

        public boolean isSuccess() {
            return Objects.equals(code, 0);
        }
    }

    @Override
    public boolean uploadDoc(RagDocumentPO po) {
        try {
            String requestUrl = ApplicationConstant.ALGORITHM_RAG_DOC_UPLOAD_URL;
            String callbackUrl = ApplicationConstant.ALGORITHM_RAG_DOC_CALLBACK_URL;
            if (StringUtils.isBlank(requestUrl) || StringUtils.isBlank(callbackUrl)) {
                return false;
            }

            Map<DocumentSplitMethodEnum, List<String>> separatorMap = new HashMap<>(2);
            separatorMap.put(DocumentSplitMethodEnum.LINE, Collections.singletonList("\n"));
            separatorMap.put(DocumentSplitMethodEnum.PUNCTUATION, Arrays.asList(".", "。", "!", "！", "?", "？"));

            Map<String, Object> params = new HashMap<>(4);
            params.put("filePath", DocumentTypeEnum.isDoc(po.getDocumentType()) ? AddOssPrefixSerializer.getAddOssPrefixUrl(po.getFileOssKey()) : po.getUrl());
            params.put("fileId", po.getId());
            DocumentSplitConfigPO splitConfig = po.getSplitConfig();
            if (Objects.nonNull(splitConfig) && !DocumentSplitTypeEnum.isAuto(splitConfig.getSplitType())) {
                Map<String, Object> segmentConfig = new HashMap<>(2);
                segmentConfig.put("maxLength", splitConfig.getSegmentMaxWords());
                segmentConfig.put("separators", separatorMap.get(splitConfig.getSplitMethod()));
                params.put("segmentConfig", segmentConfig);
            }
            params.put("callbackUrl", callbackUrl);

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add("Content-Type", "application/json");
            String body = JsonUtils.object2String(params);
            HttpEntity<String> requestEntity = new HttpEntity<>(body, httpHeaders);

            log.info("请求RAG文档上传接口, url={}, body={}", requestUrl, body);
            ResponseEntity<String> response = restTemplate.postForEntity(requestUrl, requestEntity, String.class);
            log.info("请求RAG文档上传接口结果为: response={}", response);

            return JsonUtils.string2Object(response.getBody(), Result.class).isSuccess();
        } catch (Exception e) {
            log.error("[LogHub_Warn]请求RAG文档上传接口失败,docId={}", po.getId(), e);
            return false;
        }
    }

    @Override
    public boolean deleteDoc(String docId) {
        try {
            String requestUrl = ApplicationConstant.ALGORITHM_RAG_DOC_DELETE_URL;
            if (StringUtils.isBlank(requestUrl) || StringUtils.isBlank(docId)) {
                return false;
            }

            Map<String, Object> params = new HashMap<>(2);
            params.put("fileId", docId);

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add("Content-Type", "application/json");
            String body = JsonUtils.object2String(params);
            HttpEntity<String> requestEntity = new HttpEntity<>(body, httpHeaders);

            log.info("请求RAG文档删除接口, url={}, body={}", requestUrl, body);
            ResponseEntity<String> response = restTemplate.postForEntity(requestUrl, requestEntity, String.class);
            log.info("请求RAG文档删除接口结果为: response={}", response);

            return JsonUtils.string2Object(response.getBody(), Result.class).isSuccess();
        } catch (Exception e) {
            log.error("[LogHub_Warn]请求RAG文档删除接口失败,docId={}", docId, e);
            return false;
        }
    }

    @Override
    public boolean deleteSegment(String segmentId) {
        try {
            String requestUrl = ApplicationConstant.ALGORITHM_RAG_DOC_SEGMENT_DELETE_URL;
            if (StringUtils.isBlank(requestUrl) || StringUtils.isBlank(segmentId)) {
                return false;
            }

            Map<String, Object> params = new HashMap<>(2);
            params.put("segmentId", segmentId);

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add("Content-Type", "application/json");
            String body = JsonUtils.object2String(params);
            HttpEntity<String> requestEntity = new HttpEntity<>(body, httpHeaders);

            log.info("请求RAG文档片段删除接口, url={}, body={}", requestUrl, body);
            ResponseEntity<String> response = restTemplate.postForEntity(requestUrl, requestEntity, String.class);
            log.info("请求RAG文档片段删除接口结果为: response={}", response);

            return JsonUtils.string2Object(response.getBody(), Result.class).isSuccess();
        } catch (Exception e) {
            log.error("[LogHub_Warn]请求RAG文档片段删除接口失败,segmentId={}", segmentId, e);
            return false;
        }
    }

    @Override
    public boolean addSegment(String docId, String segmentId, String content) {
        try {
            String requestUrl = ApplicationConstant.ALGORITHM_RAG_DOC_SEGMENT_ADD_URL;
            if (StringUtils.isBlank(requestUrl) || StringUtils.isBlank(segmentId) || StringUtils.isBlank(docId) || StringUtils.isBlank(content)) {
                return false;
            }

            Map<String, Object> params = new HashMap<>(4);
            params.put("fileId", docId);
            params.put("segmentId", segmentId);
            params.put("content", content);

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add("Content-Type", "application/json");
            String body = JsonUtils.object2String(params);
            HttpEntity<String> requestEntity = new HttpEntity<>(body, httpHeaders);

            log.info("请求RAG文档片段新增接口, url={}, body={}", requestUrl, body);
            ResponseEntity<String> response = restTemplate.postForEntity(requestUrl, requestEntity, String.class);
            log.info("请求RAG文档片段新增接口结果为: response={}", response);

            return JsonUtils.string2Object(response.getBody(), Result.class).isSuccess();
        } catch (Exception e) {
            log.error("[LogHub_Warn]请求RAG文档片段新增接口失败,segmentId={}", segmentId, e);
            return false;
        }
    }

    @Override
    public boolean updateSegment(String docId, String segmentId, String content) {
        return deleteSegment(segmentId) && addSegment(docId, segmentId, content);
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @FieldDefaults(level = AccessLevel.PRIVATE)
    static class SearchResult extends Result {

        List<RagDocumentSegmentVO> results;
    }

    @Override
    public List<RagDocumentSegmentVO> search(String search, List<String> docIdList) {
        try {
            String requestUrl = ApplicationConstant.ALGORITHM_RAG_DOC_SEARCH_URL;
            if (StringUtils.isBlank(requestUrl) || StringUtils.isBlank(search) || CollectionUtils.isEmpty(docIdList)) {
                return Collections.emptyList();
            }

            Map<String, Object> params = new HashMap<>(2);
            params.put("query", search);
            params.put("fileIdList", docIdList);

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add("Content-Type", "application/json");
            String body = JsonUtils.object2String(params);
            HttpEntity<String> requestEntity = new HttpEntity<>(body, httpHeaders);

            log.info("请求RAG文档检索接口, url={}, body={}", requestUrl, body);
            ResponseEntity<String> response = restTemplate.postForEntity(requestUrl, requestEntity, String.class);
            log.info("请求RAG文档检索接口结果为: response={}", response);

            return JsonUtils.string2Object(response.getBody(), SearchResult.class).getResults();
        } catch (Exception e) {
            log.error("[LogHub_Warn]请求RAG文档检索接口失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<RagDocumentSegmentPO> batchUpload(String docId, List<String> contentList) {
        try {
            String requestUrl = ApplicationConstant.ALGORITHM_RAG_DOC_SEGMENT_BATCH_ADD_URL;
            if (StringUtils.isBlank(requestUrl) || StringUtils.isBlank(docId) || CollectionUtils.isEmpty(contentList)) {
                return Collections.emptyList();
            }

            Map<String, Object> params = new HashMap<>(2);
            params.put("fileId", docId);
            params.put("contentList", contentList);

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add("Content-Type", "application/json");
            String body = JsonUtils.object2String(params);
            HttpEntity<String> requestEntity = new HttpEntity<>(body, httpHeaders);

            log.info("请求RAG文档片段批量新增接口, url={}, body={}", requestUrl, body);
            ResponseEntity<String> response = restTemplate.postForEntity(requestUrl, requestEntity, String.class);
            log.info("请求RAG文档片段批量新增接口结果为: response={}", response);

            RagDocumentCallbackVO callback = JsonUtils.string2Object(response.getBody(), RagDocumentCallbackVO.class);
            if (callback.isSuccess()) {
                return callback.getSegments();
            }
        } catch (Exception e) {
            log.error("[LogHub_Warn]请求RAG文档片段批量新增接口失败,docId={}", docId, e);
        }
        return Collections.emptyList();
    }
}