package com.yiwise.dialogflow.service.impl.intent;

import com.google.common.collect.Lists;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.MongoCollectionNameCenter;
import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.enums.AuditStatusEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum;
import com.yiwise.dialogflow.entity.po.intent.IntentConfigPO;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.OperationLogService;
import com.yiwise.dialogflow.service.RobotResourceService;
import com.yiwise.dialogflow.service.intent.IntentConfigService;
import com.yiwise.middleware.redis.service.cache.CacheTplService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/2/28
 */
@Service
public class IntentConfigServiceImpl implements IntentConfigService, RobotResourceService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private CacheTplService cacheTplService;

    @Resource
    private BotService botService;

    @Resource
    private OperationLogService operationLogService;

    @Override
    public IntentConfigPO initOnCreateBot(Long botId) {
        IntentConfigPO defaultConfig = generateDefault(botId);
        mongoTemplate.save(defaultConfig, MongoCollectionNameCenter.INTENT_CONFIG);
        return defaultConfig;
    }

    @Override
    public void save(IntentConfigPO intentConfigPO) {

        Assert.notNull(intentConfigPO.getBotId(), "botId不能为空");

        IntentConfigPO oldConfig = Optional.ofNullable(intentConfigPO.getId()).map(id -> mongoTemplate.findById(id, IntentConfigPO.class)).orElse(null);

        intentConfigPO.setUpdateTime(LocalDateTime.now());
        mongoTemplate.save(intentConfigPO);
        String redisKey = RedisKeyCenter.getIntentConfigRedisKey(intentConfigPO.getBotId());
        cacheTplService.flush(redisKey, intentConfigPO);
        botService.updateAuditStatus(intentConfigPO.getBotId(), AuditStatusEnum.DRAFT);

        buildLogAndSave(oldConfig, intentConfigPO);
    }

    private void buildLogAndSave(IntentConfigPO oldConfig, IntentConfigPO newConfig) {
        if (Objects.isNull(oldConfig)) {
            return;
        }
        List<String> detailList = Lists.newArrayList();
        if (!Objects.equals(oldConfig.getEnableAlgorithm(), newConfig.getEnableAlgorithm())) {
            detailList.add(String.format("%s算法模型", BooleanUtils.isTrue(newConfig.getEnableAlgorithm()) ? "启用" : "关闭"));
        }
        if (!Objects.equals(oldConfig.getAlgorithmInterventionCount(), newConfig.getAlgorithmInterventionCount())){
            detailList.add(String.format("修改问法介入字数：【%s字】修改为【%s字】", oldConfig.getAlgorithmInterventionCount(),newConfig.getAlgorithmInterventionCount()));
        }
        if (!Objects.equals(oldConfig.getAlgorithmLowerThreshold(), newConfig.getAlgorithmLowerThreshold()) ||
                !Objects.equals(oldConfig.getAlgorithmUpperThreshold(), newConfig.getAlgorithmUpperThreshold())){
            detailList.add(String.format("修改问答置信度：【%s-%s】修改为【%s-%s】", oldConfig.getAlgorithmLowerThreshold(),oldConfig.getAlgorithmUpperThreshold(),
                    newConfig.getAlgorithmLowerThreshold(),newConfig.getAlgorithmUpperThreshold()));
        }
        operationLogService.save(newConfig.getBotId(), OperationLogTypeEnum.INTENT, OperationLogResourceTypeEnum.ALGORITHM, detailList, newConfig.getUpdateUserId());
    }

    @Override
    public IntentConfigPO detail(Long botId) {
        String redisKey = RedisKeyCenter.getIntentConfigRedisKey(botId);
        return cacheTplService.findCache(redisKey, ApplicationConstant.POJO_CACHE_TIMEOUT_OF_SECOND, TimeUnit.SECONDS, IntentConfigPO.class, () -> getByBotId(botId));
    }

    @Override
    public List<IntentConfigPO> queryByBotIdList(List<Long> botIdList) {
        if (CollectionUtils.isEmpty(botIdList)) {
            return Collections.emptyList();
        }
        Query query = Query.query(Criteria.where("botId").in(botIdList));
        query.with(Sort.by(Sort.Order.desc("_id")));

        List<IntentConfigPO> configList = mongoTemplate.find(query, IntentConfigPO.class);

        Map<Long, IntentConfigPO> map = new HashMap<>();
        for (IntentConfigPO config : configList) {
            if (!map.containsKey(config.getBotId())) {
                map.put(config.getBotId(), config);
            }
        }

        List<IntentConfigPO> result = botIdList.stream()
                .map(botId -> {
                    IntentConfigPO config = map.get(botId);
                    if (Objects.isNull(config)) {
                        config = getByBotId(botId);
                    }
                    return config;
                }).collect(Collectors.toList());

        return result;
    }

    private IntentConfigPO getByBotId(Long botId) {
        Query query = Query.query(Criteria.where("botId").is(botId));
        IntentConfigPO config = mongoTemplate.findOne(query, IntentConfigPO.class);
        if (Objects.isNull(config)) {
            // todo 分布式锁
            synchronized (this) {
                config = mongoTemplate.findOne(query, IntentConfigPO.class);
                if (Objects.isNull(config)) {
                    config = initOnCreateBot(botId);
                }
            }
        }
        return config;
    }

    public IntentConfigPO generateDefault(Long botId) {
        IntentConfigPO config = new IntentConfigPO();
        config.setBotId(botId);
        config.setEnableAlgorithm(false);
        config.setAlgorithmInterventionCount(4);
        config.setAlgorithmLowerThreshold(0.8);
        config.setAlgorithmUpperThreshold(0.9);
        config.setCreateTime(LocalDateTime.now());
        return config;
    }

    @Override
    public void saveToSnapshot(RobotResourceContext context) {
        IntentConfigPO intentConfigPO = getByBotId(context.getSrcBotId());
        context.getSnapshot().setIntentConfig(intentConfigPO);
    }

    @Override
    public void validateResource(RobotResourceContext context) {
        RobotResourceService.super.validateResource(context);
    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {
        IntentConfigPO intentConfig = context.getSnapshot().getIntentConfig();
        if (Objects.isNull(intentConfig)) {
            intentConfig = initOnCreateBot(context.getTargetBotId());
        } else {
            intentConfig.setId(null);
            intentConfig.setBotId(context.getTargetBotId());
            intentConfig.setCreateTime(LocalDateTime.now());
            intentConfig.setUpdateTime(LocalDateTime.now());
            save(intentConfig);
        }
    }

    @Override
    public List<Class<? extends RobotResourceService>> dependsOn() {
        return RobotResourceService.super.dependsOn();
    }
}
