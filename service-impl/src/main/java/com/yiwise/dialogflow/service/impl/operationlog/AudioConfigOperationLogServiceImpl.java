package com.yiwise.dialogflow.service.impl.operationlog;

import com.yiwise.dialogflow.entity.dto.OperationLogDTO;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum;
import com.yiwise.dialogflow.entity.po.BackgroundAudioConfigPO;
import com.yiwise.dialogflow.entity.po.BotAudioConfigPO;
import com.yiwise.dialogflow.entity.po.TtsVoiceConfigPO;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.service.OperationLogService;
import com.yiwise.dialogflow.service.operationlog.AudioConfigOperationLogService;
import com.yiwise.dialogflow.service.remote.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AudioConfigOperationLogServiceImpl implements AudioConfigOperationLogService {

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private UserService userService;


    @Override
    public void compareAudioConfigAndCreateOperationLog(Long botId, BotAudioConfigPO oldConfig, BotAudioConfigPO newConfig, Long userId) {
        if (Objects.isNull(oldConfig) || Objects.isNull(newConfig)) {
            return;
        }

        List<String> changeLogList = new ArrayList<>();

        boolean changeAudioType = !Objects.equals(oldConfig.getAudioType(), newConfig.getAudioType());
        if (changeAudioType) {
            changeLogList.add(String.format("录音模式由【%s】修改为【%s】", oldConfig.getAudioType().getDesc(), newConfig.getAudioType().getDesc()));
        }

        if (!Objects.equals(oldConfig.getRecordUserId(), newConfig.getRecordUserId()) && !AudioTypeEnum.COMPOSE.equals(newConfig.getAudioType())) {
            String oldUserName = "";
            String newUserName = "";
            if (Objects.nonNull(oldConfig.getRecordUserId())) {
                UserPO user = userService.getUserById(oldConfig.getRecordUserId());
                if (Objects.nonNull(user)) {
                    oldUserName = user.getName();
                }
            }
            if (Objects.nonNull(newConfig.getRecordUserId())) {
                UserPO user = userService.getUserById(newConfig.getRecordUserId());
                if (Objects.nonNull(user)) {
                    newUserName = user.getName();
                }
            }
            changeLogList.add(String.format("录音师由【%s】修改为【%s】", oldUserName, newUserName));
        }

        if (AudioTypeEnum.COMPOSE.equals(oldConfig.getAudioType())
                && AudioTypeEnum.COMPOSE.equals(newConfig.getAudioType())) {
            changeLogList.addAll(compareTtsConfig("合成", oldConfig.getTtsConfig(), newConfig.getTtsConfig()));
        } else {
            changeLogList.addAll(compareTtsConfig("拼接", oldConfig.getSpliceTtsConfig(), newConfig.getSpliceTtsConfig()));
        }

        if (AudioTypeEnum.MAN_MADE.equals(oldConfig.getAudioType())
                && AudioTypeEnum.MAN_MADE.equals(newConfig.getAudioType())
                && !Objects.equals(oldConfig.getEnableVariableAudio(), newConfig.getEnableVariableAudio())) {
            changeLogList.add(String.format("%s录音变量", BooleanUtils.isTrue(newConfig.getEnableVariableAudio()) ? "开启" : "关闭"));
        }

        changeLogList.addAll(compareBackgroundConfig(oldConfig.getBackgroundList(), newConfig.getBackgroundList()));

        if (CollectionUtils.isNotEmpty(changeLogList)) {
            List<OperationLogDTO> logDTOList = changeLogList.stream()
                    .map(changeLog -> OperationLogDTO.builder()
                            .botId(botId)
                            .operatorId(userId)
                            .detail(changeLog)
                            .resourceType(OperationLogResourceTypeEnum.AUDIO_CONFIG)
                            .type(OperationLogTypeEnum.BOT_CONFIG)
                            .build())
                    .collect(Collectors.toList());
            operationLogService.batchSave(logDTOList);
        }
    }

    private Collection<String> compareBackgroundConfig(List<BackgroundAudioConfigPO> oldBackgroundList,
                                                       List<BackgroundAudioConfigPO> newBackgroundList) {

        BackgroundAudioConfigPO oldBackground = Optional.ofNullable(oldBackgroundList).orElse(Collections.emptyList()).stream()
                .filter(item -> BooleanUtils.isTrue(item.getEnable()))
                .findFirst()
                .orElse(null);

        BackgroundAudioConfigPO newBackground = Optional.ofNullable(newBackgroundList).orElse(Collections.emptyList()).stream()
                .filter(item -> BooleanUtils.isTrue(item.getEnable()))
                .findFirst()
                .orElse(null);
        Collection<String> logs = new ArrayList<>();
        String oldBackgroundName = Objects.isNull(oldBackground) ? "无" : oldBackground.getSource();
        String newBackgroundName = Objects.isNull(newBackground) ? "无" : newBackground.getSource();
        String newFileName = "";
        if (Objects.nonNull(newBackground) && StringUtils.isNotBlank(newBackground.getFilename())) {
            newFileName = String.format("上传文件，【%s】", newBackground.getFilename());
        }
        if (!StringUtils.equals(oldBackgroundName, newBackgroundName)) {
            return Collections.singletonList(String.format("背景音由【%s】修改为【%s】%s", oldBackgroundName, newBackgroundName, newFileName));
        } else if (Objects.nonNull(newBackground) && Objects.nonNull(oldBackground)) {
            boolean changeVolume = !Objects.equals(oldBackground.getVolume(), newBackground.getVolume());
            boolean changeFile = !StringUtils.equals(oldBackground.getFilename(), newBackground.getFilename());
            if (changeVolume) {
                logs.add(String.format("背景音音量由【%s】修改为【%s】", oldBackground.getVolume(), newBackground.getVolume()));
            }
            if (changeFile) {
                logs.add(String.format("背景音文件由【%s】修改为【%s】", oldBackground.getFilename(), newBackground.getFilename()));
            }
        }
        return logs;
    }

    private List<String> compareTtsConfig(String prefix, TtsVoiceConfigPO oldConfig, TtsVoiceConfigPO newConfig) {
        if (Objects.isNull(oldConfig) || Objects.isNull(newConfig)) {
            return Collections.emptyList();
        }
        List<String> ttsChangeLogs = new ArrayList<>();
        if (!StringUtils.equals(oldConfig.getTtsVoice(), newConfig.getTtsVoice())) {
            ttsChangeLogs.add(String.format("%s音色由【%s】修改为【%s】", prefix, oldConfig.getTtsVoiceName(), newConfig.getTtsVoiceName()));
        }
        if (!Objects.equals(oldConfig.getTtsSpeed(), newConfig.getTtsSpeed())) {
            ttsChangeLogs.add(String.format("%s音色语速由【%s】修改为【%s】", prefix, oldConfig.getTtsSpeed(), newConfig.getTtsSpeed()));
        }
        if (!Objects.equals(oldConfig.getTtsVolume(), newConfig.getTtsVolume())) {
            ttsChangeLogs.add(String.format("%s音色音量由【%s】修改为【%s】", prefix, oldConfig.getTtsVolume(), newConfig.getTtsVolume()));
        }
        return ttsChangeLogs;
    }
}
