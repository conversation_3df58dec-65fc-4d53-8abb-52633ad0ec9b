package com.yiwise.dialogflow.service.impl;

import com.google.common.collect.ImmutableSet;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.entity.dto.llmchat.*;
import com.yiwise.dialogflow.entity.enums.StepSubTypeEnum;
import com.yiwise.dialogflow.entity.enums.StepTypeEnum;
import com.yiwise.dialogflow.entity.enums.llm.RagDocumentStatusEnum;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.llm.RagDocumentPO;
import com.yiwise.dialogflow.entity.vo.LLMStepTestRequestVO;
import com.yiwise.dialogflow.entity.vo.LLMStepTestResponseVO;
import com.yiwise.dialogflow.reactor.Accumulator;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.LLMStepTestService;
import com.yiwise.dialogflow.service.StepService;
import com.yiwise.dialogflow.service.VariableService;
import com.yiwise.dialogflow.service.llm.LLMChatService;
import com.yiwise.dialogflow.service.llm.LlmStepConfigService;
import com.yiwise.dialogflow.service.llm.RagDocumentService;
import com.yiwise.dialogflow.utils.AnswerTextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.yiwise.dialogflow.entity.enums.llm.LlmStepTypeEnum.COLLECT_TASK;

@Slf4j
@Service
public class LLmStepTestServiceImpl implements LLMStepTestService {

    @Resource
    private StepService stepService;

    @Resource
    private BotService botService;

    @Resource
    private LLMChatService llmChatService;

    @Resource
    private VariableService variableService;

    @Resource
    private LlmStepConfigService llmStepConfigService;

    @Resource
    private RagDocumentService ragDocumentService;

    @Override
    public Flux<ResultObject<LLMStepTestResponseVO>> test(LLMStepTestRequestVO request) {
        String logId = MDC.get(ApplicationConstant.MDC_LOG_ID);
        return doTest(request)
                .doOnError(e -> {
                    log.error("Failed to test LLM step, botId:{}, stepId:{}, userInput:{}",
                            request.getBotId(), request.getStepId(), request.getUserInput(), e);
                })
                .map(ResultObject::success)
                .doOnNext(r -> log.debug("response:{}", JsonUtils.object2String(r)))
                .subscriberContext(context -> context.put(ApplicationConstant.MDC_LOG_ID, logId));
    }

    private Flux<LLMStepTestResponseVO> doTest(LLMStepTestRequestVO request) {
        BotPO bot = botService.getById(request.getBotId());
        if (Objects.isNull(bot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "bot不存在");
        }

        StepPO step = stepService.getById(bot.getBotId(), request.getStepId());
        if (Objects.isNull(step)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "流程不存在");
        }

        if (!StepSubTypeEnum.isLlm(step.getSubType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程不是大模型流程");
        }

        if (StringUtils.isBlank(request.getUserInput())) {
            request.setUserInput("嗯");
        }
        if (StringUtils.isBlank(request.getTrackInfo())) {
            request.setTrackInfo("");
        }

        if (CollectionUtils.isEmpty(request.getHistoryList())) {
            request.setHistoryList(new ArrayList<>());
        }

        LlmStepConfigPO stepConfig = llmStepConfigService.getByStepId(step.getBotId(), step.getId());

        if (Objects.isNull(stepConfig)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "流程配置不存在");
        }
        List<StepPO> allSteps = stepService.getAllListByBotId(step.getBotId());
        Map<String, String> stepLabel2NameMap = MyCollectionUtils.listToMap(allSteps, StepPO::getLabel, StepPO::getName);

        List<RagDocumentPO> docList = ragDocumentService.getByBotId(step.getBotId());

        LLMChatRequest llmChatRequest = initRequest(step, stepConfig, allSteps, docList);

        llmChatRequest.setQuery(request.getUserInput());
        llmChatRequest.setHistory(request.getHistoryList());
        if (StringUtils.isNotBlank(request.getTrackInfo())) {
            llmChatRequest.setTrackInfo(request.getTrackInfo());
        }
        return Flux.defer(() -> llmChatService.llmStepChat(llmChatRequest, new ResponseAccumulator(request, stepLabel2NameMap)))
                .switchIfEmpty(Mono.defer(() -> {
                    LLMStepTestResponseVO result = new LLMStepTestResponseVO();
                    result.setAnswer("接口未响应有效的文本内容");
                    result.setUserInput(request.getUserInput());
                    result.setStepId(request.getStepId());
                    result.setBotId(request.getBotId());
                    result.setTrackInfo(request.getTrackInfo());
                    result.setIsComplete( true);
                    result.setDurationMs(1);
                    return Mono.just(result);
                }));
    }


    // todo  代码重复
    private LLMChatRequest initRequest(StepPO step,
                                       LlmStepConfigPO stepConfig,
                                       List<StepPO> allSteps,
                                       List<RagDocumentPO> docList) {
        LLMChatRequest request = new LLMChatRequest();

        request.setDialogFlowId(String.valueOf(stepConfig.getBotId()));
        request.setFileIdList(new ArrayList<>());

        if (COLLECT_TASK.equals(step.getLlmStepType())) {
            LLMChatConfig config = new LLMChatConfig();
            request.setConfig(config);
            config.setBackground(renderPrompt(stepConfig.getRoleDesc()));
            config.setKnowledge(renderPrompt(stepConfig.getBackground()));
            List<LLMChatTask> tasks = new ArrayList<>();
            config.setTasks(tasks);
            if (CollectionUtils.isNotEmpty(stepConfig.getCollectTaskConfigList())) {
                for (LlmStepCollectTaskConfigPO taskConfig : stepConfig.getCollectTaskConfigList()) {
                    LLMChatTask task = new LLMChatTask();
                    task.setDescription(renderPrompt(taskConfig.getDesc()));
                    task.setScripts(Collections.singletonList(renderPrompt(taskConfig.getGuideAnswer())));
                    tasks.add(task);
                }
            }
        } else {
            // 自由配置型流程

            // 需要进行变量的渲染

            request.setSystem(renderPrompt(stepConfig.getPrompt()));
        }

        // 采集信息
        List<LlmStepVariableAssignConfigPO> variableAssignConfigList = new ArrayList<>();

        if (COLLECT_TASK.equals(step.getLlmStepType())) {
            if (CollectionUtils.isNotEmpty(stepConfig.getCollectTaskConfigList())) {
                for (LlmStepCollectTaskConfigPO llmStepCollectTaskConfigPO : stepConfig.getCollectTaskConfigList()) {
                    if (CollectionUtils.isNotEmpty(llmStepCollectTaskConfigPO.getVariableAssignConfigList())) {
                        variableAssignConfigList.addAll(llmStepCollectTaskConfigPO.getVariableAssignConfigList());
                    }
                }
            }
        } else {
            variableAssignConfigList = stepConfig.getVariableAssignConfigList();
        }

        if (CollectionUtils.isNotEmpty(variableAssignConfigList)) {
            List<LLMChatCollectInfo> collectInfoList = new ArrayList<>();
            for (LlmStepVariableAssignConfigPO collectConfig : variableAssignConfigList) {

                List<VariablePO> varList = variableService.getListByBotId(step.getBotId());
                Map<String, VariablePO> varMap = MyCollectionUtils.listToMap(varList, VariablePO::getId);

                VariablePO var = varMap.get(collectConfig.getVariableId());
                if (Objects.nonNull(var)) {
                    LLMChatCollectInfo collectInfo = new LLMChatCollectInfo();
                    collectInfo.setName(renderPrompt(var.getName()));
                    collectInfo.setDescription(renderPrompt(collectConfig.getDesc()));
                    collectInfoList.add(collectInfo);
                } else {
                    log.warn("数据错误, 变量不存在, botId:{}, varId:{}", step.getBotId(), collectConfig.getVariableId());
                }
            }
            request.setCollectInfo(collectInfoList);
        } else {
            request.setCollectInfo(Collections.emptyList());
        }

        // 工具调用, 目前仅支持跳转
        List<LLMChatTool> tools = new ArrayList<>();

        Set<String> mismatchStepIdSet = processExcludeStepIdSet(stepConfig, allSteps);

        for (StepPO oneStep : allSteps) {
            if (StepTypeEnum.INDEPENDENT.equals(oneStep.getType())
                    && !mismatchStepIdSet.contains(oneStep.getId())) {
                if (StringUtils.isNotBlank(oneStep.getDesc())) {
                    LLMChatTool tool = new LLMChatTool();
                    tool.setName(String.format("tool-%s", oneStep.getLabel()));
                    tool.setDescription(oneStep.getDesc());
                    tools.add(tool);
                }
            }
        }

        request.setTools(tools);

        // rag 文档
        List<String> docIdList = docList.stream()
                .filter(doc -> RagDocumentStatusEnum.isSuccess(doc.getStatus()) && EnabledStatusEnum.ENABLE.equals(doc.getEnabledStatus()))
                .map(RagDocumentPO::getId).collect(Collectors.toList());
        request.setFileIdList(docIdList);

        return request;
    }

    static ImmutableSet<String> processExcludeStepIdSet(Mismatch node, List<StepPO> steps) {
        if (BooleanUtils.isTrue(node.getMismatchKnowledgeAndStep())) {
            if (BooleanUtils.isNotTrue(node.getMismatchAllStep()) && CollectionUtils.isNotEmpty(node.getMismatchStepIdList())) {
                return ImmutableSet.copyOf(node.getMismatchStepIdList());
            } else if (BooleanUtils.isTrue(node.getMismatchAllStep())) {
                return ImmutableSet.copyOf(steps.stream().map(StepPO::getId).collect(Collectors.toList()));
            }
        }
        return ImmutableSet.of();
    }

    private static String renderPrompt(String text) {
        return AnswerTextUtils.renderTemplate(text, new HashMap<>());
    }

    public static class ResponseAccumulator implements Accumulator<String, LLMStepTestResponseVO> {

        LLMStepTestRequestVO request;

        private String trackInfo = "";
        private Map<String, String> stepLabelToNameMap;
        private String action = "";

        long startMs = System.currentTimeMillis();
        public ResponseAccumulator(LLMStepTestRequestVO request, Map<String, String> stepLabelToNameMap) {
            this.request = request;
            this.stepLabelToNameMap = stepLabelToNameMap == null ? Collections.emptyMap() : stepLabelToNameMap;
        }

        @Override
        public Optional<LLMStepTestResponseVO> accumulate(String responseBody) {
            log.debug("responseBody:{}", responseBody);
            if (StringUtils.isBlank(responseBody)) {
                return Optional.empty();
            }

            try {
                LLMChatResponse response = JsonUtils.string2Object(responseBody, LLMChatResponse.class);
                if (response == null
                        || response.getCode() > 0) {
                    return Optional.empty();
                }
                LLMStepTestResponseVO result = new LLMStepTestResponseVO();
                result.setAnswer(response.getResponse());
                result.setUserInput(request.getUserInput());
                result.setStepId(request.getStepId());
                result.setBotId(request.getBotId());
                trackInfo = trackInfo + response.getTrackInfo();
                result.setTrackInfo(trackInfo);
                result.setIsComplete(false);
                long now = System.currentTimeMillis();
                result.setDurationMs((int)(now - startMs));
                if (CollectionUtils.isNotEmpty(response.getTools()) && StringUtils.isBlank(action)) {
                    for (ResponseTool tool : response.getTools()) {
                        if (tool.getName().startsWith("tool-")) {
                            String label = tool.getName().replace("tool-", "");
                            String stepName = stepLabelToNameMap.getOrDefault(label, label);
                            action = "【跳转到流程:" + stepName + "】";
                            break;
                        }
                    }
                }
                return Optional.of(result);
            } catch (Exception e) {
                log.error("Failed to parse response body: [{}]", responseBody, e);
            }
            return Optional.empty();
        }

        @Override
        public Optional<LLMStepTestResponseVO> onComplete() {
            LLMStepTestResponseVO result = new LLMStepTestResponseVO();
            if (StringUtils.isNotBlank(action)) {
                result.setAnswer(action);
            } else {
                result.setAnswer("");
            }
            result.setUserInput(request.getUserInput());
            result.setStepId(request.getStepId());
            result.setBotId(request.getBotId());
            result.setTrackInfo(trackInfo);
            result.setIsComplete(true);
            long now = System.currentTimeMillis();
            result.setDurationMs((int)(now - startMs));
            return Optional.of(result);
        }
    }

}
