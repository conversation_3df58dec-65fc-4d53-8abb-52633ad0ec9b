package com.yiwise.dialogflow.service.impl.analyze;

import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.analyze.*;
import com.yiwise.dialogflow.entity.vo.analyze.*;
import com.yiwise.dialogflow.service.analyze.*;
import com.yiwise.dialogflow.utils.ParseUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class AnalyzeTemplateIntentServiceImpl implements AnalyzeTemplateIntentService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private AnalyzeTemplateService analyzeTemplateService;

    @Resource
    private AnalyzeTemplateCorpusService analyzeTemplateCorpusService;

    @Resource
    private AnalyzeTaskService analyzeTaskService;

    private void checkExists(String templateId, String name) {
        if (exists(templateId, name)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "意图名称重复,请重新输入");
        }
    }

    @Override
    public boolean exists(String templateId, String name) {
        return mongoTemplate.exists(Query.query(Criteria.where("templateId").is(templateId).and("name").is(name)), AnalyzeTemplateIntentPO.COLLECTION_NAME);
    }

    @Override
    public void deleteByTemplateId(String templateId) {
        mongoTemplate.remove(Query.query(Criteria.where("templateId").is(templateId)), AnalyzeTemplateIntentPO.COLLECTION_NAME);
    }

    @Override
    public AnalyzeTemplateIntentPO getById(String id) {
        return mongoTemplate.findById(id, AnalyzeTemplateIntentPO.class, AnalyzeTemplateIntentPO.COLLECTION_NAME);
    }

    @Override
    public List<AnalyzeTemplateIntentPO> listByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return mongoTemplate.find(Query.query(Criteria.where("_id").in(ids)), AnalyzeTemplateIntentPO.class, AnalyzeTemplateIntentPO.COLLECTION_NAME);
    }

    private void checkCorpusExists(List<String> corpusList, String templateId) {
        Map<String, List<String>> existsCorpusMap = analyzeTemplateCorpusService.findExistsCorpusMap(templateId, corpusList);
        if (MapUtils.isNotEmpty(existsCorpusMap)) {
            List<String> intentIdList = existsCorpusMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
            Map<String, String> intentIdNameMap = MyCollectionUtils.listToMap(listByIds(intentIdList), AnalyzeTemplateIntentPO::getId, AnalyzeTemplateIntentPO::getName);
            StringJoiner sj = new StringJoiner(";");
            existsCorpusMap.forEach((desc, list) -> sj.add(String.format("语料[%s]已存在于意图[%s]", desc,
                    String.join(",", list.stream().map(intentIdNameMap::get).collect(Collectors.joining(","))))));
            throw new ComException(ComErrorCode.VALIDATE_ERROR, sj.toString());
        }
    }

    @Override
    public void add(AnalyzeTemplateIntentAddVO request) {
        String templateId = request.getTemplateId();
        AnalyzeTemplatePO template = analyzeTemplateService.getById(templateId);
        if (Objects.isNull(template)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "模板不存在");
        }

        checkExists(templateId, request.getName());

        if (AnalyzeTemplateModelTypeEnum.isRejectReason(template.getModelType()) && Objects.isNull(request.getCategory())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "category不能为空");
        }

        if (AnalyzeTemplateModelTypeEnum.isLLM(template.getModelType())) {
            checkCorpusExists(request.getCorpusList(), templateId);
        }

        // 添加意图
        AnalyzeTemplateIntentPO intent = new AnalyzeTemplateIntentPO();
        intent.setTemplateId(templateId);
        intent.setName(request.getName());
        intent.setType(AnalyzeTemplateIntentTypeEnum.CUSTOMIZED);
        intent.setCategory(request.getCategory());
        intent.setCorpusSize((long) request.getCorpusList().size());
        mongoTemplate.save(intent, AnalyzeTemplateIntentPO.COLLECTION_NAME);

        // 添加语料
        List<AnalyzeTemplateCorpusPO> corpusList = request.getCorpusList().stream().map(c -> {
            AnalyzeTemplateCorpusPO corpus = new AnalyzeTemplateCorpusPO();
            corpus.setTemplateId(templateId);
            corpus.setIntentId(intent.getId());
            corpus.setCorpus(c);
            return corpus;
        }).collect(Collectors.toList());
        analyzeTemplateCorpusService.batchSave(corpusList);
    }

    @Override
    public String createTaskIntent(String templateId, String name) {
        AnalyzeTemplateIntentPO intent = new AnalyzeTemplateIntentPO();
        intent.setTemplateId(templateId);
        intent.setName(name);
        intent.setType(AnalyzeTemplateIntentTypeEnum.CUSTOMIZED);
        intent.setCategory(AnalyzeTemplateIntentCategoryEnum.WHITE);
        intent.setCorpusSize(0L);
        mongoTemplate.save(intent, AnalyzeTemplateIntentPO.COLLECTION_NAME);
        return intent.getId();
    }

    @Override
    public void update(AnalyzeTemplateIntentUpdateVO request) {
        String intentId = request.getId();
        AnalyzeTemplateIntentPO old = getById(intentId);
        if (Objects.isNull(old)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "意图不存在");
        }
        AnalyzeTemplatePO template = analyzeTemplateService.getById(old.getTemplateId());
        if (Objects.isNull(template)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "模板不存在");
        }
        if (AnalyzeTemplateModelTypeEnum.isLLM(template.getModelType()) && CollectionUtils.isEmpty(request.getCorpusList())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "意图描述不能为空");
        }
        if (AnalyzeTemplateIntentTypeEnum.isBuiltin(old.getType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "内置意图不允许改名");
        }
        String name = request.getName();
        String templateId = old.getTemplateId();
        if (!StringUtils.equals(name, old.getName())) {
            checkExists(templateId, name);
        }

        if (AnalyzeTemplateModelTypeEnum.isLLM(template.getModelType())) {
            List<String> oldCorpusList = MyCollectionUtils.listToConvertList(analyzeTemplateCorpusService.listByIntentIdList(Collections.singletonList(intentId)), AnalyzeTemplateCorpusPO::getCorpus);
            List<String> newCorpusList = ListUtils.subtract(request.getCorpusList(), oldCorpusList);
            if (CollectionUtils.isNotEmpty(newCorpusList)) {
                checkCorpusExists(newCorpusList, templateId);
            }
            // 删除旧语料
            analyzeTemplateCorpusService.deleteByIntentIdList(Collections.singletonList(intentId));
            // 保存新语料
            List<AnalyzeTemplateCorpusPO> corpusList = request.getCorpusList().stream().map(c -> {
                AnalyzeTemplateCorpusPO corpus = new AnalyzeTemplateCorpusPO();
                corpus.setTemplateId(templateId);
                corpus.setIntentId(intentId);
                corpus.setCorpus(c);
                return corpus;
            }).collect(Collectors.toList());
            analyzeTemplateCorpusService.batchSave(corpusList);
            // 修改语料数量
            old.setCorpusSize((long) request.getCorpusList().size());
        }
        old.setName(name);
        mongoTemplate.save(old, AnalyzeTemplateIntentPO.COLLECTION_NAME);
    }

    private Query buildQuery(AnalyzeTemplateIntentQueryVO request) {
        Query query = new Query();
        query.addCriteria(Criteria.where("templateId").is(request.getTemplateId()));
        if (StringUtils.isNotBlank(request.getSearch())) {
            query.addCriteria(Criteria.where("name").regex(ParseUtil.regex(request.getSearch())));
        }
        if (Objects.nonNull(request.getType())) {
            query.addCriteria(Criteria.where("type").is(request.getType()));
        }
        if (Objects.nonNull(request.getCategory())) {
            query.addCriteria(Criteria.where("category").is(request.getCategory()));
        }
        if (CollectionUtils.isNotEmpty(request.getIdList())) {
            query.addCriteria(Criteria.where("_id").in(request.getIdList()));
        }
        if (BooleanUtils.isTrue(request.getCorpusSizeGtZero())) {
            query.addCriteria(Criteria.where("corpusSize").gt(0L));
        }
        if (Objects.nonNull(request.getIsNew())) {
            if (BooleanUtils.isTrue(request.getIsNew())) {
                query.addCriteria(Criteria.where("srcIntentId").is(null));
            } else {
                query.addCriteria(Criteria.where("srcIntentId").ne(null));
            }
        }
        query.addCriteria(Criteria.where("parentIntentId").is(null));
        return query;
    }

    private List<String> findIdListByName(String templateId, String name) {
        return mongoTemplate.findDistinct(
                Query.query(Criteria.where("templateId").is(templateId).and("name").regex(ParseUtil.regex(name))),
                "_id", AnalyzeTemplateIntentPO.class, ObjectId.class
        ).stream().map(ObjectId::toString).collect(Collectors.toList());
    }

    private List<String> findIdListBySrcIntentIdList(String templateId, List<String> srcIntentIdList) {
        return mongoTemplate.findDistinct(
                Query.query(Criteria.where("templateId").is(templateId).and("srcIntentId").in(srcIntentIdList)),
                "_id", AnalyzeTemplateIntentPO.class, ObjectId.class
        ).stream().map(ObjectId::toString).collect(Collectors.toList());
    }

    @Override
    public PageResultObject<AnalyzeTemplateIntentVO> list(AnalyzeTemplateIntentQueryVO request) {
        AnalyzeTemplatePO template = analyzeTemplateService.getById(request.getTemplateId());
        if (Objects.isNull(template)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "模板不存在");
        }
        if (AnalyzeTemplateModelTypeEnum.isLLM(template.getModelType()) && StringUtils.isNotBlank(request.getSearch())) {
            List<String> intentIdList = null;
            if (AnalyzeTemplateTypeEnum.isTask(template.getType())) {
                AnalyzeTaskPO task = analyzeTaskService.getByResultTemplateId(template.getId());
                if (Objects.nonNull(task)) {
                    List<String> srcIntentIdList = analyzeTemplateCorpusService.finsIntentIdListByCorpus(task.getTemplateId(), request.getSearch());
                    if (CollectionUtils.isNotEmpty(srcIntentIdList)) {
                        intentIdList = findIdListBySrcIntentIdList(template.getId(), srcIntentIdList);
                    }
                }
            } else {
                intentIdList = new ArrayList<>(analyzeTemplateCorpusService.finsIntentIdListByCorpus(request.getTemplateId(), request.getSearch()));
            }
            if (CollectionUtils.isNotEmpty(intentIdList)) {
                intentIdList.addAll(findIdListByName(request.getTemplateId(), request.getSearch()));
                if (CollectionUtils.isNotEmpty(intentIdList)) {
                    request.setIdList(intentIdList);
                    request.setSearch(null);
                }
            }
        }

        Query query = buildQuery(request);
        long count = mongoTemplate.count(query, AnalyzeTemplateIntentPO.COLLECTION_NAME);
        if (count <= 0L) {
            return PageResultObject.of(Collections.emptyList(), request.getPageNum(), request.getPageSize());
        }
        query.with(PageRequest.of(request.getPageNum() - 1, request.getPageSize()));
        query.with(Sort.by(Sort.Direction.DESC, "corpusSize"));
        List<AnalyzeTemplateIntentVO> resultList = mongoTemplate.find(query, AnalyzeTemplateIntentVO.class, AnalyzeTemplateIntentPO.COLLECTION_NAME);
        wrap(resultList, template);
        return PageResultObject.of(resultList, request.getPageNum(), request.getPageSize(), (int) count);
    }

    @Override
    public void delete(AnalyzeTemplateIntentQueryVO request) {
        Query query = buildQuery(request);
        List<AnalyzeTemplateIntentPO> deletedIntentList = mongoTemplate.findAllAndRemove(query, AnalyzeTemplateIntentPO.class, AnalyzeTemplateIntentPO.COLLECTION_NAME);
        if (CollectionUtils.isNotEmpty(deletedIntentList)) {
            List<String> deletedIntentIdList = MyCollectionUtils.listToConvertList(deletedIntentList, AnalyzeTemplateIntentPO::getId);
            analyzeTemplateCorpusService.deleteByIntentIdList(deletedIntentIdList);
        }
    }

    private void wrap(List<AnalyzeTemplateIntentVO> resultList, AnalyzeTemplatePO template) {
        if (AnalyzeTemplateTypeEnum.isTask(template.getType())) {
            List<String> srcIntentIdList = listByTemplateId(template.getId()).stream().map(AnalyzeTemplateIntentPO::getSrcIntentId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            List<AnalyzeTemplateCorpusPO> corpusList = analyzeTemplateCorpusService.listByIntentIdList(srcIntentIdList);
            if (CollectionUtils.isNotEmpty(corpusList)) {
                Map<String, List<String>> intentIdCorpusListMap = corpusList.stream().collect(Collectors.groupingBy(AnalyzeTemplateCorpusPO::getIntentId,
                        Collectors.mapping(AnalyzeTemplateCorpusPO::getCorpus, Collectors.toList())));
                for (AnalyzeTemplateIntentVO intent : resultList) {
                    intent.setCorpusList(intentIdCorpusListMap.getOrDefault(intent.getSrcIntentId(), Collections.emptyList()));
                }
            }
            return;
        }
        if (AnalyzeTemplateModelTypeEnum.isLLM(template.getModelType())) {
            List<AnalyzeTemplateCorpusPO> corpusList = analyzeTemplateCorpusService.listByTemplateId(template.getId());
            if (CollectionUtils.isNotEmpty(corpusList)) {
                Map<String, List<String>> intentIdCorpusListMap = corpusList.stream().collect(Collectors.groupingBy(AnalyzeTemplateCorpusPO::getIntentId,
                        Collectors.mapping(AnalyzeTemplateCorpusPO::getCorpus, Collectors.toList())));
                for (AnalyzeTemplateIntentVO intent : resultList) {
                    intent.setCorpusList(intentIdCorpusListMap.getOrDefault(intent.getId(), Collections.emptyList()));
                }
            }
            return;
        }
        List<String> mergeIntentIdList = resultList.stream().filter(i -> AnalyzeTemplateIntentTypeEnum.isMerge(i.getType()))
                .map(AnalyzeTemplateIntentPO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(mergeIntentIdList)) {
            List<AnalyzeTemplateIntentPO> childIntentList = mongoTemplate.find(Query.query(Criteria.where("parentIntentId").in(mergeIntentIdList)),
                    AnalyzeTemplateIntentPO.class, AnalyzeTemplateIntentPO.COLLECTION_NAME);
            // <parentIntentId, childNameList>
            Map<String, List<String>> childMap = childIntentList.stream().collect(Collectors.groupingBy(AnalyzeTemplateIntentPO::getParentIntentId,
                    Collectors.mapping(AnalyzeTemplateIntentPO::getName, Collectors.toList())));
            for (AnalyzeTemplateIntentVO intent : resultList) {
                String id = intent.getId();
                if (childMap.containsKey(id)) {
                    intent.setChildNameList(childMap.get(id));
                }
            }
        }
    }

    @Override
    public void toggleCategory(AnalyzeTemplateIntentToggleVO request) {
        Query query = buildQuery(request);
        mongoTemplate.updateMulti(query, Update.update("category", request.getTargetCategory()), AnalyzeTemplateIntentPO.COLLECTION_NAME);
    }

    @Override
    public List<AnalyzeTemplateIntentPO> listByTemplateId(String templateId) {
        return mongoTemplate.find(Query.query(Criteria.where("templateId").is(templateId)), AnalyzeTemplateIntentPO.class, AnalyzeTemplateIntentPO.COLLECTION_NAME);
    }

    @Override
    public void merge(AnalyzeTemplateIntentMergeVO request, Long userId) {
        String templateId = request.getTemplateId();
        checkExists(templateId, request.getTargetName());

        Query query = buildQuery(request);
        List<AnalyzeTemplateIntentPO> intentList = mongoTemplate.find(query, AnalyzeTemplateIntentPO.class, AnalyzeTemplateIntentPO.COLLECTION_NAME);
        if (CollectionUtils.isEmpty(intentList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "所选意图为空");
        }
        long corpusSize = intentList.stream().mapToLong(AnalyzeTemplateIntentPO::getCorpusSize).sum();
        // 需要更新的意图id列表
        List<String> intentIdList = intentList.stream().map(AnalyzeTemplateIntentPO::getId).collect(Collectors.toList());

        // 添加意图
        AnalyzeTemplateIntentPO intent = new AnalyzeTemplateIntentPO();
        intent.setTemplateId(templateId);
        intent.setName(request.getTargetName());
        intent.setType(AnalyzeTemplateIntentTypeEnum.MERGE);
        intent.setCategory(request.getTargetCategory());
        // 语料数量为子意图语料之和
        intent.setCorpusSize(corpusSize);
        mongoTemplate.save(intent, AnalyzeTemplateIntentPO.COLLECTION_NAME);

        // 批量更新子意图
        mongoTemplate.updateMulti(
                Query.query(Criteria.where("_id").in(intentIdList)),
                Update.update("parentIntentId", intent.getId()),
                AnalyzeTemplateIntentPO.COLLECTION_NAME);
    }

    @Override
    public void batchSave(List<AnalyzeTemplateIntentPO> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            mongoTemplate.insert(list, AnalyzeTemplateIntentPO.COLLECTION_NAME);
        }
    }

    @Override
    public void revert(AnalyzeTemplateIntentRevertVO request) {
        // 删除合并意图
        AnalyzeTemplateIntentPO intent = mongoTemplate.findAndRemove(
                Query.query(Criteria.where("templateId").is(request.getTemplateId()).and("_id").is(request.getId()).and("type").is(AnalyzeTemplateIntentTypeEnum.MERGE)),
                AnalyzeTemplateIntentPO.class, AnalyzeTemplateIntentPO.COLLECTION_NAME
        );
        if (Objects.nonNull(intent)) {
            // 批量更新子意图
            mongoTemplate.updateMulti(
                    Query.query(Criteria.where("parentIntentId").is(intent.getId())),
                    Update.update("parentIntentId", null),
                    AnalyzeTemplateIntentPO.COLLECTION_NAME
            );
        }
    }

    @Override
    public void incrementCorpusSize(String intentId, Long corpusSize) {
        AnalyzeTemplateIntentPO intent = mongoTemplate.findAndModify(
                Query.query(Criteria.where("_id").is(intentId)),
                new Update().inc("corpusSize", corpusSize),
                AnalyzeTemplateIntentPO.class,
                AnalyzeTemplateIntentPO.COLLECTION_NAME
        );
        if (Objects.nonNull(intent) && StringUtils.isNotBlank(intent.getParentIntentId())) {
            incrementCorpusSize(intent.getParentIntentId(), corpusSize);
        }
    }

    @Override
    public List<String> findAllDescendantIntentIdList(String intentId) {
        return recursiveFindAllDescendantIntentIdList(Collections.singletonList(intentId));
    }

    private List<String> recursiveFindAllDescendantIntentIdList(List<String> intentIdList) {
        List<String> childIdList = mongoTemplate.findDistinct(Query.query(Criteria.where("parentIntentId").in(intentIdList)),
                "_id", AnalyzeTemplateIntentPO.COLLECTION_NAME, AnalyzeTemplateIntentPO.class, ObjectId.class)
                .stream().map(ObjectId::toString).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(childIdList)) {
            childIdList.addAll(recursiveFindAllDescendantIntentIdList(childIdList));
        }
        return childIdList;
    }
}
