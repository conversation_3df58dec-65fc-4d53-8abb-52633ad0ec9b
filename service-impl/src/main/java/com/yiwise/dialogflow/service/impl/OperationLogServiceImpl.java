package com.yiwise.dialogflow.service.impl;

import com.google.common.collect.Lists;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.OssKeyCenter;
import com.yiwise.dialogflow.entity.dto.OperationLogDTO;
import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum;
import com.yiwise.dialogflow.entity.po.OperationLogPO;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.OperationLogQueryVO;
import com.yiwise.dialogflow.entity.vo.OperationLogVO;
import com.yiwise.dialogflow.service.OperationLogService;
import com.yiwise.dialogflow.service.remote.UserService;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import com.yiwise.middleware.objectstorage.serializer.AddOssPrefixSerializer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.MDC;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.ArithmeticOperators;
import org.springframework.data.mongodb.core.aggregation.DateOperators;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/13
 */
@Slf4j
@Service
public class OperationLogServiceImpl implements OperationLogService {

    static final String HOST_NAME = ResultObject.success().getHost();

    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private UserService userService;
    @Resource
    private ObjectStorageHelper objectStorageHelper;
    private static final String HIDDEN_MODE = "hm:";

    @Override
    public List<IdNamePair<String, String>> resourceTypeList() {
        return Arrays.stream(OperationLogResourceTypeEnum.values()).map(s -> new IdNamePair<>(s.name(), s.getDesc())).collect(Collectors.toList());
    }

    @Override
    public List<IdNamePair<String, String>> logTypeList() {
        return Arrays.stream(OperationLogTypeEnum.values()).map(s -> new IdNamePair<>(s.name(), s.getDesc())).collect(Collectors.toList());
    }

    @Override
    public List<IdNamePair<Long, String>> operatorList(Long botId) {
        Query query = Query.query(Criteria.where("botId").is(botId).and("createUserId").ne(null));
        List<Long> userIdList = mongoTemplate.findDistinct(query, "createUserId", OperationLogPO.COLLECTION_NAME, OperationLogPO.class, Long.class);
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        return userService.getUserByIdList(userIdList).stream().map(s -> new IdNamePair<>(s.getUserId(), s.getName())).collect(Collectors.toList());
    }

    @Override
    public void save(Long botId, OperationLogTypeEnum type, OperationLogResourceTypeEnum resourceType, String detail, Long operatorId) {
        save(botId, type, resourceType, Collections.singletonList(detail), operatorId);
    }

    @Override
    public void save(Long botId, OperationLogTypeEnum type, OperationLogResourceTypeEnum resourceType, List<String> detailList, Long operatorId) {
        List<OperationLogDTO> list = detailList.stream()
                .map(s -> OperationLogDTO.builder().botId(botId).type(type).resourceType(resourceType).detail(s).operatorId(operatorId).build())
                .collect(Collectors.toList());
        batchSave(list);
    }

    @Override
    public void batchSave(List<OperationLogDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        if (BooleanUtils.isTrue(IGNORE.get())) {
            log.info("ignore save operation log, list: {}", list);
            return;
        }
        String requestId = MDC.get(ApplicationConstant.MDC_LOG_ID);
        LocalDateTime now = LocalDateTime.now();
        List<OperationLogPO> poList = Lists.newArrayList();
        for (OperationLogDTO dto : list) {
            OperationLogPO po = new OperationLogPO();
            po.setBotId(dto.getBotId());
            po.setType(dto.getType());
            po.setResourceType(dto.getResourceType());
            po.setDetail(dto.getDetail());
            po.setCreateTime(now);
            po.setUpdateTime(now);
            po.setCreateUserId(dto.getOperatorId());
            po.setUpdateUserId(dto.getOperatorId());
            po.setCreateUserName(Optional.ofNullable(dto.getOperatorId()).map(userService::getUserById).map(UserPO::getName).orElse(null));
            po.setHostName(HOST_NAME);
            po.setRequestId(requestId);
            poList.add(po);
        }
        mongoTemplate.insert(poList, OperationLogPO.COLLECTION_NAME);
    }

    private boolean isHiddenMode(String search) {
        return StringUtils.isNotBlank(search) && StringUtils.startsWith(search, HIDDEN_MODE);
    }

    private Query toUnPageQuery(OperationLogQueryVO queryVO) {
        Query query = new Query();
        List<Criteria> criteriaList = Lists.newArrayList();
        criteriaList.add(Criteria.where("botId").is(queryVO.getBotId()));
        if (Objects.nonNull(queryVO.getStartTime())) {
            criteriaList.add(Criteria.where("createTime").gte(queryVO.getStartTime()));
        }
        if (Objects.nonNull(queryVO.getEndTime())) {
            criteriaList.add(Criteria.where("createTime").lte(queryVO.getEndTime()));
        }
        String detailKeyword = queryVO.getDetailKeyword();
        if (isHiddenMode(detailKeyword)) {
            detailKeyword = detailKeyword.replaceFirst(HIDDEN_MODE, "");
        } else {
            criteriaList.add(Criteria.where("resourceType").nin(OperationLogResourceTypeEnum.hiddenModeTypeList()));
        }
        if (StringUtils.isNotBlank(detailKeyword)) {
            criteriaList.add(Criteria.where("detail").regex(detailKeyword));
        }
        if (CollectionUtils.isNotEmpty(queryVO.getResourceTypeList())) {
            criteriaList.add(Criteria.where("resourceType").in(queryVO.getResourceTypeList()));
        }
        if (CollectionUtils.isNotEmpty(queryVO.getOperatorIdList())) {
            criteriaList.add(Criteria.where("createUserId").in(queryVO.getOperatorIdList()));
        }
        return query.addCriteria(new Criteria().andOperator(criteriaList.toArray(new Criteria[]{})));
    }

    @Override
    public PageResultObject<OperationLogVO> list(OperationLogQueryVO queryVO) {
        Query query = toUnPageQuery(queryVO);
        long count = mongoTemplate.count(query, OperationLogPO.COLLECTION_NAME);
        query.with(PageRequest.of(queryVO.getPageNum() - 1, queryVO.getPageSize()));
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));
        List<OperationLogPO> resultList = mongoTemplate.find(query, OperationLogPO.class, OperationLogPO.COLLECTION_NAME);
        return PageResultObject.of(wrapVo(resultList), queryVO.getPageNum(), queryVO.getPageSize(), (int) count);
    }

    private List<OperationLogVO> wrapVo(List<OperationLogPO> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }
        return resultList.stream().map(po -> {
            OperationLogVO vo = new OperationLogVO();
            vo.setId(po.getId());
            vo.setBotId(po.getBotId());
            vo.setType(po.getType());
            vo.setLogTypeName(Optional.ofNullable(po.getType()).map(OperationLogTypeEnum::getDesc).orElse(null));
            vo.setResourceType(po.getResourceType());
            vo.setResourceTypeName(Optional.ofNullable(po.getResourceType()).map(OperationLogResourceTypeEnum::getDesc).orElse(null));
            vo.setDetail(po.getDetail());
            vo.setOperatorId(po.getCreateUserId());
            vo.setOperatorName(po.getCreateUserName());
            vo.setOperateTime(po.getCreateTime());
            vo.setRequestId(po.getRequestId());
            vo.setHostName(po.getHostName());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public String export(OperationLogQueryVO queryVO) {
        queryVO.setPageSize(Integer.MAX_VALUE);
        queryVO.setPageNum(1);
        List<OperationLogVO> dataList = list(queryVO).getContent();

        SXSSFWorkbook workbook = new SXSSFWorkbook();
        SXSSFSheet sheet = workbook.createSheet("操作日志");
        writeToExcel(dataList, sheet);

        String fileName = "操作日志导出-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + ".xlsx";
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            workbook.write(bos);
            return AddOssPrefixSerializer.getAddOssPrefixUrl(objectStorageHelper.upload(OssKeyCenter.getBotOperationLogExportPath(queryVO.getBotId(), fileName), bos.toByteArray()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "导出失败");
        }
    }

    private static void writeToExcel(List<OperationLogVO> dataList, SXSSFSheet sheet) {
        sheet.setDefaultRowHeightInPoints(22);
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(2, 20 * 256);
        sheet.setColumnWidth(3, 20 * 256);
        sheet.setColumnWidth(4, 100 * 256);
        SXSSFRow title = sheet.createRow(0);
        title.createCell(0).setCellValue("日志类型");
        title.createCell(1).setCellValue("操作对象");
        title.createCell(2).setCellValue("操作时间");
        title.createCell(3).setCellValue("操作人");
        title.createCell(4).setCellValue("日志详情");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (int i = 0; i < dataList.size(); i++) {
            SXSSFRow row = sheet.createRow(i + 1);
            OperationLogVO item = dataList.get(i);
            row.createCell(0).setCellValue(item.getLogTypeName());
            row.createCell(1).setCellValue(item.getResourceTypeName());
            row.createCell(2).setCellValue(item.getOperateTime().format(formatter));
            row.createCell(3).setCellValue(item.getOperatorName());
            row.createCell(4).setCellValue(item.getDetail());
        }
    }

    @Override
    public List<String> operationDateList(Long botId, LocalDateTime startTime, LocalDateTime endTime) {
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("botId").is(botId).and("createTime").gte(startTime).lte(endTime)),
                Aggregation.project().and(DateOperators.DateToString.dateOf(
                        ArithmeticOperators.Add.valueOf("createTime").add(8 * 3600000)).toString("%Y-%m-%d")).as("date"),
                Aggregation.group("date"),
                Aggregation.sort(Sort.Direction.ASC, "_id")
        );
        return mongoTemplate.aggregate(aggregation, OperationLogPO.COLLECTION_NAME, Map.class).getMappedResults()
                .stream().map(map -> map.get("_id")).map(String::valueOf).collect(Collectors.toList());
    }
}
