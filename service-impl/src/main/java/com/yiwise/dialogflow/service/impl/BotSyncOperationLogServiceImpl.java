package com.yiwise.dialogflow.service.impl;

import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.entity.bo.algorithm.ResourceId2NameBO;
import com.yiwise.dialogflow.entity.dto.OperationLogDTO;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.po.intent.IntentRuleActionPO;
import com.yiwise.dialogflow.entity.po.intent.IntentRulePO;
import com.yiwise.dialogflow.entity.query.EntitySyncVO;
import com.yiwise.dialogflow.entity.vo.StepVO;
import com.yiwise.dialogflow.entity.vo.audio.request.PublicAudioSyncRequestVO;
import com.yiwise.dialogflow.entity.vo.sync.*;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.intent.IntentRuleService;
import com.yiwise.dialogflow.utils.IntentRuleContentRenderUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BotSyncOperationLogServiceImpl implements BotSyncOperationLogService {

    @Lazy
    @Resource
    private BotService botService;

    @Lazy
    @Resource
    private StepService stepService;

    @Resource
    private OperationLogService operationLogService;

    @Lazy
    @Resource
    private KnowledgeService knowledgeService;

    @Lazy
    @Resource
    private IntentRuleService intentRuleService;

    private static final Map<SyncModeEnum, String> SYNC_MODE_NAME_MAP;
    private static final Map<SyncScopeEnum, String> SYNC_SCOPE_NAME_MAP;

    private static final String UNKNOWN_SYNC_MODE = "未知";
    private static final String UNKNOWN_SYNC_SCOPE = "未知";

    static {
        HashMap<SyncModeEnum, String> tmpModeMap = new HashMap<>(4);
        tmpModeMap.put(SyncModeEnum.COVER, "覆盖");
        tmpModeMap.put(SyncModeEnum.SKIP, "跳过");
        tmpModeMap.put(SyncModeEnum.ADD, "新增");
        tmpModeMap.put(SyncModeEnum.COMPLETE, "补充");
        SYNC_MODE_NAME_MAP = Collections.unmodifiableMap(tmpModeMap);

        HashMap<SyncScopeEnum, String> tmpScopeMap = new HashMap<>(4);
        tmpScopeMap.put(SyncScopeEnum.BASIC_INFORMATION, "基本信息");
        tmpScopeMap.put(SyncScopeEnum.AUDIO, "音频");
        tmpScopeMap.put(SyncScopeEnum.ANSWER, "答案话术");
        tmpScopeMap.put(SyncScopeEnum.POST_ANSWER_OPERATION, "回答后操作");
        tmpScopeMap.put(SyncScopeEnum.OTHER_CONFIG, "其他设置");
        tmpScopeMap.put(SyncScopeEnum.USER_SILENCE, "无应答时长设置");
        tmpScopeMap.put(SyncScopeEnum.MODAL_PARTICLE, "语气词过滤设置");
        tmpScopeMap.put(SyncScopeEnum.INPUT_MERGE, "断句补齐设置");
        SYNC_SCOPE_NAME_MAP = Collections.unmodifiableMap(tmpScopeMap);
    }

    @Override
    public void stepSync(StepVO step, StepSyncVO syncConfig, List<Long> successBotIdList, List<Long> failBotIdList) {
        if (Objects.isNull(syncConfig)
                || CollectionUtils.isEmpty(syncConfig.getTargetBotIdList())
                || CollectionUtils.isEmpty(syncConfig.getTargetIdList())) {
            return;
        }
        Long srcBotId = syncConfig.getSrcBotId();
        Map<Long, BotPO> botMap = getRefBotMap(syncConfig);
        BotPO srcBot = botMap.get(srcBotId);
        if (Objects.isNull(srcBot)) {
            log.error("botSyncOperationLogService.stepSync, bot not exist, botId:{}", srcBotId);
            return;
        }

        List<String> commonOperationDetaiList = new ArrayList<>();
        commonOperationDetaiList.add(String.format("源Bot: %s", srcBot.getName()));
        commonOperationDetaiList.add(String.format("%s:【%s】同步至【%s】", step.getType().getDesc(),
                String.join(", ", step.getName()), syncConfig.getStepTypeEnum().getDesc()));
        commonOperationDetaiList.add(String.format("流程相同处理: %s", SYNC_MODE_NAME_MAP.getOrDefault(syncConfig.getSameStep(), UNKNOWN_SYNC_MODE)));
        commonOperationDetaiList.add(String.format("触发意图相同处理: %s", SYNC_MODE_NAME_MAP.getOrDefault(syncConfig. getSameTriggerIntent(), UNKNOWN_SYNC_MODE)));
        commonOperationDetaiList.add(String.format("节点意图相同处理: %s", SYNC_MODE_NAME_MAP.getOrDefault(syncConfig. getSameNodeIntent(), UNKNOWN_SYNC_MODE)));
        commonOperationDetaiList.add(String.format("实体名称相同处理: %s", SYNC_MODE_NAME_MAP.getOrDefault(syncConfig. getSameEntity(), UNKNOWN_SYNC_MODE)));
        commonOperationDetaiList.add(String.format("同步真人录音: %s", generateBooleanDesc(syncConfig.getSyncAudio())));
        generateTargetBotInfo(successBotIdList, failBotIdList, botMap, commonOperationDetaiList);
        String logDetail = String.join(";\n", commonOperationDetaiList);
        saveLog(srcBotId, syncConfig.getTargetBotIdList(), logDetail, OperationLogResourceTypeEnum.STEP, syncConfig.getCurrentUserId());
    }

    @Override
    public void nodeSync(DialogBaseNodePO node, NodeSyncVO syncConfig, List<Long> successBotIdList, List<Long> failBotIdList) {
        Long srcBotId = syncConfig.getSrcBotId();
        Map<Long, BotPO> botMap = getRefBotMap(syncConfig);
        BotPO srcBot = botMap.get(srcBotId);
        if (Objects.isNull(srcBot)) {
            log.error("botSyncOperationLogService.nodeSync, bot not exist, botId:{}", srcBotId);
            return;
        }
        List<String> commonOperationDetaiList = new ArrayList<>();
        commonOperationDetaiList.add(String.format("源Bot: %s", srcBot.getName()));
        commonOperationDetaiList.add(String.format("源节点：【%s:%s】", node.getLabel(), node.getName()));
        commonOperationDetaiList.add(String.format("不允许用户打断: %s", generateBooleanDesc(syncConfig.getSyncUninterrupted())));
        commonOperationDetaiList.add(String.format("不关联问答知识/流程/特殊语境: %s", generateBooleanDesc(syncConfig.getSyncMismatch())));
        commonOperationDetaiList.add(String.format("设置用户无应答时长: %s", generateBooleanDesc(syncConfig.getSyncCustomUserSilence())));
        commonOperationDetaiList.add(String.format("设置AI应答时长: %s", generateBooleanDesc(syncConfig.getSyncWaitUserSayFinish())));
        commonOperationDetaiList.add(String.format("设置意向分类: %s", generateBooleanDesc(syncConfig.getSyncIntentLevel())));
        commonOperationDetaiList.add(String.format("返回时执行跳转原主动流程逻辑: %s", generateBooleanDesc(syncConfig.getSyncPullback())));
        commonOperationDetaiList.add(String.format("返回时语音播报设置: %s", generateBooleanDesc(syncConfig.getSyncCustomReplay())));
        if (BooleanUtils.isTrue(syncConfig.getSyncAssign()) && CollectionUtils.isNotEmpty(syncConfig.getSyncActionCategoryList())) {
            commonOperationDetaiList.add(String.format("触发动作:【%s】", syncConfig.getSyncActionCategoryList().stream().map(ActionCategoryEnum::getDesc).collect(Collectors.joining(","))));
        } else {
            commonOperationDetaiList.add(String.format("触发动作: %s", generateBooleanDesc(syncConfig.getSyncAssign())));
        }
        commonOperationDetaiList.add(String.format("动态变量赋值: %s", generateBooleanDesc(syncConfig.getSyncAssign())));
        commonOperationDetaiList.add(String.format("跳转至: %s", generateBooleanDesc(syncConfig.getSyncJump())));
        commonOperationDetaiList.add(String.format("节点话术: %s", generateBooleanDesc(syncConfig.getSyncAnswer())));
        commonOperationDetaiList.add(String.format("音频: %s", generateBooleanDesc(syncConfig.getSyncAudio())));
        commonOperationDetaiList.add(String.format("节点名称: %s", generateBooleanDesc(syncConfig.getSyncNodeName())));
        generateTargetBotInfo(successBotIdList, failBotIdList, botMap, commonOperationDetaiList);
        String logDetail = String.join(";\n", commonOperationDetaiList);
        saveLog(srcBotId, syncConfig.getTargetBotIdList(), logDetail, OperationLogResourceTypeEnum.NODE, syncConfig.getCurrentUserId());
    }

    private String generateBooleanDesc(Boolean value) {
        return BooleanUtils.isTrue(value) ? "是" : "否";
    }

    private Map<Long, BotPO> getRefBotMap(BasicSyncVO sync) {
        Long srcBotId = sync.getSrcBotId();
        List<Long> allBotId = new ArrayList<>(sync.getTargetBotIdList().size() + 1);
        allBotId.add(srcBotId);
        allBotId.addAll(sync.getTargetBotIdList());
        List<BotPO> botList = botService.getByIdList(allBotId);
        return MyCollectionUtils.listToMap(botList, BotPO::getBotId);
    }

    @Override
    public void knowledgeSync(KnowledgeSyncVO syncConfig,
                              List<String> srcKnowledgeIdList,
                              List<Long> successBotIdList,
                              List<Long> failBotIdList) {
        if (Objects.isNull(syncConfig)
                || Objects.isNull(syncConfig.getSrcBotId())
                || CollectionUtils.isEmpty(syncConfig.getTargetBotIdList())
                || CollectionUtils.isEmpty(srcKnowledgeIdList)) {
            return;
        }

        Long srcBotId = syncConfig.getSrcBotId();
        Map<Long, BotPO> botMap = getRefBotMap(syncConfig);
        BotPO srcBot = botMap.get(srcBotId);
        if (Objects.isNull(srcBot)) {
            log.error("botSyncOperationLogService.knowledgeSync, bot not exist, botId:{}", srcBotId);
            return;
        }
        List<KnowledgePO> knowledgeList = knowledgeService.getByIdList(srcBotId, srcKnowledgeIdList);
        List<String> knowledgeNameList = knowledgeList.stream()
                .map(item -> String.format("%s%s", item.getLabel(), item.getName()))
                .collect(Collectors.toList());
        List<String> commonOperationDetaiList = new ArrayList<>();
        commonOperationDetaiList.add(String.format("源Bot: %s", srcBot.getName()));
        commonOperationDetaiList.add(String.format("知识列表: 【%s】", String.join(", ", knowledgeNameList)));
        String syncScope = "--";
        if (CollectionUtils.isNotEmpty(syncConfig.getSyncScopeEnumSet())) {
            syncScope = syncConfig.getSyncScopeEnumSet().stream()
                    .map(SYNC_SCOPE_NAME_MAP::get)
                    .collect(Collectors.joining(", "));
        }
        commonOperationDetaiList.add(String.format("同步内容: %s", syncScope));
        commonOperationDetaiList.add(String.format("知识相同处理: %s", SYNC_MODE_NAME_MAP.getOrDefault(syncConfig.getSameKnowledge(), UNKNOWN_SYNC_MODE)));
        commonOperationDetaiList.add(String.format("触发意图相同处理: %s", SYNC_MODE_NAME_MAP.getOrDefault(syncConfig. getSameTriggerIntent(), UNKNOWN_SYNC_MODE)));
        generateTargetBotInfo(successBotIdList, failBotIdList, botMap, commonOperationDetaiList);
        String logDetail = String.join(";\n", commonOperationDetaiList);
        saveLog(srcBotId, syncConfig.getTargetBotIdList(), logDetail, OperationLogResourceTypeEnum.KNOWLEDGE, syncConfig.getCurrentUserId());
    }

    @Override
    public void intentSync(IntentSyncVO sync,
                           List<? extends IntentPO> syncIntentList,
                           List<Long> successBotIdList,
                           List<Long> failBotIdList) {
        if (Objects.isNull(sync)
                || Objects.isNull(sync.getSrcBotId())
                || CollectionUtils.isEmpty(sync.getTargetBotIdList())
                || CollectionUtils.isEmpty(syncIntentList)) {
            return;
        }

        Long srcBotId = sync.getSrcBotId();
        Map<Long, BotPO> botMap = getRefBotMap(sync);
        BotPO srcBot = botMap.get(srcBotId);
        if (Objects.isNull(srcBot)) {
            log.error("botSyncOperationLogService.intentSync, bot not exist, botId:{}", srcBotId);
            return;
        }

        List<String> intentNameList = syncIntentList.stream()
                .map(IntentPO::getName)
                .collect(Collectors.toList());
        List<String> commonOperationDetaiList = new ArrayList<>();
        commonOperationDetaiList.add(String.format("源Bot: %s", srcBot.getName()));
        commonOperationDetaiList.add(String.format("意图列表: %s", String.join(", ", intentNameList)));
        commonOperationDetaiList.add(String.format("意图相同处理: %s", SYNC_MODE_NAME_MAP.getOrDefault( sync.getSameIntent(), UNKNOWN_SYNC_MODE)));
        commonOperationDetaiList.add(String.format("是否同步模型训练: %s", generateBooleanDesc(sync.getSyncIntentConfig())));
        generateTargetBotInfo(successBotIdList, failBotIdList, botMap, commonOperationDetaiList);
        String logDetail = String.join(";\n", commonOperationDetaiList);
        saveLog(srcBotId, sync.getTargetBotIdList(), logDetail, OperationLogResourceTypeEnum.INTENT, sync.getCurrentUserId());
    }

    private static void generateTargetBotInfo(List<Long> successBotIdList,
                                              List<Long> failBotIdList,
                                              Map<Long, BotPO> botMap,
                                              List<String> commonOperationDetaiList) {
        String successName = "目标Bot";
        if (CollectionUtils.isNotEmpty(failBotIdList)) {
            successName = "同步成功Bot";
        }
        List<String> successNameList = successBotIdList.stream()
                .map(botMap::get)
                .filter(Objects::nonNull)
                .map(BotPO::getName)
                .collect(Collectors.toList());
        commonOperationDetaiList.add(String.format("%s: %s", successName, String.join(", ", successNameList)));
        if (CollectionUtils.isNotEmpty(failBotIdList)) {
            List<String> failNameList = failBotIdList.stream()
                    .map(botMap::get)
                    .filter(Objects::nonNull)
                    .map(BotPO::getName)
                    .collect(Collectors.toList());
            commonOperationDetaiList.add(String.format("同步失败Bot: %s", String.join(", ", failNameList)));
        }
    }

    @Override
    public void audioSync(PublicAudioSyncRequestVO syncRequest,
                          List<String> successAnswerList) {
        if (Objects.isNull(syncRequest)
                || Objects.isNull(syncRequest.getTargetBotId())) {
            return;
        }

        // 将答案之前添加递增序号
        AtomicInteger index = new AtomicInteger(0);
        List<String> answerList = successAnswerList.stream()
                .distinct()
                .map(item -> String.format("%s. %s", index.incrementAndGet(), item))
                .collect(Collectors.toList());

        String logContent = String.format("同步录音:【公共音频库】同步至当前bot, 音频相同处理:%s, 成功%s条: %s",
                SYNC_MODE_NAME_MAP.getOrDefault(syncRequest.getSyncMode(), UNKNOWN_SYNC_MODE),
                answerList.size(),
                String.join("; ", answerList));

        OperationLogDTO log = OperationLogDTO.builder()
                .operatorId(syncRequest.getCurrentUserId())
                .detail(logContent)
                .botId(syncRequest.getTargetBotId())
                .type(OperationLogTypeEnum.AUDIO)
                .resourceType(OperationLogResourceTypeEnum.AUDIO_LIST)
                .build();

        operationLogService.batchSave(Collections.singletonList(log));
    }

    @Override
    public void specialAnswerConfigSync(SpecialAnswerSyncVO syncConfig,
                                        List<SpecialAnswerConfigPO> specialAnswerConfigList,
                                        List<Long> successBotIdList,
                                        List<Long> failBotIdList) {
        if (Objects.isNull(syncConfig)
                || Objects.isNull(syncConfig.getSrcBotId())
                || CollectionUtils.isEmpty(syncConfig.getTargetBotIdList())
                || CollectionUtils.isEmpty(specialAnswerConfigList)) {
            return;
        }

        Long srcBotId = syncConfig.getSrcBotId();
        Map<Long, BotPO> botMap = getRefBotMap(syncConfig);
        BotPO srcBot = botMap.get(srcBotId);
        if (Objects.isNull(srcBot)) {
            log.error("botSyncOperationLogService.knowledgeSync, bot not exist, botId:{}", srcBotId);
            return;
        }
        List<String> nameList = specialAnswerConfigList.stream()
                .map(item -> String.format("%s%s", item.getLabel(), item.getName()))
                .collect(Collectors.toList());
        List<String> commonOperationDetaiList = new ArrayList<>();
        commonOperationDetaiList.add(String.format("源Bot: %s", srcBot.getName()));
        commonOperationDetaiList.add(String.format("特殊语境列表: 【%s】", String.join(", ", nameList)));
        String syncScope = "--";
        if (CollectionUtils.isNotEmpty(syncConfig.getSyncScopeEnumSet())) {
            syncScope = syncConfig.getSyncScopeEnumSet().stream()
                    .map(SYNC_SCOPE_NAME_MAP::get)
                    .collect(Collectors.joining(", "));
        }
        commonOperationDetaiList.add(String.format("同步内容: %s", syncScope));
        commonOperationDetaiList.add(String.format("触发意图相同处理: %s", SYNC_MODE_NAME_MAP.getOrDefault(syncConfig. getSameTriggerIntent(), UNKNOWN_SYNC_MODE)));
        generateTargetBotInfo(successBotIdList, failBotIdList, botMap, commonOperationDetaiList);
        String logDetail = String.join(";\n", commonOperationDetaiList);
        saveLog(srcBotId, syncConfig.getTargetBotIdList(), logDetail, OperationLogResourceTypeEnum.SPECIAL_ANSWER, syncConfig.getCurrentUserId());
    }

    @Override
    public void intentLevelRuleSync(RuleSyncVO syncConfig,
                             List<? extends IntentRulePO> list,
                             List<Long> successBotIdList,
                             List<Long> failBotIdList) {
        if (Objects.isNull(syncConfig)
                || Objects.isNull(syncConfig.getSrcBotId())
                || CollectionUtils.isEmpty(syncConfig.getTargetBotIdList())
                || CollectionUtils.isEmpty(list)) {
            return;
        }

        Long srcBotId = syncConfig.getSrcBotId();
        Map<Long, BotPO> botMap = getRefBotMap(syncConfig);
        BotPO srcBot = botMap.get(srcBotId);
        if (Objects.isNull(srcBot)) {
            log.error("botSyncOperationLogService.intentLevelRuleSync, bot not exist, botId:{}", srcBotId);
            return;
        }

        List<String> commonOperationDetaiList = new ArrayList<>();
        commonOperationDetaiList.add(String.format("源Bot: %s", srcBot.getName()));


        ResourceId2NameBO getResourceNameMap = intentRuleService.getResourceNameMap(srcBotId);

        List<String> ruleStrList = list.stream().map(item -> {
                    String conditionContent = IntentRuleContentRenderUtils.renderIntentRuleConditionContent(item.getConditionList(), getResourceNameMap);
                    return String.format("规则%s: %s", item.getMatchOrder(), conditionContent);
                }).collect(Collectors.toList());
        commonOperationDetaiList.add(String.format("规则列表: %s", String.join("; ", ruleStrList)));
        commonOperationDetaiList.add(String.format("同步方式: %s", SYNC_MODE_NAME_MAP.getOrDefault(syncConfig.getSyncType(), UNKNOWN_SYNC_MODE)));
        generateTargetBotInfo(successBotIdList, failBotIdList, botMap, commonOperationDetaiList);
        String logDetail = String.join(";\n", commonOperationDetaiList);
        saveLog(srcBotId, syncConfig.getTargetBotIdList(), logDetail, OperationLogResourceTypeEnum.INTENT_RULE_CONFIG, syncConfig.getCurrentUserId());
    }

    @Override
    public void intentActionRuleSync(RuleActionSyncVO syncConfig,
                              List<? extends IntentRuleActionPO> list,
                              List<Long> successBotIdList,
                              List<Long> failBotIdList) {
        if (Objects.isNull(syncConfig)
                || Objects.isNull(syncConfig.getSrcBotId())
                || CollectionUtils.isEmpty(syncConfig.getTargetBotIdList())
                || CollectionUtils.isEmpty(list)) {
            return;
        }

        Long srcBotId = syncConfig.getSrcBotId();
        Map<Long, BotPO> botMap = getRefBotMap(syncConfig);
        BotPO srcBot = botMap.get(srcBotId);
        if (Objects.isNull(srcBot)) {
            log.error("botSyncOperationLogService.intentLevelRuleSync, bot not exist, botId:{}", srcBotId);
            return;
        }

        List<String> commonOperationDetaiList = new ArrayList<>();
        commonOperationDetaiList.add(String.format("源Bot: %s", srcBot.getName()));
        ResourceId2NameBO getResourceNameMap = intentRuleService.getResourceNameMap(srcBotId);
        List<String> ruleStrList = list.stream().map(item -> {
            String conditionContent = IntentRuleContentRenderUtils.renderIntentRuleConditionContent(item.getConditionList(), getResourceNameMap);
            return String.format("规则%s: %s", item.getMatchOrder(), conditionContent);
        }).collect(Collectors.toList());
        commonOperationDetaiList.add(String.format("规则列表: %s", String.join("; ", ruleStrList)));
        commonOperationDetaiList.add(String.format("同步方式: %s", SYNC_MODE_NAME_MAP.getOrDefault(syncConfig.getSyncType(), UNKNOWN_SYNC_MODE)));
        generateTargetBotInfo(successBotIdList, failBotIdList, botMap, commonOperationDetaiList);
        String logDetail = String.join(";\n", commonOperationDetaiList);
        saveLog(srcBotId, syncConfig.getTargetBotIdList(), logDetail, OperationLogResourceTypeEnum.INTENT_RULE_ACTION_CONFIG, syncConfig.getCurrentUserId());
    }

    @Override
    public void speechConfigSync(BasicSyncVO sync,
                                 List<Long> successBotIdList,
                                 List<Long> failBotIdList) {
        if (Objects.isNull(sync)
                || Objects.isNull(sync.getSrcBotId())
                || CollectionUtils.isEmpty(sync.getTargetBotIdList())) {
            return;
        }
        Long srcBotId = sync.getSrcBotId();
        Map<Long, BotPO> botMap = getRefBotMap(sync);
        BotPO srcBot = botMap.get(srcBotId);
        if (Objects.isNull(srcBot)) {
            log.error("botSyncOperationLogService.speechConfigSync, bot not exist, botId:{}", srcBotId);
            return;
        }
        List<String> commonOperationDetaiList = new ArrayList<>();
        commonOperationDetaiList.add(String.format("源Bot: %s", srcBot.getName()));

        String syncScope = "--";
        if (CollectionUtils.isNotEmpty(sync.getSyncScopeEnumSet())) {
            syncScope = sync.getSyncScopeEnumSet().stream()
                    .map(SYNC_SCOPE_NAME_MAP::get)
                    .collect(Collectors.joining(", "));
        }
        commonOperationDetaiList.add(String.format("同步内容: %s", syncScope));
        generateTargetBotInfo(successBotIdList, failBotIdList, botMap, commonOperationDetaiList);
        String logDetail = String.join(";\n", commonOperationDetaiList);
        saveLog(srcBotId, sync.getTargetBotIdList(), logDetail, OperationLogResourceTypeEnum.VOICE_CONFIG, sync.getCurrentUserId());

    }

    @Override
    public void entitySync(EntitySyncVO syncConfig, List<BaseEntityPO> list, List<Long> successBotIdList, List<Long> failBotIdList) {
        if (Objects.isNull(syncConfig)
                || Objects.isNull(syncConfig.getSrcBotId())
                || CollectionUtils.isEmpty(syncConfig.getTargetBotIdList())
                || CollectionUtils.isEmpty(list)) {
            return;
        }

        Long srcBotId = syncConfig.getSrcBotId();
        Map<Long, BotPO> botMap = getRefBotMap(syncConfig);
        BotPO srcBot = botMap.get(srcBotId);
        if (Objects.isNull(srcBot)) {
            log.error("botSyncOperationLogService.knowledgeSync, bot not exist, botId:{}", srcBotId);
            return;
        }
        List<String> nameList = list.stream().map(BaseEntityPO::getName).collect(Collectors.toList());
        List<String> commonOperationDetaiList = new ArrayList<>();
        commonOperationDetaiList.add(String.format("源Bot: %s", srcBot.getName()));
        commonOperationDetaiList.add(String.format("实体列表: 【%s】", String.join(", ", nameList)));
        commonOperationDetaiList.add(String.format("实体名称相同处理: %s", SYNC_MODE_NAME_MAP.getOrDefault(syncConfig.getSameEntity(), UNKNOWN_SYNC_MODE)));
        generateTargetBotInfo(successBotIdList, failBotIdList, botMap, commonOperationDetaiList);
        String logDetail = String.join(";\n", commonOperationDetaiList);
        saveLog(srcBotId, syncConfig.getTargetBotIdList(), logDetail, OperationLogResourceTypeEnum.ENTITY, syncConfig.getCurrentUserId());
    }

    private void saveLog(Long srcBotId,
                         List<Long> subBotIdList,
                         String logDetail,
                         OperationLogResourceTypeEnum resourceType,
                         Long userId) {
        List<OperationLogDTO> logs = new ArrayList<>();
        OperationLogDTO item = new OperationLogDTO();
        item.setBotId(srcBotId);
        item.setType(OperationLogTypeEnum.BOT_SYNC);
        item.setOperatorId(userId);
        item.setResourceType(resourceType);
        item.setDetail(logDetail);
        logs.add(item);
        for (Long id : subBotIdList) {
            item = new OperationLogDTO();
            item.setBotId(id);
            item.setType(OperationLogTypeEnum.BOT_SYNC);
            item.setOperatorId(userId);
            item.setResourceType(resourceType);
            item.setDetail(logDetail);
            logs.add(item);
        }
        operationLogService.batchSave(logs);
    }
}
