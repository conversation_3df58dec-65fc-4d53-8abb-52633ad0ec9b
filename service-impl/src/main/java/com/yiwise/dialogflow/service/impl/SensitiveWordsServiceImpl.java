package com.yiwise.dialogflow.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.yiwise.base.common.helper.ServerInfoConstants;
import com.yiwise.base.common.thread.decorator.MDCDecoratorRunnable;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.api.dto.response.SensitiveWordsDetectResultBO;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.entity.bo.SnapshotInvalidFailItemMsg;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import com.yiwise.dialogflow.entity.enums.BotResourceTypeEnum;
import com.yiwise.dialogflow.entity.po.NlsFlashRecognizerResultPO;
import com.yiwise.dialogflow.entity.vo.audio.AnswerAudioWrapVO;
import com.yiwise.dialogflow.entity.vo.audio.AnswerPlaceholderElementVO;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.feishu.FeishuApiService;
import com.yiwise.dialogflow.thread.FeiShuWarningThreadExecutorHelper;
import com.yiwise.dialogflow.thread.SensitiveWordsDetectThreadExecutorHelper;
import com.yiwise.dialogflow.utils.FeiShuCardTemplate;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.text.StrSubstitutor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SensitiveWordsServiceImpl implements SensitiveWordsService {

    @Resource
    private NlsService nlsService;

    @Resource
    private AliyunSensitiveWordsService aliyunSensitiveWordsService;

    @Resource
    private FeishuApiService feishuApiService;

    @Resource
    private AnswerAudioManagerService answerAudioManagerService;

    @Resource
    private ObjectStorageHelper objectStorageHelper;

    /**
     * 敏感词缓存, key: 敏感词文件 oss key, value: 敏感词集合
     * 更新时通过上传新的文件, 并更新 apollo 配置来更新缓存
     */
    private final Cache<String, BuildInSensitive> BUILD_IN_SENSITIVE_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(30, TimeUnit.DAYS)
            .build();

    @Override
    public void saveToSnapshot(RobotResourceContext context) {
    }

    @Override
    public void validateResource(RobotResourceContext context) {
        if (context.noNeedSensitiveWordsCheck()) {
            return;
        }
        Long botId = context.getTargetBotId();
        List<AnswerAudioWrapVO> answerAudioWrapList = answerAudioManagerService.getByRobotSnapshot(botId, context.getSnapshot());
        if (CollectionUtils.isEmpty(answerAudioWrapList)) {
            return;
        }

        String botName = context.getSnapshot().getBot().getName();

        Map<String, String> ossKeyTextMap = MyCollectionUtils.listToConvertMap(nlsService.findAll(botId), NlsFlashRecognizerResultPO::getUrl, NlsFlashRecognizerResultPO::getText);

        List<SnapshotInvalidFailItemMsg> invalidMsgList = new CopyOnWriteArrayList<>();

        long startTime = System.currentTimeMillis();
        CountDownLatch latch = new CountDownLatch(answerAudioWrapList.size());
        for (AnswerAudioWrapVO answer : answerAudioWrapList) {
            SensitiveWordsDetectThreadExecutorHelper.execute("敏感词检验-" + answer.getText(), new MDCDecoratorRunnable(
                    () -> {
                        try {
                            AnswerLocateBO locate = answer.getLocate();

                            // 识别敏感词并发送预警消息
                            sensitiveWordsDetect(answer.getText(), words -> {
                                String errorMsg = locate.getErrorLocateInfo() + "："
                                        + "文案" + (CollectionUtils.isEmpty(words) ? "中存在违规内容，请校验" : "中存在的\"" + String.join("，", words) + "\"为违规内容，请校验");
                                invalidMsgList.add(SnapshotInvalidFailItemMsg.fromAnswerLocate(locate, errorMsg));

                                // 发送预警消息
                                pushWarningMsg(() -> generateTextWarningMsg(botId, botName, locate, answer.getText(), words));
                            });

                            List<AnswerPlaceholderElementVO> elementList = answer.getAnswerElementList();
                            if (CollectionUtils.isNotEmpty(elementList)) {
                                for (AnswerPlaceholderElementVO element : elementList) {
                                    if (AudioTypeEnum.MAN_MADE.equals(element.getAudioType())) {
                                        String text = ossKeyTextMap.get(element.getUrl());
                                        if (StringUtils.isNotBlank(text)) {

                                            // 识别敏感词并发送预警消息
                                            sensitiveWordsDetect(text, words -> {
                                                String errorMsg = locate.getErrorLocateInfo() + "："
                                                        + "音频" + (CollectionUtils.isEmpty(words) ? "中存在违规内容，请校验" : "中存在的\"" + String.join("，", words) + "\"为违规内容，请校验");
                                                invalidMsgList.add(SnapshotInvalidFailItemMsg.fromAnswerLocate(locate, errorMsg));

                                                // 发送预警消息
                                                pushWarningMsg(() -> generateAudioWarningMsg(botId, botName, locate, text, words, element.getFullUrl()));
                                            });
                                        }
                                    }
                                }
                            }
                        } finally {
                            latch.countDown();
                        }
                    }
            ));
        }
        try {
            latch.await();
        } catch (Exception e) {
            log.error("[LogHub_Warn]敏感词检测失败", e);
            SnapshotInvalidFailItemMsg msg = SnapshotInvalidFailItemMsg.builder()
                    .resourceType(BotResourceTypeEnum.AUDIO)
                    .failMsg("敏感词检测失败，请稍后重试")
                    .build();
            invalidMsgList.add(msg);
        } finally {
            context.getInvalidMsgList().addAll(invalidMsgList);
            log.info("敏感词检测耗时={}ms", System.currentTimeMillis() - startTime);
        }
    }

    private String generateTextWarningMsg(Long botId, String botName, AnswerLocateBO locate, String text, List<String> words) {
        Map<String, String> map = generateWarningBaseMap("敏感词检测-文本", botId, botName, locate, text, words);
        map.put("atUsers", obtainAtUsers());
        return new StrSubstitutor(map).replace(FeiShuCardTemplate.SENSITIVE_TEXT_WARNING);
    }

    private String generateAudioWarningMsg(Long botId, String botName, AnswerLocateBO locate, String text, List<String> words, String url) {
        Map<String, String> map = generateWarningBaseMap("敏感词检测-音频", botId, botName, locate, text, words);
        map.put("audioAddress", url);
        return new StrSubstitutor(map).replace(FeiShuCardTemplate.SENSITIVE_AUDIO_WARNING);
    }

    private Map<String, String> generateWarningBaseMap(String title, Long botId, String botName, AnswerLocateBO locate, String text, List<String> words) {
        Map<String, String> map = new HashMap<>();
        map.put("title", title);
        map.put("botId", String.valueOf(botId));
        map.put("botName", botName);
        map.put("location", locate.getErrorLocateInfo());
        map.put("hostName", ServerInfoConstants.SERVER_HOSTNAME);
        map.put("logId", MDC.get("MDC_LOG_ID"));
        map.put("content", StringEscapeUtils.escapeJson(text));
        map.put("words", words.stream().map(word -> "<font color='red'>" + word + "</font>").collect(Collectors.joining("，")));
        map.put("atUsers", obtainAtUsers());
        return map;
    }

    private String obtainAtUsers() {
        List<String> mobileList = Arrays.asList(ApplicationConstant.SENSITIVE_WORDS_WARING_AT_USERS.split(","));
        String atUsers = feishuApiService.getUserIdsFeishu(mobileList).stream().map(userId -> "<at id=" + userId + "></at>").collect(Collectors.joining());
        if (StringUtils.isBlank(atUsers)) {
            atUsers = "<at id=all></at>";
        }
        return atUsers;
    }

    private void pushWarningMsg(Supplier<String> supplier) {
        // 发送飞书预警消息
        FeiShuWarningThreadExecutorHelper.execute("敏感词预警推送",
                new MDCDecoratorRunnable(() ->
                        feishuApiService.sendFeishuWarn(supplier.get(), ApplicationConstant.SENSITIVE_WORDS_WARNING_WEBHOOK_URL)
                )
        );
    }

    private void sensitiveWordsDetect(String text, Consumer<List<String>> wordsConsumer) {
        // 敏感词检测
        SensitiveWordsDetectResultBO sensitiveWordsDetectResult = detect(text);
        if (BooleanUtils.isTrue(sensitiveWordsDetectResult.getIsSafe())) {
            return;
        }
        wordsConsumer.accept(sensitiveWordsDetectResult.getWords());
    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {

    }

    @Override
    public SensitiveWordsDetectResultBO detect(String text) {
        if (StringUtils.isBlank(text)) {
            return new SensitiveWordsDetectResultBO(text, true, Collections.emptyList());
        }
        return aliyunSensitiveWordsService.detect(text);
    }

    @Override
    public SensitiveWordsDetectResultBO detectByConfig(String text) {
        SensitiveWordsDetectResultBO defaultResult = new SensitiveWordsDetectResultBO(text, true, Collections.emptyList());

        if (StringUtils.isBlank(text)) {
            return defaultResult;
        }

        String ossKey = ApplicationConstant.SENSITIVE_CONFIG_OSS_KEY;
        if (StringUtils.isBlank(ossKey)) {
            return defaultResult;
        }

        BuildInSensitive buildInSensitive = BUILD_IN_SENSITIVE_CACHE.getIfPresent(ossKey);
        if (Objects.isNull(buildInSensitive)) {
            Optional<BuildInSensitive> parseResult = parse(ossKey);
            parseResult.ifPresent(s -> BUILD_IN_SENSITIVE_CACHE.put(ossKey, s));
            buildInSensitive = BUILD_IN_SENSITIVE_CACHE.getIfPresent(ossKey);
        }

        if (Objects.isNull(buildInSensitive) || CollectionUtils.isEmpty(buildInSensitive.words)) {
            return defaultResult;
        }

        List<String> sensitiveWords = new ArrayList<>();
        for (String word : buildInSensitive.words) {
            if (text.contains(word)) {
                sensitiveWords.add(word);
            }
        }
        return new SensitiveWordsDetectResultBO(text, CollectionUtils.isEmpty(sensitiveWords), sensitiveWords);
    }

    @Override
    public List<SensitiveWordsDetectResultBO> batchDetect(List<String> textList) {
        if (CollectionUtils.isEmpty(textList)) {
            return Collections.emptyList();
        }

        return textList.stream()
                .map(this::detect)
                .collect(Collectors.toList());
    }

    private synchronized Optional<BuildInSensitive> parse(String ossKey) {
        try {
            if (!objectStorageHelper.doesObjectExist(ossKey)) {
                log.error("[LogHub_Warn] 敏感词文件不存在, ossKey={}", ossKey);
                return Optional.empty();
            }
            byte[] bytes = objectStorageHelper.downloadToBytes(ossKey);
            if (bytes == null) {
                log.error("[LogHub_Warn] 敏感词文件下载失败, ossKey={}", ossKey);
                return Optional.empty();
            }
            String content = new String(bytes, StandardCharsets.UTF_8);
            if (StringUtils.isBlank(content)) {
                log.error("[LogHub_Warn] 敏感词文件内容为空, ossKey={}", ossKey);
                return Optional.empty();
            }
            Set<String> words = Arrays.stream(content.split("\n"))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());;
            return Optional.of(new BuildInSensitive(words));
        } catch (Exception e) {
            log.error("[LogHub_Warn] 解析敏感词文件失败", e);
        }
        return Optional.empty();
    }

    static class BuildInSensitive {
        final Set<String> words;
        BuildInSensitive(Set<String> words) {
            this.words = words;
        }
    }

}
