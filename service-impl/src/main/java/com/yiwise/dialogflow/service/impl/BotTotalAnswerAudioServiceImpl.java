package com.yiwise.dialogflow.service.impl;

import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.api.dto.response.audio.*;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.vo.audio.AnswerAudioWrapVO;
import com.yiwise.dialogflow.service.AnswerAudioManagerService;
import com.yiwise.dialogflow.service.BotRefService;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.BotTotalAnswerAudioService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BotTotalAnswerAudioServiceImpl implements BotTotalAnswerAudioService {

    @Resource
    private BotService botService;

    @Resource
    private BotRefService botRefService;

    @Resource
    private AnswerAudioManagerService answerAudioManagerService;


    @Override
    public BotTotalAudioDetail getTotalAnswerAudioByDialogFlowId(Long dialogFlowId) {
        Long botId = botRefService.getBotId(dialogFlowId);
        if (Objects.isNull(botId)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "bot不存在");
        }
        BotPO bot = botService.getById(botId);
        if (Objects.isNull(bot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "bot不存在");
        }
        List<AnswerAudioWrapVO> allAudioList = answerAudioManagerService.getAllByBotId(botId);

        BotTotalAudioDetail botTotalAudioDetail = new BotTotalAudioDetail();

        botTotalAudioDetail.setBotId(botId);
        botTotalAudioDetail.setName(bot.getName());
        botTotalAudioDetail.setDialogFlowId(dialogFlowId);
        List<StepTotalAudioDetail> stepList = new ArrayList<>();
        Map<String, KnowledgeAudioDetail> knowledgeMap = new HashMap<>();
        Map<String, StepTotalAudioDetail> stepMap = new HashMap<>();
        Map<String, SpecialAnswerConfigAudioDetail> speicalMap = new HashMap<>();
        Map<String, NodeAudioDetail> nodeMap = new HashMap<>();
        allAudioList.forEach(audio -> {
            AnswerLocateBO locate = audio.getLocate();
            AnswerAudioDetail audioDetail = new AnswerAudioDetail();
            audioDetail.setLabel(audio.getLabel());
            audioDetail.setText(audio.getText());
            audioDetail.setAnswerElementList(
                    audio.getAnswerElementList().stream()
                            .map(item -> {
                                AnswerAudioElement element = new AnswerAudioElement();
                                element.setValue(item.getValue());
                                element.setType(item.getType());
                                element.setAudioUrl(item.getFullUrl());
                                return element;
                            }).collect(Collectors.toList())
            );
            switch (locate.getAnswerSource()) {
                case KNOWLEDGE:
                 String knowledgeId = locate.getKnowledgeId();
                 KnowledgeAudioDetail knowledgeAudioDetail = knowledgeMap.computeIfAbsent(knowledgeId, (k) -> {
                     KnowledgeAudioDetail r = new KnowledgeAudioDetail();
                     r.setId(k);
                     r.setLabel(locate.getKnowledgeLabel());
                     r.setName(locate.getKnowledgeName());
                     r.setAnswerList(new ArrayList<>());
                     return r;
                 });
                 knowledgeAudioDetail.getAnswerList().add(audioDetail);
                 break;
                case SPECIAL_ANSWER:
                    String specialId = locate.getSpecialAnswerConfigId();
                    SpecialAnswerConfigAudioDetail specialAnswerConfigAudioDetail = speicalMap.computeIfAbsent(specialId, (k) -> {
                        SpecialAnswerConfigAudioDetail r = new SpecialAnswerConfigAudioDetail();
                        r.setId(k);
                        r.setLabel(locate.getSpecialAnswerConfigLabel());
                        r.setName(locate.getSpecialAnswerConfigName());
                        r.setAnswerList(new ArrayList<>());
                        return r;
                    });
                    specialAnswerConfigAudioDetail.getAnswerList().add(audioDetail);
                    break;
                case STEP:
                    String stepId = locate.getStepId();
                    String nodeId = locate.getNodeId();
                    StepTotalAudioDetail stepDetail = stepMap.computeIfAbsent(stepId, (k) -> {
                        StepTotalAudioDetail r = new StepTotalAudioDetail();
                        r.setId(k);
                        r.setName(locate.getStepName());
                        r.setLabel(locate.getStepLabel());
                        r.setNodeList(new ArrayList<>());
                        stepList.add(r);
                        return r;
                    });

                    NodeAudioDetail nodeDetail = nodeMap.computeIfAbsent(nodeId, (k) -> {
                       NodeAudioDetail r = new NodeAudioDetail();
                       r.setAnswerList(new ArrayList<>());
                       r.setName(locate.getNodeName());
                       r.setLabel(locate.getNodeLabel());
                       r.setId(locate.getNodeId());
                       return r;
                    });
                    if (!stepDetail.getNodeList().contains(nodeDetail)) {
                        stepDetail.getNodeList().add(nodeDetail);
                    }
                    nodeDetail.getAnswerList().add(audioDetail);
                    break;
                default:
                 break;
            }
        });

        botTotalAudioDetail.setKnowledgeList(new ArrayList<>(knowledgeMap.values()));
        botTotalAudioDetail.setSpecialAnswerConfigList(new ArrayList<>(speicalMap.values()));
        botTotalAudioDetail.setStepList(stepList);

        return botTotalAudioDetail;
    }
}
