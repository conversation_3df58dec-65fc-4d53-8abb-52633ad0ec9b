package com.yiwise.dialogflow.service.impl.entitycollect;

import com.google.common.collect.Lists;
import com.yiwise.base.common.utils.bean.DeepCopyUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.bo.EntityValueBO;
import com.yiwise.dialogflow.entity.bo.SnapshotInvalidFailItemMsg;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.query.BotQuery;
import com.yiwise.dialogflow.entity.query.EntityQueryVO;
import com.yiwise.dialogflow.entity.query.EntitySyncVO;
import com.yiwise.dialogflow.entity.vo.*;
import com.yiwise.dialogflow.entity.vo.entitycollect.EntityDeleteRequestVO;
import com.yiwise.dialogflow.entity.vo.sync.BotSyncResultVO;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.entitycollect.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EntityServiceImpl implements EntityService, RobotResourceService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Lazy
    @Resource
    private EntityPreprocessService entityPreprocessService;

    @Lazy
    @Resource
    private EntityOperationLogService entityOperationLogService;

    @Resource
    private SourceRefService sourceRefService;

    @Lazy
    @Resource
    private BotService botService;

    @Resource
    private BotSyncOperationLogService botSyncOperationLogService;

    private List<SystemEntityPO> getSystemEntityList() {
        return Arrays.stream(SystemEntityCategoryEnum.values())
                .filter(item -> BooleanUtils.isTrue(item.getShowOnList()))
                .map(category -> {
                    SystemEntityPO entity = new SystemEntityPO();
                    entity.setName(category.getDesc());
                    entity.setType(EntityTypeEnum.SYSTEM);
                    entity.setEntityCategory(category);
                    entity.setDescription(category.getEffect());
                    return entity;
                })
                .collect(Collectors.toList());
    }

    @Override
    public void initSystemEntity(Long botId) {
        List<SystemEntityPO> systemEntityList = getSystemEntityList().stream()
                .peek(item -> item.setBotId(botId))
                .collect(Collectors.toList());
        mongoTemplate.insert(systemEntityList, BaseEntityPO.COLLECTION_NAME);
    }

    @Override
    public SystemEntityPO getOriginInputSystemEntity() {
        SystemEntityCategoryEnum category = SystemEntityCategoryEnum.ORIGIN_INPUT;
        SystemEntityPO entity = new SystemEntityPO();
        entity.setId(SystemEntityCategoryEnum.convertToId(category));
        entity.setName(category.getDesc());
        entity.setType(EntityTypeEnum.SYSTEM);
        entity.setEntityCategory(category);
        return entity;
    }

    @Override
    public BaseEntityPO create(BaseEntityPO entity, Long userId) {
        if (EntityTypeEnum.SYSTEM.equals(entity.getType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "系统实体不允许创建");
        }
        entityPreprocessService.validate(entity);
        List<BaseEntityPO> sameNameEntityList = queryByName(entity.getBotId(), null, entity.getName());
        if (CollectionUtils.isNotEmpty(sameNameEntityList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "实体名称已存在");
        }
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setCreateUserId(userId);
        entity.setUpdateUserId(userId);
        mongoTemplate.insert(entity, BaseEntityPO.COLLECTION_NAME);
        entityOperationLogService.compareAndCreateOperationLog(entity.getBotId(), null, entity, userId);
        botService.onUpdateBotResource(entity.getBotId());
        return entity;
    }

    @Override
    public Map<String, String> getAllEntityIdNameMapByBotId(Long botId) {
        List<BaseEntityPO> entityList = getAllByBotId(botId);
        return MyCollectionUtils.listToConvertMap(entityList, BaseEntityPO::getId, BaseEntityPO::getName);
    }

    @Override
    public BaseEntityPO update(BaseEntityPO entity, Long userId) {
        Optional<BaseEntityPO> oldEntity = queryById(entity.getBotId(), entity.getId());
        if (!oldEntity.isPresent()) {
            throw new ComException(ComErrorCode.NOT_EXIST, "实体不存在");
        }

        if (entity instanceof SystemEntityPO || oldEntity.get() instanceof SystemEntityPO) {
            if (!Objects.equals(entity.getType(), oldEntity.get().getType())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "系统实体类型不能变更");
            }

            // 这里是兼容前端的数据, 前端未提交entityCategory
            SystemEntityPO newSystemEntity = (SystemEntityPO) entity;
            SystemEntityPO oldSystemEntity = (SystemEntityPO) oldEntity.get();
            newSystemEntity.setEntityCategory(oldSystemEntity.getEntityCategory());
        }
        if (entity instanceof LargeModelEntityPO && oldEntity.get() instanceof LargeModelEntityPO) {
            LargeModelEntityPO newLargeModelEntity = (LargeModelEntityPO) entity;
            LargeModelEntityPO oldLargeModelEntity = (LargeModelEntityPO) oldEntity.get();
            if (!Objects.equals(newLargeModelEntity.getModelType(), oldLargeModelEntity.getModelType())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "大模型实体类型不允许变更");
            }
        }
        entityPreprocessService.validate(entity);
        List<BaseEntityPO> sameNameEntityList = queryByName(entity.getBotId(), entity.getId(), entity.getName());
        if (CollectionUtils.isNotEmpty(sameNameEntityList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "实体名称已存在");
        }
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateUserId(userId);
        mongoTemplate.save(entity, BaseEntityPO.COLLECTION_NAME);
        entityOperationLogService.compareAndCreateOperationLog(entity.getBotId(), oldEntity.get(), entity, userId);
        botService.onUpdateBotResource(entity.getBotId());
        return entity;
    }

    private Optional<BaseEntityPO> queryById(Long botId, String id) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId));
        query.addCriteria(Criteria.where("_id").is(id));
        return Optional.ofNullable(mongoTemplate.findOne(query, BaseEntityPO.class, BaseEntityPO.COLLECTION_NAME));
    }

    private Query buildQuery(EntityQueryVO condition) {
        return Query.query(new Criteria().andOperator(buildCriteriaList(condition).toArray(new Criteria[]{})));
    }

    private List<Criteria> buildCriteriaList(EntityQueryVO condition) {
        List<Criteria> criteriaList = Lists.newArrayList();
        criteriaList.add(Criteria.where("botId").is(condition.getBotId()));
        if (StringUtils.isNotBlank(condition.getName())) {
            criteriaList.add(Criteria.where("name").regex(condition.getName()));
        }
        if (Objects.nonNull(condition.getType())) {
            criteriaList.add(Criteria.where("type").is(condition.getType()));
        }
        if (CollectionUtils.isNotEmpty(condition.getEntityIdList())) {
            criteriaList.add(Criteria.where("_id").in(condition.getEntityIdList()));
        }
        return criteriaList;
    }

    @Override
    public PageResultObject<BaseEntityPO> queryByCondition(EntityQueryVO condition) {
        Query query = buildQuery(condition);
        long totalCount = mongoTemplate.count(query, BaseEntityPO.COLLECTION_NAME);
        if (totalCount == 0) {
            return PageResultObject.of(Collections.emptyList());
        }
        query.with(PageRequest.of(condition.getPageNum() - 1, condition.getPageSize()));
        query.with(Sort.by(Sort.Direction.DESC, "updateTime"));

        List<SourceRefPO> sourceRefList = sourceRefService.getListBySourceType(condition.getBotId(), SourceTypeEnum.ENTITY);
        Map<String, List<SourceRefPO>> sourceRefMap = MyCollectionUtils.listToMapList(sourceRefList, SourceRefPO::getSourceId);
        List<BaseEntityPO> list = mongoTemplate.find(query, BaseEntityPO.class, BaseEntityPO.COLLECTION_NAME)
                .stream().map(po -> {
                    if (po instanceof StandardEntityPO) {
                        StandardEntityPO standardEntity = (StandardEntityPO) po;
                        StandardEntityVO vo = MyBeanUtils.copy(standardEntity, StandardEntityVO.class);
                        vo.setSourceRefList(sourceRefMap.getOrDefault(standardEntity.getId(), Collections.emptyList()));
                        return vo;
                    } else if (po instanceof SystemEntityPO) {
                        SystemEntityPO systemEntity = (SystemEntityPO) po;
                        SystemEntityVO vo = MyBeanUtils.copy(systemEntity, SystemEntityVO.class);
                        vo.setSourceRefList(sourceRefMap.getOrDefault(systemEntity.getId(), Collections.emptyList()));
                        return vo;
                    } else if (po instanceof RegexEntityPO) {
                        RegexEntityPO regexEntity = (RegexEntityPO) po;
                        RegexEntityVO vo = MyBeanUtils.copy(regexEntity, RegexEntityVO.class);
                        vo.setSourceRefList(sourceRefMap.getOrDefault(regexEntity.getId(), Collections.emptyList()));
                        return vo;
                    } else if (po instanceof LargeModelEntityPO) {
                        LargeModelEntityPO largeModelEntity = (LargeModelEntityPO) po;
                        LargeModelEntityVO vo = MyBeanUtils.copy(largeModelEntity, LargeModelEntityVO.class);
                        vo.setSourceRefList(sourceRefMap.getOrDefault(largeModelEntity.getId(), Collections.emptyList()));
                        return vo;
                    }
                    return null;
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());

        return PageResultObject.of(list, condition.getPageNum(), condition.getPageSize(), (int) totalCount);
    }

    @Override
    public void batchDelete(EntityDeleteRequestVO request, Long userId) {
        List<Criteria> criteriaList = buildCriteriaList(request);
        if (StringUtils.isNotBlank(request.getEntityId())) {
            criteriaList.add(Criteria.where("_id").is(request.getEntityId()));
        }
        // 系统实体不能删除
        criteriaList.add(Criteria.where("type").ne(EntityTypeEnum.SYSTEM));
        Query query = Query.query(new Criteria().andOperator(criteriaList.toArray(new Criteria[]{})));

        List<BaseEntityPO> entityList = mongoTemplate.find(query, BaseEntityPO.class, BaseEntityPO.COLLECTION_NAME);
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }

        Long botId = request.getBotId();
        List<String> entityIdList = MyCollectionUtils.listToConvertList(entityList, BaseEntityPO::getId);
        List<SourceRefPO> refList = sourceRefService.getBySourceIdList(botId, entityIdList, SourceTypeEnum.ENTITY);
        if (CollectionUtils.isNotEmpty(refList)) {
            // 被引用的实体id列表
            List<String> refEntityIdList = MyCollectionUtils.listToConvertList(refList, SourceRefPO::getSourceId);
            entityIdList.removeAll(refEntityIdList);
        }
        if (CollectionUtils.isEmpty(entityIdList)) {
            return;
        }

        Query deleteQuery = new Query();
        deleteQuery.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("_id").in(entityIdList));
        List<BaseEntityPO> deletedList = mongoTemplate.findAllAndRemove(deleteQuery, BaseEntityPO.class, BaseEntityPO.COLLECTION_NAME);
        for (BaseEntityPO deletedEntity : deletedList) {
            entityOperationLogService.compareAndCreateOperationLog(botId, deletedEntity, null, userId);
        }
        botService.onUpdateBotResource(botId);
    }

    @Override
    public void validateResource(RobotResourceContext context) {
        List<BaseEntityPO> entityList = context.getSnapshot().getEntityList();
        for (BaseEntityPO entity : entityList) {
            try {
                entityPreprocessService.validate(entity);
            } catch (ComException e) {
                context.getInvalidMsgList().add(
                  SnapshotInvalidFailItemMsg.builder()
                          .resourceId(entity.getId())
                          .resourceName(entity.getName())
                          .resourceType(BotResourceTypeEnum.ENTITY)
                          .failMsg(e.getDetailMsg())
                          .build()
                );
            }
        }
    }

    private List<BaseEntityPO> getByIdList(Long botId, List<String> entityIdList) {
        if (CollectionUtils.isEmpty(entityIdList)) {
            return Collections.emptyList();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("_id").in(entityIdList));
        return mongoTemplate.find(query, BaseEntityPO.class, BaseEntityPO.COLLECTION_NAME);
    }

    @Override
    public List<BaseEntityPO> getAllByBotId(Long botId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .with(Sort.by(Sort.Direction.DESC, "updateTime"));
        return mongoTemplate.find(query, BaseEntityPO.class, BaseEntityPO.COLLECTION_NAME);
    }

    @Override
    public List<SimpleEntityVO> getIdNamePairListByBotId(Long botId) {
        Assert.notNull(botId, "botId不能为空");
        return getAllByBotId(botId).stream()
                .map(entity -> MyBeanUtils.copy(entity, SimpleEntityVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public void fixData() {
        // 把所有现在的bot都初始化系统实体
        List<BotVO> allBotList = botService.queryListWithoutPage(new BotQuery());
        for (int i = 0; i < allBotList.size(); i++) {
            BotVO bot = allBotList.get(i);
            fixDataByBotId(bot.getBotId());
            log.info("botId={}的bot初始化系统实体完成, progress:{}/{}", bot.getBotId(), i + 1, allBotList.size());
        }
    }

    @Override
    public void fixDataByBotId(Long botId) {
        if (Objects.isNull(botId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "botId不能为空");
        }
        Set<SystemEntityCategoryEnum> oldSystemEntityCategorySet = getAllByBotId(botId).stream()
                .filter(item -> item instanceof SystemEntityPO)
                .map(item -> (SystemEntityPO) item)
                .map(SystemEntityPO::getEntityCategory)
                .collect(Collectors.toSet());
        List<SystemEntityPO> newSystemEntityList = getSystemEntityList()
                .stream()
                .filter(item -> !oldSystemEntityCategorySet.contains(item.getEntityCategory()))
                .peek(item -> item.setBotId(botId))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(newSystemEntityList)) {
            mongoTemplate.insert(newSystemEntityList, BaseEntityPO.COLLECTION_NAME);
        }
    }

    @Override
    public BaseEntityPO getById(Long botId, String id) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId))
                .addCriteria(Criteria.where("_id").is(id));
        return mongoTemplate.findOne(query, BaseEntityPO.class, BaseEntityPO.COLLECTION_NAME);
    }

    @Override
    public void saveToSnapshot(RobotResourceContext context) {
        Long botId = context.getSrcBotId();
        context.getSnapshot().setEntityList(getAllByBotId(botId));
    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {
        // bot复制
        if (!context.isCopy() || CollectionUtils.isEmpty(context.getSnapshot().getEntityList())) {
            return;
        }
        List<BaseEntityPO> entityList = context.getSnapshot().getEntityList();
        Map<String, String> entityIdMapping = context.getResourceCopyReferenceMapping().getEntityIdMapping();
        for (BaseEntityPO entity : entityList) {
            String oldId = entity.getId();
            String newId = new ObjectId().toString();
            entityIdMapping.put(oldId, newId);
            entity.setId(newId);
            entity.setBotId(context.getTargetBotId());
            entity.setCreateTime(LocalDateTime.now());
            entity.setUpdateTime(LocalDateTime.now());
            entity.setCreateUserId(context.getCurrentUserId());
            entity.setUpdateUserId(context.getCurrentUserId());
        }
        mongoTemplate.insert(entityList, BaseEntityPO.COLLECTION_NAME);
    }

    private List<BaseEntityPO> queryByName(Long botId, String excludeId, String name) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId));
        if (StringUtils.isNotBlank(excludeId)) {
            query.addCriteria(Criteria.where("_id").ne(excludeId));
        }
        query.addCriteria(Criteria.where("name").is(name));
        return mongoTemplate.find(query, BaseEntityPO.class, BaseEntityPO.COLLECTION_NAME);
    }

    @Override
    public List<BaseEntityPO> getByIdList(Collection<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        return mongoTemplate.find(new Query(Criteria.where("_id").in(idList)), BaseEntityPO.class, BaseEntityPO.COLLECTION_NAME);
    }

    @Override
    public BaseEntityPO singleSync(Long targetBotId, BaseEntityPO srcEntity, SyncModeEnum syncMode) {
        BaseEntityPO targetEntity = mongoTemplate.find(Query.query(Criteria.where("botId").is(targetBotId).and("name").is(srcEntity.getName())), BaseEntityPO.class, BaseEntityPO.COLLECTION_NAME)
                        .stream().findFirst().orElse(null);
        return singleSync(targetBotId, srcEntity, targetEntity, syncMode);
    }

    @Override
    public BotSyncResultVO sync(EntitySyncVO request) {
        Query query = buildQuery(request.getQuery());
        List<BaseEntityPO> entityList = mongoTemplate.find(query, BaseEntityPO.class, BaseEntityPO.COLLECTION_NAME);
        if (CollectionUtils.isEmpty(entityList)) {
            return BotSyncResultVO.defaultInstance();
        }

        SyncModeEnum syncMode = request.getSameEntity();
        Map<String, BaseEntityPO> srcEntityMap = MyCollectionUtils.listToMap(entityList, BaseEntityPO::getName);

        List<Long> successBotIdList = new ArrayList<>();
        List<Long> failBotIdList = new ArrayList<>();

        for (Long targetBotId : request.getTargetBotIdList()) {
            try {
                Map<String, BaseEntityPO> targetEntityMap = MyCollectionUtils.listToMap(getAllByBotId(targetBotId), BaseEntityPO::getName);
                srcEntityMap.forEach((name, srcEntity) -> singleSync(targetBotId, srcEntity, targetEntityMap.get(name), syncMode));
                successBotIdList.add(targetBotId);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                failBotIdList.add(targetBotId);
            }
        }
        botSyncOperationLogService.entitySync(request, entityList, successBotIdList, failBotIdList);
        return BotSyncResultVO.builder().successNum(successBotIdList.size()).failNum(failBotIdList.size()).build();
    }

    private BaseEntityPO singleSync(Long targetBotId, BaseEntityPO srcEntity, BaseEntityPO targetEntity, SyncModeEnum syncMode) {
        if (Objects.isNull(srcEntity)) {
            return null;
        }

        // 不存在就创建新的
        if (Objects.isNull(targetEntity)) {
            targetEntity = DeepCopyUtils.copyObject(srcEntity);
            targetEntity.setId(null);
            targetEntity.setBotId(targetBotId);
            mongoTemplate.save(targetEntity, BaseEntityPO.COLLECTION_NAME);
            return targetEntity;
        }

        // 跳过
        if (SyncModeEnum.SKIP.equals(syncMode)) {
            return targetEntity;
        }

        // 覆盖
        if (SyncModeEnum.COVER.equals(syncMode)) {
            String id = targetEntity.getId();
            targetEntity = DeepCopyUtils.copyObject(srcEntity);
            targetEntity.setId(id);
            targetEntity.setBotId(targetBotId);
            mongoTemplate.save(targetEntity, BaseEntityPO.COLLECTION_NAME);
            return targetEntity;
        }

        // 类型不同直接跳过
        EntityTypeEnum type = srcEntity.getType();
        if (!type.equals(targetEntity.getType())) {
            return targetEntity;
        }

        targetEntity.setSkipRegexList(union(srcEntity.getSkipRegexList(), targetEntity.getSkipRegexList()));

        if (srcEntity instanceof SystemEntityPO && targetEntity instanceof SystemEntityPO) {
            SystemEntityPO srcSystemEntity = (SystemEntityPO) srcEntity;
            SystemEntityPO targetSystemEntity = (SystemEntityPO) targetEntity;
            targetSystemEntity.setExtraRegexList(union(srcSystemEntity.getExtraRegexList(), targetSystemEntity.getExtraRegexList()));
        } else if (srcEntity instanceof StandardEntityPO && targetEntity instanceof StandardEntityPO) {
            StandardEntityPO srcStandardEntity = (StandardEntityPO) srcEntity;
            StandardEntityPO targetStandardEntity = (StandardEntityPO) targetEntity;
            targetStandardEntity.setMemberList(unionEntitySynonym(targetStandardEntity.getMemberList(), srcStandardEntity.getMemberList()));
        } else if (srcEntity instanceof RegexEntityPO && targetEntity instanceof RegexEntityPO) {
            RegexEntityPO srcRegexEntity = (RegexEntityPO) srcEntity;
            RegexEntityPO targetRegexEntity = (RegexEntityPO) targetEntity;
            targetRegexEntity.setRegexList(union(srcRegexEntity.getRegexList(), targetRegexEntity.getRegexList()));
        } else if (srcEntity instanceof LargeModelEntityPO && targetEntity instanceof LargeModelEntityPO) {
            LargeModelEntityPO srcLargeModelEntity = (LargeModelEntityPO) srcEntity;
            LargeModelEntityPO targetLargeModelEntity = (LargeModelEntityPO) targetEntity;
            if (srcLargeModelEntity.getModelType().equals(targetLargeModelEntity.getModelType())) {
                if (LargeModelEntityModelTypeEnum.isTuneModel(srcLargeModelEntity.getModelType())) {
                    targetLargeModelEntity.setMemberList(unionEntitySynonym(targetLargeModelEntity.getMemberList(), srcLargeModelEntity.getMemberList()));
                }
            }
        }
        mongoTemplate.save(targetEntity, BaseEntityPO.COLLECTION_NAME);
        return targetEntity;
    }

    private List<EntitySynonymPO> unionEntitySynonym(List<EntitySynonymPO> first, List<EntitySynonymPO> second) {
        if (CollectionUtils.isEmpty(first)) {
            return second;
        }
        if (CollectionUtils.isEmpty(second)) {
            return first;
        }
        Map<String, EntitySynonymPO> firstMap = MyCollectionUtils.listToMap(first, EntitySynonymPO::getName);
        Map<String, EntitySynonymPO> secondMap = MyCollectionUtils.listToMap(second, EntitySynonymPO::getName);
        for (Map.Entry<String, EntitySynonymPO> entry : secondMap.entrySet()) {
            String name = entry.getKey();
            if (!firstMap.containsKey(name)) {
                first.add(entry.getValue());
                continue;
            }

            EntitySynonymPO src = firstMap.get(name);
            src.setSynonymList(union(src.getSynonymList(), entry.getValue().getSynonymList()));
            src.setRegexSynonymList(union(src.getRegexSynonymList(), entry.getValue().getRegexSynonymList()));
        }
        return first;
    }

    private List<String> union(List<String> first, List<String> second) {
        if (CollectionUtils.isEmpty(first)) {
            return second;
        }
        if (CollectionUtils.isEmpty(second)) {
            return first;
        }
        second.stream().filter(i -> !first.contains(i)).forEach(first::add);
        return first;
    }
}
