package com.yiwise.dialogflow.service.impl.asrmodel;

import com.yiwise.dialogflow.common.MongoCollectionNameCenter;
import com.yiwise.dialogflow.entity.enums.AsrSourceConcurrencyRecordTypeEnum;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrSourceConcurrencyRecordPO;
import com.yiwise.dialogflow.entity.po.stats.AnswerStatsPO;
import com.yiwise.dialogflow.service.asrmodel.AsrSourceConcurrencyRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/10/27 13:58:40
 */

@Service
public class AsrSourceConcurrencyRecordServiceImpl implements AsrSourceConcurrencyRecordService {
    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public List<AsrSourceConcurrencyRecordPO> countByTime(LocalDate startTime, LocalDate endTime, List<Long> sourceIdList, AsrSourceConcurrencyRecordTypeEnum asrSourceConcurrencyRecordType) {
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        if (Objects.nonNull(startTime)) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("createTime").gte(startTime)));
        }
        if (Objects.nonNull(endTime)) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("createTime").lte(endTime)));
        }
        aggregationOperationList.add(Aggregation.match(Criteria.where("sourceType").is(asrSourceConcurrencyRecordType)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("sourceId").in(sourceIdList)));
        AggregationOperation groupOperation = Aggregation.group("sourceId").sum("count").as("count")
                .first("sourceId").as("sourceId");
        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        return mongoTemplate.aggregate(aggregation, AsrSourceConcurrencyRecordPO.COLLECTION_NAME, AsrSourceConcurrencyRecordPO.class)
                .getMappedResults();
    }

    @Override
    public void save(AsrSourceConcurrencyRecordPO asrSourceConcurrencyRecordPO) {
        mongoTemplate.save(asrSourceConcurrencyRecordPO, AsrSourceConcurrencyRecordPO.COLLECTION_NAME);
    }
}