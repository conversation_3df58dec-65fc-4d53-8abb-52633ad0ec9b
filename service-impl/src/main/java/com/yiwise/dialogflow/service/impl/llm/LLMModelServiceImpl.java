package com.yiwise.dialogflow.service.impl.llm;

import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.service.llm.LLMModelService;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class LLMModelServiceImpl implements LLMModelService {

    @Data
    private static class ModelResponse {
        private int code;
        private List<String> modelNameList;
        private String defaultModel;
        private String info;
    }

    @Resource
    private RestTemplate restTemplate;
    
    @Override
    public List<String> getModelNameList() {
        String url = ApplicationConstant.LLM_MODEL_NAME_LIST_URL;
        if (StringUtils.isBlank(url)) {
            log.warn("LLM model name list URL is empty");
            return Collections.emptyList();
        }

        try {
            String response = restTemplate.getForObject(url, String.class);
            if (StringUtils.isBlank(response)) {
                log.warn("Received empty response from LLM model name list API");
                return Collections.emptyList();
            }

            ModelResponse modelResponse = JSON.parseObject(response, ModelResponse.class);
            if (modelResponse == null || modelResponse.getCode() != 0) {
                log.warn("Failed to get model name list, response: {}", response);
                return Collections.emptyList();
            }

            String defaultModel = "基础版";
            if (StringUtils.isNotBlank(modelResponse.getDefaultModel())) {
                defaultModel = modelResponse.getDefaultModel();
            }

            List<String> result = new ArrayList<>();
            result.add(defaultModel);

            if (CollectionUtils.isNotEmpty(modelResponse.getModelNameList())) {
                result.addAll(modelResponse.getModelNameList());
            }
            return result.stream().distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error while fetching model name list", e);
            return Collections.singletonList("基础版");
        }
    }
}
