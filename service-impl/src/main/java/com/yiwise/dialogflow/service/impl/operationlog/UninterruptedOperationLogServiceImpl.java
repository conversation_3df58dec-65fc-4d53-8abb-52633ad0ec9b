package com.yiwise.dialogflow.service.impl.operationlog;

import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.po.Uninterrupted;
import com.yiwise.dialogflow.service.operationlog.UninterruptedOperationLogService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class UninterruptedOperationLogServiceImpl implements UninterruptedOperationLogService {
    
    private boolean changeEnableStatus(Boolean oldStats, Boolean newStats) {
        return BooleanUtils.isTrue(oldStats) != BooleanUtils.isTrue(newStats);
    }

    private String generateInterruptDescription(Uninterrupted uninterrupted) {
        String description;
        if (BooleanUtils.isTrue(uninterrupted.getEnableUninterrupted())) {
            // 不可打断
            if (Integer.valueOf(100).equals(uninterrupted.getCustomInterruptThreshold())) {
                description = "不允许用户打断";
            } else {
                description = "录音播放进度大于" + uninterrupted.getCustomInterruptThreshold() + "%后可打断";
            }
        } else {
            description = "允许用户打断";
        }
        return description;
    }

    @Override
    public List<String> compare(String prefix, Uninterrupted oldUninterrupted, Uninterrupted newUninterrupted, DependentResourceBO resource) {
        List<String> logs = new ArrayList<>();
        boolean changeEnableInterrupt = changeEnableStatus(oldUninterrupted.getEnableUninterrupted(), newUninterrupted.getEnableUninterrupted());
        boolean changeInterruptThreshold = !changeEnableInterrupt && BooleanUtils.isTrue(newUninterrupted.getEnableUninterrupted()) &&  !Objects.equals(oldUninterrupted.getCustomInterruptThreshold(), newUninterrupted.getCustomInterruptThreshold());
        if (changeEnableInterrupt) {
            String oldVal = generateInterruptDescription(oldUninterrupted);
            String newVal = generateInterruptDescription(newUninterrupted);
            logs.add(String.format("%s用户打断设置【%s】修改为【%s】",
                    prefix, oldVal, newVal));
        }
        if (changeInterruptThreshold) {
            String oldVal = oldUninterrupted.getCustomInterruptThreshold() + "%";
            String newVal = newUninterrupted.getCustomInterruptThreshold() + "%";
            logs.add(String.format("%s允许用户打断比例【%s】修改为【%s】",
                    prefix, oldVal, newVal));
        }

        if (BooleanUtils.isTrue(newUninterrupted.getEnableUninterrupted())) {
            String oldInterruptKnowledgeInfo = CollectionUtils.isEmpty(oldUninterrupted.getUninterruptedReplyKnowledgeIdList()) ? "无" : oldUninterrupted.getUninterruptedReplyKnowledgeIdList().stream()
                    .map(id -> resource.getKnowledgeIdNameMap().get(id))
                    .filter(StringUtils::isNotBlank)
                    .map(name -> String.format("【%s】", name))
                    .collect(Collectors.joining("、"));

            String newInterruptKnowledgeInfo = CollectionUtils.isEmpty(newUninterrupted.getUninterruptedReplyKnowledgeIdList()) ? "无" : newUninterrupted.getUninterruptedReplyKnowledgeIdList().stream()
                    .map(id -> resource.getKnowledgeIdNameMap().get(id))
                    .filter(StringUtils::isNotBlank)
                    .map(name -> String.format("【%s】", name))
                    .collect(Collectors.joining("、"));

            String oldInterruptStepInfo = CollectionUtils.isEmpty(oldUninterrupted.getUninterruptedReplyStepIdList()) ? "无" : oldUninterrupted.getUninterruptedReplyStepIdList().stream()
                    .map(id -> resource.getStepIdNameMap().get(id))
                    .filter(StringUtils::isNotBlank)
                    .map(name -> String.format("【%s】", name))
                    .collect(Collectors.joining("、"));

            String newInterruptStepInfo = CollectionUtils.isEmpty(newUninterrupted.getUninterruptedReplyStepIdList()) ? "无" : newUninterrupted.getUninterruptedReplyStepIdList().stream()
                    .map(id -> resource.getStepIdNameMap().get(id))
                    .filter(StringUtils::isNotBlank)
                    .map(name -> String.format("【%s】", name))
                    .collect(Collectors.joining("、"));


            String oldSpecialAnswerInfo = CollectionUtils.isEmpty(oldUninterrupted.getUninterruptedReplySpecialAnswerIdList()) ? "无" : oldUninterrupted.getUninterruptedReplySpecialAnswerIdList().stream()
                    .map(id -> resource.getSpecialAnswerIdNameMap().get(id))
                    .filter(StringUtils::isNotBlank)
                    .map(name -> String.format("【%s】", name))
                    .collect(Collectors.joining("、"));

            String newSpecialAnswerInfo = CollectionUtils.isEmpty(newUninterrupted.getUninterruptedReplySpecialAnswerIdList()) ? "无" : newUninterrupted.getUninterruptedReplySpecialAnswerIdList().stream()
                    .map(id -> resource.getSpecialAnswerIdNameMap().get(id))
                    .filter(StringUtils::isNotBlank)
                    .map(name -> String.format("【%s】", name))
                    .collect(Collectors.joining("、"));

            String oldInterruptBranchIntentInfo = CollectionUtils.isEmpty(oldUninterrupted.getUninterruptedReplyBranchIntentIdList()) ? "无" : oldUninterrupted.getUninterruptedReplyBranchIntentIdList().stream()
                    .map(id -> resource.getIntentIdNameMap().get(id))
                    .filter(StringUtils::isNotBlank)
                    .map(name -> String.format("【%s】", name))
                    .collect(Collectors.joining("、"));

            String newInterruptBranchIntentInfo = CollectionUtils.isEmpty(newUninterrupted.getUninterruptedReplyBranchIntentIdList()) ? "无" : newUninterrupted.getUninterruptedReplyBranchIntentIdList().stream()
                    .map(id -> resource.getIntentIdNameMap().get(id))
                    .filter(StringUtils::isNotBlank)
                    .map(name -> String.format("【%s】", name))
                    .collect(Collectors.joining("、"));

            if (!StringUtils.equals(oldInterruptStepInfo, newInterruptStepInfo)) {
                logs.add(String.format("%s不可打断期间仍允许跳转流程设置【%s】修改为【%s】",
                        prefix, oldInterruptStepInfo, newInterruptStepInfo));
            }

            if (!StringUtils.equals(oldInterruptKnowledgeInfo, newInterruptKnowledgeInfo)) {
                logs.add(String.format("%s不可打断期间仍允许跳转知识设置【%s】修改为【%s】",
                        prefix, oldInterruptKnowledgeInfo, newInterruptKnowledgeInfo));
            }
            if (!StringUtils.equals(oldSpecialAnswerInfo, newSpecialAnswerInfo)) {
                logs.add(String.format("%s不可打断期间仍允许跳转特殊语境设置【%s】修改为【%s】",
                        prefix, oldSpecialAnswerInfo, newSpecialAnswerInfo));
            }
            if (!StringUtils.equals(oldInterruptBranchIntentInfo, newInterruptBranchIntentInfo)) {
                logs.add(String.format("%s不可打断期间仍允许跳转节点分支设置【%s】修改为【%s】",
                        prefix, oldInterruptBranchIntentInfo, newInterruptBranchIntentInfo));
            }
        }
        return logs;
    }
}