package com.yiwise.dialogflow.service.impl.botgenerate;

import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.enums.BotGenerateTemplateStatusEnum;
import com.yiwise.dialogflow.entity.po.botgenerate.BotGenerateTemplatePO;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.entity.query.botgenerate.BotGenerateTemplateQueryVO;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.botgenerate.BotGenerateResultVO;
import com.yiwise.dialogflow.entity.vo.botgenerate.BotGenerateTemplateVO;
import com.yiwise.dialogflow.service.botgenerate.BotGenerateRecordService;
import com.yiwise.dialogflow.service.botgenerate.BotGenerateTemplateService;
import com.yiwise.dialogflow.service.remote.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BotGenerateTemplateServiceImpl implements BotGenerateTemplateService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private UserService userService;

    @Resource
    private BotGenerateRecordService botGenerateRecordService;

    @Override
    public PageResultObject<BotGenerateTemplateVO> queryByCondition(BotGenerateTemplateQueryVO condition) {
        Query query = new Query();

        query.addCriteria(Criteria.where("enabledStatus").is(EnabledStatusEnum.ENABLE));
        if (CollectionUtils.isNotEmpty(condition.getCreateUserIdList())) {
            query.addCriteria(Criteria.where("createUserId").in(condition.getCreateUserIdList()));
        }

        if (Objects.nonNull(condition.getBeginCreateTime()) && Objects.nonNull(condition.getEndCreateTime())) {
            query.addCriteria(Criteria.where("createTime").gte(condition.getBeginCreateTime()).lte(condition.getEndCreateTime().plusDays(1)));
        }

        if (Objects.nonNull(condition.getBeginUpdateTime()) && Objects.nonNull(condition.getEndUpdateTime())) {
            query.addCriteria(Criteria.where("updateTime").gte(condition.getBeginUpdateTime()).lte(condition.getEndUpdateTime().plusDays(1)));
        }

        if (StringUtils.isNotBlank(condition.getSearch())) {
            query.addCriteria(Criteria.where("name").regex(condition.getSearch()));
        }

        long totalCount = queryTotalCount(query);
        if (totalCount < 1) {
            return PageResultObject.of(Collections.emptyList());
        }

        query.with(Sort.by(Sort.Order.desc("updateTime")));
        query.skip((long) (condition.getPageNum() - 1) * condition.getPageSize());
        query.limit(condition.getPageSize());

        List<BotGenerateTemplatePO> list = mongoTemplate.find(query, BotGenerateTemplatePO.class, BotGenerateTemplatePO.COLLECTION_NAME);
        List<BotGenerateTemplateVO> voList = convertVO(list);
        return PageResultObject.of(voList, condition.getPageNum(), condition.getPageSize(), (int) totalCount);
    }

    @Override
    public List<IdNamePair<Long, String>> getAllCreateUserInfoList() {
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(Aggregation.match(Criteria.where("createUserId").ne(null)));
        AggregationOperation groupOperation = Aggregation.group("createUserId")
                .first("createUserId").as("createUserId");

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        List<Map> list = mongoTemplate.aggregate(aggregation, BotGenerateTemplatePO.COLLECTION_NAME, Map.class).getMappedResults();
        if (CollectionUtils.isEmpty(list) || MapUtils.isEmpty(list.get(0))) {
            return Collections.emptyList();
        }
        List<Long> userIdList = new ArrayList<>();
        for (Map map : list) {
            Object userId = map.get("createUserId");
            if (Objects.nonNull(userId)) {
                userIdList.add(Long.valueOf(userId.toString()));
            }
        }
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }

        List<UserPO> userList = userService.getUserByIdList(userIdList);
        return userList.stream().map(item -> IdNamePair.of(item.getUserId(), item.getName())).collect(Collectors.toList());
    }

    private List<BotGenerateTemplateVO> convertVO(List<BotGenerateTemplatePO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<Long> createUserIdList = list.stream().map(BotGenerateTemplatePO::getCreateUserId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> updateUserIdList = list.stream().map(BotGenerateTemplatePO::getUpdateUserId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> userIdList = new ArrayList<>(createUserIdList);
        userIdList.addAll(updateUserIdList);

        List<UserPO> userList = userService.getUserByIdList(userIdList.stream().distinct().collect(Collectors.toList()));
        Map<Long, String> userMap = MyCollectionUtils.listToMap(userList, UserPO::getUserId, UserPO::getName);

        return list.stream()
                .map(item -> MyBeanUtils.copy(item, BotGenerateTemplateVO.class))
                .peek(vo -> {
                    vo.setCreateUserName(userMap.get(vo.getCreateUserId()));
                    vo.setUpdateUserName(userMap.get(vo.getUpdateUserId()));
                }).collect(Collectors.toList());
    }

    private BotGenerateTemplateVO convertVO(BotGenerateTemplatePO template) {
        if (Objects.isNull(template)) {
            return null;
        }
        return convertVO(Collections.singletonList(template)).get(0);
    }

    private long queryTotalCount(Query query) {
        return mongoTemplate.count(query, BotGenerateTemplatePO.COLLECTION_NAME);
    }

    @Override
    public BotGenerateTemplateVO create(BotGenerateTemplatePO template, Long userId) {
        template.setCreateTime(LocalDateTime.now());
        template.setUpdateTime(LocalDateTime.now());
        template.setCreateUserId(userId);
        template.setUpdateUserId(userId);
        template.setEnabledStatus(EnabledStatusEnum.ENABLE);
        template.setStatus(BotGenerateTemplateStatusEnum.CREATED);
        mongoTemplate.insert(template, BotGenerateTemplatePO.COLLECTION_NAME);
        return convertVO(template);
    }

    /**
     * 生成中的模板，不允许进行任何操作
     */
    private void noOperationAllowedIfRunning(BotGenerateTemplateStatusEnum status) {
        if (Objects.nonNull(status) && status.equals(BotGenerateTemplateStatusEnum.RUNNING)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "任务生成中,不允许操作");
        }
    }

    @Override
    public BotGenerateTemplateVO update(BotGenerateTemplatePO template, Long userId) {
        noOperationAllowedIfRunning(getById(template.getId()).orElseThrow(() -> new ComException(ComErrorCode.NOT_EXIST, "表单不存在")).getStatus());
        template.setUpdateUserId(userId);
        template.setUpdateTime(LocalDateTime.now());
        template.setEnabledStatus(EnabledStatusEnum.ENABLE);
        mongoTemplate.save(template, BotGenerateTemplatePO.COLLECTION_NAME);
        return convertVO(template);
    }

    @Override
    public void deleteById(String templateId, Long userId) {
        log.info("deleteById, templateId={}, userId={}", templateId, userId);

        Optional<BotGenerateTemplatePO> template = getById(templateId);
        template.orElseThrow(() -> new ComException(ComErrorCode.NOT_EXIST, "表单不存在"));

        template.get().setEnabledStatus(EnabledStatusEnum.DISABLE);
        template.get().setUpdateUserId(userId);
        template.get().setUpdateTime(LocalDateTime.now());
        mongoTemplate.save(template.get(), BotGenerateTemplatePO.COLLECTION_NAME);
    }

    @Override
    public BotGenerateTemplatePO copy(String sourceTemplateId, Long userId) {
        Optional<BotGenerateTemplatePO> sourceTemplate = getById(sourceTemplateId);
        sourceTemplate.orElseThrow(() -> new ComException(ComErrorCode.NOT_EXIST, "源表单不存在"));

        BotGenerateTemplatePO template = sourceTemplate.get();
        template.setId(null);
        template.setCreateTime(LocalDateTime.now());
        template.setUpdateTime(LocalDateTime.now());
        template.setCreateUserId(userId);
        template.setUpdateUserId(userId);
        template.setEnabledStatus(EnabledStatusEnum.ENABLE);
        template.setLastCompletePercent(null);
        template.setStatus(BotGenerateTemplateStatusEnum.CREATED);
        mongoTemplate.insert(template, BotGenerateTemplatePO.COLLECTION_NAME);
        return template;
    }

    @Override
    public BotGenerateResultVO generate(String templateId, Long userId, Boolean ignoreWarning) {
        Optional<BotGenerateTemplatePO> template = getById(templateId);
        template.orElseThrow(() -> new ComException(ComErrorCode.NOT_EXIST, "表单不存在"));
        return doGenerate(template.get(), ignoreWarning, userId);
    }

    @Override
    public Optional<BotGenerateTemplatePO> getById(String templateId) {
        if (StringUtils.isBlank(templateId)) {
            return Optional.empty();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(templateId));
        query.addCriteria(Criteria.where("enabledStatus").is(EnabledStatusEnum.ENABLE));
        return Optional.ofNullable(mongoTemplate.findOne(query, BotGenerateTemplatePO.class, BotGenerateTemplatePO.COLLECTION_NAME));
    }

    @Override
    public Optional<BotGenerateTemplateVO> getVOById(String templateId) {
        return getById(templateId)
                .map(this::convertVO);
    }

    @Override
    public BotGenerateResultVO updateAndGenerate(BotGenerateTemplateVO template, Long userId) {
        if (StringUtils.isBlank(template.getId())) {
            create(template, userId);
        } else {
            update(template, userId);
        }
        return doGenerate(template, template.getIgnoreWarning(), userId);
    }

    @Override
    public void updateLastCompletePercentAndStatus(String templateId, String lastCompletePercent, BotGenerateTemplateStatusEnum status) {
        log.info("updateLastCompletePercent, templateId={}, lastCompletePercent={}, status={}", templateId, lastCompletePercent, status);
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(templateId));
        Update update = new Update();
        // 仅在生成成功时更新进度
        if (BotGenerateTemplateStatusEnum.SUCCESS.equals(status)) {
            update.set("lastCompletePercent", lastCompletePercent);
        }
        update.set("status", status);
        update.set("updateTime", LocalDateTime.now());
        mongoTemplate.updateFirst(query, update, BotGenerateTemplatePO.COLLECTION_NAME);
    }

    private BotGenerateResultVO doGenerate(BotGenerateTemplatePO template, Boolean ignoreWarning, Long userId) {
        noOperationAllowedIfRunning(template.getStatus());
        BotGenerateResultVO result = botGenerateRecordService.generate(template, ignoreWarning, userId);
        if (Objects.nonNull(result) && result.isSuccess()) {
            // 将模板状态修改为进行中
            template.setStatus(BotGenerateTemplateStatusEnum.RUNNING);
            template.setUpdateTime(LocalDateTime.now());
            mongoTemplate.save(template, BotGenerateTemplatePO.COLLECTION_NAME);
        }
        return result;
    }

}
