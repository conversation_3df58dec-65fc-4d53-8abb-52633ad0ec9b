package com.yiwise.dialogflow.service.impl.audio;

import com.google.common.collect.Lists;
import com.yiwise.base.common.audio.AudioHandleUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.common.utils.file.FileUtils;
import com.yiwise.base.common.utils.file.MyFileUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.TempFilePathKeyCenter;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.query.BotQuery;
import com.yiwise.dialogflow.entity.vo.BotAudioProgressVO;
import com.yiwise.dialogflow.entity.vo.BotVO;
import com.yiwise.dialogflow.entity.vo.SimpleIntentVO;
import com.yiwise.dialogflow.entity.vo.audio.*;
import com.yiwise.dialogflow.entity.vo.audio.request.AnswerUpdateAudioRequestVO;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.service.intent.IntentService;
import com.yiwise.dialogflow.service.operationlog.AnswerAudioOperationLogService;
import com.yiwise.dialogflow.thread.DynamicDataSourceApplicationExecutorHolder;
import com.yiwise.dialogflow.utils.AnswerLocateUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MiniAppAudioManagerServiceImpl implements MiniAppAudioManagerService {


    @Resource
    private BotService botService;

    @Resource
    private BotConfigService botConfigService;

    @Lazy
    @Resource
    private AnswerAudioManagerService answerAudioManagerService;

    @Resource
    private StepService stepService;

    @Resource
    private KnowledgeService knowledgeService;

    @Resource
    private StepNodeService stepNodeService;

    @Resource
    private SpecialAnswerConfigService specialAnswerConfigService;

    @Resource
    private AnswerAudioMappingService answerAudioMappingService;

    @Resource
    private IntentService intentService;

    @Resource
    private AudioUploadService audioUploadService;

    @Resource
    private AnswerAudioOperationLogService answerAudioOperationLogService;

    @Override
    public List<AudioTotalInfoVO> getTreeifyTotalInfoByBotId(Long botId, Long recordUserId) {
        if (Objects.isNull(recordUserId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "录音师id不能为空");
        }
        BotAudioConfigPO config = botConfigService.getAudioConfig(botId);
        if (AudioTypeEnum.COMPOSE.equals(config.getAudioType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "合成音不支持录音");
        }
        if (!recordUserId.equals(config.getRecordUserId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "当前录音师并非机器人配置的录音师, 请检查");
        }

        // 查询所有的流程, 问答知识, 特殊语境
        List<StepPO> stepList = stepService.getAllListByBotId(botId).stream().filter(step -> !step.isLlmStep()).collect(Collectors.toList());
        List<DialogBaseNodePO> nodeList = stepNodeService.getAllListByBotId(botId);
        List<KnowledgePO> knowledgeList = knowledgeService.getAllListByBotId(botId);
        List<SpecialAnswerConfigPO> specialAnswerConfigList = specialAnswerConfigService.getByBotId(botId);
        return wrapTreeifyInfoBySourceType(stepList, nodeList, knowledgeList, specialAnswerConfigList, config);
    }

    private List<AudioTotalInfoVO> processTreeifyStepTotalInfo(List<StepPO> stepList,
                                                            Map<String, AnswerAudioMappingVO> audioMappingMap,
                                                            Map<String, SimpleIntentVO> intentMap,
                                                            Map<String, List<DialogBaseNodePO>> stepNodeListMap,
                                                            AudioTypeEnum audioType) {
        List<AudioTotalInfoVO> result = new ArrayList<>();
        stepList.stream()
                .sorted(Comparator.comparingInt(step -> {
                     if (Objects.isNull(step.getOrderNum())) {
                         return Integer.MAX_VALUE;
                     } else {
                         return step.getOrderNum();
                     }
                }))
                .forEach(step -> {
                    List<DialogBaseNodePO> nodes = stepNodeListMap.get(step.getId());
                    Optional<TreeifyAnswerAudioVO> treeifyAudioOpt = wrapStepNodeTreeifyAudio(audioType, step, nodes, audioMappingMap, intentMap);
                    AudioTotalInfoVO totalInfo = convertStepToTotalInfo(step, treeifyAudioOpt.orElseGet(TreeifyAnswerAudioVO::new));
                    result.add(totalInfo);
                });

        return result;
    }


    private List<TreeifyAnswerAudioVO> processListableStepTotalInfo(List<StepPO> stepList,
                                                                Map<String, AnswerAudioMappingVO> audioMappingMap,
                                                                Map<String, SimpleIntentVO> intentMap,
                                                                Map<String, List<DialogBaseNodePO>> stepNodeListMap,
                                                                AudioTypeEnum audioType,
                                                                String search) {
        // 需要把树形转成列表

        List<TreeifyAnswerAudioVO> result = new ArrayList<>();
        stepList.stream()
                .sorted(Comparator.comparingInt(step -> {
                    if (Objects.isNull(step.getOrderNum())) {
                        return Integer.MAX_VALUE;
                    } else {
                        return step.getOrderNum();
                    }
                }))
                .forEach(step -> {
                    List<DialogBaseNodePO> nodes = stepNodeListMap.get(step.getId());
                    Optional<TreeifyAnswerAudioVO> treeifyAudioOpt = wrapStepNodeTreeifyAudio(audioType, step, nodes, audioMappingMap, intentMap);
                    if (treeifyAudioOpt.isPresent()) {
                        TreeifyAnswerAudioVO treeifyAudio = treeifyAudioOpt.get();
                        List<TreeifyAnswerAudioVO> tmpList = new ArrayList<>();
                        tmpList.add(treeifyAudio);
                        List<TreeifyAnswerAudioVO> childList = treeifyAudio.getChildList();
                        treeifyAudio.setChildList(Collections.emptyList());
                        while (CollectionUtils.isNotEmpty(childList)) {
                            List<TreeifyAnswerAudioVO> tmpChildList = new ArrayList<>();
                            childList.forEach(child -> {
                                tmpChildList.addAll(child.getChildList());
                                child.setChildList(Collections.emptyList());
                                tmpList.add(child);
                            });
                            childList = tmpChildList;
                        }
                        if (StringUtils.isNotBlank(search)) {
                            tmpList.forEach(answerAudio -> {
                                if (CollectionUtils.isNotEmpty(answerAudio.getAnswerAudioList())) {
                                    List<AnswerAudioWrapVO> answerAudioList = answerAudio.getAnswerAudioList()
                                            .stream()
                                            .filter(wrap -> StringUtils.containsIgnoreCase(wrap.getText(), search))
                                            .collect(Collectors.toList());
                                    if (CollectionUtils.isNotEmpty(answerAudioList)) {
                                        answerAudio.setAnswerAudioList(answerAudioList);
                                        result.add(answerAudio);
                                    }
                                }
                            });
                        }
                    }
                });
        return result;
    }

    @Override
    public List<TreeifyAnswerAudioVO> searchAnswerAudioInfo(Long botId, Long recordUserId, String search) {
        if (Objects.isNull(recordUserId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "录音师id不能为空");
        }
        BotAudioConfigPO config = botConfigService.getAudioConfig(botId);
        if (AudioTypeEnum.COMPOSE.equals(config.getAudioType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "合成音不支持录音");
        }
        if (!recordUserId.equals(config.getRecordUserId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "当前录音师并非机器人配置的录音师, 请检查");
        }

        // 查询所有的流程, 问答知识, 特殊语境
        List<StepPO> stepList = stepService.getAllListByBotId(botId);
        List<DialogBaseNodePO> nodeList = stepNodeService.getAllListByBotId(botId);
        List<KnowledgePO> knowledgeList = knowledgeService.getAllListByBotId(botId);
        List<SpecialAnswerConfigPO> specialAnswerConfigList = specialAnswerConfigService.getByBotId(botId);

        List<AnswerAudioMappingVO> allAudioList = answerAudioMappingService.listAllAvailableAudioVO(botId, recordUserId, AudioTypeEnum.MAN_MADE);
        Map<String, AnswerAudioMappingVO> audioMappingMap = MyCollectionUtils.listToMap(allAudioList, AnswerAudioMappingPO::getText);

        List<IntentPO> intentList = intentService.getAllByBotId(botId);

        List<SimpleIntentVO> simpleIntentList = prepareSimpleIntentList(intentList);


        Map<String, SimpleIntentVO> intentMap = MyCollectionUtils.listToMap(simpleIntentList, SimpleIntentVO::getId);
        // 对数据进行包装

        // 包装流程
        Map<String, List<DialogBaseNodePO>> stepNodeListMap = MyCollectionUtils.listToMapList(nodeList, DialogBaseNodePO::getStepId);

        List<TreeifyAnswerAudioVO> result = new LinkedList<>();
        AudioTypeEnum audioType = config.getAudioType();

        result.addAll(processListableStepTotalInfo(stepList, audioMappingMap, intentMap, stepNodeListMap, audioType, search));

        // 问答知识
        result.addAll(
                processKnowledgeTotalInfoList(knowledgeList, audioMappingMap, intentMap, audioType, search).stream()
                        .map(AudioTotalInfoVO::getTreeifyAnswerAudio)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())
        );

        // 特殊语境
        result.addAll(
                processSpecialAnswerTotalInfo(specialAnswerConfigList, audioMappingMap, intentMap, audioType, search).stream()
                        .map(AudioTotalInfoVO::getTreeifyAnswerAudio)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())
        );

        return result;
    }

    private List<SimpleIntentVO> prepareSimpleIntentList(List<IntentPO> intentList) {
        List<SimpleIntentVO> simpleIntentList = new ArrayList<>();
        simpleIntentList.add(SimpleIntentVO.defaultIntent());
        simpleIntentList.add(SimpleIntentVO.userSilence());
        simpleIntentList.add(SimpleIntentVO.collectSuccess());
        simpleIntentList.add(SimpleIntentVO.collectFailed());
        simpleIntentList.addAll(
                intentList.stream()
                        .map(SimpleIntentVO::createFrom)
                        .collect(Collectors.toList())
        );
        return simpleIntentList;
    }

    private List<AudioTotalInfoVO> processSpecialAnswerTotalInfo(List<SpecialAnswerConfigPO> specialAnswerConfigList,
                                                                 Map<String, AnswerAudioMappingVO> audioMappingMap,
                                                                 Map<String, SimpleIntentVO> intentMap,
                                                                 AudioTypeEnum audioType,
                                                                 String search) {
        List<AudioTotalInfoVO> result = new LinkedList<>();
        // 特殊语境
        specialAnswerConfigList.forEach(specialAnswerConfig -> {
            if (!EnabledStatusEnum.ENABLE.equals(specialAnswerConfig.getEnabledStatus())) {
                return;
            }
            List<AnswerAudioWrapVO> audioList = convertAndFilterKnowledgeAnswer(audioMappingMap, audioType, search, specialAnswerConfig.getAnswerList(), createLocate(specialAnswerConfig));
            if (CollectionUtils.isNotEmpty(audioList)) {
                TreeifyAnswerAudioVO treeifyAnswerAudio = new TreeifyAnswerAudioVO();
                treeifyAnswerAudio.setAnswerAudioList(audioList);
                treeifyAnswerAudio.setChildList(Collections.emptyList());
                if (CollectionUtils.isNotEmpty(specialAnswerConfig.getTriggerIntentIdList())) {
                    List<SimpleIntentVO> intentList = specialAnswerConfig.getTriggerIntentIdList().stream()
                            .map(intentMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    treeifyAnswerAudio.setSimpleIntentList(intentList);
                }
                AudioTotalInfoVO knowledgeTotalInfo = convertSpecialAnswerToTotalInfo(specialAnswerConfig, treeifyAnswerAudio);
                result.add(knowledgeTotalInfo);
            }
        });

        return result;
    }

    private List<AnswerAudioWrapVO> convertAndFilterKnowledgeAnswer(Map<String, AnswerAudioMappingVO> audioMappingMap,
                                                                    AudioTypeEnum audioType,
                                                                    String search,
                                                                    List<KnowledgeAnswer> answerList,
                                                                    AnswerLocateBO locate) {
        List<AnswerAudioWrapVO> audioList = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(answerList)) {
            audioList = answerList
                    .stream()
                    .filter(answer -> {
                        if (StringUtils.isNotBlank(search)) {
                            return StringUtils.containsIgnoreCase(answer.getText(),search);
                        }
                        return true;
                    })
                    .map(answer -> answerAudioManagerService.convertAnswer(audioMappingMap, locate, answer, audioType))
                    .collect(Collectors.toList());
        }
        return audioList;
    }

    private List<AudioTotalInfoVO> processKnowledgeTotalInfoList(List<KnowledgePO> knowledgeList,
                                                                 Map<String, AnswerAudioMappingVO> audioMappingMap,
                                                                 Map<String, SimpleIntentVO> intentMap,
                                                                 AudioTypeEnum audioType,
                                                                 String search) {
        List<AudioTotalInfoVO> result = new ArrayList<>();
        // 问答知识
        knowledgeList.stream()
                .sorted(Comparator.comparingInt(knowledge -> knowledge.getCategory().getCode()))
                .forEach(knowledge -> {
                    List<AnswerAudioWrapVO> audioList = convertAndFilterKnowledgeAnswer(audioMappingMap, audioType, search, knowledge.getAnswerList(), createLocate(knowledge));
                    if (CollectionUtils.isNotEmpty(audioList)) {
                        TreeifyAnswerAudioVO treeifyAnswerAudio = new TreeifyAnswerAudioVO();
                        treeifyAnswerAudio.setAnswerAudioList(audioList);
                        treeifyAnswerAudio.setChildList(Collections.emptyList());
                        if (CollectionUtils.isNotEmpty(knowledge.getTriggerIntentIdList())) {
                            List<SimpleIntentVO> intentList = knowledge.getTriggerIntentIdList().stream()
                                    .map(intentMap::get)
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList());
                            treeifyAnswerAudio.setSimpleIntentList(intentList);
                        }
                        AudioTotalInfoVO knowledgeTotalInfo = convertKnowledgeToTotalInfo(knowledge, treeifyAnswerAudio);
                        result.add(knowledgeTotalInfo);
                    }
                });

        return result;
    }

    private AudioTotalInfoVO convertStepToTotalInfo(StepPO step, TreeifyAnswerAudioVO treeify) {
        AudioTotalInfoVO info = new AudioTotalInfoVO();

        info.setName(step.getName());
        info.setDisplayType(step.getType().getDesc());
        info.setOriginId(step.getId());
        info.setOriginType(AnswerSourceEnum.STEP);
        info.setTreeifyAnswerAudio(treeify);
        info.setCompleted(treeifyAnswerAudioCompleted(treeify));

        return info;
    }


    private AudioTotalInfoVO convertSpecialAnswerToTotalInfo(SpecialAnswerConfigPO specialAnswerConfig,
                                                             TreeifyAnswerAudioVO treeify) {
        AudioTotalInfoVO info = new AudioTotalInfoVO();

        info.setName(specialAnswerConfig.getName());
        info.setDisplayType(AnswerSourceEnum.SPECIAL_ANSWER.getDesc());
        info.setOriginId(specialAnswerConfig.getId());
        info.setOriginType(AnswerSourceEnum.SPECIAL_ANSWER);
        info.setTreeifyAnswerAudio(treeify);
        info.setCompleted(treeifyAnswerAudioCompleted(treeify));

        return info;
    }

    private AudioTotalInfoVO convertKnowledgeToTotalInfo(KnowledgePO knowledge, TreeifyAnswerAudioVO treeify) {
        AudioTotalInfoVO info = new AudioTotalInfoVO();

        info.setName(knowledge.getName());
        info.setDisplayType(knowledge.getCategory().getDesc());
        info.setOriginId(knowledge.getId());
        info.setOriginType(AnswerSourceEnum.KNOWLEDGE);
        info.setTreeifyAnswerAudio(treeify);
        info.setCompleted(treeifyAnswerAudioCompleted(treeify));

        return info;
    }

    private boolean allAudioWrapCompleted(List<AnswerAudioWrapVO> wrapList) {
        return wrapList.stream().allMatch(item -> BooleanUtils.isTrue(item.getCompleted()));
    }

    private boolean treeifyAnswerAudioCompleted(TreeifyAnswerAudioVO treeify) {
        if (CollectionUtils.isEmpty(treeify.getAnswerAudioList()) && CollectionUtils.isEmpty(treeify.getChildList())) {
            return true;
        }
        if (!allAudioWrapCompleted(treeify.getAnswerAudioList())) {
            return false;
        }
        if (CollectionUtils.isEmpty(treeify.getChildList())) {
            return true;
        }
        return treeify.getChildList().stream().allMatch(this::treeifyAnswerAudioCompleted);
    }

    private Optional<TreeifyAnswerAudioVO> wrapStepNodeTreeifyAudio(AudioTypeEnum audioType,
                                                                    StepPO step,
                                                                    List<DialogBaseNodePO> nodeList,
                                                                    Map<String, AnswerAudioMappingVO> audioMappingMap,
                                                                    Map<String, SimpleIntentVO> intentMap) {
        if (CollectionUtils.isEmpty(nodeList)) {
            return Optional.empty();
        }

        // root 应该只有一个, 如果是多个是应该提示错误的
        List<DialogBaseNodePO> rootNodeList = stepNodeService.getStepRootNode(nodeList);

        if (CollectionUtils.size(rootNodeList) > 1) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程数据不正确, 出现多个根节点");
        } else if (CollectionUtils.isEmpty(rootNodeList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "流程数据不正确, 不存在根节点");
        }
        Map<String, DialogBaseNodePO> nodeMap = MyCollectionUtils.listToMap(nodeList, DialogBaseNodePO::getId);
        Set<String> processedNodeIdSet = new HashSet<>();
        return recursionConvertNodeAnswer(processedNodeIdSet, nodeMap, audioMappingMap, intentMap, audioType, step, rootNodeList.get(0));
    }

    private String getNodeDisplayInfo(DialogBaseNodePO node) {
        if (StringUtils.isBlank(node.getLabel())) {
            return String.format("%s", node.getName());
        } else {
            return String.format("%s[%s]", node.getName(), node.getLabel());
        }
    }

    private Optional<TreeifyAnswerAudioVO> recursionConvertNodeAnswer(Set<String> processedNodeIdSet,
                                                                      Map<String, DialogBaseNodePO> nodeMap,
                                                                      Map<String, AnswerAudioMappingVO> mappingMap,
                                                                      Map<String, SimpleIntentVO> intentMap,
                                                                      AudioTypeEnum audioType,
                                                                      StepPO step,
                                                                      DialogBaseNodePO node) {

        if (processedNodeIdSet.contains(node.getId())) {
            return Optional.empty();
        }
        processedNodeIdSet.add(node.getId());
        TreeifyAnswerAudioVO treeNode = new TreeifyAnswerAudioVO();
        treeNode.setDisplayLabel(getNodeDisplayInfo(node));

        // 处理本节点的答案
        List<NodeAnswer> answerList = node.getAnswerList();
        if (CollectionUtils.isNotEmpty(answerList)) {
            treeNode.setAnswerAudioList(answerList.stream()
                    .map(answer -> answerAudioManagerService.convertAnswer(mappingMap, step, node, answer, audioType))
                    .collect(Collectors.toList())
            );
        }

        // 处理子节点
        if (node instanceof DialogChatNodePO) {
            // 获取child node
            DialogChatNodePO chatNode = (DialogChatNodePO) node;

            Map<String, List<String>> node2RefResourceListMap = new HashMap<>();
            chatNode.getIntentRelatedNodeMap().forEach((intentId, nodeId) -> {
                List<String> intentIdList = node2RefResourceListMap.computeIfAbsent(nodeId,(i) -> new ArrayList<>());
                intentIdList.add(intentId);
            });

            node2RefResourceListMap.forEach((nodeId, refIdList) -> {
                DialogBaseNodePO child = nodeMap.get(nodeId);
                if (Objects.nonNull(child)) {
                    recursionConvertNodeAnswer(processedNodeIdSet, nodeMap, mappingMap, intentMap, audioType, step, child)
                            .ifPresent(treeify -> {
                                // 设置意图信息
                                List<SimpleIntentVO> intentList = refIdList.stream().map(intentMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                                treeify.setSimpleIntentList(intentList);
                                treeNode.getChildList().add(treeify);
                            });
                } else {
                    log.warn("数据错误, 节点丢失, nodeId={}", nodeId);
                }
            });
        }

        if (node instanceof DialogJudgeNodePO) {
            Collection<String> nodeIdList = ((DialogJudgeNodePO) node).getBranchRelatedNodeMap().values();
            nodeIdList.forEach((nodeId) -> {
                DialogBaseNodePO child = nodeMap.get(nodeId);
                if (Objects.nonNull(child)) {
                    recursionConvertNodeAnswer(processedNodeIdSet, nodeMap, mappingMap, intentMap, audioType, step, child).ifPresent(treeNode.getChildList()::add);
                }
            });
        }
        return Optional.of(treeNode);
    }

    private AnswerLocateBO createLocate(KnowledgePO knowledge) {
        return AnswerLocateUtils.generate(knowledge);
    }

    private AnswerLocateBO createLocate(SpecialAnswerConfigPO config) {
        return AnswerLocateUtils.generate(config);
    }

    private AnswerLocateBO createLocate(StepPO step, DialogBaseNodePO node) {
        return AnswerLocateUtils.generate(step, node);
    }

    @Override
    public PageResultObject<BotAudioProgressVO> queryBotAudioProgress(Long recordUserId, String search, Integer pageNum, Integer pageSize) {
        // 查询出所有的botId, 先不考虑一个录音师绑定了上千个机器人
        List<Long> botIdList = botConfigService.queryBotIdListByRecordUserId(recordUserId);
        if (CollectionUtils.isEmpty(botIdList)) {
            return PageResultObject.of(Collections.emptyList());
        }
        BotQuery query = new BotQuery();
        query.setBotIdList(botIdList);
        query.setName(search);
        query.setPageNum(pageNum);
        query.setPageSize(pageSize);
        query.setAuditStatusSet(Stream.of(AuditStatusEnum.DRAFT, AuditStatusEnum.PENDING).collect(Collectors.toSet()));
        query.setOrderByUpdateTimeDesc(true);
        PageResultObject<BotVO> botPage = botService.queryListWithoutWrapVO(query);
        if (CollectionUtils.isEmpty(botPage.getContent())) {
            return PageResultObject.of(Collections.emptyList());
        }

        List<BotAudioProgressVO> progressList = botPage.getContent().stream()
                .map(bot -> {
                    BotAudioProgressVO vo = new BotAudioProgressVO();
                    vo.setBotId(bot.getBotId());
                    vo.setName(bot.getName());
                    vo.setUpdateTime(bot.getUpdateTime());
                    vo.setRecordUserId(recordUserId);
                    vo.setAudioStatus(bot.getAuditStatus());
                    AudioCompleteProgressVO progress = answerAudioManagerService.getAudioCompleteProgress(bot.getBotId());
                    vo.setPercent(progress.getCompletedPercent());
                    vo.setTotalCount(progress.getTotalCount());
                    vo.setDistTotalCount(progress.getDistTotalCount());
                    vo.setCompletedCount(progress.getCompletedCount());
                    vo.setDistCompletedCount(progress.getDistCompletedCount());
                    vo.setIncompleteCount(progress.getIncompleteCount());
                    vo.setDistIncompleteCount(progress.getDistIncompleteCount());
                    return vo;
                }).collect(Collectors.toList());

        PageResultObject<BotAudioProgressVO> result = PageResultObject.of(progressList);
        result.setNumber(botPage.getNumber());
        result.setPageSize(botPage.getPageSize());
        result.setPages(botPage.getPages());
        result.setTotalElements(botPage.getTotalElements());
        return result;
    }

    @Override
    public void batchAdjustVolume(Long botId, Long recordUserId, Integer volume, List<String> audioOssKeyList, Long userId) {
        if (CollectionUtils.isEmpty(audioOssKeyList)) {
            return;
        }

        final List<String> distList = audioOssKeyList.stream().distinct().filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(distList)) {
            return;
        }
        // sharding distList
        List<List<String>> shardingList = Lists.partition(distList, 10);

        List<String> adjustSuccessTextList = new ArrayList<>();
        // 多线程处理
        // 拆成多个任务
        CountDownLatch latch = new CountDownLatch(shardingList.size());
        for (int i = 0; i < shardingList.size(); i++) {
            String taskName = "批量调整音频音量-" + i;
            final List<String> subJobList = shardingList.get(i);
            DynamicDataSourceApplicationExecutorHolder.execute(taskName, () -> {
                try {
                    long totalNeedProcessCount = subJobList.size();
                    for (int index = 0; index < subJobList.size(); index++) {
                        log.info("{}开始处理第{}个任务, 共{}个任务", taskName, index + 1, totalNeedProcessCount);
                        String audioOssKey = subJobList.get(index);
                        AudioUploadResultVO audioUploadResult = audioUploadService.adjustVolume(botId, audioOssKey, volume);
                        answerAudioMappingService.adjustVolume(botId, audioOssKey, audioUploadResult.getVolume());
                        List<AnswerAudioMappingPO> mappingList = answerAudioMappingService.queryByUrl(botId, audioOssKey);
                        if (CollectionUtils.isNotEmpty(mappingList)) {
                            adjustSuccessTextList.addAll(mappingList.stream().map(AnswerAudioMappingPO::getText).map(StringUtils::trimToEmpty).collect(Collectors.toList()));
                        }
                    }
                } catch (Exception e) {
                    log.warn("{}处理失败", taskName, e);
                } finally {
                    latch.countDown();
                }
            });
        }

        try {
            boolean finish = latch.await(1, TimeUnit.MINUTES);
            answerAudioOperationLogService.batchAdjustVolume(botId, adjustSuccessTextList, volume, userId);
            if (!finish) {
                throw new ComException(ComErrorCode.UNKNOWN_ERROR, "批量调整音频音量处理超时");
            }
        } catch (InterruptedException e) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "批量调整音频音量异常", e);
        }
    }

    @Override
    @SneakyThrows
    public AudioUploadResultVO uploadAudio(Long botId, String answerText, MultipartFile file, Long userId) {
        if (StringUtils.isBlank(answerText)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "答案内容不能为空");
        }
        log.info("botId={}, answer={}, 上传音频:{}",botId, answerText, file.getName());
        log.info("file size={}", file.getSize());
        log.info("file content type={}", file.getContentType());


        // 保存到本地
        String path = String.format("%s/%s.wav", TempFilePathKeyCenter.getBotUploadAudioDirPath(botId), System.currentTimeMillis());
        File localFile = new File(path);
        log.info("开始保存音频文件到本地:{}", localFile.getPath());
        FileUtils.makeFileExist(path);
        file.transferTo(localFile);
        log.info("file:{}", file);

        Integer samplesPerSec = Optional.ofNullable(AudioHandleUtils.getWavHeader(localFile)).map(head -> head.SamplesPerSec).orElse(null);
        log.info("采样频率={}", samplesPerSec);
        if (Objects.isNull(samplesPerSec)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "音频文件格式异常");
        }

        boolean not8kAudio = 8000 != samplesPerSec;
        File finalFile = localFile;
        if (not8kAudio) {
            // 24k音频转换为8k,原始音频入库保存
            String tmpFilePath = TempFilePathKeyCenter.getBot8kAudioFilePath(botId);
            log.info("原始音频转换后保存至={}", tmpFilePath);
            MyFileUtils.makeFileExist(tmpFilePath);
            finalFile = new File(tmpFilePath);
            AudioHandleUtils.convertSf(path, tmpFilePath, 8000);
            audioUploadService.uploadAndSave(botId, answerText, localFile, samplesPerSec, userId);
        }
        AudioUploadResultVO result = audioUploadService.validAndUpload(botId, finalFile, file.getOriginalFilename(), true, true);
        AnswerUpdateAudioRequestVO request = new AnswerUpdateAudioRequestVO();
        request.setUrl(result.getUrl());
        request.setVolume(result.getVolume());
        request.setBotId(botId);
        request.setFullUrl(request.getFullUrl());
        request.setAnswerText(answerText);
        request.setDuration(result.getDuration());
        answerAudioManagerService.updateAnswerAudio(request, userId);
        return result;
    }

    @Override
    public AudioTotalInfoVO getTreeifyInfoBySourceType(Long botId, Long recordUserId, AnswerSourceEnum answerSource, String originId) {
        if (Objects.isNull(recordUserId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "录音师id不能为空");
        }
        BotAudioConfigPO config = botConfigService.getAudioConfig(botId);
        if (AudioTypeEnum.COMPOSE.equals(config.getAudioType())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "合成音不支持录音");
        }
        if (!recordUserId.equals(config.getRecordUserId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "当前录音师并非机器人配置的录音师, 请检查");
        }
        if (Objects.isNull(answerSource)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "answerSource不能为空");
        }
        if (StringUtils.isBlank(originId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "originId不能为空");
        }
        // 查询所有的流程, 问答知识, 特殊语境
        List<StepPO> stepList = new ArrayList<>();
        List<DialogBaseNodePO> nodeList = new ArrayList<>();
        List<KnowledgePO> knowledgeList = new ArrayList<>();
        List<SpecialAnswerConfigPO> specialAnswerConfigList = new ArrayList<>();
        switch (answerSource) {
            case STEP:
                StepPO step = stepService.getPOById(botId, originId);
                if (Objects.isNull(step)) {
                    throw new ComException(ComErrorCode.NOT_EXIST, "流程不存在");
                }
                stepList.add(step);
                List<DialogBaseNodePO> list = stepNodeService.getListByStepId(botId, originId);
                if (CollectionUtils.isNotEmpty(list)) {
                    nodeList.addAll(list);
                }
                break;
            case KNOWLEDGE:
                KnowledgePO knowledge = knowledgeService.getById(botId, originId);
                if (Objects.isNull(knowledge)) {
                    throw new ComException(ComErrorCode.NOT_EXIST, "知识不存在");
                }
                knowledgeList.add(knowledge);
                break;
            case SPECIAL_ANSWER:
                SpecialAnswerConfigPO specialAnswerConfig = specialAnswerConfigService.getById(botId, originId);
                if (Objects.isNull(specialAnswerConfig)) {
                    throw new ComException(ComErrorCode.NOT_EXIST, "特殊语境不存在");
                }
                specialAnswerConfigList.add(specialAnswerConfig);
            default:
                break;
        }

        List<AudioTotalInfoVO> list = wrapTreeifyInfoBySourceType(stepList, nodeList, knowledgeList, specialAnswerConfigList, config);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    private List<AudioTotalInfoVO> wrapTreeifyInfoBySourceType(List<StepPO> stepList,
                                                               List<DialogBaseNodePO> nodeList,
                                                               List<KnowledgePO> knowledgeList,
                                                               List<SpecialAnswerConfigPO> specialAnswerConfigList,
                                                               BotAudioConfigPO config) {
        Long botId = config.getBotId();
        List<AnswerAudioMappingVO> allAudioList = answerAudioMappingService.listAllAvailableAudioVO(config.getBotId(), config.getRecordUserId(), AudioTypeEnum.MAN_MADE);
        Map<String, AnswerAudioMappingVO> audioMappingMap = MyCollectionUtils.listToMap(allAudioList, AnswerAudioMappingPO::getText);

        List<IntentPO> intentList = intentService.getAllByBotId(botId);
        List<SimpleIntentVO> simpleIntentList = prepareSimpleIntentList(intentList);
        Map<String, SimpleIntentVO> intentMap = MyCollectionUtils.listToMap(simpleIntentList, SimpleIntentVO::getId);
        // 对数据进行包装

        // 包装流程
        Map<String, List<DialogBaseNodePO>> stepNodeListMap = MyCollectionUtils.listToMapList(nodeList, DialogBaseNodePO::getStepId);

        List<AudioTotalInfoVO> result = new LinkedList<>();
        AudioTypeEnum audioType = config.getAudioType();

        result.addAll(processTreeifyStepTotalInfo(stepList, audioMappingMap, intentMap, stepNodeListMap, audioType));

        result.addAll(processKnowledgeTotalInfoList(knowledgeList, audioMappingMap, intentMap, audioType, null));

        result.addAll(processSpecialAnswerTotalInfo(specialAnswerConfigList, audioMappingMap, intentMap, audioType, null));

        return result;
    }
}

