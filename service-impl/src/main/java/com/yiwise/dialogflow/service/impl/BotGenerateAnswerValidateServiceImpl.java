package com.yiwise.dialogflow.service.impl;

import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.dialogflow.entity.bo.SnapshotInvalidFailItemMsg;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.enums.BotResourceTypeEnum;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.service.BotGenerateAnswerValidateService;
import com.yiwise.dialogflow.service.RobotResourceService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/6/6
 */
@Service
public class BotGenerateAnswerValidateServiceImpl implements BotGenerateAnswerValidateService {

    @Override
    public void saveToSnapshot(RobotResourceContext context) {

    }

    @Override
    public void loadFromSnapshot(RobotResourceContext context) {

    }

    @Override
    public List<Class<? extends RobotResourceService>> dependsOn() {
        return Arrays.asList(StepServiceImpl.class, StepNodeServiceImpl.class, KnowledgeServiceImpl.class, SpecialAnswerConfigServiceImpl.class);
    }

    private boolean isInvalidAnswer(String answerText) {
        return StringUtils.startsWith(StringUtils.trimToEmpty(answerText), "[生成失败]");
    }

    private String generateErrorMsg(String resourceName, String resourceLabel) {
        return String.format("%s[%s]:%s", resourceName, resourceLabel, "话术生成未完全");
    }

    private void validateNodeAnswer(RobotSnapshotPO snapshot, List<SnapshotInvalidFailItemMsg> invalidMsgList) {
        List<DialogBaseNodePO> nodeList = snapshot.getNodeList();
        if (CollectionUtils.isEmpty(nodeList)) {
            return;
        }
        Map<String, StepPO> stepMap = MyCollectionUtils.listToMap(snapshot.getStepList(), StepPO::getId);
        for (DialogBaseNodePO node : nodeList) {
            List<NodeAnswer> answerList = node.getAnswerList();
            if (CollectionUtils.isEmpty(answerList)) {
                continue;
            }
            for (NodeAnswer answer : answerList) {
                if (isInvalidAnswer(answer.getText())) {
                    String nodeId = node.getId();
                    StepPO step = stepMap.get(node.getStepId());
                    if (Objects.nonNull(step)) {
                        invalidMsgList.add(SnapshotInvalidFailItemMsg
                                .builder()
                                .resourceType(BotResourceTypeEnum.NODE)
                                .resourceId(step.getId())
                                .resourceName(step.getName())
                                .resourceLabel(step.getLabel())
                                .nodeId(nodeId)
                                .nodeName(node.getName())
                                .nodeLabel(node.getLabel())
                                .failMsg(generateErrorMsg(node.getName(), node.getLabel()))
                                .isWarning(true)
                                .build());
                        break;
                    }
                }
            }
        }
    }

    private void validateKnowledgeAnswer(RobotSnapshotPO snapshot, List<SnapshotInvalidFailItemMsg> invalidMsgList) {
        List<KnowledgePO> knowledgeList = snapshot.getKnowledgeList();
        if (CollectionUtils.isEmpty(knowledgeList)) {
            return;
        }
        for (KnowledgePO knowledge : knowledgeList) {
            List<KnowledgeAnswer> answerList = knowledge.getAnswerList();
            if (CollectionUtils.isEmpty(answerList)) {
                continue;
            }
            for (KnowledgeAnswer answer : answerList) {
                if (isInvalidAnswer(answer.getText())) {
                    invalidMsgList.add(SnapshotInvalidFailItemMsg
                            .builder()
                            .resourceType(BotResourceTypeEnum.KNOWLEDGE)
                            .resourceId(knowledge.getId())
                            .resourceName(knowledge.getName())
                            .resourceLabel(knowledge.getLabel())
                            .failMsg(generateErrorMsg(knowledge.getName(), knowledge.getLabel()))
                            .isWarning(true)
                            .build());
                    break;
                }
            }
        }
    }

    private void validateSpecialAnswer(RobotSnapshotPO snapshot, List<SnapshotInvalidFailItemMsg> invalidMsgList) {
        List<SpecialAnswerConfigPO> specialAnswerConfigList = snapshot.getSpecialAnswerConfigList();
        if (CollectionUtils.isEmpty(specialAnswerConfigList)) {
            return;
        }
        for (SpecialAnswerConfigPO specialAnswer : specialAnswerConfigList) {
            List<KnowledgeAnswer> answerList = specialAnswer.getAnswerList();
            if (CollectionUtils.isEmpty(answerList)) {
                continue;
            }
            for (KnowledgeAnswer answer : answerList) {
                if (isInvalidAnswer(answer.getText())) {
                    invalidMsgList.add(SnapshotInvalidFailItemMsg
                            .builder()
                            .resourceType(BotResourceTypeEnum.SPECIAL_ANSWER_CONFIG)
                            .resourceId(specialAnswer.getId())
                            .resourceName(specialAnswer.getName())
                            .resourceLabel(specialAnswer.getLabel())
                            .failMsg(generateErrorMsg(specialAnswer.getName(), specialAnswer.getLabel()))
                            .isWarning(true)
                            .build());
                    break;
                }
            }
        }
    }

    @Override
    public void validateResource(RobotResourceContext context) {
        // 对算法生成失败的答案(以[生成失败]开头)进行warning提示,方便AIT手动修改
        List<SnapshotInvalidFailItemMsg> invalidMsgList = context.getInvalidMsgList();
        RobotSnapshotPO snapshot = context.getSnapshot();
        validateNodeAnswer(snapshot, invalidMsgList);
        validateKnowledgeAnswer(snapshot, invalidMsgList);
        validateSpecialAnswer(snapshot, invalidMsgList);
    }
}
