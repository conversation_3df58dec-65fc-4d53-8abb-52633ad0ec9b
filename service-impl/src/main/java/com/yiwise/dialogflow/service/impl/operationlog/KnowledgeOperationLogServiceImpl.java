package com.yiwise.dialogflow.service.impl.operationlog;

import com.yiwise.dialogflow.entity.bo.ActionNameResourceBO;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.dto.OperationLogDTO;
import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.KnowledgeAssignConfigPO;
import com.yiwise.dialogflow.entity.po.KnowledgePO;
import com.yiwise.dialogflow.helper.AnswerChangeLogHelper;
import com.yiwise.dialogflow.helper.IntentActionConfigCompareHelper;
import com.yiwise.dialogflow.helper.IntentLevelConfigCompareHelper;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.DependResourceService;
import com.yiwise.dialogflow.service.OperationLogService;
import com.yiwise.dialogflow.service.operationlog.KnowledgeOperationLogService;
import com.yiwise.dialogflow.service.intent.IntentRuleActionService;
import com.yiwise.dialogflow.service.remote.IntentLevelTagDetailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class KnowledgeOperationLogServiceImpl implements KnowledgeOperationLogService {

    @Resource
    private IntentRuleActionService intentRuleActionService;

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private DependResourceService dependResourceService;

    @Resource
    private IntentLevelTagDetailService intentLevelTagDetailService;

    @Resource
    private BotService botService;

    @Override
    public void compareAndCreateOperationLog(Long botId, KnowledgePO oldKnowledge, KnowledgePO newKnowledge, Long userId) {
        try {
            doCompareAndCreateOperationLog(botId, oldKnowledge, newKnowledge, userId);
        } catch (Exception e) {
            log.warn("处理操作日志异常", e);
        }
    }

    @Override
    public void createDeleteOperationLog(Long botId, List<KnowledgePO> deleteKnowledgeList, Long userId) {
        if (CollectionUtils.isEmpty(deleteKnowledgeList)) {
            return;
        }
        List<String> detailList = deleteKnowledgeList.stream()
                .map(oldKnowledge -> String.format("删除%s问答知识【%s:%s】", oldKnowledge.getCategory().getDesc(), oldKnowledge.getLabel(), oldKnowledge.getName()))
                .collect(Collectors.toList());
        operationLogService.save(botId, OperationLogTypeEnum.KNOWLEDGE, OperationLogResourceTypeEnum.KNOWLEDGE, detailList, userId);
    }

    @Override
    public void batchCreate(Long botId, List<KnowledgePO> knowledgeList, Long userId) {
        if (CollectionUtils.isEmpty(knowledgeList)) {
            return;
        }
        List<String> detailList = knowledgeList.stream().map(newKnowledge -> String.format("新增%s问答知识【%s:%s】", newKnowledge.getCategory().getDesc(), newKnowledge.getLabel(), newKnowledge.getName())).collect(Collectors.toList());
        operationLogService.save(botId, OperationLogTypeEnum.KNOWLEDGE, OperationLogResourceTypeEnum.KNOWLEDGE, detailList, userId);
    }

    private void doCompareAndCreateOperationLog(Long botId, KnowledgePO oldKnowledge, KnowledgePO newKnowledge, Long userId) {
        if (Objects.isNull(oldKnowledge) && Objects.isNull(newKnowledge)) {
            return;
        }

        List<String> logDetailList = new ArrayList<>();

        if (Objects.isNull(oldKnowledge)) {
            // 新增问答知识
            logDetailList.add(String.format("新增%s问答知识【%s:%s】", newKnowledge.getCategory().getDesc(), newKnowledge.getLabel(), newKnowledge.getName()));
        } else if (Objects.isNull(newKnowledge)) {
            // 删除问答知识
            logDetailList.add(String.format("删除%s问答知识【%s:%s】", oldKnowledge.getCategory().getDesc(), oldKnowledge.getLabel(), oldKnowledge.getName()));
        } else {
            // 更新问答知识
            DependentResourceBO resource = dependResourceService.generateByCondition(new DependentResourceBO.Condition(botId).step().variable().intent().specialAnswerConfig().knowledge());

            compareIntent(oldKnowledge, newKnowledge, logDetailList, resource);

            compareTitle(oldKnowledge, newKnowledge, logDetailList);

            compareCategory(oldKnowledge, newKnowledge, logDetailList);

            compareCustomerConcern(oldKnowledge, newKnowledge, logDetailList);

            compareVariableAssign(oldKnowledge, newKnowledge, logDetailList, resource.getVariableIdNameMap());

            String prefix = String.format("编辑%s问答知识【%s:%s】", newKnowledge.getCategory().getDesc(), newKnowledge.getLabel(), newKnowledge.getName());
            logDetailList.addAll(AnswerChangeLogHelper.compareAnswerChange(oldKnowledge.getAnswerList(), newKnowledge.getAnswerList(), prefix, resource));
            BotPO bot = botService.selectByKey(botId);
            Map<Integer, String> intentLevelCode2NameMap = Collections.emptyMap();
            if (Objects.nonNull(bot) && Objects.nonNull(bot.getIntentLevelTagId())) {
                intentLevelCode2NameMap = intentLevelTagDetailService.getIntentLevelTagDetailCode2NameMap(bot.getIntentLevelTagId());
            }
            compareIntentLevel(oldKnowledge, newKnowledge, logDetailList, intentLevelCode2NameMap);

            ActionNameResourceBO actionNameResource = intentRuleActionService.getSourceId2NameMapByBotId(newKnowledge.getBotId());

            // 触发动作设置
            compareActionConfig(oldKnowledge, newKnowledge, logDetailList, actionNameResource);

            // 流程重复
            compareRepeatStep(oldKnowledge, newKnowledge, logDetailList, resource);

            // 人工介入
            compareEnableHumanIntervention(oldKnowledge, newKnowledge, logDetailList);
        }
        save(botId, logDetailList, userId);
    }

    private void compareEnableHumanIntervention(KnowledgePO oldKnowledge, KnowledgePO newKnowledge, List<String> logDetailList) {
        boolean oldEnableHumanIntervention = BooleanUtils.isTrue(oldKnowledge.getEnableHumanIntervention());
        boolean newEnableHumanIntervention = BooleanUtils.isTrue(newKnowledge.getEnableHumanIntervention());
        if (oldEnableHumanIntervention != newEnableHumanIntervention) {
            logDetailList.add(String.format("%s启动人工介入:【%s】修改为【%s】",
                    updatePrefix(oldKnowledge),
                    oldEnableHumanIntervention ? "开启" : "关闭",
                    newEnableHumanIntervention ? "开启" : "关闭"));
        }
    }

    private void compareVariableAssign(KnowledgePO oldKnowledge, KnowledgePO newKnowledge, List<String> logDetailList, Map<String, String> variableId2NameMap) {
        String oldInfo = renderVariableAssign(oldKnowledge, variableId2NameMap);
        String newInfo = renderVariableAssign(newKnowledge, variableId2NameMap);
        if (!StringUtils.equals(oldInfo, newInfo)) {
            logDetailList.add(String.format("%s动态变量赋值设置由【%s】变更为【%s】", updatePrefix(oldKnowledge), oldInfo, newInfo));
        }
    }

    private String renderVariableAssign(KnowledgePO knowledge, Map<String, String> variableId2NameMap) {
        if (BooleanUtils.isNotTrue(knowledge.getEnableAssign()) || Objects.isNull(knowledge.getAssignConfig())) {
            return "未启用";
        }
        KnowledgeAssignConfigPO assignConfig = knowledge.getAssignConfig();
        return String.format("【常量赋值: %s保存到%s】", assignConfig.getConstantValue(), variableId2NameMap.getOrDefault(assignConfig.getVariableId(), "未知"));
    }

    private void compareIntent(KnowledgePO oldKnowledge, KnowledgePO newKnowledge, List<String> logDetailList, DependentResourceBO resource) {
        String oldIntentNameList = "";
        String newIntentNameList = "";
        if (CollectionUtils.isNotEmpty(oldKnowledge.getTriggerIntentIdList())) {
            oldIntentNameList = oldKnowledge.getTriggerIntentIdList().stream()
                    .map(resource.getIntentIdNameMap()::get)
                    .filter(StringUtils::isNotBlank)
                    .sorted()
                    .collect(Collectors.joining("、"));
        }
        if (CollectionUtils.isNotEmpty(newKnowledge.getTriggerIntentIdList())) {
            newIntentNameList = newKnowledge.getTriggerIntentIdList().stream()
                    .map(resource.getIntentIdNameMap()::get)
                    .filter(StringUtils::isNotBlank)
                    .sorted()
                    .collect(Collectors.joining("、"));
        }
        if (!StringUtils.equals(oldIntentNameList, newIntentNameList)) {
            logDetailList.add(String.format("%s关联意图【%s】修改为【%s】",
                    updatePrefix(oldKnowledge), oldIntentNameList, newIntentNameList));
        }
    }

    private String updatePrefix(KnowledgePO oldKnowledge) {
        return String.format("编辑%s问答知识【%s:%s】", oldKnowledge.getCategory().getDesc(), oldKnowledge.getLabel(), oldKnowledge.getName());
    }

    private void compareRepeatStep(KnowledgePO oldKnowledge, KnowledgePO newKnowledge, List<String> logDetailList, DependentResourceBO resource) {
        boolean changeStatus = changeEnableStatus(oldKnowledge.getEnableRepeatStep(), newKnowledge.getEnableRepeatStep());
        boolean changeValue = !changeStatus && BooleanUtils.isTrue(newKnowledge.getEnableRepeatStep()) && !Objects.equals(oldKnowledge.getRepeatStepIdList(), newKnowledge.getRepeatStepIdList());

        String oldRepeatStepName = "";
        String newRepeatStepName = "";
        if (CollectionUtils.isNotEmpty(oldKnowledge.getRepeatStepIdList())) {
            oldRepeatStepName = oldKnowledge.getRepeatStepIdList().stream()
                    .map(resource.getStepIdNameMap()::get)
                    .filter(StringUtils::isNotBlank)
                    .map(item -> String.format("【%s】", item))
                    .collect(Collectors.joining("、"));
        }
        if (CollectionUtils.isNotEmpty(newKnowledge.getRepeatStepIdList())) {
            newRepeatStepName = newKnowledge.getRepeatStepIdList().stream()
                    .map(resource.getStepIdNameMap()::get)
                    .filter(StringUtils::isNotBlank)
                    .map(item -> String.format("【%s】", item))
                    .collect(Collectors.joining("、"));
        }
        if (changeStatus) {
            String oldVal = BooleanUtils.isNotTrue(oldKnowledge.getEnableRepeatStep()) ? "未开启": oldRepeatStepName;
            String newVal = BooleanUtils.isNotTrue(newKnowledge.getEnableRepeatStep()) ? "未开启": newRepeatStepName;
            logDetailList.add(String.format("编辑%s问答知识: 原【%s:%s】与主流程去重设置【%s】修改为【%s】",
                    oldKnowledge.getCategory().getDesc(), oldKnowledge.getLabel(), oldKnowledge.getName(), oldVal, newVal));
        }
        if (changeValue) {
            logDetailList.add(String.format("编辑%s问答知识: 原【%s:%s】与主流程去重设置【%s】修改为【%s】",
                    oldKnowledge.getCategory().getDesc(), oldKnowledge.getLabel(), oldKnowledge.getName(), oldRepeatStepName, newRepeatStepName));
        }
    }

    private boolean changeEnableStatus(Boolean oldStats, Boolean newStats) {
        return BooleanUtils.isTrue(oldStats) != BooleanUtils.isTrue(newStats);
    }

    private void compareActionConfig(KnowledgePO oldKnowledge, KnowledgePO newKnowledge, List<String> logDetailList, ActionNameResourceBO actionNameResource) {
        String prefix = String.format("编辑%s问答知识【%s:%s】", oldKnowledge.getCategory().getDesc(), oldKnowledge.getLabel(), oldKnowledge.getName());
        logDetailList.addAll(IntentActionConfigCompareHelper.compareIntentActionConfigChangeLog(oldKnowledge, newKnowledge, actionNameResource, prefix));
    }

    private void compareIntentLevel(KnowledgePO oldKnowledge, KnowledgePO newKnowledge, List<String> logDetailList,
                                    Map<Integer, String> intentLevelCode2NameMap) {
        String prefix = String.format("编辑%s问答知识【%s:%s】", oldKnowledge.getCategory().getDesc(), oldKnowledge.getLabel(), oldKnowledge.getName());
        logDetailList.addAll(IntentLevelConfigCompareHelper.compareIntentLevelConfigChangeLog(oldKnowledge, newKnowledge, prefix, intentLevelCode2NameMap));
    }

    private void compareCategory(KnowledgePO oldKnowledge, KnowledgePO newKnowledge, List<String> logDetailList) {
        if (!Objects.equals(oldKnowledge.getCategory(), newKnowledge.getCategory())) {
            logDetailList.add(String.format("修改%s问答知识【%s:%s】知识类型【%s】修改为【%s】",
                    oldKnowledge.getCategory().getDesc(), oldKnowledge.getLabel(), oldKnowledge.getName(), oldKnowledge.getCategory().getDesc(), newKnowledge.getCategory().getDesc()));
        }
    }

    private void compareTitle(KnowledgePO oldKnowledge, KnowledgePO newKnowledge, List<String> logDetailList) {
        if (!Objects.equals(oldKnowledge.getName(), newKnowledge.getName())) {
            logDetailList.add(String.format("修改%s问答知识:原【%s:%s】知识标题【%s】修改为【%s】",
                    oldKnowledge.getCategory().getDesc(), oldKnowledge.getLabel(), oldKnowledge.getName(), oldKnowledge.getName(), newKnowledge.getName()));
        }
    }

    private void compareCustomerConcern(KnowledgePO oldKnowledge, KnowledgePO newKnowledge, List<String> logDetailList) {
        if (!Objects.equals(oldKnowledge.getIsCustomerConcern(), newKnowledge.getIsCustomerConcern())) {
            logDetailList.add(String.format("修改%s问答知识:原【%s:%s】%s关注点",
                    oldKnowledge.getCategory().getDesc(), oldKnowledge.getLabel(), oldKnowledge.getName(), BooleanUtils.isTrue(newKnowledge.getIsCustomerConcern()) ? "设为" : "取消"));
        }
    }

    // 比较集合是否相等
    private boolean compareListIsEqual(Collection<?> oldList, Collection<?> newList) {
        if (CollectionUtils.isEmpty(oldList) && CollectionUtils.isEmpty(newList)) {
            return true;
        }
        if (CollectionUtils.isEmpty(oldList) || CollectionUtils.isEmpty(newList)) {
            return false;
        }
        if (oldList.size() != newList.size()) {
            return false;
        }
        Set<?> oldSet = new HashSet<>(oldList);
        Set<?> newSet = new HashSet<>(newList);
        return oldSet.equals(newSet);
    }

    private void save(Long botId, List<String> detailList, Long userId) {
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }

        List<OperationLogDTO> logs = detailList.stream()
                .map(detail -> OperationLogDTO.builder()
                        .botId(botId)
                        .operatorId(userId)
                        .detail(detail)
                        .type(OperationLogTypeEnum.KNOWLEDGE)
                        .resourceType(OperationLogResourceTypeEnum.KNOWLEDGE)
                        .build()
                )
                .collect(Collectors.toList());

        operationLogService.batchSave(logs);
    }

}
