package com.yiwise.dialogflow.service.impl.train;

import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.base.model.websocket.BasicMsg;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.entity.bo.algorithm.ModelTrainKey;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.TrainResultPO;
import com.yiwise.dialogflow.entity.po.intent.IntentConfigPO;
import com.yiwise.dialogflow.entity.vo.*;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.helper.AliMessageQueueHelper;
import com.yiwise.dialogflow.service.FolderService;
import com.yiwise.dialogflow.service.OperationLogService;
import com.yiwise.dialogflow.service.asrmodel.AsrErrorCorrectionService;
import com.yiwise.dialogflow.service.intent.IntentConfigService;
import com.yiwise.dialogflow.service.intent.IntentService;
import com.yiwise.dialogflow.service.remote.UserService;
import com.yiwise.dialogflow.service.train.ModelVersionService;
import com.yiwise.dialogflow.service.train.TrainService;
import com.yiwise.dialogflow.service.train.TrainSyncService;
import com.yiwise.middleware.redis.service.RedisOpsService;
import javaslang.Tuple;
import javaslang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/12/2
 */
@Slf4j
@Service
public class TrainServiceImpl implements TrainService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private RedisOpsService redisOpsService;

    @Resource
    private UserService userService;

    @Resource
    private TrainSyncService trainSyncService;

    @Resource
    private IntentService intentService;

    @Resource
    private IntentConfigService intentConfigService;

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private AsrErrorCorrectionService asrErrorCorrectionService;

    @Resource
    private ModelVersionService modelVersionService;

    @Resource
    private BotService botService;

    @Resource
    private FolderService folderService;

    @Override
    public void train(Long botId, Long userId, String desc, SystemEnum systemType) {

        AlgorithmTrainTypeEnum trainTypeEnum  = AlgorithmTrainTypeEnum.INTENT;
        ModelTrainKey modelTrainKey = ModelTrainKey.of(botId);

        // 保存训练版本历史记录
        String modelId = null;
        int version = 1;
        TrainResultPO lastSuccess = lastSuccess(modelTrainKey);
        if (Objects.nonNull(lastSuccess)) {
            version = lastSuccess.getVersion() + 1;
            modelId = lastSuccess.getModelId();
        }

        if (StringUtils.isEmpty(modelId)) {
            modelId = modelVersionService.create(0L, botId, trainTypeEnum);
        }

        BotPO bot = botService.getById(botId);
        if (Objects.isNull(bot)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "机器人不存在");
        }

        TrainResultPO trainResultPO = new TrainResultPO();
        trainResultPO.setTenantId(0L);
        trainResultPO.setBotId(botId);
        trainResultPO.setTrainType(trainTypeEnum);
        trainResultPO.setVersion(version);
        trainResultPO.setModelId(modelId);
        trainResultPO.setDesc(desc);
        trainResultPO.setSystemType(systemType);
        trainResultPO.setModelName("NLP" + "_" + version);
        trainResultPO.setCreateUserId(userId);
        trainResultPO.setModelTrainKey(modelTrainKey);
        trainResultPO.setDomainName(bot.getDomainName());
        // 如果domainName传null, 算法那边会训练失败, 默认添加scrm
        if (StringUtils.isBlank(trainResultPO.getDomainName())) {
            trainResultPO.setDomainName("scrm");
        }
        create(trainResultPO);

        operationLogService.save(botId, OperationLogTypeEnum.INTENT, OperationLogResourceTypeEnum.ALGORITHM, "训练算法模型", userId);
    }


    /**
     * Websocket发送训练回调结果
     */
    @Override
    public void message(TrainResultPO trainResultPO) {
        String msgContent = BooleanUtils.isTrue(trainResultPO.getSuccess()) ? "模型训练成功" : "模型训练失败";
        BasicMsg<TrainResultPO> msg = new BasicMsg<>(msgContent, BasicMsg.class.getSimpleName());
        msg.setInfo(trainResultPO);
        AliMessageQueueHelper.sendWebSocketMessage(ApplicationConstant.WEBSOCKET_TRAIN_SUBSCRIPT_URL, msg, "TrainResult");
    }

    @Override
    public void create(TrainResultPO trainResultPO) {
        trainResultPO.setCreateTime(LocalDateTime.now());
        mongoTemplate.save(trainResultPO);

        // 保存训练请求到redis
        trainSyncService.postTrainSignal(TrainDataVO.of(trainResultPO));

        updateTrainingStatus(trainResultPO.getModelTrainKey());
    }

    @Override
    public void callback(TrainStatusVO trainResultPO) {
        // 1.0话术这边传过来的tenantID是0
        Long tenantId = trainResultPO.getTenantId();
        String refId = trainResultPO.getRobotId();
        AlgorithmTrainTypeEnum trainType = trainResultPO.getTrainType();
        ModelTrainKey modelTrainKey = ModelTrainKey.of(tenantId, refId, trainType);
        if (trainType == null) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "训练类型不能为空");
        }

        Query query = Query.query(Criteria.where("modelId").is(trainResultPO.getModelId()).and("success").exists(false));
        TrainResultPO lastSuccess = mongoTemplate.findOne(query, TrainResultPO.class);
        if (Objects.isNull(lastSuccess)) {
            log.error("模型训练回调，modelId={}模型不存在", trainResultPO.getModelId());
            return;
        }
        lastSuccess.setSuccess(trainResultPO.getSuccess());
        lastSuccess.setFinishTime(trainResultPO.getFinishTime());

        Update update = Update.update("success", trainResultPO.getSuccess()).set("finishTime", trainResultPO.getFinishTime());
        // 这边将tenantId设为0，是因为操作日志需要记录tenantId，利用数据库将异步请求的参数带过来，而模型和模型历史是三端通用的
        if (AlgorithmTrainTypeEnum.INTENT.equals(trainType) || AlgorithmTrainTypeEnum.MIXED_MODEL.equals(trainType)) {
            update.set("tenantId", 0L);
        }
        mongoTemplate.updateFirst(query, update, TrainResultPO.class);
        try {
            if (trainType.equals(AlgorithmTrainTypeEnum.ASR_ERROR_CORRECTION)) {
                asrErrorCorrectionService.trainSuccess(refId);
            } else {
                Long botId = Long.valueOf(refId);
                if (BooleanUtils.isTrue(lastSuccess.getSuccess())) {
                    // 如果训练成功，更新训练状态
                    intentService.updateTrainingStatus(botId);
                } else {
                    intentService.updateTrainingStatus(botId, TrainStatusEnum.TRAINING_FAIL);
                }
                // 添加操作日志
                saveOperationLog(lastSuccess, trainResultPO);
            }
            message(lastSuccess);
            // 设置可以训练
            deleteTrainingStatus(modelTrainKey);
        } catch (Exception e) {
            log.error("训练作用域错误", e);
        }
    }

    private void saveOperationLog(TrainResultPO lastTrain, TrainStatusVO trainStatus) {
        String detail = BooleanUtils.isTrue(lastTrain.getSuccess()) ? "模型训练成功" : "模型训练失败, 原因:" + trainStatus.getOperate() ;
        operationLogService.save(lastTrain.getBotId(), OperationLogTypeEnum.INTENT, OperationLogResourceTypeEnum.ALGORITHM, detail, lastTrain.getCreateUserId());
    }

    @Override
    public List<TrainStatusVO> history(Long tenantId, AlgorithmTrainTypeEnum trainType) {
        Query query = Query.query(
                Criteria.where("tenantId").is(tenantId)
                        .and("trainType").is(trainType)
                        .and("finishTime").exists(true)
        ).with(Sort.by(Sort.Direction.DESC, "finishTime")).limit(5);
        List<TrainStatusVO> trainStatusVOList = mongoTemplate.find(query, TrainStatusVO.class);
        trainStatusVOList.forEach(trainStatusVO -> trainStatusVO.setCreateUserName(userService.getUserById(trainStatusVO.getCreateUserId()).getName()));
        return trainStatusVOList;
    }

    @Override
    public TrainResultPO lastSuccess(ModelTrainKey modelTrainKey) {
        Query query = Query.query(
                Criteria.where("tenantId").is(modelTrainKey.getTenantId())
                        .and("botId").is(modelTrainKey.getBotId())
                        .and("trainType").is(modelTrainKey.getTrainType())
                        .and("finishTime").exists(true)
                        .and("success").is(Boolean.TRUE)
        ).with(Sort.by(Sort.Direction.DESC, "finishTime")).limit(1);
        return mongoTemplate.findOne(query, TrainResultPO.class);
    }

    @Override
    public TrainResultPO earliestUntrained(ModelTrainKey modelTrainKey) {
        Query query = new Query();
        AlgorithmTrainTypeEnum trainType = modelTrainKey.getTrainType();
        // 这边因为1.0话术的特殊逻辑，需要将tenantId排除在查询条件之外
        if (!(AlgorithmTrainTypeEnum.INTENT.equals(trainType) || AlgorithmTrainTypeEnum.MIXED_MODEL.equals(trainType))) {
            query.addCriteria(Criteria.where("tenantId").is(modelTrainKey.getTenantId()));
        }
        query.addCriteria(Criteria.where("botId").is(modelTrainKey.getBotId())
                        .and("trainType").is(trainType)
                        .and("success").exists(false)
                        .and("modelId").exists(true))
                .with(Sort.by(Sort.Direction.DESC, "createTime")).limit(1);
        return mongoTemplate.findOne(query, TrainResultPO.class);
    }

    @Override
    public TrainStatusVO enableTrain(ModelTrainKey modelTrainKey) {
        TrainButtonStatusEnum trainButtonStatus = TrainButtonStatusEnum.FINISHED;
        Long botId = modelTrainKey.getBotId();
        Boolean isTraining = queryIsTraining(modelTrainKey);
        if (isTraining) {
            trainButtonStatus = TrainButtonStatusEnum.TRAINING;
        } else if (!intentService.hasIntent(botId)) {
            trainButtonStatus = TrainButtonStatusEnum.EMPTY;
        } else if (intentService.enableTrain(botId)) {
            trainButtonStatus = TrainButtonStatusEnum.READY;
        }
        TrainStatusVO result = new TrainStatusVO();
        result.setIsTraining(TrainButtonStatusEnum.TRAINING.equals(trainButtonStatus));
        result.setTrainButtonEnabled(TrainButtonStatusEnum.READY.equals(trainButtonStatus));
        result.setModelTrainKey(modelTrainKey);
        result.setTrainButtonStatus(trainButtonStatus);
        return result;
    }

    @Override
    public void updateTrainingStatus(ModelTrainKey modelTrainKey) {
        String key = RedisKeyCenter.getTrainingStatusRedisKey(modelTrainKey.getTenantId(), modelTrainKey.getModelId(), modelTrainKey.getTrainType().name());
        redisOpsService.set(key, true, 10 * 60);
    }

    @Override
    public void deleteTrainingStatus(ModelTrainKey modelTrainKey) {
        String key = RedisKeyCenter.getTrainingStatusRedisKey(modelTrainKey.getTenantId(), modelTrainKey.getModelId(), modelTrainKey.getTrainType().name());
        redisOpsService.delete(key);
    }

    @Override
    public Boolean queryIsTraining(ModelTrainKey modelTrainKey) {
        String key = RedisKeyCenter.getTrainingStatusRedisKey(modelTrainKey.getTenantId(), modelTrainKey.getModelId(), modelTrainKey.getTrainType().name());
        return redisOpsService.isKeyExist(key);
    }

    @Override
    public BatchTrainResultVO batchTrain(BatchTrainRequestVO request, Long tenantId) {
        BatchTrainResultVO preResult = preBatchTrain(request, tenantId);
        List<Long> successList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(preResult.getReadyBotIdList())) {
            for (Long botId : preResult.getReadyBotIdList()) {
                train(botId, request.getUserId(), "批量训练", SystemEnum.CRM);
                successList.add(botId);
            }
        }
        List<BotPO> botList = botService.getByIdList(successList);
        Map<Long, String> botIdNameMap = MyCollectionUtils.listToConvertMap(botList, BotPO::getBotId, BotPO::getName);
        preResult.setSuccessList(successList.stream()
                .map(botId -> IdNamePair.of(botId, botIdNameMap.get(botId)))
                .collect(Collectors.toList())
        );
        preResult.setReadyBotIdList(Collections.emptyList());
        return preResult;
    }

    @Override
    public BatchTrainResultVO preBatchTrain(BatchTrainRequestVO request, Long tenantId) {
        List<Long> botIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(request.getFolderIdList())) {
            botIdList.addAll(folderService.getBotIdListByFolderIdList(request.getFolderIdList(), true));
        }
        if (CollectionUtils.isNotEmpty(request.getBotIdList())) {
            botIdList.addAll(request.getBotIdList());
        }
        if (CollectionUtils.isEmpty(botIdList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "bot列表不能为空");
        }

        List<BotPO> botList = botService.getByIdList(botIdList);
        List<IntentConfigPO> intentConfigList = intentConfigService.queryByBotIdList(botIdList);

        Map<Long, String> botIdNameMap = MyCollectionUtils.listToConvertMap(botList, BotPO::getBotId, BotPO::getName);

        List<Tuple3<Long, String, String>> filterList = new ArrayList<>();
        List<Long> readyBotIdList = new ArrayList<>();
        for (IntentConfigPO intentConfig : intentConfigList) {
            if (BooleanUtils.isTrue(intentConfig.getEnableAlgorithm())) {
                // 判断是否正在训练中
                ModelTrainKey trainKey = ModelTrainKey.of(0L, intentConfig.getBotId(), AlgorithmTrainTypeEnum.INTENT);
                TrainStatusVO trainStatus = enableTrain(trainKey);
                switch (trainStatus.getTrainButtonStatus()) {
                    case READY:
                        readyBotIdList.add(intentConfig.getBotId());
                        break;
                    case TRAINING:
                        filterList.add(Tuple.of(intentConfig.getBotId(), botIdNameMap.get(intentConfig.getBotId()), "模型正在训练中"));
                        break;
                    case EMPTY:
                        filterList.add(Tuple.of(intentConfig.getBotId(), botIdNameMap.get(intentConfig.getBotId()), "意图语料为空"));
                        break;
                    case FINISHED:
                        filterList.add(Tuple.of(intentConfig.getBotId(), botIdNameMap.get(intentConfig.getBotId()), "模型已训练"));
                        break;
                    default:
                        break;
                }
            } else {
                filterList.add(Tuple.of(intentConfig.getBotId(), botIdNameMap.get(intentConfig.getBotId()), "未开启算法"));
            }
        }

        BatchTrainResultVO result = new BatchTrainResultVO();
        result.setSuccessList(Collections.emptyList());
        result.setFilterList(filterList);
        result.setReadyBotIdList(readyBotIdList);
        return result;
    }

}
