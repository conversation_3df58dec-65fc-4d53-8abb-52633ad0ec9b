package com.yiwise.dialogflow.service.impl.operationlog;

import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.po.Mismatch;
import com.yiwise.dialogflow.service.operationlog.MismatchOperationLogService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class MismatchOperationLogServiceImpl implements MismatchOperationLogService {

    private boolean changeEnableStatus(Boolean oldStats, Boolean newStats) {
        return BooleanUtils.isTrue(oldStats) != BooleanUtils.isTrue(newStats);
    }

    // 比较集合是否相等
    private boolean compareListIsEqual(Collection<?> oldList, Collection<?> newList) {
        if (CollectionUtils.isEmpty(oldList) && CollectionUtils.isEmpty(newList)) {
            return true;
        }
        if (CollectionUtils.isEmpty(oldList) || CollectionUtils.isEmpty(newList)) {
            return false;
        }
        if (oldList.size() != newList.size()) {
            return false;
        }
        Set<?> oldSet = new HashSet<>(oldList);
        Set<?> newSet = new HashSet<>(newList);
        return oldSet.equals(newSet);
    }

    @Override
    public List<String> compare(String prefix, Mismatch oldMismatch, Mismatch newMismatch, DependentResourceBO resource) {
        List<String> logs = new ArrayList<>();
        boolean changeStatus = changeEnableStatus(oldMismatch.getMismatchKnowledgeAndStep(), newMismatch.getMismatchKnowledgeAndStep());
        boolean changeMismatchStep = !changeStatus && BooleanUtils.isTrue(newMismatch.getMismatchKnowledgeAndStep())
                && (!Objects.equals(oldMismatch.getMismatchAllStep(), newMismatch.getMismatchAllStep())
                || !compareListIsEqual(oldMismatch.getMismatchStepIdList(), newMismatch.getMismatchStepIdList()));
        boolean changeMismatchKnowledge = !changeStatus && BooleanUtils.isTrue(newMismatch.getMismatchKnowledgeAndStep()) && (
                !Objects.equals(oldMismatch.getMismatchAllKnowledge(), newMismatch.getMismatchAllKnowledge())
                        || !compareListIsEqual(oldMismatch.getMismatchKnowledgeIdList(), newMismatch.getMismatchKnowledgeIdList()));
        boolean changeMismatchSpecialAnswer = !changeStatus && BooleanUtils.isTrue(newMismatch.getMismatchKnowledgeAndStep()) && (
                !Objects.equals(oldMismatch.getMismatchAllSpecialAnswerConfig(), newMismatch.getMismatchAllSpecialAnswerConfig())
                        || !compareListIsEqual(oldMismatch.getMismatchSpecialAnswerConfigIdList(), newMismatch.getMismatchSpecialAnswerConfigIdList()));

        BiFunction<List<String>, Map<String, String>, String> displayNameConvertFunc = (ids, idNameMap) -> {
            if (CollectionUtils.isEmpty(ids)) {
                return "";
            }
            String names = ids.stream()
                    .map(idNameMap::get)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining("、"));
            return String.format("【%s】", names);
        };

        String oldMismatchKnowledgeConfig = BooleanUtils.isTrue(oldMismatch.getMismatchAllKnowledge())
                ? "【全部知识库】"
                : displayNameConvertFunc.apply(oldMismatch.getMismatchKnowledgeIdList(), resource.getKnowledgeIdNameMap());

        String newMismatchKnowledgeConfig = BooleanUtils.isTrue(newMismatch.getMismatchAllKnowledge())
                ? "【全部知识库】"
                : displayNameConvertFunc.apply(newMismatch.getMismatchKnowledgeIdList(), resource.getKnowledgeIdNameMap());

        String oldMismatchStepConfig = BooleanUtils.isTrue(oldMismatch.getMismatchAllStep())
                ? "【全部流程】"
                : displayNameConvertFunc.apply(oldMismatch.getMismatchStepIdList(), resource.getStepIdNameMap());

        String newMismatchStepConfig = BooleanUtils.isTrue(newMismatch.getMismatchAllStep())
                ? "【全部流程】"
                : displayNameConvertFunc.apply(newMismatch.getMismatchStepIdList(), resource.getStepIdNameMap());

        String oldMismatchSpecialAnswerConfig = BooleanUtils.isTrue(oldMismatch.getMismatchAllSpecialAnswerConfig())
                ? "【全部特殊语境】"
                : displayNameConvertFunc.apply(oldMismatch.getMismatchSpecialAnswerConfigIdList(), resource.getSpecialAnswerIdNameMap());

        String newMismatchSpecialAnswerConfig = BooleanUtils.isTrue(newMismatch.getMismatchAllSpecialAnswerConfig())
                ? "【全部特殊语境】"
                : displayNameConvertFunc.apply(newMismatch.getMismatchSpecialAnswerConfigIdList(), resource.getSpecialAnswerIdNameMap());

        if (changeStatus) {
            String oldVal = BooleanUtils.isTrue(oldMismatch.getMismatchKnowledgeAndStep()) ?
                    String.format("%s,%s,%s", oldMismatchKnowledgeConfig, oldMismatchStepConfig, oldMismatchSpecialAnswerConfig)
                    : "未开启";
            String newVal = BooleanUtils.isTrue(newMismatch.getMismatchKnowledgeAndStep()) ?
                    String.format("%s,%s,%s", newMismatchKnowledgeConfig, newMismatchStepConfig, newMismatchSpecialAnswerConfig) :
                    "未开启";
            logs.add(String.format("%s不关联问答知识/流程/特殊语境设置【%s】修改为【%s】",
                    prefix, oldVal, newVal));
        }
        if (changeMismatchStep) {
            logs.add(String.format("%s不关联流程设置%s修改为%s",
                    prefix, oldMismatchStepConfig, newMismatchStepConfig));
        }
        if (changeMismatchKnowledge) {
            logs.add(String.format("%s不关联问答知识设置%s修改为%s",
                    prefix, oldMismatchKnowledgeConfig, newMismatchKnowledgeConfig));
        }
        if (changeMismatchSpecialAnswer) {
            logs.add(String.format("%s不关联特殊语境设置%s修改为%s",
                    prefix, oldMismatchSpecialAnswerConfig, newMismatchSpecialAnswerConfig));
        }
        return logs;
    }
}