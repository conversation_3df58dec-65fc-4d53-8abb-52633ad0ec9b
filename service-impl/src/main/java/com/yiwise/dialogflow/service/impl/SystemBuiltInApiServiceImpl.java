package com.yiwise.dialogflow.service.impl;

import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.entity.po.SystemBuiltInApiPO;
import com.yiwise.dialogflow.service.SystemBuiltInApiService;
import com.yiwise.dialogflow.thread.DynamicDataSourceApplicationExecutorHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Service
public class SystemBuiltInApiServiceImpl implements SystemBuiltInApiService {

    // 缓存超时时间 1小时
    private static final long TIMEOUT_MS = 60 * 60 * 1000;
    private final AtomicReference<List<SystemBuiltInApiPO>> CACHE = new AtomicReference<>(Collections.emptyList());

    private final AtomicLong nextFlushTime = new AtomicLong(0);

    @Resource
    private MongoTemplate mongoTemplate;

    @PostConstruct
    public void init() {
        tryFlush();
    }

    private void asyncFlush() {
        DynamicDataSourceApplicationExecutorHolder.execute("刷新内置api接口配置", () -> {
            try {
                String env = "PROD";
                if (ApplicationConstant.CURR_ENV.isPre()) {
                    env = "PRE";
                } else if (ApplicationConstant.CURR_ENV.isTest()) {
                    env = "DAILY";
                }
                log.info("flush built in api, env = {}", env);
                List<SystemBuiltInApiPO> result = doQueryFromDB(env);
                CACHE.set(result);
            } catch (Exception e) {
                log.warn("[LogHub_Warn]刷新内置api接口配置失败", e);
                // 10秒后重试
                nextFlushTime.set(System.currentTimeMillis() + 10 * 1000);
            }
        });
    }

    @Override
    public List<SystemBuiltInApiPO> queryAllEnabledListWithCache() {
        List<SystemBuiltInApiPO> result = CACHE.get();
        tryFlush();
        return new ArrayList<>(result);
    }

    private List<SystemBuiltInApiPO> doQueryFromDB(String env) {
        Query query = new Query();
        query.addCriteria(Criteria.where("env").is(env));
        query.addCriteria(Criteria.where("enabledStatus").is(EnabledStatusEnum.ENABLE));
        return mongoTemplate.find(query, SystemBuiltInApiPO.class, SystemBuiltInApiPO.COLLECTION_NAME);
    }

    private void tryFlush() {
        long now = System.currentTimeMillis();
        long next = nextFlushTime.get();
        if (next < now && nextFlushTime.compareAndSet(next, now + TIMEOUT_MS)) {
              asyncFlush();
        }
    }
}
