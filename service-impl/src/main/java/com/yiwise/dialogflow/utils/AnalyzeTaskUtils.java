package com.yiwise.dialogflow.utils;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.calloutjob.api.vo.CallOutJobWebVO;
import com.yiwise.calloutjob.api.vo.QueryByIdsVO;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.entity.bo.analyze.AnalyzeTemplateBlackData;
import com.yiwise.dialogflow.entity.bo.analyze.AnalyzeTemplateImportModel;
import com.yiwise.dialogflow.entity.bo.analyze.AnalyzeTemplateWhiteData;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.analyze.*;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.service.analyze.AnalyzeTemplateCorpusService;
import com.yiwise.dialogflow.service.analyze.AnalyzeTemplateIntentService;
import com.yiwise.dialogflow.service.analyze.AnalyzeTemplateService;
import com.yiwise.dialogflow.service.remote.calloutjob.CallOutJobClient;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class AnalyzeTaskUtils {

    private static AnalyzeTemplateService analyzeTemplateService = AppContextUtils.getBean(AnalyzeTemplateService.class);
    private static AnalyzeTemplateIntentService analyzeTemplateIntentService = AppContextUtils.getBean(AnalyzeTemplateIntentService.class);
    private static AnalyzeTemplateCorpusService analyzeTemplateCorpusService = AppContextUtils.getBean(AnalyzeTemplateCorpusService.class);
    private static RestTemplate restTemplate = (RestTemplate) AppContextUtils.getBean("restTemplate");
    private static CallOutJobClient callOutJobClient = AppContextUtils.getBean(CallOutJobClient.class);

    private static String getFatherName(AnalyzeTemplateIntentPO intent, Map<String, AnalyzeTemplateIntentPO> intentIdPoMap) {
        return StringUtils.isBlank(intent.getParentIntentId()) || !intentIdPoMap.containsKey(intent.getParentIntentId())
                ? intent.getName()
                : getFatherName(intentIdPoMap.get(intent.getParentIntentId()), intentIdPoMap);
    }

    public static AnalyzeTemplateImportModel buildTemplateConfig(String templateId) {
        AnalyzeTemplatePO template = Objects.requireNonNull(analyzeTemplateService.getById(templateId));
        return buildTemplateConfig(template);
    }

    public static AnalyzeTemplateImportModel buildTemplateConfig(AnalyzeTemplatePO template) {
        List<AnalyzeTemplateIntentPO> intentList = analyzeTemplateIntentService.listByTemplateId(template.getId());
        List<AnalyzeTemplateCorpusPO> corpusList = analyzeTemplateCorpusService.listByTemplateId(template.getId());
        return buildTemplateConfig(intentList, corpusList, template.getDomain());
    }

    public static AnalyzeTemplateImportModel buildTemplateConfig(List<AnalyzeTemplateIntentPO> intentList, List<AnalyzeTemplateCorpusPO> corpusList, String domain) {
        AnalyzeTemplateBlackData blackData = new AnalyzeTemplateBlackData();
        blackData.setKeywords(Lists.newArrayList());
        blackData.setBuiltin(Lists.newArrayList());
        blackData.setCustomized(Lists.newArrayList());

        AnalyzeTemplateImportModel model = new AnalyzeTemplateImportModel();
        model.setWhiteMap(Maps.newHashMap());
        model.setBlackInfo(blackData);
        model.setDomain(domain);

        // <意图id, 意图实体>
        Map<String, AnalyzeTemplateIntentPO> intentIdPoMap = MyCollectionUtils.listToMap(intentList, AnalyzeTemplateIntentPO::getId);

        // <意图id, 语料列表>
        Map<String, List<String>> intentIdCorpusListMap = corpusList.stream().collect(Collectors.groupingBy(AnalyzeTemplateCorpusPO::getIntentId,
                Collectors.mapping(AnalyzeTemplateCorpusPO::getCorpus, Collectors.toList())));

        // 填充黑名单信息
        List<AnalyzeTemplateIntentPO> blackIntentList = intentList.stream().filter(i -> AnalyzeTemplateIntentCategoryEnum.isBlack(i.getCategory())).collect(Collectors.toList());
        for (AnalyzeTemplateIntentPO blackIntent : blackIntentList) {
            if (AnalyzeTemplateIntentTypeEnum.isBuiltin(blackIntent.getType())) {
                blackData.getBuiltin().add(blackIntent.getName());
            }
            blackData.getCustomized().addAll(intentIdCorpusListMap.getOrDefault(blackIntent.getId(), Collections.emptyList()));
        }

        // 填充白名单信息
        List<AnalyzeTemplateIntentPO> whilteIntentList = intentList.stream().filter(i -> AnalyzeTemplateIntentCategoryEnum.isWhite(i.getCategory())).collect(Collectors.toList());
        for (AnalyzeTemplateIntentPO intent : whilteIntentList) {
            AnalyzeTemplateWhiteData whiteData = new AnalyzeTemplateWhiteData();
            whiteData.setData(intentIdCorpusListMap.getOrDefault(intent.getId(), Collections.emptyList()));
            whiteData.setIsBuiltin(AnalyzeTemplateIntentTypeEnum.isBuiltin(intent.getType()));

            String fatherName = getFatherName(intent, intentIdPoMap);
            if (StringUtils.equals(fatherName, intent.getName())) {
                fatherName = "";
            }
            whiteData.setFather(fatherName);

            model.getWhiteMap().put(intent.getName(), whiteData);
        }
        return model;
    }

    public static void analyze(AnalyzeTaskPO task) {
        AnalyzeTemplatePO template = analyzeTemplateService.getById(task.getTemplateId());
        if (Objects.isNull(template)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "模板不存在");
        }

        String url = AnalyzeTemplateModelTypeEnum.isLLM(template.getModelType()) ? ApplicationConstant.ANALYZE_TASK_LLM_REQUEST_URL : ApplicationConstant.ANALYZE_TASK_REQUEST_URL;
        if (StringUtils.isBlank(url)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "请求地址为空");
        }

        String body = buildRequestBody(task);
        log.info("请求算法会话分析接口, url={}, body={}", url, body);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Content-Type", "application/json");
        HttpEntity<String> requestEntity = new HttpEntity<>(body, httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        log.info("请求算法会话分析接口结果为: response={}", response);

        Result result = JsonUtils.string2Object(response.getBody(), Result.class);
        if (Objects.isNull(result.getCode()) || !result.getCode().equals(0)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "请求算法会话分析接口失败");
        }
    }

    private static List<Map<String, Object>> buildNluChoices(AnalyzeTemplatePO template) {
        String templateId = template.getId();
        List<AnalyzeTemplateIntentPO> intentList = analyzeTemplateIntentService.listByTemplateId(templateId);
        List<AnalyzeTemplateCorpusPO> corpusList = analyzeTemplateCorpusService.listByTemplateId(templateId);
        Map<String, List<String>> corpusMap = corpusList.stream().collect(Collectors.groupingBy(AnalyzeTemplateCorpusPO::getIntentId, Collectors.mapping(AnalyzeTemplateCorpusPO::getCorpus, Collectors.toList())));
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (AnalyzeTemplateIntentPO intent : intentList) {
            Map<String, Object> map = new HashMap<>(2);
            map.put("value", intent.getName());
            map.put("descriptions", corpusMap.get(intent.getId()));
            resultList.add(map);
        }
        return resultList;
    }

    public static String buildRequestBody(AnalyzeTaskPO task) {
        if (StringUtils.isBlank(ApplicationConstant.ANALYZE_TASK_CALLBACK_URL)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "回调地址为空");
        }
        String templateId = task.getTemplateId();
        AnalyzeTemplatePO template = analyzeTemplateService.getById(templateId);
        if (Objects.isNull(template)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "模板不存在");
        }

        Boolean isLlm = AnalyzeTemplateModelTypeEnum.isLLM(template.getModelType());

        Model model = new Model();
        model.setTaskId(String.valueOf(task.getId()));
        model.setPlatform(ApplicationConstant.ANALYZE_TASK_PLATFORM);
        if (!isLlm) {
            model.setConfig(buildTemplateConfig(template));
            model.setTaskType("refusal");
        } else {
            model.setChoices(buildNluChoices(template));
            model.setInfoDescription(template.getDesc());
        }
        List<ConditionData> list = Lists.newArrayList();
        for (AnalyzeTaskCorpusPO corpus : task.getCorpusList()) {
            AnalyzeTaskCorpusSourceTypeEnum source = corpus.getSource();
            if (AnalyzeTaskCorpusSourceTypeEnum.isTenant(source)) {
                list.add(new ConditionData(
                        Lists.newArrayList(),
                        Lists.newArrayList(),
                        corpus.getTenantList().stream().map(IdNamePair::getId).map(String::valueOf).collect(Collectors.toList()),
                        corpus.getStartDate(),
                        corpus.getEndDate().plusDays(1),
                        true
                ));
                list.add(new ConditionData(
                        Lists.newArrayList(),
                        Lists.newArrayList(),
                        corpus.getTenantList().stream().map(IdNamePair::getId).map(String::valueOf).collect(Collectors.toList()),
                        corpus.getStartDate(),
                        corpus.getEndDate().plusDays(1),
                        false
                ));
            }
            if (AnalyzeTaskCorpusSourceTypeEnum.isDialogFlow(source)) {
                list.add(new ConditionData(
                        Lists.newArrayList(),
                        corpus.getDialogFlowList().stream().map(IdNamePair::getId).map(String::valueOf).collect(Collectors.toList()),
                        Lists.newArrayList(),
                        corpus.getStartDate(),
                        corpus.getEndDate().plusDays(1),
                        true
                ));
                list.add(new ConditionData(
                        Lists.newArrayList(),
                        corpus.getDialogFlowList().stream().map(IdNamePair::getId).map(String::valueOf).collect(Collectors.toList()),
                        Lists.newArrayList(),
                        corpus.getStartDate(),
                        corpus.getEndDate().plusDays(1),
                        false
                ));
            }
            if (AnalyzeTaskCorpusSourceTypeEnum.isCallJob(source)) {
                list.add(new ConditionData(
                        corpus.getRobotCallJobList().stream().map(IdNamePair::getId).map(String::valueOf).collect(Collectors.toList()),
                        Lists.newArrayList(),
                        Lists.newArrayList(),
                        corpus.getStartDate(),
                        corpus.getEndDate().plusDays(1),
                        false
                ));
            }
            if (AnalyzeTaskCorpusSourceTypeEnum.isNewCallJob(source)) {
                QueryByIdsVO queryByIdsVO = new QueryByIdsVO();
                queryByIdsVO.setIds(corpus.getNewRobotCallJobList().stream().map(IdNamePair::getId).collect(Collectors.toList()));
                List<String> tenantIdList = callOutJobClient.getCallOutJobListByIds(queryByIdsVO).stream().map(CallOutJobWebVO::getTenantId)
                                .filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(tenantIdList)) {
                    tenantIdList = Collections.singletonList(String.valueOf(ApplicationConstant.OPE_TENANT_ID));
                }
                list.add(new ConditionData(
                        corpus.getNewRobotCallJobList().stream().map(IdNamePair::getId).map(String::valueOf).collect(Collectors.toList()),
                        Lists.newArrayList(),
                        tenantIdList,
                        corpus.getStartDate(),
                        corpus.getEndDate().plusDays(1),
                        true
                ));
            }
        }
        model.setDataConfig(list);
        model.setCallbackApi(ApplicationConstant.ANALYZE_TASK_CALLBACK_URL);
        return JsonUtils.object2String(model);
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    private static class Result implements Serializable {

        Integer code;

        String info;
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    private static class Model implements Serializable {

        String taskId;

        String platform;

        AnalyzeTemplateImportModel config;

        List<Map<String, Object>> choices;

        List<ConditionData> dataConfig;

        String taskType;

        String callbackApi;

        String infoDescription;
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @NoArgsConstructor
    @AllArgsConstructor
    private static class ConditionData implements Serializable {

        List<String> outboundTaskIds;

        List<String> outboundScriptIds;

        List<String> outboundTenantIds;

        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        LocalDate outboundStartTime;

        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        LocalDate outboundEndTime;

        /**
         * 是否为新版外呼
         */
        Boolean isLight;
    }
}
