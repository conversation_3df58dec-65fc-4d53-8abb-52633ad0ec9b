package com.yiwise.dialogflow.utils;

import com.yiwise.dialogflow.entity.enums.ConditionEnum;
import com.yiwise.dialogflow.entity.enums.ConditionVarTypeEnum;
import com.yiwise.dialogflow.entity.po.BaseAnswerContent;
import com.yiwise.dialogflow.entity.po.CollectNodeSkipCondition;
import com.yiwise.dialogflow.entity.po.ConditionExpressionPO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class AnswerConditionUtil {

    // todo 重复代码
    public static String generateConditionStrContent(BaseAnswerContent answer, Map<String, String> varIdNameMap, Map<String, String> intentIdNameMap) {
        return BooleanUtils.isNotTrue(answer.getEnableVarCondition()) ? "轮询" : generateConditionStrContent(answer.getConditionList(), varIdNameMap, intentIdNameMap);
    }

    public static String generateConditionStrContent(List<List<ConditionExpressionPO>> conditionList, Map<String, String> varIdNameMap, Map<String, String> intentIdNameMap) {
        if (CollectionUtils.isEmpty(conditionList)) {
            return "";
        }
        return conditionList.stream().map(
                list -> list.stream().map(c -> condition2String(c, varIdNameMap, intentIdNameMap)).collect(Collectors.joining("，"))
        ).collect(Collectors.joining(" 或 "));
    }


    public static String condition2String(CollectNodeSkipCondition condition, Map<String, String> intentId2nameMap) {
        if (Objects.isNull(condition)) {
            return "";
        }
        String skipTypeDesc = Objects.isNull(condition.getSkipType()) ? "" : condition.getSkipType().getDesc();
        return String.format("%s 跳过 %s", condition2String(condition, new HashMap<>(), intentId2nameMap), skipTypeDesc);
    }

    private static String condition2String(ConditionExpressionPO condition, Map<String, String> varIdNameMap, Map<String, String> intentIdNameMap) {
        if (Objects.isNull(condition)) {
            return "";
        }
        ConditionVarTypeEnum preVarType = condition.getPreVarType();
        String preTypeName = preVarType.getDesc();
        String conditionName = condition.getCondition().getDesc();

        if (ConditionVarTypeEnum.isIntent(preVarType)) {
            String intentNameList = condition.getIntentIdList().stream().map(intentIdNameMap::get).collect(Collectors.joining("，"));
            return String.format("%s【%s】%s", preTypeName, intentNameList, conditionName);
        }

        String preVarName = ConditionVarTypeEnum.AI.equals(preVarType) ? "性别" : varIdNameMap.getOrDefault(condition.getPreVarId(), "");

        if (ConditionEnum.BLANK.equals(condition.getCondition()) || ConditionEnum.NOT_BLANK.equals(condition.getCondition())) {
            return String.format("%s【%s】%s", preTypeName, preVarName, conditionName);
        } else {
            ConditionVarTypeEnum postVarType = condition.getPostVarType();
            String postVarName;
            switch (postVarType) {
                case CUSTOM:
                case DYNAMIC:
                    postVarName = varIdNameMap.getOrDefault(condition.getPostVarId(), "");
                    break;
                case AI:
                    postVarName = "性别";
                    break;
                case CONSTANT:
                    postVarName = condition.getConstantStr();
                    break;
                default:
                    postVarName = "";
                    break;
            }
            return String.format("%s【%s】%s%s【%s】", preTypeName, preVarName, conditionName, postVarType.getDesc(), postVarName);
        }
    }
}
