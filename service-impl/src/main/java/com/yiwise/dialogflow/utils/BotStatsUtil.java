package com.yiwise.dialogflow.utils;

import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.entity.bo.stats.BotStatsAnalysisResult;
import com.yiwise.dialogflow.entity.query.BaseStatsQuery;
import com.yiwise.dialogflow.entity.vo.KnowledgeQueryVO;
import com.yiwise.dialogflow.entity.vo.KnowledgeVO;
import com.yiwise.dialogflow.entity.vo.stats.CommonPercentVO;
import com.yiwise.dialogflow.entity.vo.stats.KnowledgeStatsVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.nio.channels.Selector;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Objects;
import java.util.Optional;

public class BotStatsUtil {

    public static AggregationOperation generateCommonEpochHourCondition(Long beginEpochHour, Long endEpochHour) {
        return Aggregation.match(Criteria.where("epochHour").gte(beginEpochHour).lte(endEpochHour));
    }

    public static AggregationOperation generateCommonEpochDayCondition(Long beginEpochDay, Long endEpochDay) {
        return Aggregation.match(Criteria.where("epochDay").gte(beginEpochDay).lte(endEpochDay));
    }


    public static long toEpochHour(LocalDateTime localDateTime) {
        return (localDateTime.toLocalDate().toEpochDay() * 24 ) + localDateTime.getHour();
    }

    public static Optional<AggregationOperation> generateTenantCondition(BaseStatsQuery query) {
        if (Objects.nonNull(query.getTenantId()) && query.getTenantId() > 0 && SystemEnum.isAICC(query.getSystemType())) {
            return Optional.of(Aggregation.match(Criteria.where("tenantId").is(query.getTenantId())));
        }
        return Optional.empty();
    }

    public static Optional<AggregationOperation> generateCallJobCondition(BaseStatsQuery query) {
        if (BooleanUtils.isTrue(query.getQueryMaFlow())) {
            // 查询ma外呼数据
            if (CollectionUtils.isNotEmpty(query.getMaFlowIdList())) {
                return Optional.of(Aggregation.match(Criteria.where("maFlowId").in(query.getMaFlowIdList())));
            } else {
                return Optional.of(Aggregation.match(Criteria.where("maFlowId").gt(0)));
            }
        } else if (BooleanUtils.isTrue(query.getQueryNewCallJob())) {
            // 查询新版外呼任务
            if (CollectionUtils.isNotEmpty(query.getNewCallJobIdList())) {
                return Optional.of(Aggregation.match(Criteria.where("newCallJobId").in(query.getNewCallJobIdList())));
            } else {
                return Optional.of(Aggregation.match(Criteria.where("newCallJobId").gt(0)));
            }
        } else if (BooleanUtils.isTrue(query.getQueryCallIn())) {
            // 查询呼入
            return Optional.of(Aggregation.match(Criteria.where("callInJobId").gt(0)));
        } else {
            // 查询外呼任务数据
            if (CollectionUtils.isNotEmpty(query.getCallJobIdList())) {
                return Optional.of(Aggregation.match(Criteria.where("callJobId").in(query.getCallJobIdList())));
            } else {
                return Optional.of(Aggregation.match(Criteria.where("callJobId").gt(0)));
            }
        }
    }

    public static long toEpochDay(LocalDateTime localDateTime) {
        return localDateTime.toLocalDate().toEpochDay();
    }

    public static LocalDate epochDay2LocalDate(long epochDay) {
        return LocalDate.ofEpochDay(epochDay);
    }

    public static LocalDateTime epochHour2LocalDateTime(long epochHour) {
        LocalDate localDate = epochDay2LocalDate(epochHour / 24);
        int hour = (int) (epochHour % 24);
        return LocalDateTime.of(localDate, java.time.LocalTime.of(hour, 0));
    }

    public static Query generateCommonQuery(BotStatsAnalysisResult analysisResult) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(analysisResult.getBotId()));
        query.addCriteria(Criteria.where("tenantId").is(analysisResult.getTenantId()));
        query.addCriteria(Criteria.where("callJobId").is(analysisResult.getCallJobId()));
        query.addCriteria(Criteria.where("maFlowId").is(analysisResult.getMaFlowId()));
        query.addCriteria(Criteria.where("newCallJobId").is(analysisResult.getNewCallJobId()));
        query.addCriteria(Criteria.where("callInJobId").is(analysisResult.getCallInJobId()));
        query.addCriteria(Criteria.where("year").is(analysisResult.getCallEndTime().getYear()));
        query.addCriteria(Criteria.where("month").is(analysisResult.getCallEndTime().getMonthValue()));
        query.addCriteria(Criteria.where("day").is(analysisResult.getCallEndTime().getDayOfMonth()));
        query.addCriteria(Criteria.where("hour").is(analysisResult.getCallEndTime().getHour()));
        query.addCriteria(Criteria.where("epochDay").is(analysisResult.getCallEndTime().toLocalDate().toEpochDay()));
        query.addCriteria(Criteria.where("epochHour").is(BotStatsUtil.toEpochHour(analysisResult.getCallEndTime())));
        query.addCriteria(Criteria.where("version").is(analysisResult.getVersion()));
        return query;
    }

    public static long calculateAnswerTemplateHash(String answerTemplate) {
        answerTemplate = StringUtils.trimToEmpty(answerTemplate);
        return answerTemplate.hashCode();
    }

    public static LocalDateTime getQueryableEarliestDateTime() {
        return LocalDateTime.now().minusDays(14);
    }

    public static LocalDateTime getBeginDateTimeOrDefault(LocalDateTime localDateTime) {
        return localDateTime == null ? getQueryableEarliestDateTime() : localDateTime;
    }

    public static LocalDateTime getEndDateTimeOrDefault(LocalDateTime localDateTime) {
        return localDateTime == null ? LocalDateTime.now() : localDateTime;
    }

    public static CommonPercentVO calculatePercent(int itemCount, int totalCount) {
        if (totalCount <= 0 || itemCount <= 0) {
            return new CommonPercentVO(0, 0);
        }
        return new CommonPercentVO(itemCount, (double) itemCount / totalCount);
    }

}
