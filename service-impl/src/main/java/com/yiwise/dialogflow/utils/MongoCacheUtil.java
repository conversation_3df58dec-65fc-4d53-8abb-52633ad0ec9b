package com.yiwise.dialogflow.utils;

import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class MongoCacheUtil {
    public static String CACHE_PREFIX = "{cache}";

    public static String LOCK_KEY_PREFIX = "k_lock";

    public static String KEY_SPLITTER = ":::";

    public static String PROPERTY_SPLITTER = "::";

    public static String LOCK_COLLECTION_PREFIX = "c_lock";

    public static String TIME_KEY_PREFIX = "time";

    public static String getCollectionScanKey(String collectionName) {
        return CACHE_PREFIX + KEY_SPLITTER + collectionName + KEY_SPLITTER + "*";
    }

    public static String getTimeRedisKey(String collectionName) {
        return TIME_KEY_PREFIX + KEY_SPLITTER + collectionName;
    }

    public static String getCLockRedisKey(String collectionName, int index) {
        return LOCK_COLLECTION_PREFIX + KEY_SPLITTER + collectionName + KEY_SPLITTER + index;
    }

    public static String generateRedisKey(String collectionName, Query query) {
        String queryKey = queryToKey(query);
        return CACHE_PREFIX + KEY_SPLITTER + collectionName + KEY_SPLITTER + queryKey;
    }

    public static String getCollectionNameFromRedisKey(String redisKey) {
        int idx1 = StringUtils.indexOf(redisKey, KEY_SPLITTER);
        if (idx1 < 0) {
            return null;
        }
        String tmp1 = StringUtils.substring(redisKey, idx1 + KEY_SPLITTER.length());
        int idx2 = StringUtils.indexOf(tmp1, KEY_SPLITTER);
        if (idx2 < 0) {
            return null;
        }
        String collectionName = StringUtils.substring(tmp1, 0, idx2);
        return collectionName;
    }

    public static Query getQueryFromRedisKey(String redisKey) {
        int idx1 = StringUtils.indexOf(redisKey, KEY_SPLITTER);
        if (idx1 < 0) {
            return null;
        }
        String tmp1 = StringUtils.substring(redisKey, idx1 + KEY_SPLITTER.length());
        int idx2 = StringUtils.indexOf(tmp1, KEY_SPLITTER);
        if (idx2 < 0) {
            return null;
        }
        String tmp2 = StringUtils.substring(tmp1, idx2 + KEY_SPLITTER.length());
        Query query = keyToQuery(tmp2);
        return query;
    }

    public static String queryToKey(Query query) {
        List<String> kvList = new ArrayList<>();
        Document d = query.getQueryObject();
        for(Map.Entry<String, Object> entry : d.entrySet()) {
            String key = entry.getKey();
            Object obj = entry.getValue();
            String value = obj.toString();
            String type;
            if (obj instanceof Long) {
                type = "long";
            } else if (obj instanceof Integer) {
                type = "int";
            } else if (obj instanceof Boolean) {
                type = "bool";
            } else if (obj instanceof LocalDate) {
                type = "localDate";
            } else if (obj instanceof LocalDateTime) {
                type = "localDateTime";
            } else {
                type = "string";
            }
            String condition = key + PROPERTY_SPLITTER + value + PROPERTY_SPLITTER + type;
            kvList.add(condition);
        }
        String result = StringUtils.join(kvList, KEY_SPLITTER);
        return result;
    }

    public static Query keyToQuery(String key) {
        Query query = new Query();
        String[] criterias = StringUtils.splitByWholeSeparator(key, KEY_SPLITTER);
        for (String criteriaStr : criterias) {
            Criteria criteria = null;
            String[] conditions = StringUtils.splitByWholeSeparator(criteriaStr, "__");
            for(String condition : conditions) {
                String[] kv = StringUtils.splitByWholeSeparator(condition, PROPERTY_SPLITTER);
                String k = kv[0];
                String v = kv[1];
                String type = kv[2];
                Object ov;
                if (type.equals("int")) {
                    ov = Integer.valueOf(v);
                } else if (type.equals("long")) {
                    ov = Long.valueOf(v);
                } else if (type.equals("bool")) {
                    ov = Boolean.valueOf(v);
                } else if (type.equals("localDate")) {
                    ov = LocalDate.parse(v);
                } else if (type.equals("localDateTime")) {
                    ov = LocalDateTime.parse(v);
                } else {
                    ov = v;
                }
                criteria = Criteria.where(k).is(ov);
            }
            query.addCriteria(criteria);
        }
        return query;
    }
}