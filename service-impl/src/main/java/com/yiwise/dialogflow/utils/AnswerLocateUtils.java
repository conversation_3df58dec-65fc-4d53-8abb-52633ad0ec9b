package com.yiwise.dialogflow.utils;

import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.entity.po.*;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

public class AnswerLocateUtils {

    public static AnswerLocateBO generate(StepPO step, DialogBaseNodePO node, BaseAnswerContent answer) {
        AnswerLocateBO locate = generate(step, node);

        locate.setAnswerId(answer.getUniqueId());
        locate.setAnswerLabel(answer.getLabel());
        locate.setIndex(getAnswerIndex(node.getAnswerList(), answer));
        return locate;
    }

    public static AnswerLocateBO generate(StepPO step, DialogBaseNodePO node) {

        AnswerLocateBO locate = new AnswerLocateBO();
        locate.setAnswerSource(AnswerSourceEnum.STEP);
        locate.setStepId(step.getId());
        locate.setStepName(step.getName());
        locate.setStepLabel(step.getLabel());

        locate.setNodeId(node.getId());
        locate.setNodeName(node.getName());
        locate.setNodeLabel(node.getLabel());

        return locate;
    }

    public static AnswerLocateBO generate(KnowledgePO knowledge, BaseAnswerContent answer) {
        AnswerLocateBO locate = generate(knowledge);
        locate.setAnswerId(answer.getUniqueId());
        locate.setAnswerLabel(answer.getLabel());
        locate.setIndex(getAnswerIndex(knowledge.getAnswerList(), answer));

        return locate;
    }

    public static AnswerLocateBO generate(KnowledgePO knowledge) {
        AnswerLocateBO locate = new AnswerLocateBO();
        locate.setAnswerSource(AnswerSourceEnum.KNOWLEDGE);
        locate.setKnowledgeId(knowledge.getId());
        locate.setKnowledgeName(knowledge.getName());
        locate.setKnowledgeLabel(knowledge.getLabel());

        return locate;
    }

    public static AnswerLocateBO generate(SpecialAnswerConfigPO specialAnswerConfig, BaseAnswerContent answer) {
        AnswerLocateBO locate = generate(specialAnswerConfig);

        locate.setAnswerId(answer.getUniqueId());
        locate.setAnswerLabel(answer.getLabel());
        locate.setIndex(getAnswerIndex(specialAnswerConfig.getAnswerList(), answer));

        return locate;
    }
    public static AnswerLocateBO generate(SpecialAnswerConfigPO specialAnswerConfig) {
        AnswerLocateBO locate = new AnswerLocateBO();
        locate.setAnswerSource(AnswerSourceEnum.SPECIAL_ANSWER);
        locate.setSpecialAnswerConfigId(specialAnswerConfig.getId());
        locate.setSpecialAnswerConfigName(specialAnswerConfig.getName());
        locate.setSpecialAnswerConfigLabel(specialAnswerConfig.getLabel());

        return locate;
    }

    private static Integer getAnswerIndex(List<? extends BaseAnswerContent> answerContextList, BaseAnswerContent answer) {
        if (CollectionUtils.isEmpty(answerContextList)) {
            return -1;
        }
        for (int i = 0; i < answerContextList.size(); i++) {
            if (answerContextList.get(i).getUniqueId().equals(answer.getUniqueId())) {
                return i;
            }
        }
        return -1;
    }
}
