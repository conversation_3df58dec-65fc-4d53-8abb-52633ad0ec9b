package com.yiwise.dialogflow.mapper;

import com.yiwise.dialogflow.entity.po.BotRefPO;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/21
 */
public interface BotRefPOMapper extends Mapper<BotRefPO> {

    Long getBotId(@Param("dialogFlowId") Long dialogFlowId);

    Long getDialogFlowId(@Param("botId") Long botId);

    int deleteByDialogFlowId(@Param("dialogFlowId") Long dialogFlowId);

    int deleteByBotId(@Param("botId") Long botId);

    /**
     * 获取tenantId
     *
     * @param botId
     * @return
     */
    Long getTenantIdByBotId(@Param("botId") Long botId);

    List<BotRefPO> getByBotIdList(@Param("botIdList") List<Long> botIdList);

    void updateByDialogFlowIds(@Param("list") List<Long> dialogFlowIds, @Param("tenantId") Long tenantId);

    List<Long> getBotIdListByDialogFlowIds(@Param("list") List<Long> dialogFlowIds);

    List<BotRefPO> getByDialogFlowIdList(@Param("dialogFlowIdList") List<Long> dialogFlowIdList);

    List<BotRefPO> getAllBindList();
}
