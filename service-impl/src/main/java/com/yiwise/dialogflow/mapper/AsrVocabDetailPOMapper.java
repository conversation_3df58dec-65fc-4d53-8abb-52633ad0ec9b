package com.yiwise.dialogflow.mapper;

import com.yiwise.dialogflow.entity.po.asrmodel.AsrVocabDetailPO;
import com.yiwise.dialogflow.entity.query.AsrVocabDetailQuery;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrVocabDetailVO;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/27 09:52:40
 */
public interface AsrVocabDetailPOMapper extends Mapper<AsrVocabDetailPO> {
    List<AsrVocabDetailVO> listByCondition(@Param("query") AsrVocabDetailQuery query);

    List<AsrVocabDetailPO> getByName(@Param("name") String name);
}