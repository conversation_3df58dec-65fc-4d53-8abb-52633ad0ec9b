package com.yiwise.dialogflow.mapper;

import com.yiwise.dialogflow.entity.po.FolderPO;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/20
 */
public interface FolderPOMapper extends Mapper<FolderPO> {

    List<FolderPO> getByTenantIdAndDepth(@Param("tenantId") Long tenantId, @Param("depth") Integer depth, @Param("createUserIdList") List<Long> createUserIdList);

    Integer countByDepth(@Param("tenantId") Long tenantId, @Param("depth") Integer depth);

    List<FolderPO> search(@Param("tenantId") Long tenantId, @Param("name") String name);

    List<Long> selectCreateUserIdByTenantId(@Param("tenantId") Long tenantId);
}
