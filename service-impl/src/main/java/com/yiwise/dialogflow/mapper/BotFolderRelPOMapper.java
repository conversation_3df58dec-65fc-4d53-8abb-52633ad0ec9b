package com.yiwise.dialogflow.mapper;

import com.yiwise.dialogflow.entity.po.BotFolderRelPO;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/20
 */
public interface BotFolderRelPOMapper extends Mapper<BotFolderRelPO> {

    List<Long> getBotIdListByFolderId(@Param("folderId") Long folderId);

    List<BotFolderRelPO> listByFolderIdList(@Param("folderIdList") Collection<Long> folderIdList);

    Long getFolderIdByBotId(@Param("tenantId") Long tenantId, @Param("botId") Long botId);

    List<BotFolderRelPO> listByBotIdList(@Param("tenantId") Long tenantId, @Param("botIdList") Collection<Long> botIdList);
}
