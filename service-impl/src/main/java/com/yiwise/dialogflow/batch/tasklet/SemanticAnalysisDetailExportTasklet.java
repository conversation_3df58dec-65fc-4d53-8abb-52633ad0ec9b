package com.yiwise.dialogflow.batch.tasklet;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageHelper;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.AtomicLongMap;
import com.yiwise.base.common.thread.decorator.MDCDecoratorRunnable;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.batch.model.dto.SheetInfoDTO;
import com.yiwise.batch.service.SpringBatchJobLogService;
import com.yiwise.batch.support.SheetWriter;
import com.yiwise.batch.support.WorkbookWriter;
import com.yiwise.dialogflow.entity.dto.semantic.analysis.SemanticAnalysisDetailDTO;
import com.yiwise.dialogflow.entity.po.SessionDetailIntentInfoPO;
import com.yiwise.dialogflow.entity.po.callout.CallDetailPO;
import com.yiwise.dialogflow.entity.po.remote.CallDetailSessionInfoPO;
import com.yiwise.dialogflow.entity.po.semantic.analysis.SemanticAnalysisConditionPO;
import com.yiwise.dialogflow.entity.po.semantic.analysis.SemanticAnalysisIntentPO;
import com.yiwise.dialogflow.entity.po.semantic.analysis.SemanticAnalysisTemplatePO;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.mapper.CallDetailSessionInfoPOMapper;
import com.yiwise.dialogflow.mapper.SessionDetailIntentInfoPOMapper;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.SemanticAnalysisService;
import com.yiwise.dialogflow.service.remote.RobotCallJobService;
import com.yiwise.dialogflow.service.remote.TenantService;
import com.yiwise.dialogflow.thread.SemanticAnalysisDetailExportThreadExecutorHelper;
import com.yiwise.dialogflow.utils.ParseUtil;
import javaslang.Tuple;
import javaslang.Tuple2;
import javaslang.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.item.ItemStreamException;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.yiwise.dialogflow.common.BatchConstant.Header.*;

/**
 * <AUTHOR>
 * @date 2023/6/28
 */
@Slf4j
@StepScope
@Component
public class SemanticAnalysisDetailExportTasklet implements Tasklet, StepExecutionListener {

    @Resource
    private SemanticAnalysisService semanticAnalysisService;

    @Resource
    private CallDetailSessionInfoPOMapper callDetailSessionInfoPOMapper;

    @Resource
    private SessionDetailIntentInfoPOMapper sessionDetailIntentInfoPOMapper;

    @Resource
    private BotService botService;

    @Resource
    private RobotCallJobService robotCallJobService;

    @Resource
    private TenantService tenantService;

    @Resource
    protected SpringBatchJobLogService springBatchJobLogService;

    @Value("#{jobParameters['EXPORT_FILE_PATH']}")
    protected String exportFilePath;

    @Value("#{jobParameters['TEMPLATE_PO']}")
    private String templatePOStr;

    private List<SemanticAnalysisConditionPO> conditionList;

    protected List<SheetInfoDTO> sheetInfoList = Lists.newArrayList();

    private Boolean exportDetail;

    private final Map<Pattern, String> patternMap = Maps.newHashMap();

    private final AtomicLongMap<String> emptyStatsMap = AtomicLongMap.create();

    private final AtomicLongMap<String> histStatMap = AtomicLongMap.create();

    protected WorkbookWriter workbookWriter;

    protected List<SheetWriter> sheetWriterList = Lists.newArrayList();

    private Long jobInstanceId;

    @Override
    public void beforeStep(StepExecution stepExecution) {
        SemanticAnalysisTemplatePO template = JsonUtils.string2Object(templatePOStr, SemanticAnalysisTemplatePO.class);
        Assert.notNull(template, "templatePO不能为空");
        conditionList = template.getConditionList();
        Assert.notEmpty(conditionList, "conditionPOList不能为空");

        exportDetail = template.getExportDetail();
        List<SemanticAnalysisIntentPO> intentList = template.getIntentList();
        if (CollectionUtils.isNotEmpty(intentList)) {
            intentList.forEach(intent -> {
                List<String> keywords = intent.getKeywords();
                keywords.forEach(keyword -> {
                    Pattern pattern = Pattern.compile(ParseUtil.regex(keyword));
                    patternMap.put(pattern, intent.getIntentName());
                });
            });
        }
        jobInstanceId = stepExecution.getJobExecution().getJobInstance().getId();
        String sheetInfoListStr = stepExecution.getJobParameters().getString("SHEET_INFO_LIST");
        sheetInfoList = JsonUtils.string2Object(sheetInfoListStr, new TypeReference<List<SheetInfoDTO>>() {});
        try {
            workbookWriter = new WorkbookWriter();
            workbookWriter.open(exportFilePath);
            sheetInfoList.forEach(sheetInfoDTO -> workbookWriter.createSheet(sheetInfoDTO.getSheetName(), sheetInfoDTO.getHeaderList()));
            sheetWriterList = workbookWriter.getSheetWriterList();
        } catch (IOException | InvalidFormatException e) {
            throw new ItemStreamException("打开输出文件" + exportFilePath + "报错", e);
        }
    }

    private String predict(String userInput) {
        if (MapUtils.isNotEmpty(patternMap)) {
            for (Map.Entry<Pattern, String> entry : patternMap.entrySet()) {
                Pattern k = entry.getKey();
                String v = entry.getValue();
                Matcher matcher = k.matcher(userInput);
                if (matcher.find()) {
                    return v;
                }
            }
        }
        return null;
    }

    @Override
    public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
        int index = 0 ;
        int conditionSize = conditionList.size();
        long lastCallDetailId = -1L;
        AtomicInteger count = new AtomicInteger();

        while (index < conditionSize) {

            PageHelper.startPage(1, 5000, false);
            SemanticAnalysisConditionPO curCondition = MyBeanUtils.copy(conditionList.get(index), SemanticAnalysisConditionPO.class);
            curCondition.setCallDetailIdGt(lastCallDetailId);
            List<CallDetailPO> records = semanticAnalysisService.selectRecords(curCondition);

            log.info("handle size={}", count.addAndGet(records.size()));

            if (CollectionUtils.isEmpty(records)) {
                index++;
                lastCallDetailId = -1L;
                continue;
            }
            lastCallDetailId = records.get(records.size() -1).getCallDetailId();

            List<SemanticAnalysisDetailDTO> dtoList = convert2DTO(records);
            if (CollectionUtils.isNotEmpty(dtoList)) {
                dtoList = dtoList.stream().sorted(Comparator.comparing(SemanticAnalysisDetailDTO::getCallDetailId)).collect(Collectors.toList());
                for (SemanticAnalysisDetailDTO item : dtoList) {
                    item.setTestIntent(predict(item.getText()));
                    if (StringUtils.isNotEmpty(item.getRealIntent())) {
                        histStatMap.getAndIncrement(item.getRealIntent());
                    } else if (StringUtils.isEmpty(item.getRealIntent()) && StringUtils.isNotEmpty(item.getTestIntent())) {
                        emptyStatsMap.getAndIncrement(item.getTestIntent());
                    }
                    if (BooleanUtils.isTrue(exportDetail) && sheetWriterList.size() > 2) {
                        DIALOGFLOW_NAME_CACHE.get(item.getDialogFlowId()).ifPresent(item::setDialogFlowName);
                        ROBOT_CALL_JOB_NAME_CACHE.get(item.getRobotCallJobId()).ifPresent(item::setRobotCallJobName);
                        TENANT_NAME_CACHE.get(item.getTenantId()).ifPresent(item::setTenantName);
                        sheetWriterList.get(2).writeRow(item.getRowModelMap());
                    }
                }
                contribution.incrementWriteCount(dtoList.size());
                springBatchJobLogService.updateSuccessAndFailureCounts(contribution.getWriteCount(), 0, null, jobInstanceId);
            }
        }

        writeBaseSheet();

        return RepeatStatus.FINISHED;
    }

    private void writeBaseSheet() {
        Map<String, Object> tempMap = new HashMap<>(2);
        emptyStatsMap.asMap().forEach((k,v) -> {
            tempMap.put(意图名称, k);
            tempMap.put(命中次数, v);
            sheetWriterList.get(0).writeRow(tempMap);
        });

        Map<String, Object> temp2Map = new HashMap<>(2);
        histStatMap.asMap().forEach((k,v) -> {
            temp2Map.put(历史命中意图, k);
            temp2Map.put(命中次数, v);
            sheetWriterList.get(1).writeRow(temp2Map);
        });
    }

    private List<SemanticAnalysisDetailDTO> convert2DTO(List<CallDetailPO> callDetailList1) {
        List<SemanticAnalysisDetailDTO> finalList = new CopyOnWriteArrayList<>();
        List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();
        for (List<CallDetailPO> callDetailList : Lists.partition(callDetailList1, 50)) {
            CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(
                    new MDCDecoratorRunnable(() -> {
                        List<Long> ids = callDetailList.stream().map(CallDetailPO::getCallDetailId).collect(Collectors.toList());
                        List<CallDetailSessionInfoPO> callDetailSessionInfoList = callDetailSessionInfoPOMapper.listByIds(ids)
                                .stream().filter(p -> StringUtils.isNotBlank(p.getSessionId()) && Objects.nonNull(p.getSeq())).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(callDetailSessionInfoList)) {
                            log.warn("callDetailSessionInfoList为空");
                            return;
                        }

                        List<Tuple2<String, Integer>> tuple2List = callDetailSessionInfoList.stream().map(p -> Tuple.of(p.getSessionId(), p.getSeq())).collect(Collectors.toList());
                        List<SessionDetailIntentInfoPO> sessionDetailIntentInfoList = sessionDetailIntentInfoPOMapper.listBySessionIdSeqList(tuple2List);
                        if (CollectionUtils.isEmpty(sessionDetailIntentInfoList)) {
                            log.warn("sessionDetailIntentInfoList为空");
                            return;
                        }

                        Map<Tuple2<String, Integer>, SessionDetailIntentInfoPO> intentInfoMap =
                                MyCollectionUtils.listToMap(sessionDetailIntentInfoList, p -> Tuple.of(p.getSessionId(), p.getSeq()));
                        Map<Long, CallDetailSessionInfoPO> sessionInfoMap = MyCollectionUtils.listToMap(callDetailSessionInfoList, CallDetailSessionInfoPO::getCallDetailId);
                        List<SemanticAnalysisDetailDTO> dtoList = MyBeanUtils.copyList(callDetailList, SemanticAnalysisDetailDTO.class);

                        for (SemanticAnalysisDetailDTO dto : dtoList) {
                            CallDetailSessionInfoPO sessionInfo = sessionInfoMap.get(dto.getCallDetailId());
                            if (Objects.isNull(sessionInfo)) {
                                log.warn("callDetailId={} sessionInfo为空", dto.getCallDetailId());
                                continue;
                            }
                            dto.setSeq(sessionInfo.getSeq());
                            dto.setSessionId(sessionInfo.getSessionId());
                            SessionDetailIntentInfoPO intentInfo = intentInfoMap.get(Tuple.of(dto.getSessionId(), dto.getSeq()));
                            if (Objects.isNull(intentInfo)) {
                                log.debug("callDetailId={} intentInfo为空", dto.getCallDetailId());
                                continue;
                            }
                            dto.setRealIntent(intentInfo.getIntentName());
                            finalList.add(dto);
                        }
                    }), SemanticAnalysisDetailExportThreadExecutorHelper.getExecutor()
            );
            completableFutureList.add(completableFuture);
        }
        CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[]{})).join();
        return finalList;
    }

    @Override
    public ExitStatus afterStep(StepExecution stepExecution) {
        if (workbookWriter != null) {
            try {
                workbookWriter.close();
            } catch (IOException e) {
                throw new ItemStreamException("关闭excelWriter报错", e);
            }
        }
        return stepExecution.getExitStatus();
    }

    private final LoadingCache<Long, Optional<String>> DIALOGFLOW_NAME_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build(CacheLoader.from(this::getDialogFlowName));

    private final LoadingCache<Long, Optional<String>> ROBOT_CALL_JOB_NAME_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build(CacheLoader.from(this::getRobotCallJobName));

    private final LoadingCache<Long, Optional<String>> TENANT_NAME_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build(CacheLoader.from(this::getTenantName));

    private Optional<String> getDialogFlowName(Long dialogFlowId) {
        return botService.getNameByDialogFlowId(dialogFlowId);
    }

    private Optional<String> getRobotCallJobName(Long robotCallJobId) {
        List<IdNamePair<Long, String>> idNamePairList = robotCallJobService.selectJobIdNamePairByIdList(Lists.newArrayList(robotCallJobId));
        if (CollectionUtils.isNotEmpty(idNamePairList)) {
            return Optional.ofNullable(idNamePairList.get(0).getName());
        }
        return Optional.empty();
    }

    private Optional<String> getTenantName(Long tenantId) {
        return Try.of(() -> Optional.ofNullable(tenantService.selectByKey(tenantId).getCompanyName()))
                .onFailure(ex -> log.error(ex.getMessage(), ex)).getOrElse(Optional.empty());
    }
}
