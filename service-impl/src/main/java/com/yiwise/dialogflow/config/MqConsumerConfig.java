package com.yiwise.dialogflow.config;

import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.bean.ConsumerBean;
import com.yiwise.dialogflow.helper.AliMessageQueueHelper;
import com.yiwise.dialogflow.listener.WebsocketMessageListener;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR>
 * @date 2023/3/22
 */
public class MqConsumerConfig {

    @Bean(name = "websocketMessageListener")
    public WebsocketMessageListener websocketMessageListener() {
        return new WebsocketMessageListener();
    }

    @<PERSON>(name = "websocketMessageConsumer")
    public ConsumerBean websocketMessageConsumer(@Qualifier("websocketMessageListener") MessageListener websocketMessageListener) {
        return AliMessageQueueHelper.getWebsocketConsumeBean(AliMessageQueueHelper.websocketTopic, "*", websocketMessageListener);
    }

}
