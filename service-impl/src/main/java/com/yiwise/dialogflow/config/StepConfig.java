package com.yiwise.dialogflow.config;

import com.yiwise.batch.config.AbstractStepConfig;
import com.yiwise.dialogflow.batch.tasklet.SemanticAnalysisDetailExportTasklet;
import org.springframework.batch.core.Step;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/11/29
 */
@Configuration
public class StepConfig extends AbstractStepConfig {

    @Bean
    public Step semanticAnalysisDetailExportStep(SemanticAnalysisDetailExportTasklet tasklet) {
        return stepBuilderFactory.get(springBatchJobTypeService.getByName("SEMANTIC_ANALYSIS_DETAIL_EXPORT_STEP").getJobCountStepName())
                .tasklet(tasklet)
                .build();
    }
}
