package com.yiwise.dialogflow.helper;

import com.yiwise.base.model.enums.CodeDescEnum;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.DialogFlowExtraRuleConditionNodePO;
import com.yiwise.dialogflow.entity.po.intent.IntentRuleConditionPO;
import com.yiwise.dialogflow.entity.po.intent.IntentRulePO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Deprecated
@Slf4j
public class IntentRuleRenderHelper {

    public static String convertRuleToString(IntentRulePO rule, DependentResourceBO resource) {
        return convertRuleToString(rule, resource.getKnowledgeIdNameMap(), resource.getNodeIdNameMap(),
                resource.getIntentLevelIdNameMap(), resource.getStepIdNameMap(),
                resource.getEntityId2NameMap(),
                resource.getIntentIdNameMap(),
                resource.getVariableIdNameMap());
    }

    public static String convertRuleToString(IntentRulePO rule,
                                             Map<String, String> knowledgeIdNameMap,
                                             Map<String, String> nodeIdNameMap,
                                             Map<Integer, String> intentLevelIdNameMap,
                                             Map<String, String> stepId2NameMap,
                                             Map<String, String> entityIdNameMap,
                                             Map<String, String> intentId2NameMap,
                                             Map<String, String> varId2NameMap) {
        SpecialIntentRuleTypeEnum specialIntentRuleType = rule.getSpecialIntentRuleType();
        if (specialIntentRuleType != null) {
            return specialIntentRuleType.getDesc();
        } else {
            return convertRuleToString(rule.getConditionList(), knowledgeIdNameMap, nodeIdNameMap, intentLevelIdNameMap, stepId2NameMap, entityIdNameMap, intentId2NameMap, varId2NameMap);
        }
    }

    private static String convertRuleToString(List<IntentRuleConditionPO> conditionList,
                                              Map<String, String> knowledgeIdNameMap,
                                              Map<String, String> nodeIdNameMap,
                                              Map<Integer, String> intentLevelIdNameMap,
                                              Map<String, String> stepIdNameMap,
                                              Map<String, String> entityIdNameMap,
                                              Map<String, String> intentId2NameMap,
                                              Map<String, String> varId2NameMap) {
        try {
            if (CollectionUtils.isEmpty(conditionList)) {
                return "";
            }
            StringBuilder sb = new StringBuilder();
            for (IntentRuleConditionPO condition : conditionList) {
                if (CollectionUtils.isNotEmpty(condition.getRobotKnowledgeIdList())) {
                    // 问答知识要重新渲染了, 多了后面的操作了
                    if (CollectionUtils.isNotEmpty(condition.getRobotKnowledgeIdList())) {
                        List<String> knowledgeList = new ArrayList<>();
                        for(String item : condition.getRobotKnowledgeIdList()) {
                            if (knowledgeIdNameMap.containsKey(item)) {
                                knowledgeList.add(knowledgeIdNameMap.get(item));
                            }
                        }
                        if (!knowledgeList.isEmpty()) {
                            sb.append(String.join("或", knowledgeList));
                        }
                    }
                }
                sb.append(String.format("【%s】", condition.getType().getDesc()));
                if (CollectionUtils.isNotEmpty(condition.getStepList())) {
                    List<String> stepNameList = new ArrayList<>();
                    for (String s : condition.getStepList()) {
                        stepNameList.add(stepIdNameMap.getOrDefault(s, s));
                    }
                    sb.append(String.join("或", stepNameList));
                }
                if (condition.getOperation() != null) {
                    sb.append(condition.getOperation().getDesc());
                }
                if (condition.getNumber() != null) {
                    if (DialogFlowConditionTypeEnum.ALGORITHM_PRIVATE_DOMAIN_INTENT_LEVEL.equals(condition.getType()) ||
                            DialogFlowConditionTypeEnum.ALGORITHM_PRIVATE_DOMAIN_INTENT_LEVEL_PASSIVE.equals(condition.getType())) {
                        AlgorithmPrivateIntentLevelEnum intentLevelEnum
                                = CodeDescEnum.getFromCodeOrNull(AlgorithmPrivateIntentLevelEnum.class, condition.getNumber());
                        sb.append(Objects.nonNull(intentLevelEnum) ? intentLevelEnum.getDesc() : null);
                    } else if (DialogFlowConditionTypeEnum.ALGORITHM_ACTIVITY_INTENT_LEVEL.equals(condition.getType())) {
                        AlgorithmActivityIntentLevelEnum intentLevelEnum
                                = CodeDescEnum.getFromCodeOrNull(AlgorithmActivityIntentLevelEnum.class, condition.getNumber());
                        sb.append(Objects.nonNull(intentLevelEnum) ? intentLevelEnum.getDesc() : null);
                    } else if (DialogFlowConditionTypeEnum.ALGORITHM_EDUCATION_ACTIVITY_INTENT_LEVEL.equals(condition.getType())) {
                        AlgorithmEducationActivityIntentLevelEnum intentLevelEnum
                                = CodeDescEnum.getFromCodeOrNull(AlgorithmEducationActivityIntentLevelEnum.class, condition.getNumber());
                        sb.append(Objects.nonNull(intentLevelEnum) ? intentLevelEnum.getDesc() : null);
                    } else {
                        sb.append(condition.getNumber());
                    }
                } else if (CollectionUtils.isNotEmpty(condition.getKeywords())) {
                    sb.append(condition.getKeywords());
                } else if (CollectionUtils.isNotEmpty(condition.getNodeList())) {
                    for (DialogFlowExtraRuleConditionNodePO node : condition.getNodeList()) {
                        String stepId = node.getStepIndex();
                        String nodeId = node.getNodeIndex();
                        String nodeName = nodeIdNameMap.get(nodeId);
                        if (StringUtils.isNotBlank(nodeName)) {
                            sb.append(nodeName);
                        }
                    }
                } else if (CollectionUtils.isNotEmpty(condition.getDialStatusList())) {
                    sb.append(condition.getDialStatusList().stream().map(DialStatusEnum::getDesc).reduce((d1, d2) -> d1 + "," + d2).orElse(""));
                } else if (CollectionUtils.isNotEmpty(condition.getIntentLevelIdList())) {
                    List<String> intentLevelList = new ArrayList<>();
                    condition.getIntentLevelIdList().forEach(item -> intentLevelList.add(String.format("%s", intentLevelIdNameMap.getOrDefault(item, null))));
                    String content = String.join("或", intentLevelList);
                    sb.append(content);
                } else if (CollectionUtils.isNotEmpty(condition.getEntityIdList())) {
                    List<String> entityNameList = new ArrayList<>();
                    condition.getEntityIdList().forEach(entityId -> {
                        String entityName = entityIdNameMap.getOrDefault(entityId, null);
                        if (StringUtils.isNotBlank(entityName)) {
                            entityNameList.add(entityName);
                        }
                    });
                    String content = String.join(", ", entityNameList);
                    sb.append(content);
                } else if (CollectionUtils.isNotEmpty(condition.getIntentIdList())) {
                    List<String> intentNameList = new ArrayList<>();
                    condition.getIntentIdList().forEach(intentId -> {
                        String intentName = intentId2NameMap.getOrDefault(intentId, null);
                        if (StringUtils.isNotBlank(intentName)) {
                            intentNameList.add(intentName);
                        }
                    });
                    String content = String.join(", ", intentNameList);
                    sb.append(content);
                } else if (CollectionUtils.isNotEmpty(condition.getVariableIdList())) {
                    List<String> varNameList = new ArrayList<>();
                    condition.getVariableIdList().forEach(varId -> {
                        String varName = varId2NameMap.getOrDefault(varId, null);
                        if (StringUtils.isNotBlank(varName)) {
                            varNameList.add(varName);
                        }
                    });
                    String content = String.join(", ", varNameList);
                    sb.append(content);
                }
                sb.append(";");
            }
            if (sb.length() > 0) {
                sb.deleteCharAt(sb.length() - 1);
            }
            return sb.toString();
        } catch (Exception e) {
            log.error("[LogHub_Warn]条件转换为字符串错误",  e);
            return "";
        }
    }
}
