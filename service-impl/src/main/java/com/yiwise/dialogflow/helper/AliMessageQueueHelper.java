package com.yiwise.dialogflow.helper;

import com.aliyun.openservices.ons.api.*;
import com.aliyun.openservices.ons.api.bean.ConsumerBean;
import com.aliyun.openservices.ons.api.bean.Subscription;
import com.yiwise.base.common.utils.PropertyLoaderUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.websocket.BasicMsg;
import com.yiwise.base.model.websocket.UserIdPrincipal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;

public class AliMessageQueueHelper {

    private static final Logger logger = LoggerFactory.getLogger(AliMessageQueueHelper.class);

    private static String accessKey;
    private static String secretKey;
    private static String namesrvAddr;
    private static Producer commonProducer;
    public static String websocketTopic;
    private static String websocketConsumerGroupId;

    static {
        accessKey = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.accessKey");
        secretKey = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.secretKey");
        namesrvAddr = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.namesrvAddr");
        // websocket消息发送的topic和消费者组id
        websocketTopic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.websocket.topic");
        websocketConsumerGroupId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.websocket.groupId");

        Properties producerProperties = getProperties();
        String producerId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.producerId");
        producerProperties.put(PropertyKeyConst.GROUP_ID, producerId);
        commonProducer = ONSFactory.createProducer(producerProperties);
        commonProducer.start();
        logger.info("==== aliMessageQueueHelper execute ====");
    }

    private static Properties getProperties() {
        Properties properties = new Properties();
        properties.put(PropertyKeyConst.AccessKey, accessKey);
        properties.put(PropertyKeyConst.SecretKey, secretKey);
        properties.put(PropertyKeyConst.NAMESRV_ADDR, namesrvAddr);
        properties.put(PropertyKeyConst.ConsumeTimeout, "3000");
        return properties;
    }

    public static <T> void sendWebSocketMessage(String destination, BasicMsg<T> msg, String moduleTag) {
        sendWebSocketMessage(null, destination, msg, moduleTag);
    }

    public static <T> void sendWebSocketMessage(UserIdPrincipal userIdPrincipal, String destination, BasicMsg<T> msg, String moduleTag) {
        Message message = new Message(websocketTopic, moduleTag, JsonUtils.object2Bytes(msg));
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        properties.put("destination", destination);
        if (Objects.nonNull(userIdPrincipal)) {
            properties.put("user", userIdPrincipal.getName());
        }
        message.setUserProperties(properties);
        sendMessage(message, moduleTag, msg);
    }

    public static ConsumerBean getWebsocketConsumeBean(String topic, String tag, MessageListener messageListener) {
        ConsumerBean consumerBean = getMessageQueueConsumerBean(websocketConsumerGroupId, topic, tag, messageListener, PropertyValueConst.BROADCASTING);
        Properties properties = consumerBean.getProperties();
        properties.put(PropertyKeyConst.ConsumeThreadNums, 30);
        properties.put(PropertyKeyConst.ConsumeMessageBatchMaxSize, 10);

        consumerBean.start();
        return consumerBean;
    }

    /**
     * 这里是用于装配bean的，有两种bean要装
     *
     * @param consumerId      消费者ID
     * @param topic           topic
     * @param tag             tag
     * @param messageListener messageListener处理监听到的消息
     * @param messageModel    发送方式群发或者集群
     * @return
     */
    private static ConsumerBean getMessageQueueConsumerBean(String consumerId, String topic, String tag,
                                                            MessageListener messageListener, String messageModel) {
        ConsumerBean consumerBean = new ConsumerBean();

        Properties properties = getProperties();
        properties.put(PropertyKeyConst.GROUP_ID, consumerId);
        properties.put(PropertyKeyConst.MessageModel, messageModel);
        return getConsumerBean(topic, tag, messageListener, consumerBean, properties);
    }

    private static ConsumerBean getConsumerBean(String topic, String tag, MessageListener messageListener, ConsumerBean consumerBean, Properties properties) {
        consumerBean.setProperties(properties);

        Map<Subscription, MessageListener> subscriptionTable = new HashMap<>();

        Subscription subscription = new Subscription();
        subscription.setTopic(topic);
        subscription.setExpression(tag);

        subscriptionTable.put(subscription, messageListener);

        consumerBean.setSubscriptionTable(subscriptionTable);
        return consumerBean;
    }

    /**
     * 发送MQ
     */
    private static void sendMessage(Message message, String moduleTag, Object msg) {
        commonProducer.sendAsync(message, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                logger.debug("[AliMessageQueue]阿里云发送回调消息成功，发送消息为=[{}], Id={}, tag={}", msg.toString(), sendResult.getMessageId(), moduleTag);
            }

            @Override
            public void onException(OnExceptionContext onExceptionContext) {
                logger.error("[LogHub_Warn] [AliMessageQueue] 阿里云发送回调消息成失败，请检查，消息内容=[{}], messageId={}, tag={}, topic={}, errormsg={}", msg.toString(), onExceptionContext.getMessageId(), moduleTag, onExceptionContext.getTopic(), onExceptionContext.getException().getMessage(), onExceptionContext.getException().getCause());
            }
        });
    }
}
