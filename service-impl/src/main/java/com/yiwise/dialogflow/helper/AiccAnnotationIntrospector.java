package com.yiwise.dialogflow.helper;

import com.fasterxml.jackson.databind.introspect.Annotated;
import com.yiwise.dialogflow.annotations.AddOssPrefix;
import com.yiwise.middleware.objectstorage.common.CustomAnnotationIntrospector;

public class AiccAnnotationIntrospector extends CustomAnnotationIntrospector {
    private static final long serialVersionUID = 1L;

    @Override
    public Object findSerializer(Annotated am) {
        Object serializer = super.findSerializer(am);
        if (serializer != null) {
            return serializer;
        }

        AddOssPrefix addOssPrefix = am.getAnnotation(AddOssPrefix.class);
        if (addOssPrefix != null) {
            return AddOssPrefixSerializer.class;
        }

        return null;
    }

    @Override
    public Object findDeserializer(Annotated am) {
        return null;
    }
}
