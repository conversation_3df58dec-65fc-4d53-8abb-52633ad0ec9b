package com.yiwise.dialogflow.repository.magic;

import org.springframework.data.repository.CrudRepository;

import com.yiwise.dialogflow.entity.po.magic.MagicTemplateVarGenerateRecordDetailPO;

public interface MagicTemplateVarGenerateRecordDetailRepository extends CrudRepository<MagicTemplateVarGenerateRecordDetailPO, String> {

    /**
     * 根据记录ID查询详情列表
     */
    Iterable<MagicTemplateVarGenerateRecordDetailPO> findByRecordId(String recordId);
} 