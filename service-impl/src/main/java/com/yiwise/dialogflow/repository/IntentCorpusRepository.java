package com.yiwise.dialogflow.repository;

import com.yiwise.dialogflow.entity.po.intent.IntentCorpusPO;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/15
 */
@Repository
public interface IntentCorpusRepository extends ElasticsearchRepository<IntentCorpusPO, String> {

    IntentCorpusPO findByIntentId(String intentId);

    List<IntentCorpusPO> findByIntentIdIn(Collection<String> intentIdList);

    List<IntentCorpusPO> findByBotId(Long botId);

    void deleteByIntentIdIn(Collection<String> intentIdList);

}
