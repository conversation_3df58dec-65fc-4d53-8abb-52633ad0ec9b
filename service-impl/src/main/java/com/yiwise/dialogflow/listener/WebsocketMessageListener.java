package com.yiwise.dialogflow.listener;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.thread.decorator.NewMDCDecoratorCallable;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.websocket.BasicMsg;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.simp.SimpMessagingTemplate;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/3/22
 */
public class WebsocketMessageListener implements MessageListener {

    private static final Logger logger = LoggerFactory.getLogger(WebsocketMessageListener.class);
    private final SimpMessagingTemplate simpMessagingTemplate = AppContextUtils.getBean(SimpMessagingTemplate.class);

    @Override
    public Action consume(Message message, ConsumeContext context) {
        Object actionResult;
        try {
            String mdcLogId = message.getUserProperties("MDC_LOG_ID");
            actionResult = new NewMDCDecoratorCallable(mdcLogId, () -> {
                logger.debug("[AliMessageQueue]获取阿里云消息队列，内容为={}, 消息部分为={}", message, new String(message.getBody(), StandardCharsets.UTF_8));
                String destination = message.getUserProperties("destination");
                String user = message.getUserProperties("user");
                if (StringUtils.isNotBlank(user)) {
                    BasicMsg<?> basicMsg = JsonUtils.bytes2Object(message.getBody(), BasicMsg.class);
                    simpMessagingTemplate.convertAndSendToUser(user, destination, basicMsg);
                    return Action.CommitMessage;
                } else {
                    BasicMsg<?> basicMsg = JsonUtils.bytes2Object(message.getBody(), BasicMsg.class);
                    simpMessagingTemplate.convertAndSend(destination, basicMsg);
                    return Action.CommitMessage;
                }
            }).call();
        } catch (Exception e) {
            String msg = "[LogHub_Warn]处理消息失败, 原始消息=" + new String(message.getBody(), StandardCharsets.UTF_8);
            logger.error(msg, e);
            // ws消息没必要重新消费
            return Action.CommitMessage;
        }
        return (Action) actionResult;
    }
}
