<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 4.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiwise.dialogflow.mapper.SessionDetailIntentInfoPOMapper">
    <resultMap id="BaseResultMap" type="com.yiwise.dialogflow.entity.po.SessionDetailIntentInfoPO">
        <id column="session_detail_intent_info_id" property="sessionDetailIntentInfoId" jdbcType="BIGINT"/>
        <result column="bot_id" property="botId" jdbcType="BIGINT"/>
        <result column="session_id" property="sessionId" jdbcType="VARCHAR"/>
        <result column="seq" property="seq" jdbcType="BIGINT"/>
        <result column="user_input" property="userInput" jdbcType="VARCHAR"/>
        <result column="intent_id" property="intentId" jdbcType="VARCHAR"/>
        <result column="intent_name" property="intentName" jdbcType="VARCHAR"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="BASE_COLUMN_LIST">
        session_detail_intent_info_id,bot_id,session_id,seq,user_input,intent_id,intent_name,create_time,update_time
    </sql>

    <insert id="batchInsert">
        insert into `session_detail_intent_info` (`bot_id`, `session_id`, `seq`, `user_input`, `intent_id`,`intent_name`)
        VALUES
        <foreach collection="list" index="index" separator="," item="item">
            (#{item.botId},#{item.sessionId},#{item.seq},#{item.userInput},#{item.intentId},#{item.intentName})
        </foreach>
    </insert>

    <select id="listBySessionIdSeqList" resultMap="BaseResultMap">
        SELECT
        <include refid="BASE_COLUMN_LIST"/>
        FROM session_detail_intent_info
        <where>
            <foreach collection="list" open="" close="" separator="or" item="t">
                (
                    session_id = #{t._1} and seq=#{t._2}
                )
            </foreach>
        </where>
    </select>
</mapper>