<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 4.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiwise.dialogflow.mapper.AsrVocabDetailPOMapper">
    <resultMap id="BaseResultMap" type="com.yiwise.dialogflow.entity.po.asrmodel.AsrVocabDetailPO">
        <id column="asr_vocab_id" property="asrVocabId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"
                typeHandler="com.yiwise.middleware.mysql.handler.StringListToJsonTypeHandler"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
    </resultMap>

    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yiwise.dialogflow.entity.vo.asrmodel.AsrVocabDetailVO">
    </resultMap>

    <sql id="condition">
        <where>
            <if test="query.name != null and query.name != ''">
                and `name` like concat('%', #{query.name}, '%')
            </if>
            <if test="query.status != null">
                and status = #{query.status}
            </if>
            <if test="query.asrVocabIdList != null and query.asrVocabIdList.size() > 0">
                and asr_vocab_id in
                <foreach collection="query.asrVocabIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by update_time desc
    </sql>


    <select id="listByCondition" resultMap="VOResultMap">
        select *
        from asr_vocab_detail
        <include refid="condition"/>
    </select>

    <select id="getByName" resultMap="BaseResultMap">
        select *
        from asr_vocab_detail
        where name = #{name}
    </select>

</mapper>