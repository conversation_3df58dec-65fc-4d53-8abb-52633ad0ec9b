<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 4.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiwise.dialogflow.mapper.AsrSelfLearningProviderRelationPOMapper">
    <resultMap id="BaseResultMap" type="com.yiwise.dialogflow.entity.po.asrmodel.AsrSelfLearningProviderRelationPO">
        <id column="asr_self_learning_provider_relation_id" property="asrSelfLearningProviderRelationId"
            jdbcType="BIGINT"/>
        <result column="asr_self_learning_id" property="asrSelfLearningId" jdbcType="BIGINT"/>
        <result column="provider" property="provider" jdbcType="TINYINT"
                typeHandler="com.yiwise.middleware.mysql.handler.CodeDescEnumHandler"/>
        <result column="provider_model_id" property="providerModelId" jdbcType="VARCHAR"/>
        <result column="train_status" property="trainStatus" jdbcType="INTEGER"/>
        <result column="data_id" property="dataId" jdbcType="VARCHAR"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="getByAsrSelfLearningId" resultMap="BaseResultMap">
        select *
        from asr_self_learning_provider_relation
        where asr_self_learning_id = #{asrSelfLearningId}
    </select>

    <select id="getByAsrSelfLearningIdAndProvider" resultMap="BaseResultMap">
        select *
        from asr_self_learning_provider_relation
        where asr_self_learning_id = #{asrSelfLearningId}
          and provider = #{provider,typeHandler=com.yiwise.middleware.mysql.handler.CodeDescEnumHandler}
    </select>

</mapper>