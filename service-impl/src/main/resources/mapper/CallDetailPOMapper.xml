<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiwise.dialogflow.mapper.CallDetailPOMapper">
    <resultMap id="BaseResultMap" type="com.yiwise.dialogflow.entity.po.callout.CallDetailPO">
        <id column="call_detail_id" jdbcType="BIGINT" property="callDetailId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="call_record_id" jdbcType="BIGINT" property="callRecordId"/>
        <result column="text" jdbcType="VARCHAR" property="text"/>
        <result column="debug_log" jdbcType="VARCHAR" property="debugLog"/>
        <result column="start_offset" jdbcType="INTEGER" property="startOffset"/>
        <result column="end_offset" jdbcType="INTEGER" property="endOffset"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="robot_knowledge_oid" jdbcType="VARCHAR" property="robotKnowledgeOid"/>
        <result column="dialog_flow_step_oid" jdbcType="VARCHAR" property="dialogFlowStepOid"/>
        <result column="intent_branch_oid" jdbcType="VARCHAR" property="intentBranchOid"/>
        <result column="judge_branch_oid" jdbcType="VARCHAR" property="judgeBranchOid"/>
        <result column="emotion" jdbcType="TINYINT" property="emotion"/>
        <result column="feedback_status" jdbcType="TINYINT" property="feedbackStatus"/>
        <result column="correction_status" jdbcType="TINYINT" property="correctionStatus"/>
        <result column="correction_text" jdbcType="VARCHAR" property="correctionText"/>
        <result column="robot_call_job_id" jdbcType="BIGINT" property="robotCallJobId"/>
        <result column="dialog_flow_id" jdbcType="BIGINT" property="dialogFlowId"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="branch_confidence" jdbcType="FLOAT" property="branchConfidence"/>
    </resultMap>

    <sql id="Base_Column_List">
        call_detail_id, tenant_id, call_record_id, text, start_offset, end_offset,
        type, debug_log, robot_knowledge_oid, dialog_flow_step_oid, intent_branch_oid, judge_branch_oid, emotion,
        correction_text, feedback_status, correction_status,robot_call_job_id,dialog_flow_id,start_time
    </sql>
    <sql id="intent_call_detail_sql">
        call_record_id,call_detail_id,`text`,tenant_id,robot_call_job_id,dialog_flow_id,start_time, branch_confidence
    </sql>

    <select id="selectRecords" resultMap="BaseResultMap">
        select
        <include refid="intent_call_detail_sql"/>
        from call_detail
        <where>
            <if test="conditionPO.tenantIdList != null and conditionPO.tenantIdList.size() > 0">
                and tenant_id in
                <foreach collection="conditionPO.tenantIdList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="conditionPO.robotCallJobIdList != null and conditionPO.robotCallJobIdList.size() > 0">
                and robot_call_job_id in
                <foreach collection="conditionPO.robotCallJobIdList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="conditionPO.dialogFlowIdList != null and conditionPO.dialogFlowIdList.size() > 0">
                and dialog_flow_id in
                <foreach collection="conditionPO.dialogFlowIdList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="conditionPO.startDate != null">
                and start_time <![CDATA[ >= ]]> #{conditionPO.startDate}
            </if>
            <if test="conditionPO.endDate != null">
                and start_time <![CDATA[ <= ]]> #{conditionPO.endDate}
            </if>
            <if test="conditionPO.callDetailIdGt != null">
                and call_detail_id <![CDATA[ > ]]> #{conditionPO.callDetailIdGt}
            </if>
            and type = 1
        </where>
        ORDER BY call_detail_id ASC
    </select>

    <select id="countRecords" resultType="java.lang.Integer">
        select count(0)
        from call_detail
        <where>
            <if test="conditionPO.tenantIdList != null and conditionPO.tenantIdList.size() > 0">
                and tenant_id in
                <foreach collection="conditionPO.tenantIdList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="conditionPO.robotCallJobIdList != null and conditionPO.robotCallJobIdList.size() > 0">
                and robot_call_job_id in
                <foreach collection="conditionPO.robotCallJobIdList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="conditionPO.dialogFlowIdList != null and conditionPO.dialogFlowIdList.size() > 0">
                and dialog_flow_id in
                <foreach collection="conditionPO.dialogFlowIdList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="conditionPO.startDate != null">
                and start_time <![CDATA[ >= ]]> #{conditionPO.startDate}
            </if>
            <if test="conditionPO.endDate != null">
                and start_time <![CDATA[ <= ]]> #{conditionPO.endDate}
            </if>
            and type = 1
        </where>
    </select>
</mapper>