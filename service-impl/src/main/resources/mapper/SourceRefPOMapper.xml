<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 4.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiwise.dialogflow.mapper.SourceRefPOMapper">
    <resultMap id="BaseResultMap" type="com.yiwise.dialogflow.entity.po.SourceRefPO">
        <id column="source_ref_id" property="sourceRefId" jdbcType="BIGINT"/>
        <result column="source_id" property="sourceId" jdbcType="VARCHAR"/>
        <result column="source_type" property="sourceType" jdbcType="TINYINT"/>
        <result column="bot_id" property="botId" jdbcType="BIGINT"/>
        <result column="ref_id" property="refId" jdbcType="VARCHAR"/>
        <result column="ref_type" property="refType" jdbcType="TINYINT"/>
        <result column="parent_ref_type" property="parentRefType" jdbcType="TINYINT"/>
        <result column="parent_ref_id" property="parentRefId" jdbcType="VARCHAR"/>
        <result column="parent_ref_label" property="parentRefLabel" jdbcType="VARCHAR"/>
        <result column="ref_label" property="refLabel" jdbcType="VARCHAR"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Column_List">
        source_ref_id
        ,bot_id,source_type,source_id,parent_ref_id,parent_ref_type,ref_id,ref_type,ref_label,parent_ref_label,
        create_user_id,update_user_id,create_time,update_time
    </sql>

    <insert id="batchAddSourceRef">
        REPLACE
        INTO `source_ref` (
        `bot_id`,
        `source_type`,
        `source_id`,
        `parent_ref_id`,
        `parent_ref_type`,
        `ref_id`,
        `ref_type`,
        `ref_label`,
        `parent_ref_label`,
        `create_user_id`,
        `update_user_id`
        )
        VALUES
        <foreach collection="p.sourceIdSet" index="index" separator="," item="item">
            (
            #{p.botId,jdbcType=BIGINT},
            #{sourceType,jdbcType=TINYINT},
            #{item},
            #{p.parentRefId,jdbcType=VARCHAR},
            <if test="parentRefType != null">
                #{parentRefType},
            </if>
            <if test="parentRefType == null">
                null,
            </if>
            #{p.refId,jdbcType=VARCHAR},
            #{refType,jdbcType=TINYINT},
            #{p.refLabel,jdbcType=VARCHAR},
            #{p.parentRefLabel,jdbcType=VARCHAR},
            0,
            0
            )
        </foreach>
    </insert>
    <insert id="batchAddSourceRefList" parameterType="java.util.List">
        REPLACE
        INTO `source_ref` (
        `bot_id`,
        `source_type`,
        `source_id`,
        `parent_ref_id`,
        `parent_ref_type`,
        `ref_id`,
        `ref_type`,
        `ref_label`,
        `parent_ref_label`,
        `create_user_id`,
        `update_user_id`
        )
        VALUES
        <foreach collection="list" separator="," item="p">
            (
            #{p.botId},
            #{p.sourceType},
            #{p.sourceId},
            #{p.parentRefId},
            #{p.parentRefType},
            #{p.refId},
            #{p.refType},
            #{p.refLabel},
            #{p.parentRefLabel},
            0,
            0
            )
        </foreach>
    </insert>
    <update id="updateRefLabelByRefId">
        update source_ref
        set ref_label = #{refLabel}
        where bot_id = #{botId}
        and ref_id = #{refId}
    </update>
    <delete id="deleteSourceByParentRefId">
        delete from source_ref
        <where>
            and bot_id = #{botId}
            and parent_ref_id = #{parentRefId}
            and parent_ref_type = #{parentRefType}
        </where>
    </delete>
    <delete id="deleteSourceByParentRefIdList">
        delete from source_ref
        <where>
            and bot_id = #{botId}
            and parent_ref_type = #{parentRefType}
            and parent_ref_id in
            <foreach collection="parentRefIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </delete>
    <delete id="deleteSourceByRefId">
        delete from source_ref
        <where>
            and bot_id = #{botId}
            and ref_id = #{refId}
        </where>
    </delete>
    <delete id="deleteSourceByRefIds">
        delete from source_ref
        <where>
            and ref_id in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and bot_id = #{botId}
        </where>
    </delete>
    <delete id="deleteByRefIdsAndSourceType">
        delete from source_ref
        <where>
            and source_type = #{sourceType}
            and ref_id in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and bot_id = #{botId}
        </where>
    </delete>
    <delete id="deleteSourceByRefIdList">
        delete from source_ref
        <where>
            and bot_id = #{botId}
            and ref_id in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and ref_type = #{refType}
        </where>
    </delete>
    <delete id="deleteSourceByRefIdAndSourceTypeList">
        delete from source_ref
        <where>
            and bot_id = #{botId}
            and ref_id in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and ref_type = #{refType}
            and source_type = #{sourceType}
        </where>
    </delete>

    <delete id="deleteBySourceIdList">
        delete from source_ref
        <where>
            and bot_id = #{botId}
            and source_id in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and ref_type = #{refType}
        </where>
    </delete>
    <delete id="deleteByRefType">
        delete from source_ref
        <where>
            and bot_id = #{botId}
            and ref_type = #{refType}
        </where>
    </delete>

    <select id="getIntentByRefIdList" resultMap="BaseResultMap">
        select
        <include refid="Column_List"/>
        from source_ref
        <where>
            and bot_id = #{botId}
            and ref_id in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and ref_type = #{refType}
        </where>
    </select>
    <select id="getBySourceIdList" resultMap="BaseResultMap">
        select
        <include refid="Column_List"/>
        from source_ref
        <where>
            and bot_id = #{botId}
            and source_id in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and source_type = #{sourceType}
        </where>
    </select>
    <select id="getAllByBotId" resultMap="BaseResultMap">
        select
        <include refid="Column_List"></include>
        from source_ref
        <where>
            and bot_id = #{botId}
        </where>
    </select>
    <select id="getListBySourceTypeAndRefType" resultMap="BaseResultMap">
        select
        <include refid="Column_List"></include>
        from source_ref
        <where>
            and bot_id= #{botId}
            and source_type =#{sourceType}
            <if test="refTypeCode != null">
                and ref_type = #{refTypeCode}
            </if>
        </where>
    </select>
    <select id="getListByBotIdListAndSourceType" resultMap="BaseResultMap">
        select
        <include refid="Column_List"></include>
        from source_ref
        <where>
            and bot_id in
            <foreach collection="botIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and source_type =#{sourceType}
        </where>
    </select>

</mapper>