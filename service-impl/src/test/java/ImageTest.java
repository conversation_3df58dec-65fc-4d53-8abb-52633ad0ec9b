import net.coobird.thumbnailator.Thumbnails;
import net.coobird.thumbnailator.geometry.Positions;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.net.URL;

public class ImageTest {

    public static void main(String[] args) throws IOException {

        Font[]fontArr = GraphicsEnvironment.getLocalGraphicsEnvironment().getAllFonts();

        for (Font font : fontArr) {
            System.out.println(String.format("font: family:%s, name:%s", font.getFamily(), font.getName()));
        }

        String targetImgPath = "/Users/<USER>/Downloads/1234.jpg";
        String qrCodePath = "/Users/<USER>/Downloads/getQrCodeImage.jpeg";

        BufferedImage originalImage = Thumbnails.of(new URL("https://ai-call-platform.oss-cn-hangzhou.aliyuncs.com/dialog/common_template.jpeg"))
                .size(1080, 1920)
                .asBufferedImage();

        Graphics2D g2d = (Graphics2D) originalImage.getGraphics();
        g2d.setColor(Color.BLACK);

        String fontName = "微软雅黑";

        g2d.setFont(new Font(fontName, Font.PLAIN, 80));
        g2d.drawString("体验", 80, 260);

        g2d.setFont(new Font(fontName, Font.BOLD, 128));
        g2d.drawString("一知话术", 80, 400);

        g2d.setFont(new Font(fontName, Font.PLAIN, 36));
        g2d.drawString("czy-tts音色测试", 80, 460);

        g2d.setFont(new Font(fontName, Font.PLAIN, 36));
        g2d.drawString("微信扫描二维码开始体验", 600, 630);

        g2d.setFont(new Font(fontName, Font.PLAIN, 36));
        g2d.drawString("单个号码呼频限制：10 次", 580, 1380);

        g2d.setFont(new Font(fontName, Font.PLAIN, 36));
        g2d.drawString("失效时间：2023-12-09 15:39:01", 440, 1440);

        g2d.drawImage(ImageIO.read(new File(qrCodePath)), 440, 705, 550, 550, null);

        g2d.dispose();

        ImageIO.write(originalImage, "jpg", new File(targetImgPath));

    }
}
