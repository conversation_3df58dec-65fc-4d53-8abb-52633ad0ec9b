package com.yiwise.dialogflow.aop;

import java.lang.annotation.*;

/**
 * 租户隔离
 *
 * <AUTHOR>
 * @date 2023/8/7
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Repeatable(TenantIsolations.class)
public @interface TenantIsolation {

    /**
     * SpEL，用户获取botId或者botIdList
     */
    String value() default "";

    /**
     * SpEL，满足条件时才会进行租户的校验
     */
    String condition() default "true";
}
