package com.yiwise.dialogflow.config;

import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.*;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.transaction.annotation.EnableTransactionManagement;


@Configuration
@Order(value = Ordered.HIGHEST_PRECEDENCE)
@ComponentScan(basePackages = {
        "com.yiwise.dialogflow", "com.yiwise.base", "com.yiwise.batch"
})
@EnableTransactionManagement
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class ContextConfig extends CommonContextConfig {

    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> tomcatCustomizer() {
        return factory -> {
            factory.addConnectorCustomizers(connector -> {
                connector.setAsyncTimeout(120000); // 设置异步超时为120秒
            });
        };
    }

}