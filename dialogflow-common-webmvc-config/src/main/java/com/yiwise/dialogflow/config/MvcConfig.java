package com.yiwise.dialogflow.config;

import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.helper.EnvironmentConstants;
import com.yiwise.base.monitoring.config.CommonMvcWebConfig;
import com.yiwise.dialogflow.interceptor.LoginInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.web.servlet.DispatcherServletPath;
import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.DispatcherServlet;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;


public class MvcConfig extends CommonMvcWebConfig {

    private static final Logger logger = LoggerFactory.getLogger(MvcConfig.class);

//    @Override
//    public void addResourceHandlers(ResourceHandlerRegistry registry) {
//        logger.info("==== addResourceHandlers execute ====");
//        if (ApplicationConstant.CURR_ENV.isProd()) {
//            registry.addResourceHandler("/**")
//                    .addResourceLocations("classpath:/static/html/health.html");
//        } else {
//            registry.addResourceHandler("/**")
//                    .addResourceLocations("classpath:/static/html/");
//            registry.addResourceHandler("/static/**")
//                    .addResourceLocations("classpath:/static/");
//        }
//    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        logger.info("==== addInterceptors init ====");

        // 这个bean不能通过注入创建，这个bean通过注入创建的话会出现这个bean比aop配置还早的情况，会导致不会生成cglib代理对象，引发aop不生效、事物不生效等问题
        LoginInterceptor loginInterceptor = AppContextUtils.getBean(LoginInterceptor.class);

        // 多个拦截器组成一个拦截器链, addPathPatterns 用于添加拦截规则, excludePathPatterns 用户排除拦截
        InterceptorRegistration interceptorRegistration = registry.addInterceptor(loginInterceptor);
        if (!EnvironmentConstants.CURR_ENV.isProd()) {
            interceptorRegistration.excludePathPatterns("/static/**")
                    .excludePathPatterns("/**.html");
        }
        interceptorRegistration.excludePathPatterns("/apiHealthCheck/**");
        interceptorRegistration.addPathPatterns("/**").excludePathPatterns("/apiBot/v3/chatService/**");
    }

    @Bean
    public DispatcherServletPath dispatcherServletPath(DispatcherServlet dispatcherServlet) {
        return () -> "/";
    }
}
