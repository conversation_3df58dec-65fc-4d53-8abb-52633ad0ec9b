package com.yiwise.dialogflow.interceptor;

import com.yiwise.base.common.helper.EnvironmentConstants;
import com.yiwise.base.common.utils.NetUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.annotation.auth.InnerOnly;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.dialogflow.common.SystemHolder;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.service.remote.IPWhiteListService;
import com.yiwise.dialogflow.service.remote.LoginToCrmService;
import com.yiwise.dialogflow.service.remote.TenantService;
import com.yiwise.dialogflow.service.remote.UserService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import com.yiwise.dialogflow.utils.TokenUtils;
import com.yiwise.middleware.redis.service.RedisOpsService;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;
import sun.net.util.IPAddressUtil;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.regex.Pattern;

import static com.yiwise.base.common.helper.ServerInfoConstants.API_SUCCESS;

/**
 * 基础登录拦截器
 *
 * <AUTHOR> yangdehong
 * @date : 2018/11/30 17:09
 */
@SuppressWarnings("NullableProblems")
public class LoginInterceptor implements HandlerInterceptor {


    private final Logger logger = LoggerFactory.getLogger(LoginInterceptor.class);

    public static final String DEFAULT_CONTENT_TYPE = "application/json;charset=UTF-8";

    private static final Pattern SYSTEM_PATTERN = Pattern.compile("\"systemType\":\"(.*)?\"");

    @Resource
    private RedisOpsService redisOpsService;
    @Resource
    private UserService userService;
    @Resource
    private IPWhiteListService ipWhiteListService;
    @Resource
    private LoginToCrmService loginToCrmService;
    @Resource
    private TenantService tenantService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response,
                             Object handler) throws Exception {
        String systemType = request.getParameter("systemType");
        if (StringUtils.isEmpty(systemType)) {

            String host = request.getHeader("Host");
            // 兼容开发自测
            if (host.contains("ope") || host.contains("fi-ope") || host.startsWith("localhost") || IPAddressUtil.isIPv4LiteralAddress(StringUtils.substringBefore(host,":"))) {
                systemType = "OPE";
            } else if (host.contains("miniapp") || host.contains("fi-miniapp")){
                systemType = "MINIAPP";
            } else {
                systemType = "AICC";
            }
            logger.info("host={}, systemType={}", host, systemType);
//            String bodyString = HttpBodyHelper.getBodyString(request);
//            Matcher matcher = SYSTEM_PATTERN.matcher(bodyString);
//            if (matcher.find()) {
//                systemType = matcher.group(1);
//            } else {
//                systemType = "AICC";
//            }
        } else {
            if (!SystemEnum.OPE.name().equals(systemType)
                    && !SystemEnum.BOSS.name().equals(systemType)
                    && !SystemEnum.MINIAPP.name().equals(systemType)
                    && !SystemEnum.MINIAPP_CRM.name().equals(systemType)) {
                systemType = "AICC";
            }
        }
        SystemEnum systemEnum = SystemEnum.valueOf(systemType);
        SystemHolder.setSystem(systemEnum);
        return doPreHandle(request, response, handler, systemEnum);
    }

    public boolean doPreHandle(HttpServletRequest request, HttpServletResponse response,
                               Object handler, SystemEnum system) throws Exception {
        boolean isAuthorization = false;
        UserPO user = null;

        // token是否配对
        String userToken = TokenUtils.checkAndReturnToken(request);
        logger.info("userToken={}", userToken);
        if (StringUtils.isNotBlank(userToken)) {
            SecurityUtils.flushUserInfo(system, userToken, redisOpsService);
            user = SecurityUtils.getUserInfo();
            isAuthorization = (user != null && user.getUserId() != null && user.getTenantId() != null);
        }

        String ip = NetUtils.getRemoteIpAddress(request);
        logger.info("ip={}", ip);
        // 判断是否已经禁用
        if (Objects.nonNull(user)) {
            logger.debug("当前的system={}", system);
            logger.info("userId:{}, userName:{}, ip:{}, token:{}", user.getUserId(), user.getName(), ip, userToken);
            //ope登录aicc的用户无需校验
            UserPO aiccUser;
            if (Objects.equals(SystemEnum.AICC, system) || Objects.equals(SystemEnum.CRM, system)) {
                aiccUser = loginToCrmService.getAiccUserForOpeLogin(user.getUserId());
                Long tenantId = aiccUser == null ? user.getTenantId() : aiccUser.getTenantId();
                if (!tenantService.checkTenantIp(tenantId, ip)) {
                    logger.error("当前登录ip={}", ip);
                    writeErrorMsg(response, ResultObject.fail(ComErrorCode.USER_NOT_LOGIN, "仅支持在内网环境登陆"), userService, userToken, system);
                    return false;
                }
            }
        }

        // 操作日志中需用记录ip地址，所以在这里添加到threadLocal里面

        SecurityUtils.flushRemoteIpAddress(ip);

        if (isAuthorization) {
            SecurityUtils.setUserInfo(userToken, user, redisOpsService, system);
            return true;
        }

        if (handler instanceof ResourceHttpRequestHandler) {
            boolean isProd = EnvironmentConstants.CURR_ENV.isProd();
            if (isProd) {
                writeErrorMsg(response, ResultObject.fail(ComErrorCode.USER_NOT_LOGIN, "不允许访问静态资源"), userService, userToken, system);
            }
            return !isProd;
        }

        HandlerMethod method = (HandlerMethod) handler;
        NoLogin noLogin = method.getMethodAnnotation(NoLogin.class);
        if (noLogin != null) {
            InnerOnly only = method.getMethodAnnotation(InnerOnly.class);
            if (only != null) {
                if (ipWhiteListService.checkIsInWhiteList(ip)) {
                    return true;
                } else {
                    writeErrorMsg(response, ResultObject.fail(ComErrorCode.USER_NOT_LOGIN, "权限错误，IP黑名单拦截", "权限错误，IP黑名单拦截，当前远程ip=" + ip), userService, "", system);
                    return true;
                }
            }
            return true;
        }

        // 是否是只能内部访问
        InnerOnly only = method.getMethodAnnotation(InnerOnly.class);
        if (only != null) {
            if (ipWhiteListService.checkIsInWhiteList(ip)) {
                return true;
            } else {
                writeErrorMsg(response, ResultObject.fail(ComErrorCode.USER_NOT_LOGIN, "权限错误，IP黑名单拦截", "权限错误，IP黑名单拦截，当前远程ip=" + ip), userService, userToken, system);
                return true;
            }
        }

        // 未登录可以访问
        NoLogin less = method.getMethodAnnotation(NoLogin.class);
        if (less == null) {
            response.setContentType(ContentType.APPLICATION_JSON.toString());
            String tipMessage;
            if (!StringUtils.isBlank(userToken)) {
                tipMessage = "登录已经过期，请重新登录";
            } else {
                tipMessage = "您未登录，请重新登录";
            }
            writeErrorMsg(response, ResultObject.fail(ComErrorCode.USER_NOT_LOGIN, tipMessage), userService, userToken, system);
            return false;
        }

        response.setHeader(API_SUCCESS, "true");
        return true;
    }

    public static void writeErrorMsg(HttpServletResponse response, ResultObject ResultObject, UserService userService, String userToken, SystemEnum system) throws IOException {
        if (StringUtils.isNotBlank(userToken) && Objects.nonNull(SecurityUtils.getUserInfo())) {
            userService.logout(userToken, SecurityUtils.getUserId(), system);
        }
        response.setContentType(DEFAULT_CONTENT_TYPE);
        response.setHeader(API_SUCCESS, "false");
        try (ServletOutputStream writer = response.getOutputStream()) {
            writer.write(JsonUtils.object2String(ResultObject).getBytes(StandardCharsets.UTF_8));
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        SecurityUtils.remove();
        SystemHolder.remove();
    }
}
