### 查找答案
POST {{HOST}}/apiBot/v3/textSearch/search
Accept: application/json
Content-Type: text/plain; charset=utf-8
Cookie: {{COOKIE}}

{
  "botId": 139,
  "searchType": "CUSTOM_VARIABLE",
  "searchText": "联系电话",
  "answerSourceList": ["STEP", "KNOWLEDGE", "SPECIAL_ANSWER"]
}


### 替换答案
POST {{HOST}}/apiBot/v3/textSearch/replace
Accept: application/json
Content-Type: text/plain; charset=utf-8
Cookie: {{COOKIE}}

{
  "botId": 139,
  "searchType": "CUSTOM_VARIABLE",
  "searchText": "联系电话",
  "answerSourceList": ["STEP", "KNOWLEDGE", "SPECIAL_ANSWER"],
  "botTextSearchReplaceResultVOList": [{
      "originText": "你好呀${客户名}jfiew",
      "location": "M1-1:对话节点",
      "intentId": null,
      "searchType": "CUSTOM_VARIABLE",
      "answerLocate": {
        "answerSource": "STEP",
        "answerId": "628b348da92bad5f66dc39f3",
        "answerLabel": "6",
        "stepId": "628b3468a92bad5f66dc39e3",
        "stepName": "开场白",
        "stepLabel": "M1",
        "nodeId": "628b346ca92bad5f66dc39f1",
        "nodeName": "对话节点",
        "nodeLabel": "M1-1",
        "knowledgeId": null,
        "knowledgeName": null,
        "knowledgeLabel": null,
        "specialAnswerConfigId": null,
        "specialAnswerConfigName": null,
        "specialAnswerConfigLabel": null,
        "displayName": "开场白"
      },
      "action": null
    }],
  "replaceText": "呵呵呵"
}

