package com.yiwise.dialogflow.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiwise.base.monitoring.helper.NotControllerResponseAdvice;
import com.yiwise.common.cloud.feign.helper.response.RPCResult;
import com.yiwise.common.cloud.feign.helper.response.ResultCode;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@RestControllerAdvice(basePackages = {"com.yiwise.dialogflow.apicontroller"})
public class ControllerResponseAdvice implements ResponseBodyAdvice<Object> {

    public ControllerResponseAdvice() {
    }

    public boolean supports(MethodParameter methodParameter, Class<? extends HttpMessageConverter<?>> aClass) {
        if (methodParameter.getParameterType().isAssignableFrom(RPCResult.class)) {
            return false;
        } else {
            return !methodParameter.hasMethodAnnotation(NotControllerResponseAdvice.class);
        }
    }

    public Object beforeBodyWrite(Object data, MethodParameter returnType, MediaType mediaType, Class<? extends HttpMessageConverter<?>> aClass, ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
        if (returnType.getGenericParameterType().equals(String.class)) {
            ObjectMapper objectMapper = new ObjectMapper();

            try {
                return objectMapper.writeValueAsString(RPCResult.success(data));
            } catch (JsonProcessingException var9) {
                return RPCResult.fail(ResultCode.UNKNOWN_ERROR);
            }
        } else {
            return RPCResult.success(data);
        }
    }
}