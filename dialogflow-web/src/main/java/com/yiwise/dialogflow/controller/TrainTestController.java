package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.entity.vo.RobotSnapshotCreateResult;
import com.yiwise.dialogflow.entity.vo.UserVariableNameResult;
import com.yiwise.dialogflow.service.RobotSnapshotService;
import com.yiwise.dialogflow.service.VariableService;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Set;

@RestController
@RequestMapping("apiBot/v3/trainTest")
public class TrainTestController {

    @Resource
    private RobotSnapshotService robotSnapshotService;

    @Resource
    private VariableService variableService;

    @GetMapping("check")
    public ResultObject<RobotSnapshotCreateResult> check(@NotNull(message = "botId不能为空") Long botId,
                                      @NotNull(message = "isSpeechTrain不能为空") Boolean isSpeechTrain,
                                      SystemEnum systemType) {
        RobotSnapshotCreateResult result = robotSnapshotService.createTrainTestSnapshot(botId, BooleanUtils.isTrue(isSpeechTrain) ? RobotSnapshotUsageTargetEnum.SPEECH_TEST : RobotSnapshotUsageTargetEnum.TEXT_TEST, systemType);
        if (BooleanUtils.isNotTrue(result.isSuccess())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, result.getMsgList().get(0).getFailMsg());
        }
        return ResultObject.success(result);
    }

    @GetMapping("validate")
    public ResultObject<RobotSnapshotCreateResult> validate(@NotNull(message = "botId不能为空") Long botId,
                                      @NotNull(message = "isSpeechTrain不能为空") Boolean isSpeechTrain,
                                      SystemEnum systemType) {
        RobotSnapshotCreateResult result = robotSnapshotService.createTrainTestSnapshot(botId, BooleanUtils.isTrue(isSpeechTrain) ? RobotSnapshotUsageTargetEnum.SPEECH_TEST : RobotSnapshotUsageTargetEnum.TEXT_TEST, systemType);
        return ResultObject.success(result);
    }

    @GetMapping("getUsedVariableSet")
    public ResultObject<Set<String>> getUsedVariable(@NotNull(message = "botId不能为空") Long botId) {
        return ResultObject.success(variableService.getRealUsedVariableNameSet(botId));
    }

    @GetMapping("getUsedVariableNameList")
    public ResultObject<UserVariableNameResult> getUsedVariableNameList(@NotNull(message = "botId不能为空") Long botId) {
        return ResultObject.success(variableService.getRealUsedVariableNameResult(botId, null, false, true));
    }
}
