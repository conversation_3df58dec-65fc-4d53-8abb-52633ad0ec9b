package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.service.DataFixService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 数据修复
 *
 * <AUTHOR>
 * @date 2022/7/29
 */
@RestController
@RequestMapping("/apiBot/v3/dataFix")
public class DataFixController {

    @Resource
    private DataFixService dataFixService;

    @NoLogin
    @PostMapping("/updateBackgroundVolume")
    public ResultObject<Void> updateBackgroundVolume (Long botId){
        dataFixService.updateBackgroundVolume(botId);
        return ResultObject.success(null);
    }

    /**
     * 公共音频库添加‘全部音频’分组,如果不存在的话
     */
    @NoLogin
    @PostMapping("/initAllAudioGroupIfNotExists")
    public ResultObject<Void> initAllAudioGroupIfNotExists() {
        dataFixService.initAllAudioGroupIfNotExists();
        return ResultObject.success(null);
    }

    @NoLogin
    @PostMapping("/updateTtsVolume")
    public ResultObject<Void> updateTtsVolume() {
        dataFixService.updateTtsVolume();
        return ResultObject.success(null);
    }

    @NoLogin
    @PostMapping("/fixVar")
    public ResultObject<Void> fixVar() {
        dataFixService.fixVar();
        return ResultObject.success(null);
    }

    @NoLogin
    @GetMapping("/fixSystemVar")
    public ResultObject<Void> fixSystemVar() {
        dataFixService.fixSystemVar();
        return ResultObject.success(null);
    }

    @NoLogin
    @PostMapping("/fixBot")
    public ResultObject fixBot() {
        dataFixService.fixBot();
        return ResultObject.success();
    }

    @NoLogin
    @PostMapping("/fixAiRepeatName")
    public ResultObject<Void> fixAiRepeatName() {
        dataFixService.fixAiRepeatName();
        return ResultObject.success(null);
    }

    @NoLogin
    @PostMapping("/fixBotGenerateTemplateStatus")
    public ResultObject<Void> fixBotGenerateTemplateStatus() {
        dataFixService.fixBotGenerateTemplateStatus();
        return ResultObject.success(null);
    }
}
