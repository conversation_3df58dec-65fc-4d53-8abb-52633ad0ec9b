package com.yiwise.dialogflow.controller.engine;

import com.yiwise.base.model.annotation.auth.InnerOnly;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.engine.service.EngineBatchTestService;
import com.yiwise.dialogflow.entity.vo.batchtest.EngineBatchTestRequestVO;
import com.yiwise.dialogflow.entity.vo.batchtest.EngineBatchTestResultVO;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import java.util.List;

@RestController
@RequestMapping("apiBot/v3/batchTest")
public class EngineBatchTestController {

    @Resource
    private EngineBatchTestService engineBatchTestService;

    @PostMapping("start")
    public ResultObject<EngineBatchTestResultVO> start(@RequestBody EngineBatchTestRequestVO request) {
        return ResultObject.success(engineBatchTestService.batchTest(request));
    }

    @PostMapping("startFromFile")
    public ResultObject<EngineBatchTestResultVO> startFromFile(@RequestParam(value = "file", required = false) @NotNull(message = "file不能为空")  MultipartFile file) {
        return ResultObject.success(engineBatchTestService.batchTestFromFile(file));
    }

    @NoLogin
    @InnerOnly
    @PostMapping("startFromCsv")
    public ResultObject<List<EngineBatchTestResultVO>> startFromCsv(@NotNull(message = "botId不能为空") Long botId,
                                                                   @RequestParam(value = "file", required = false) @NotNull(message = "file不能为空")  MultipartFile file) {
        return ResultObject.success(engineBatchTestService.batchTestFromCsv(botId, file));
    }

}
