package com.yiwise.dialogflow.controller.test;

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.service.remote.UserService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("apiBot/v3/test/user")
public class UserTestController {

    @Resource
    private UserService userService;

    @NoLogin
    @GetMapping("getUserById")
    public ResultObject<List<UserPO>> getUserById(Long id) {
        return ResultObject.success(userService.getUserByIdList(Collections.singletonList(id)));
    }
}
