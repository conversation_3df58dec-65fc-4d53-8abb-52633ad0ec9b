package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.aop.TenantIsolation;
import com.yiwise.dialogflow.entity.po.intent.IntentConfigPO;
import com.yiwise.dialogflow.service.intent.IntentConfigService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/2/16
 */
@RestController
@RequestMapping("/apiBot/v3/intentConfig")
public class IntentConfigController {

    @Resource
    private IntentConfigService intentConfigService;

    @PostMapping(value = "/save")
    @TenantIsolation("#intentConfigPO.botId")
    public ResultObject<String> save(@RequestBody IntentConfigPO intentConfigPO) {
        intentConfigPO.setUpdateUserId(SecurityUtils.getUserId());
        intentConfigService.save(intentConfigPO);
        return ResultObject.success("请求成功");
    }

    @GetMapping(value = "/detail")
    public ResultObject<IntentConfigPO> detail(@RequestParam Long botId) {
        return ResultObject.success(intentConfigService.detail(botId));
    }
}
