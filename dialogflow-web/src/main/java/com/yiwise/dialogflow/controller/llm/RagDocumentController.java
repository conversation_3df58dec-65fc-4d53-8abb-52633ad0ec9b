package com.yiwise.dialogflow.controller.llm;

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.entity.po.llm.RagDocumentPO;
import com.yiwise.dialogflow.entity.query.RagDocumentQueryVO;
import com.yiwise.dialogflow.entity.vo.llm.*;
import com.yiwise.dialogflow.service.llm.RagDocumentService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 文档知识
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apiBot/v3/ragDocument")
public class RagDocumentController {

    @Resource
    private RagDocumentService ragDocumentService;

    /**
     * 上传知识库文件
     *
     * @param file  支持txt、dock、docx、xls、xlsx格式
     * @param botId 话术id
     * @return 文件oss地址
     */
    @PostMapping("/upload")
    public ResultObject<String> upload(@RequestPart MultipartFile file, @RequestParam Long botId) {
        return ResultObject.success(ragDocumentService.upload(file, botId));
    }

    /**
     * 创建文档
     *
     * @param request form
     * @return 文档实体
     */
    @PostMapping("/create")
    public ResultObject<Void> createDocument(@RequestBody @Valid RagDocumentCreateVO request) {
        ragDocumentService.createDocument(request, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }

    /**
     * 处理回调
     *
     * @param callback form
     */
    @NoLogin
    @PostMapping("/callback")
    public ResultObject<Void> callback(@RequestBody String callback) {
        ragDocumentService.callback(callback);
        return ResultObject.success(null);
    }

    /**
     * 更新启用/停用状态
     */
    @PostMapping("/updateEnabledStatus")
    public ResultObject<Void> updateEnabledStatus(@RequestBody RagDocumentVO request) {
        ragDocumentService.updateEnabledStatus(request.getBotId(), request.getId(), request.getEnabledStatus(), SecurityUtils.getUserId());
        return ResultObject.success(null);
    }

    /**
     * 更新文档名称
     *
     * @param request form
     */
    @PostMapping("/update")
    public ResultObject<Void> update(@RequestBody @Valid RagDocumentUpdateVO request) {
        ragDocumentService.update(request, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }

    /**
     * 删除文档
     *
     * @param query form
     */
    @PostMapping("/delete")
    public ResultObject<Void> delete(@RequestBody @Valid RagDocumentQueryVO query) {
        ragDocumentService.delete(query, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 文档列表
     */
    @PostMapping("/list")
    public ResultObject<PageResultObject<RagDocumentVO>> queryByCondition(@RequestBody @Valid RagDocumentQueryVO condition) {
        return ResultObject.success(ragDocumentService.queryByCondition(condition));
    }

    /**
     * 判断是否开启大模型兜底对话
     *
     * @param botId 话术id
     * @return 是否开启
     */
    @PostMapping("/checkEnableLLMChat")
    public ResultObject<Boolean> checkEnableLLMChat(@RequestParam Long botId) {
        return ResultObject.success(ragDocumentService.checkEnableLLMChat(botId));
    }

    /**
     * 根据文档id查询文档
     *
     * @param docId 文档id
     * @return 文档实体
     */
    @GetMapping("/getById")
    public ResultObject<RagDocumentPO> getById(@RequestParam String docId) {
        return ResultObject.success(ragDocumentService.getById(docId));
    }

    /**
     * 重新拆分
     *
     * @param request form
     */
    @PostMapping("/reSplit")
    public ResultObject<Void> reSplit(@RequestBody @Valid RagDocumentSplitRequestVO request) {
        ragDocumentService.reSplit(request, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }

    /**
     * 重新解析
     *
     * @param botId 话术id
     * @param docId 文档id
     */
    @PostMapping("/reParse")
    public ResultObject<Void> reParse(@RequestParam Long botId, @RequestParam String docId) {
        ragDocumentService.reParse(botId, docId, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }

    /**
     * 内容检索
     *
     * @param request form
     * @return 匹配结果
     */
    @PostMapping("/test")
    public ResultObject<RagDocumentTestResultVO> test(@RequestBody @Valid RagDocumentTestRequestVO request) {
        return ResultObject.success(ragDocumentService.test(request));
    }

    /**
     * 导出单个文档
     *
     * @param docId 文档id
     * @return 导出文件oss地址
     */
    @GetMapping("/export")
    public ResultObject<String> export(@RequestParam String docId) {
        return ResultObject.success(ragDocumentService.export(docId));
    }

    /**
     * 批量导出文档
     *
     * @param request query
     * @return zip oss地址
     */
    @PostMapping("/batchExport")
    public ResultObject<String> batchExport(@RequestBody @Valid RagDocumentQueryVO request) {
        return ResultObject.success(ragDocumentService.batchExport(request));
    }
}