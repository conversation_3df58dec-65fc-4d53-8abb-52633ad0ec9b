package com.yiwise.dialogflow.controller.llm;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.entity.po.LlmLabelPO;
import com.yiwise.dialogflow.entity.vo.llm.LlmLabelQueryVO;
import com.yiwise.dialogflow.service.llm.LlmLabelService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 大模型分类
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apiBot/v3/llmLabel")
public class LlmLabelController {

    @Resource
    private LlmLabelService llmLabelService;

    /**
     * 查询大模型分类列表
     *
     * @param request 查询参数
     * @return 分类列表
     */
    @GetMapping("/list")
    public ResultObject<List<LlmLabelPO>> list(LlmLabelQueryVO request) {
        return ResultObject.success(llmLabelService.list(request));
    }

    /**
     * 修复数据
     */
    @PostMapping("/fixData")
    public ResultObject<Void> fixData() {
        llmLabelService.fixData();
        return ResultObject.success(null);
    }

    /**
     * 根据话术id修复数据
     *
     * @param botId 话术id
     */
    @PostMapping("/fixDataByBotId")
    public ResultObject<Void> fixDataByBotId(@RequestParam Long botId) {
        llmLabelService.fixDataByBotId(botId);
        return ResultObject.success(null);
    }

    /**
     * 创建分类
     *
     * @param request form
     * @return 结果
     */
    @PostMapping("/create")
    public ResultObject<LlmLabelPO> create(@RequestBody LlmLabelPO request) {
        return ResultObject.success(llmLabelService.create(request, SecurityUtils.getUserId()));
    }

    /**
     * 编辑分类
     *
     * @param request form
     * @return 结果
     */
    @PostMapping("/update")
    public ResultObject<LlmLabelPO> update(@RequestBody LlmLabelPO request) {
        return ResultObject.success(llmLabelService.update(request, SecurityUtils.getUserId()));
    }

    /**
     * 删除分类
     *
     * @param id 分类id
     */
    @PostMapping("/delete")
    public ResultObject<Void> delete(@RequestParam String id) {
        llmLabelService.delete(id, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }
}