package com.yiwise.dialogflow.controller.audio;

import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.entity.po.BotAudioConfigPO;
import com.yiwise.dialogflow.entity.vo.audio.*;
import com.yiwise.dialogflow.entity.vo.audio.request.BatchAdjustVolumeRequestVO;
import com.yiwise.dialogflow.entity.vo.audio.request.ResetAudioRequestVO;
import com.yiwise.dialogflow.service.*;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("apiBot/v3/miniapp/audio")
public class MiniAppController {

    @Resource
    private MiniAppAudioManagerService miniAppAudioManagerService;

    @Resource
    private BotConfigService botConfigService;

    @Resource
    private AudioUploadService audioUploadService;

    @Resource
    private AnswerAudioManagerService answerAudioManagerService;


    @GetMapping("getBotTotalAudioInfo")
    public ResultObject getBotTotalAudioInfo(@NotNull(message = "botId不能为空") Long botId) {
        BotAudioConfigPO config = botConfigService.getAudioConfig(botId);
        return ResultObject.success(miniAppAudioManagerService.getTreeifyTotalInfoByBotId(botId, config.getRecordUserId()));
    }

    @GetMapping("getBotTotalAudioInfoByType")
    public ResultObject getBotTotalAudioInfo(@NotNull(message = "botId不能为空") Long botId,
                                            @NotNull(message = "originType不能为空") AnswerSourceEnum originType,
                                            @NotBlank(message = "originId不能为空") String originId) {

        // todo 从登录用户中获取
        BotAudioConfigPO config = botConfigService.getAudioConfig(botId);
        return ResultObject.success(miniAppAudioManagerService.getTreeifyInfoBySourceType(botId, config.getRecordUserId(), originType, originId));
    }

    @GetMapping("getBotTotalAudioInfoByType/v2")
    public ResultObject getBotTotalAudioInfoV2(@NotNull(message = "botId不能为空") Long botId,
                                            @NotNull(message = "originType不能为空") AnswerSourceEnum originType,
                                            @NotBlank(message = "originId不能为空") String originId) {
        BotAudioConfigPO config = botConfigService.getAudioConfig(botId);
        AudioTotalInfoVO audioTotalInfo = miniAppAudioManagerService.getTreeifyInfoBySourceType(botId, config.getRecordUserId(), originType, originId);
        Optional.ofNullable(audioTotalInfo).map(AudioTotalInfoVO::getTreeifyAnswerAudio).ifPresent(this::processTreeifyAnswerAudio);
        return ResultObject.success(audioTotalInfo);
    }

    /**
     * 没有答案的节点自动填充一条答案信息，业务上没有任何意思，只是为了兼容前端
     */
    private void processTreeifyAnswerAudio(TreeifyAnswerAudioVO treeifyAnswerAudio) {
        List<AnswerAudioWrapVO> answerAudioList = treeifyAnswerAudio.getAnswerAudioList();
        if (CollectionUtils.isEmpty(answerAudioList)) {
            AnswerAudioWrapVO vo = new AnswerAudioWrapVO();
            vo.setText("--");
            AnswerPlaceholderElementVO element = new AnswerPlaceholderElementVO();
            element.setType(TextPlaceholderTypeEnum.TEXT);
            element.setValue("--");
            element.setUrl("--");
            vo.setAnswerElementList(Collections.singletonList(element));
            treeifyAnswerAudio.setAnswerAudioList(Collections.singletonList(vo));
        }
        List<TreeifyAnswerAudioVO> childList = treeifyAnswerAudio.getChildList();
        if (CollectionUtils.isNotEmpty(childList)) {
            childList.forEach(this::processTreeifyAnswerAudio);
        }
    }

    @GetMapping("searchBotTotalAudioInfo")
    public ResultObject searchBotTotalAudioInfo(@NotNull(message = "botId不能为空") Long botId,
                                               @NotBlank(message = "搜索内容不能为空") String search) {
        BotAudioConfigPO config = botConfigService.getAudioConfig(botId);
        return ResultObject.success(miniAppAudioManagerService.searchAnswerAudioInfo(botId, config.getRecordUserId(), search));
    }

    @GetMapping("getBotListByLoginRecordUser")
    public ResultObject getBotList(Long recordUserId,
                                  String search,
                                  @RequestParam(defaultValue = "1") Integer pageNum,
                                  @RequestParam(defaultValue = "10") Integer pageSize) {
        if (Objects.isNull(recordUserId)) {
            recordUserId = SecurityUtils.getUserId();
        }
        return ResultObject.success(miniAppAudioManagerService.queryBotAudioProgress(recordUserId, search, pageNum, pageSize));
    }

    @PostMapping("adjustVolume")
    public ResultObject adjustVolume(@RequestBody BatchAdjustVolumeRequestVO request) {
        BotAudioConfigPO config = botConfigService.getAudioConfig(request.getBotId());
        // todo 从登录用户获取
        miniAppAudioManagerService.batchAdjustVolume(request.getBotId(), config.getRecordUserId(), request.getVolume(), request.getUrlList(), SecurityUtils.getUserId());
        return ResultObject.success();
    }

    @PostMapping("uploadFile")
    public ResultObject<AudioUploadResultVO> uploadFile(@RequestParam(value = "botId", required = false) @NotNull(message = "botId不能为空") Long botId,
                                                       @NotBlank(message = "answerText不能为空") String answerText,
                                                       @RequestParam(value = "file", required = false) @NotNull(message = "file不能为空") MultipartFile file) {
        return ResultObject.success(miniAppAudioManagerService.uploadAudio(botId, answerText, file, SecurityUtils.getUserId()));
    }

    @GetMapping("generateWaveform")
    public ResultObject<AudioUploadResultVO> generateWaveform(@NotNull(message = "url不能为空") String url) {
        return ResultObject.success(audioUploadService.generateWaveform(url));
    }

    @PostMapping("resetByAnswer")
    public ResultObject resetByAnswer(@RequestBody ResetAudioRequestVO request) {
        answerAudioManagerService.resetByAnswer(request.getBotId(), request.getAnswerTextList(), SecurityUtils.getUserId());
        return ResultObject.success();
    }

}
