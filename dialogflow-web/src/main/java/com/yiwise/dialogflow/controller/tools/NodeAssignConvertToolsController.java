package com.yiwise.dialogflow.controller.tools;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.utils.NodeAssignUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

@RestController
@RequestMapping("apiBot/v3/tools")
public class NodeAssignConvertToolsController {

    @GetMapping("processNodeAssignByBotId")
    public ResultObject processNodeAssignByBotId(@NotNull Long botId) {
        // 实现逻辑
        NodeAssignUtils.processByBotId(botId);
        return ResultObject.success();
    }
    @GetMapping("processAllBotNodeAssign")
    public ResultObject processAllBotNodeAssign() {
        // 实现逻辑
        NodeAssignUtils.processAllBot();
        return ResultObject.success();
    }
}
