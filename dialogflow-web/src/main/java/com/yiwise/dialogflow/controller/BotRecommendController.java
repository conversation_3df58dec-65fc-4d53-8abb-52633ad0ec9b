package com.yiwise.dialogflow.controller;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.entity.po.BotRecommendRephraseRecordPO;
import com.yiwise.dialogflow.entity.vo.*;
import com.yiwise.dialogflow.service.BotRecommendService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 话术推荐
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apiBot/v3/botRecommend")
public class BotRecommendController {

    @Resource
    private BotRecommendService botRecommendService;

    /**
     * 话术文本解析
     *
     * @param botId 话术id
     * @return 解析结果
     */
    @PostMapping("/textParsing")
    public ResultObject<Boolean> textParsing(@RequestParam("botId") Long botId) {
        return ResultObject.success(botRecommendService.textParsing(botId));
    }

    /**
     * 解析符合条件的话术列表
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    @PostMapping("/textParsingByRangeTime")
    public ResultObject<Void> textParsingByRangeTime(@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") @RequestParam("startTime") LocalDateTime startTime,
                                                    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") @RequestParam("endTime") LocalDateTime endTime,
                                                    @RequestParam("minCallCount") Long minCallCount) {
        botRecommendService.textParsingByRangeTime(startTime, endTime, minCallCount);
        return ResultObject.success(null);
    }

    /**
     * 搜索相似话术
     *
     * @param request form
     * @return 相似话术列表
     */
    @PostMapping("/search")
    public ResultObject<List<BotTextSearchResultVO>> search(@RequestBody @Valid BotRecommendSearchRequestVO request) {
        return ResultObject.success(botRecommendService.search(request));
    }

    /**
     * 排序
     *
     * @param request form
     * @return 排序结果
     */
    @PostMapping("/sort")
    public ResultObject<List<BotTextSearchResultVO>> sort(@RequestBody BotRecommendSearchResultSortRequestVO request) {
        return ResultObject.success(botRecommendService.sort(request));
    }

    /**
     * 改写
     *
     * @param request form
     * @return 改写结果
     */
    @PostMapping("/rephrase")
    public ResultObject<Map<String, List<String>>> rephrase(@RequestBody @Valid BotRecommendRephraseRequestVO request) {
        return ResultObject.success(botRecommendService.rephrase(request));
    }

    /**
     * 话术中答案改写
     *
     * @param request form
     * @return 改写结果
     */
    @PostMapping("/botAnswerRephrase")
    public ResultObject<Map<String, List<BotRecommendRephraseRecordPO>>> botAnswerRephrase(@RequestBody @Valid BotAnswerRephraseRequestVO request) {
        return ResultObject.success(botRecommendService.botAnswerRephrase(request, SecurityUtils.getUserId()));
    }

    /**
     * 查询答案改写记录
     *
     * @param request form
     * @return 改写记录
     */
    @PostMapping("/queryRephraseRecordList")
    public ResultObject<List<BotRecommendRephraseRecordPO>> queryRephraseRecordList(@RequestBody BotAnswerRephraseRequestVO request) {
        return ResultObject.success(botRecommendService.queryRephraseRecordList(request));
    }

    /**
     * 更新改写结果
     *
     * @param record 改写记录
     */
    @PostMapping("/updateRephrasedSentence")
    public ResultObject<Void> updateRephrasedSentence(@RequestBody @Valid BotRecommendRephraseRecordPO record) {
        botRecommendService.updateRephrasedSentence(record.getId(), record.getRephrasedSentence(), SecurityUtils.getUserId());
        return ResultObject.success(null);
    }

    /**
     * 收藏
     *
     * @param record 改写记录
     */
    @PostMapping("/starred")
    public ResultObject<Void> starred(@RequestBody @Valid BotRecommendRephraseRecordPO record) {
        botRecommendService.starred(record.getId(), SecurityUtils.getUserId());
        return ResultObject.success(null);
    }

    /**
     * 删除改写记录
     *
     * @param idList id列表
     */
    @PostMapping("/deleteRephrasedRecord")
    public ResultObject<Void> deleteRephrasedRecord(@RequestBody List<String> idList) {
        botRecommendService.deleteRephrasedRecord(idList);
        return ResultObject.success(null);
    }

    /**
     * 查询指定时间范围内使用了话术推荐的botId列表
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return botId列表
     */
    @NoLogin
    @GetMapping("/queryBotIdListByRangeTime")
    public ResultObject<List<Long>> queryBotIdListByRangeTime(@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") LocalDateTime startTime,
                                                              @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") LocalDateTime endTime) {
        return ResultObject.success(botRecommendService.queryBotIdListByRangeTime(startTime, endTime));
    }

    /**
     * 查询话术中答案和被收藏的改写记录
     *
     * @param botId botId
     * @return 改写记录
     */
    @NoLogin
    @PostMapping("/queryAnswerStarredRephraseRecordList")
    public ResultObject<List<BotRecommendRephraseRecordVO>> queryAnswerStarredRephraseRecordList(@RequestParam Long botId) {
        return ResultObject.success(botRecommendService.queryAnswerStarredRephraseRecordList(botId));
    }
}