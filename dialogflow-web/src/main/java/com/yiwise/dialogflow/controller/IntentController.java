package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.aop.TenantIsolation;
import com.yiwise.dialogflow.entity.CheckKeywordExistsRequestVO;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.query.IntentQuery;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.IntentChangeGroupVO;
import com.yiwise.dialogflow.entity.vo.IntentVO;
import com.yiwise.dialogflow.entity.vo.sync.BotSyncResultVO;
import com.yiwise.dialogflow.entity.vo.sync.IntentSyncVO;
import com.yiwise.dialogflow.service.intent.IntentService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 意图库
 *
 * <AUTHOR>
 * @date 2022/2/16
 */
@Validated
@RestController
@RequestMapping("/apiBot/v3/intent")
public class IntentController {

    @Resource
    private IntentService intentService;

    /**
     * 保存意图
     */
    @PostMapping(value = "/save")
    @TenantIsolation("#intentVO.botId")
    public ResultObject<IntentPO> save(@RequestBody IntentVO intentVO) {
        intentVO.setUpdateUserId(SecurityUtils.getUserId());
        return ResultObject.success(intentService.save(intentVO));
    }

    /**
     * 列表查询
     */
    @PostMapping(value = "/list")
    public ResultObject<PageResultObject<IntentVO>> list(@RequestBody IntentQuery intentQuery) {
        return ResultObject.success(intentService.list(intentQuery));
    }

    /**
     * 根据botId查询全部的意图id和名称
     *
     * @param botId 话术id
     * @return <id,name>
     */
    @GetMapping("/simpleList")
    public ResultObject<List<IdNamePair<String, String>>> simpleList(@RequestParam("botId") Long botId) {
        return ResultObject.success(intentService.simpleList(botId));
    }

    @GetMapping(value = "/detail")
    public ResultObject<IntentVO> detail(String intentId) {
        return ResultObject.success(intentService.detail(intentId));
    }

    @GetMapping(value = "/getByName")
    public ResultObject<IntentVO> detail(@NotNull(message = "botId不能为空") Long botId,
                                        @NotBlank(message = "name不能为空") String name) {
        return ResultObject.success(intentService.getByName(botId, name).orElse(null));
    }

    @DeleteMapping(value = "/delete")
    @TenantIsolation("#intentQuery.botId")
    public ResultObject<String> delete(@RequestBody IntentQuery intentQuery) {
        intentService.delete(intentQuery, SecurityUtils.getUserId());
        return ResultObject.success("请求成功");
    }

    /**
     * 移动分组
     */
    @PostMapping("/changeGroup")
    @TenantIsolation("#changeGroupVO.botId")
    public ResultObject<Void> changeGroup(@RequestBody IntentChangeGroupVO changeGroupVO){
        changeGroupVO.setUserId(SecurityUtils.getUserId());
        intentService.changeGroup(changeGroupVO);
        return ResultObject.success(null);
    }

    @PostMapping(value = "/sync")
    @TenantIsolation("#syncVO.srcBotId")
    @TenantIsolation("#syncVO.targetBotIdList")
    public ResultObject<BotSyncResultVO> sync(@RequestBody IntentSyncVO syncVO) {
        syncVO.setCurrentUserId(SecurityUtils.getUserId());
        return ResultObject.success(intentService.sync(syncVO));
    }

    @PostMapping("/findExistsKeyword")
    public ResultObject<Map<String, Set<String>>> findExistsKeyword(@RequestBody @Valid CheckKeywordExistsRequestVO request) {
        return ResultObject.success(intentService.findExistsKeyword(request));
    }

    /**
     * 关键词转移
     */
    @PostMapping("/deleteKeyword")
    @TenantIsolation("#request.botId")
    public ResultObject<Void> deleteKeyword(@RequestBody @Valid CheckKeywordExistsRequestVO request) {
        intentService.deleteKeyword(request, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }
}
