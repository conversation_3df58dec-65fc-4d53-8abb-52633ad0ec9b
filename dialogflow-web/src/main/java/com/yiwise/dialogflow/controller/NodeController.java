package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.aop.TenantIsolation;
import com.yiwise.dialogflow.entity.bo.SnapshotValidateConfigBO;
import com.yiwise.dialogflow.entity.query.NodeJumpOutStatsQuery;
import com.yiwise.dialogflow.entity.query.StepNodeQuery;
import com.yiwise.dialogflow.entity.vo.*;
import com.yiwise.dialogflow.entity.vo.node.DialogBaseNodeVO;
import com.yiwise.dialogflow.entity.vo.node.StepNodeSaveResultVO;
import com.yiwise.dialogflow.entity.vo.stats.NodeJumpStatsVO;
import com.yiwise.dialogflow.entity.vo.sync.NodeSyncResultVO;
import com.yiwise.dialogflow.entity.vo.sync.NodeSyncVO;
import com.yiwise.dialogflow.service.StepNodeService;
import com.yiwise.dialogflow.service.stats.BotStatsFacadeService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("apiBot/v3/node")
public class NodeController {

    @Resource
    private StepNodeService stepNodeService;

    @Resource
    private BotStatsFacadeService botStatsFacadeService;

    @PostMapping("batchSave")
    @TenantIsolation("#request.botId")
    public ResultObject<StepNodeSaveResultVO> batchSave(@RequestBody NodeBatchSaveRequestVO request) {
        SnapshotValidateConfigBO snapshotValidateConfig = new SnapshotValidateConfigBO();
        if (SystemEnum.OPE.equals(request.getSystem())) {
            snapshotValidateConfig.setRequireValidActionRule(true);
            snapshotValidateConfig.setRequireValidOutsideResource(true);
        }
        return ResultObject.success(stepNodeService.validAndSave(SecurityUtils.getTenantId(), request.getBotId(), request.getStepId(), request.getNodeList(), snapshotValidateConfig, SecurityUtils.getUserId()));
    }

    @PostMapping("autoSave")
    @TenantIsolation("#request.botId")
    public ResultObject<StepNodeSaveResultVO> autoSave(@RequestBody NodeBatchSaveRequestVO request) {
        return ResultObject.success(stepNodeService.autoSave(request.getBotId(), request.getStepId(), request.getNodeList(), SecurityUtils.getUserId()));
    }

    @GetMapping("getListByStepId")
    public ResultObject<List<DialogBaseNodeVO>> getListByStepId(@NotNull(message = "botId不能为空") Long botId,
                                                               @NotNull(message = "stepId不能为空") String stepId) {
        StepNodeQuery queryCondition = new StepNodeQuery();
        queryCondition.setStepId(stepId);
        queryCondition.setBotId(botId);
        queryCondition.setTenantId(SecurityUtils.getTenantId());
        return ResultObject.success(stepNodeService.getVOListByStepId(queryCondition));
    }

    /**
     * 查询流程节点列表
     */
    @PostMapping("getListByStepId")
    public ResultObject<List<DialogBaseNodeVO>> getListByStepIdWithStats(@Validated @RequestBody StepNodeQuery queryCondition) {
        return ResultObject.success(stepNodeService.getVOListByStepId(queryCondition));
    }

    /**
     * 查询节点跳转详情
     */
    @PostMapping("getNodeJumpOutStats")
    public ResultObject<List<NodeJumpStatsVO>> getNodeJumpOutStats(@Validated @RequestBody NodeJumpOutStatsQuery condition) {
        condition.setTenantId(SecurityUtils.getTenantId());
        return ResultObject.success(botStatsFacadeService.queryNodeJumpOutStats(condition.getBotId(), condition.getStepId(), condition.getNodeId(), condition));
    }

    /**
     * 生成节点id
     * @return
     */
    @GetMapping("generateId")
    public ResultObject<String> generateNodeId() {
        return ResultObject.success(stepNodeService.generateNodeId());
    }

    /**
     * 批量生成节点id
     * @param count 生成数量
     * @return 节点id列表
     */
    @GetMapping("generateIdList")
    public ResultObject<List<String>> generateIdList(@RequestParam(defaultValue = "10") Integer count) {
        return ResultObject.success(stepNodeService.generateNodeIdList(count));
    }

    @GetMapping("getAllNodeRefIntentList")
    public ResultObject<List<SimpleIntentVO>> getAllNodeRefIntentList(@NotNull(message = "botId不能为空") Long botId) {
        return ResultObject.success(stepNodeService.queryAllNodeRefIntentInfoList(botId));
    }

    /**
     * 话术中是否包含转人工节点
     */
    @NoLogin
    @GetMapping("/containsSwitchToHumanServiceNode")
    public ResultObject<Boolean> containsSwitchToHumanServiceNode(Long dialogFlowId) {
        return ResultObject.success(stepNodeService.containsSwitchToHumanServiceNode(dialogFlowId));
    }

    /**
     * 查询节点接口测试
     *
     * @param req form
     * @return 请求结果
     */
    @PostMapping("/apiTest")
    public Mono<ResultObject<QueryNodeApiTestResultVO>> apiTest(@RequestBody @Valid QueryNodeApiTestReqVO req) {
        return stepNodeService.apiTest(req).map(ResultObject::success);
    }

    /**
     * 节点同步
     *
     * @param request form
     * @return 同步结果
     */
    @PostMapping("/sync")
    public ResultObject<NodeSyncResultVO> sync(@RequestBody @Valid NodeSyncVO request) {
        return ResultObject.success(stepNodeService.sync(request, SecurityUtils.getUserId()));
    }
}
