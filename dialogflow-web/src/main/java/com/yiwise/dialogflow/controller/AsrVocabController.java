package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.entity.query.AsrVocabDetailQuery;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrVocabDetailVO;
import com.yiwise.dialogflow.service.asrmodel.AsrVocabService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/10/19 09:43:27
 */
@RestController
@RequestMapping("/apiBot/v3/asrVocab")
public class AsrVocabController {
    @Resource
    private AsrVocabService asrVocabService;

    @PostMapping("list")
    public ResultObject list(@RequestBody AsrVocabDetailQuery asrVocabDetailVO) {
        return ResultObject.success(asrVocabService.listWithPage(asrVocabDetailVO));
    }

    @PostMapping("update")
    public ResultObject update(@RequestBody AsrVocabDetailVO asrVocabDetailVO) {
        asrVocabDetailVO.setCurrentUserId(SecurityUtils.getUserId());
        asrVocabService.update(asrVocabDetailVO);
        return ResultObject.success("请求成功");
    }

    @PostMapping("delete")
    public ResultObject delete(@RequestBody AsrVocabDetailVO asrVocabDetailVO) {
        asrVocabService.delete(asrVocabDetailVO);
        return ResultObject.success("请求成功");
    }

    @PostMapping("stop")
    public ResultObject stop(@RequestBody AsrVocabDetailVO asrVocabDetailVO) {
        asrVocabDetailVO.setUpdateUserId(SecurityUtils.getUserId());
        asrVocabService.stop(asrVocabDetailVO);
        return ResultObject.success("请求成功");
    }

    @PostMapping("start")
    public ResultObject start(@RequestBody AsrVocabDetailVO asrVocabDetailVO) {
        asrVocabDetailVO.setUpdateUserId(SecurityUtils.getUserId());
        asrVocabService.start(asrVocabDetailVO);
        return ResultObject.success("请求成功");
    }

    @GetMapping("getBotList")
    public ResultObject getBotList(@RequestParam("asrVocabId") Long asrVocabId, @RequestParam(value = "botInfo", required = false) String botInfo,
                                  @RequestParam("pageSize") Integer pageSize, @RequestParam("pageNum") Integer pageNum) {
        return ResultObject.success(asrVocabService.getBotList(asrVocabId, botInfo, pageSize, pageNum));
    }

    @GetMapping("getById")
    public ResultObject getById(@RequestParam("asrVocabId") Long asrVocabId) {
        return ResultObject.success(asrVocabService.getById(asrVocabId));
    }

    @PostMapping("unbindBot")
    public ResultObject unbindBot(@RequestBody AsrVocabDetailVO asrVocabDetailVO) {
        asrVocabService.unbindBot(asrVocabDetailVO);
        return ResultObject.success("请求成功");
    }

    @PostMapping("bindBot")
    public ResultObject bindBot(@RequestBody AsrVocabDetailVO asrVocabDetailVO) {
        asrVocabService.bindBot(asrVocabDetailVO);
        return ResultObject.success("请求成功");
    }
}