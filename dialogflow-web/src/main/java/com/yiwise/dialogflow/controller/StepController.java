package com.yiwise.dialogflow.controller;


import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.aop.TenantIsolation;
import com.yiwise.dialogflow.entity.enums.StepTypeEnum;
import com.yiwise.dialogflow.entity.po.StepPO;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.dialogflow.entity.query.StepQueryVO;
import com.yiwise.dialogflow.entity.vo.StepBatchDeleteRequest;
import com.yiwise.dialogflow.entity.vo.StepVO;
import com.yiwise.dialogflow.entity.vo.step.SimpleStepInfoVO;
import com.yiwise.dialogflow.entity.vo.step.StepSortRequestVO;
import com.yiwise.dialogflow.entity.vo.sync.StepSyncResultVO;
import com.yiwise.dialogflow.entity.vo.sync.StepSyncVO;
import com.yiwise.dialogflow.service.StepService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 流程
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequestMapping("apiBot/v3/step")
public class StepController {

    @Resource
    private StepService stepService;

    /**
     * 创建流程
     * @param step
     * @return
     */
    @PostMapping("create")
    @TenantIsolation("#step.botId")
    public ResultObject<StepVO> create(@RequestBody StepPO step) {
        return ResultObject.success(stepService.create(step, SecurityUtils.getUserId()));
    }

    /**
     * bot 内复制流程
     * @param botId botId
     * @param sourceStepId 源流程 id
     * @param name 名称
     * @param targetStepType 目标流程类型
     * @return 流程
     */
    @PostMapping("copyInBot")
    @TenantIsolation("#botId")
    public ResultObject<StepPO> copyInBot(@NotNull(message = "botId不能为空") Long botId,
                                         @NotBlank(message = "sourceStepId不能为空") String sourceStepId,
                                         @NotBlank(message = "name不能为空") String name,
                                         @NotNull(message = "targetStepType不能为空") StepTypeEnum targetStepType) {
        return ResultObject.success(stepService.copyStepInBot(botId, sourceStepId, name, targetStepType, SecurityUtils.getUserId()));
    }

    /**
     * 更新流程
     * @param step 表单
     * @return 更新后结果
     */
    @PostMapping("update")
    @TenantIsolation("#step.botId")
    public ResultObject<StepVO> update(@RequestBody StepPO step) {
        return ResultObject.success(stepService.update(step, SecurityUtils.getUserId()));
    }

    /**
     * 根据 id 删除
     * @param botId botId
     * @param stepId 流程 id
     * @return 删除前流程内容
     */
    @PostMapping("deleteById")
    @TenantIsolation("#botId")
    public ResultObject<StepPO> deleteById(@NotNull(message = "botId不能为空") Long botId, @NotBlank(message = "stepId不能为空") String stepId) {
        return ResultObject.success(stepService.deleteById(botId, stepId, SecurityUtils.getUserId()));
    }

    /**
     * 根据类型查询流程列表
     * @param botId botId
     * @param type 流程类型
     * @return 流程列表
     */
    @GetMapping("getListByBotId")
    public ResultObject<List<StepVO>> getListByBotId(@NotNull(message = "botId不能为空") Long botId, StepTypeEnum type) {
        StepQueryVO condition = new StepQueryVO();
        condition.setBotId(botId);
        condition.setType(type);
        condition.setTenantId(SecurityUtils.getTenantId());
        return ResultObject.success(stepService.queryByCondition(condition));
    }

    /**
     * 查询流程信息(包含统计信息)
     * @param condition 查询条件
     * @return 流程列表
     */
    @PostMapping("getListByBotId")
    public ResultObject<List<StepVO>> getListByBotIdWithStats(@RequestBody @Validated StepQueryVO condition) {
        condition.setTenantId(SecurityUtils.getTenantId());

        UserPO user = SecurityUtils.getUserInfo();
        if (user != null) {
            log.debug("[LogHub] 用户:{} 正在查看话术:{}", user.getName(), condition.getBotId());
        }

        return ResultObject.success(stepService.queryByCondition(condition));
    }

    /**
     * 查询流程极简信息列表(仅返回名称, id, 标签, 类型)
     * @param botId botId
     * @param type 流程类型
     * @return 列表
     */
    @GetMapping("getSimpleInfoListByBotId")
    public ResultObject<List<SimpleStepInfoVO>> getSimpleInfoListByBotId(@NotNull(message = "botId不能为空") Long botId, StepTypeEnum type) {
        return ResultObject.success(stepService.getSimpleStepInfo(botId, type));
    }

    /**
     * 为流程拖动排序
     * @param request 排序请求
     * @return 排序后列表
     */
    @PostMapping("sort")
    public ResultObject<List<StepVO>> sort(@RequestBody StepSortRequestVO request) {
        return ResultObject.success(stepService.mainStepSort(request.getBotId(), request.getStepIdList()));
    }

    /**
     * 流程同步
     * @param syncVO 同步请求
     * @return 同步结果
     */
    @PostMapping(value = "/sync")
    @TenantIsolation("#syncVO.srcBotId")
    @TenantIsolation("#syncVO.targetBotIdList")
    public ResultObject<StepSyncResultVO> sync(@RequestBody StepSyncVO syncVO) {
        syncVO.setCurrentUserId(SecurityUtils.getUserId());
        return ResultObject.success(stepService.sync(syncVO));
    }

    /**
     * 批量删除
     * @param request 批量删除请求
     * @return 批量删除结果
     */
    @PostMapping("deleteByBotIdListAndStepIdList")
    @TenantIsolation("#request.botIdList")
    public ResultObject<List<StepPO>> deleteByBotIdsAndStepIdList(@RequestBody StepBatchDeleteRequest request) {
        return ResultObject.success(stepService.deleteByBotIdsAndStepIds(request.getBotIdList(), request.getStepIdList(), SecurityUtils.getUserId()));
    }
}
