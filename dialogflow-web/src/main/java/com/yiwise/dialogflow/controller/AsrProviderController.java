package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrLanguagePO;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrProviderPO;
import com.yiwise.dialogflow.service.asrmodel.AsrProviderService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/14 15:23:16
 */

@RestController
@RequestMapping("/apiBot/v3/asrProvider")
public class AsrProviderController {

    @Resource
    private AsrProviderService asrProviderService;

    @GetMapping("list")
    public ResultObject<List<AsrProviderPO>> list() {
        return ResultObject.success(asrProviderService.list());
    }

    @GetMapping("languageList")
    public ResultObject<List<AsrLanguagePO>> languageList() {
        return ResultObject.success(asrProviderService.languageList());
    }
}