package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.aop.TenantIsolation;
import com.yiwise.dialogflow.entity.po.KnowledgePO;
import com.yiwise.dialogflow.entity.vo.*;
import com.yiwise.dialogflow.entity.vo.sync.BotSyncResultVO;
import com.yiwise.dialogflow.entity.vo.sync.KnowledgeSyncVO;
import com.yiwise.dialogflow.service.KnowledgeService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 知识库
 *
 * <AUTHOR>
 */
@RestController
@Validated
@RequestMapping("apiBot/v3/knowledge")
public class KnowledgeController {

    @Resource
    private KnowledgeService knowledgeService;

    /**
     * 新增知识
     */
    @PostMapping("create")
    @TenantIsolation("#knowledge.botId")
    public ResultObject<KnowledgeVO> create(@RequestBody KnowledgePO knowledge) {
        return ResultObject.success(knowledgeService.create(knowledge, getUserId()));
    }

    /**
     * 批量创建知识
     */
    @PostMapping("/batchCreate")
    @TenantIsolation("#batchCreateVO.botId")
    public ResultObject<Void> batchCreate(@RequestBody @Valid KnowledgeBatchCreateVO batchCreateVO) {
        knowledgeService.batchCreate(batchCreateVO, SecurityUtils.getUserId());
        return ResultObject.success(null);
    }

    /**
     * 修改知识
     */
    @PostMapping("update")
    @TenantIsolation("#knowledge.botId")
    public ResultObject<KnowledgeVO> update(@RequestBody KnowledgeVO knowledge) {
        return ResultObject.success(knowledgeService.update(knowledge, getUserId()));
    }

    /**
     * 问答知识列表(包含统计数据)
     */
    @PostMapping("queryList")
    public ResultObject<PageResultObject<KnowledgeVO>> queryList(@RequestBody KnowledgeQueryVO condition) {
        condition.setTenantId(SecurityUtils.getTenantId());
        return ResultObject.success(knowledgeService.queryByCondition(condition));
    }

    /**
     * 知识库导出
     *
     * @param condition 查询参数
     * @return oss链接
     */
    @PostMapping("/export")
    public ResultObject<String> exportKnowledge(@RequestBody KnowledgeBatchOperateRequestVO condition) {
        return ResultObject.success(knowledgeService.exportKnowledge(condition));
    }

    /**
     * 导入知识
     *
     * @param excel 模板文件
     * @param botId 话术id
     * @return 错误信息链接
     */
    @PostMapping("/import")
    public ResultObject<KnowledgeImportResultVO> importKnowledge(@RequestPart MultipartFile excel, @RequestParam Long botId) {
        return ResultObject.success(knowledgeService.importKnowledge(excel, botId, SecurityUtils.getUserId()));
    }

    /**
     * 移动分组
     */
    @PostMapping("/changeGroup")
    @TenantIsolation("#condition.botId")
    public ResultObject<Void> changeGroup(@RequestBody KnowledgeChangeGroupVO condition) {
        knowledgeService.changeGroup(condition, getUserId());
        return ResultObject.success(null);
    }

    @PostMapping("batchDelete")
    @TenantIsolation("#condition.botId")
    public ResultObject<List<KnowledgePO>> batchDelete(@RequestBody KnowledgeBatchOperateRequestVO condition) {
        return ResultObject.success(knowledgeService.batchDelete(condition, getUserId()));
    }

    @PostMapping("deleteById")
    @TenantIsolation("#botId")
    public ResultObject<KnowledgePO> deleteById(@NotNull(message = "botId不能为空") Long botId, @NotBlank(message = "id不能为空") String id) {
        return ResultObject.success(knowledgeService.deleteById(botId, id, getUserId()));
    }

    @GetMapping("getById")
    public ResultObject<KnowledgeVO> getById(@NotNull(message = "botId不能为空") Long botId, @NotBlank(message = "id不能为空") String id) {
        return ResultObject.success(knowledgeService.getVOById(botId, id));
    }

    @PostMapping(value = "/sync")
    @TenantIsolation("#syncVO.srcBotId")
    @TenantIsolation("#syncVO.targetBotIdList")
    public ResultObject<BotSyncResultVO> sync(@RequestBody KnowledgeSyncVO syncVO) {
        syncVO.setCurrentUserId(SecurityUtils.getUserId());
        return ResultObject.success(knowledgeService.sync(syncVO));
    }

    @PostMapping("deleteByBotIdListAndKnowledgeIdList")
    @TenantIsolation("#request.botIdList")
    public ResultObject<List<KnowledgePO>> deleteByBotIdsAndStepIdList(@RequestBody KnowledgeBatchDeleteRequest request) {
        return ResultObject.success(knowledgeService.deleteByBotIdsAndKnowledgeIdList(request.getBotIdList(), request.getKnowledgeIdList(), SecurityUtils.getUserId()));
    }

    private Long getUserId() {
        return SecurityUtils.getUserId();
    }

}
