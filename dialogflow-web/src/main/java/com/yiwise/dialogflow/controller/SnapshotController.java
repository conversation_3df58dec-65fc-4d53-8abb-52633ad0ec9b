package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.annotation.auth.InnerOnly;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.entity.po.RobotSnapshotPO;
import com.yiwise.dialogflow.service.RobotSnapshotService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("apiBot/v3/snapshot")
public class SnapshotController {

    @Resource
    private RobotSnapshotService robotSnapshotService;

    @GetMapping("create")
    public ResultObject create(@NotNull(message = "botId不能为空") Long botId) {
        robotSnapshotService.create(botId, SecurityUtils.getUserId(), true);
        return ResultObject.success();
    }

    @GetMapping("createTextTest")
    public ResultObject createTextTest(@NotNull(message = "dialogFlowId不能为空") Long dialogFlowId) {
        return ResultObject.success(robotSnapshotService.createTextTestSnapshot(dialogFlowId));
    }

    @GetMapping("getMetaData")
    public ResultObject getMetaData(@NotNull(message = "botId不能为空") Long botId,
                                   @NotNull(message = "version不能为空") Integer version) {
        return ResultObject.success(robotSnapshotService.getBotMetaData(botId, RobotSnapshotUsageTargetEnum.TEXT_TEST, version));
    }

    @InnerOnly
    @NoLogin
    @GetMapping("getBotMetaDataByDialogFlowId")
    public ResultObject getBotMetaDataByDialogFlowId(@NotNull(message = "dialogFlowId不能为空") Long dialogFlowId,
                                   @NotNull RobotSnapshotUsageTargetEnum usageTarget,
                                   @NotNull(message = "version不能为空") Integer version) {

        return ResultObject.success(robotSnapshotService.getBotMetaDataByDialogFlowId(dialogFlowId, usageTarget, version));
    }

    @GetMapping("getLastVersion")
    public ResultObject<Integer> getLastVersion(@NotNull(message = "botId不能为空") Long botId) {
        return ResultObject.success(robotSnapshotService.getLastVersionNumber(botId, RobotSnapshotUsageTargetEnum.TEXT_TEST));
    }

    @NoLogin
    @InnerOnly
    @GetMapping("getSnapshot")
    public ResultObject<RobotSnapshotPO> getSnapshot(@NotNull Long botId, Integer version, RobotSnapshotUsageTargetEnum usageTarget) {
        return ResultObject.success(robotSnapshotService.getByVersion(botId, usageTarget, version));
    }

    @NoLogin
    @InnerOnly
    @GetMapping("getRealtimeSnapshot")
    public ResultObject<RobotSnapshotPO> getRealtimeSnapshot(@NotNull Long botId) {
        return ResultObject.success(robotSnapshotService.getRealtimeResourceSnapshot(botId));
    }

}
