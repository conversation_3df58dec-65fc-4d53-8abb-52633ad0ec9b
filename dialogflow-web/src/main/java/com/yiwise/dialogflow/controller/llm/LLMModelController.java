package com.yiwise.dialogflow.controller.llm;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.service.llm.LLMModelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 大模型模型列表
 */
@Slf4j
@RestController
@RequestMapping("/apiBot/v3/llmModel")
public class LLMModelController {

    @Resource
    private LLMModelService llmModelService;

    /**
     * 获取大模型模型列表
     * @return 大模型模型列表
     */
    @GetMapping("/list")
    public ResultObject<List<String>> getModelList() {
        return ResultObject.success(llmModelService.getModelNameList());
    }
}