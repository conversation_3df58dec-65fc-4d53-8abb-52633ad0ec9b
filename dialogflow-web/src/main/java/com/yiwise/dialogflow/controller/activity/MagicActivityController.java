package com.yiwise.dialogflow.controller.activity;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.entity.po.magic.MagicActivityConfigPO;
import com.yiwise.dialogflow.service.magic.MagicActivityConfigService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 新版轻量化活动配置
 */
@RestController
@RequestMapping("apiBot/v3/magicActivity/config")
public class MagicActivityController {
    @Resource
    private MagicActivityConfigService magicActivityConfigService;

    /**
     * 修改配置
     */
    @PostMapping("modify")
    public ResultObject modify(@RequestBody MagicActivityConfigPO config) {
        magicActivityConfigService.modify(config, SecurityUtils.getUserId());
        return ResultObject.success();
    }

    /**
     * 查询详情
     */
    @GetMapping("getDetail")
    public ResultObject<MagicActivityConfigPO> getDetail(@NotNull Long botId, @NotBlank Long activityId) {
        return ResultObject.success(magicActivityConfigService.getBotIdAndActivityId(botId, activityId).orElse(null));
    }
}
