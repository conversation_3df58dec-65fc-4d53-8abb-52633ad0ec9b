package com.yiwise.dialogflow.controller;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/7/26
 * @class <code>DataSyncController</code>
 * @see
 * @since JDK1.8
 */

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.service.DataSyncService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/apiBot/v3/sync")
public class DataSyncController {

    @Resource
    private DataSyncService dataSyncService;

    @NoLogin
    @PostMapping(value = "/start")
    public ResultObject dataSync() {
        return ResultObject.success(dataSyncService.syncSourceRef());
    }

    @NoLogin
    @PostMapping("/fixBotDependenceStep")
    public ResultObject<Void> fixBotDependenceStep() {
        dataSyncService.fixBotDependenceStep();
        return ResultObject.success(null);
    }
}
