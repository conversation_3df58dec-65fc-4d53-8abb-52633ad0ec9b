package com.yiwise.dialogflow.controller.activity;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.entity.po.MagicFormTemplatePO;
import com.yiwise.dialogflow.service.magic.MagicFormTemplateService;
import com.yiwise.dialogflow.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 轻量化表单
 */
@Slf4j
@RestController
@RequestMapping("apiBot/v3/magicForm")
public class MagicFormTemplateController {

    @Resource
    private MagicFormTemplateService magicFormTemplateService;

    @PostMapping("save")
    public ResultObject<MagicFormTemplatePO> save(@RequestBody MagicFormTemplatePO template) {
        return ResultObject.success(magicFormTemplateService.create(template, getUserId()));
    }

    @PostMapping("saveAndPublish")
    public ResultObject<MagicFormTemplatePO> saveAndPublish(@RequestBody MagicFormTemplatePO template) {
        return ResultObject.success(magicFormTemplateService.createAndPublish(template, getUserId()));
    }

    @GetMapping("getLast")
    public ResultObject<MagicFormTemplatePO> getLast(Long botId) {
        return ResultObject.success(magicFormTemplateService.getLastMagicFormTemplate(botId));
    }

    private Long getUserId() {
        return SecurityUtils.getUserId();
    }
}
