package com.yiwise.dialogflow.schedule;

import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.entity.enums.AsrSourceConcurrencyRecordTypeEnum;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrSourceConcurrencyRecordPO;
import com.yiwise.dialogflow.service.asrmodel.AsrSourceConcurrencyRecordService;
import com.yiwise.middleware.redis.service.RedisOpsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Map;
import java.util.Objects;

/**
 * 定时从redis中获取热词组和自学习模型的调用次数，存入mongodb
 *
 * <AUTHOR>
 * @date 2022/11/3 17:20:16
 */
@Component
@EnableScheduling
public class AsrSourceConcurrencyScheduler {
    private static final Logger logger = LoggerFactory.getLogger(AsrSourceConcurrencyScheduler.class);

    private String aliSelfKey = RedisKeyCenter.getAliSelfCountKey();
    private String aliVocabKey = RedisKeyCenter.getAliVocabCountKey();
    private String tencentSelfKey = RedisKeyCenter.getTencentSelfCountKey();
    private String tencentVocabKey = RedisKeyCenter.getTencentVocabCountKey();
    private String asrSourceScheduleLockKey = RedisKeyCenter.getAsrSourceScheduleLockKey();

    @Resource
    private RedisOpsService redisOpsService;

    @Resource
    private AsrSourceConcurrencyRecordService asrSourceConcurrencyRecordService;

    @Scheduled(cron = "* */30 * * * ?")
    private void task() {
        try {
            if (redisOpsService.setIfAbsent(asrSourceScheduleLockKey, System.currentTimeMillis(), 10000)) {
                logger.debug("asr资源调用统计开始");
                Map<Object, Object> aliSelfMap = redisOpsService.getGroup(aliSelfKey);
                Map<Object, Object> tencentSelfMap = redisOpsService.getGroup(this.tencentSelfKey);
                Map<Object, Object> aliVocabMap = redisOpsService.getGroup(this.aliVocabKey);
                Map<Object, Object> tencentVocabMap = redisOpsService.getGroup(this.tencentVocabKey);
                if (Objects.nonNull(aliSelfMap)) {
                    aliSelfMap.forEach((k, v) -> {
                        if (Integer.parseInt(v.toString()) != 0) {
                            save(Long.parseLong(k.toString()), Integer.parseInt(v.toString()), AsrSourceConcurrencyRecordTypeEnum.ASR_SELF_LEARNING);
                            redisOpsService.setGroupKeyValue(aliSelfKey, k, 0);
                        }
                    });
                }
                if (Objects.nonNull(tencentSelfMap)) {
                    tencentSelfMap.forEach((k, v) -> {
                        if (Integer.parseInt(v.toString()) != 0) {
                            save(Long.parseLong(k.toString()), Integer.parseInt(v.toString()), AsrSourceConcurrencyRecordTypeEnum.ASR_SELF_LEARNING);
                            redisOpsService.setGroupKeyValue(tencentSelfKey, k, 0);
                        }
                    });
                }
                if (Objects.nonNull(aliVocabMap)) {
                    aliVocabMap.forEach((k, v) -> {
                        if (Integer.parseInt(v.toString()) != 0) {
                            save(Long.parseLong(k.toString()), Integer.parseInt(v.toString()), AsrSourceConcurrencyRecordTypeEnum.ASR_VOCOB);
                            redisOpsService.setGroupKeyValue(aliVocabKey, k, 0);
                        }
                    });
                }
                if (Objects.nonNull(tencentVocabMap)) {
                    tencentVocabMap.forEach((k, v) -> {
                        if (Integer.parseInt(v.toString()) != 0) {
                            save(Long.parseLong(k.toString()), Integer.parseInt(v.toString()), AsrSourceConcurrencyRecordTypeEnum.ASR_VOCOB);
                            redisOpsService.setGroupKeyValue(tencentVocabKey, k, 0);
                        }
                    });
                }
            }
            logger.debug("asr资源调用统计结束");
        } catch (Exception e) {
            logger.error("热词组、自学习模型调用次数统计错误：{}", e);
        } finally {
            redisOpsService.delete(asrSourceScheduleLockKey);
        }
    }

    private void save(Long sourceId, Integer count, AsrSourceConcurrencyRecordTypeEnum type) {
        AsrSourceConcurrencyRecordPO aliSelfPO = new AsrSourceConcurrencyRecordPO();
        aliSelfPO.setSourceId(sourceId);
        aliSelfPO.setCount(count);
        aliSelfPO.setSourceType(type);
        aliSelfPO.setCreateTime(LocalDate.now());
        aliSelfPO.setUpdateTime(LocalDate.now());
        asrSourceConcurrencyRecordService.save(aliSelfPO);
    }
}