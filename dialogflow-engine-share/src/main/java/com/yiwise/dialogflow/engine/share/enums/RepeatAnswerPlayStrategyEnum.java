package com.yiwise.dialogflow.engine.share.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * 重复答案时, 录音播放策略
 * <AUTHOR>
 */
public enum RepeatAnswerPlayStrategyEnum implements CodeDescEnum {
    REPLAY(0, "重播"),
    RESUME(1, "续播"),
    // 用于流式答案
    APPEND(2, "追加到当前答案")
    ;

    RepeatAnswerPlayStrategyEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    Integer code;
    String desc;

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
