package com.yiwise.dialogflow.engine.share;

import com.yiwise.dialogflow.engine.share.response.AddWhiteListBO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class IntentLevelAnalysisResult {

    Integer intentLevel;

    String basis;

    private Set<String> customerFocus;

    private Integer aiRounds;

    private int declineCount = 0;

    private Set<String> customerAttribute;

    private boolean skip = false;

    /**
     * 命中开发科次数
     */
    private int definiteCount = 0;
    /**
     * 业务问题次数
     */
    private int businessQuestionCount = 0;
    /**
     * 命中否定次数
     */
    private int negativeCount = 0;

    private Map<Integer, Map<Long, String>> extraInfoMapId;

    /**
     * 命中的意向规则id, 如果不是用户自定义意向规则计算的结果, 则为null
     */
    private String matchedIntentRuleId;

    /**
     * 意向等级结果匹配信息, 来自节点还是规则等
     */
    private IntentLevelRuleMatchInfo intentLevelRuleMatchInfo;

    /**
     * 短信模板
     */
    private Set<Long> smsTemplateIds;

    /**
     * 加黑名单
     */
    private List<AddWhiteListBO> addWhiteListGroupIdList;

    /**
     * 打标签
     */
    private Set<Long> customerLevelTagDetailIdSet;

    /**
     * 意向动作规则信息
     */
    private List<IntentRuleActionResult> intentRuleActionResultList;

    BotBusiMetrics botBusiMetrics;

    /**
     * 所有被引用的动态变量的值, key: 变量名称, value: 变量收集到的值
     * 如果动态变量未采集到值, 则值为空字符串
     */
    Map<String, String> dynamicVariableValueMap;

    /**
     * 同步到客户属性的变量值, key: 客户属性id, value: 变量收集到的值
     */
    Map<Long, List<String>> customerAttributeValueMap;

    /**
     * 宜信定制化需求之 挂机点
     * 需求: https://k3465odso4.feishu.cn/wiki/U5G5wMBHSi1IqkktAnVcOgtZnic
     */
    String hangupOn;

    /**
     * 宜信定制化需求之 命中意图名称列表
     * 需求: https://k3465odso4.feishu.cn/wiki/U5G5wMBHSi1IqkktAnVcOgtZnic
     */
    List<String> yixinMatchIntentNameList;
}