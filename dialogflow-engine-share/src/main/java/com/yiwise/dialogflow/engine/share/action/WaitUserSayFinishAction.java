package com.yiwise.dialogflow.engine.share.action;

import com.yiwise.dialogflow.engine.share.enums.ActionScopeEnum;
import com.yiwise.dialogflow.engine.share.enums.ActionTypeEnum;
import lombok.Data;

@Data
public class WaitUserSayFinishAction extends ChatAction{

    int waitUserSayFinishMs;

    public WaitUserSayFinishAction() {
        this(0);
    }

    public WaitUserSayFinishAction(int waitUserSayFinishMs) {
        super(ActionTypeEnum.WAIT_USER_SAY_FINISH, ActionScopeEnum.INTERACTION);
        this.waitUserSayFinishMs = waitUserSayFinishMs;
    }
}
