package com.yiwise.dialogflow.engine.share.response;

import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import lombok.Data;

import java.util.Objects;

/**
 * 答案文本的位置信息, 比如这个答案是位于问答知识的, 还是位于流程节点中的,
 * <AUTHOR>
 */
@Data
public class AnswerLocateBO {
    AnswerSourceEnum answerSource;
    String answerId;
    String answerLabel;

    /**
     * 答案在原答案列表(answerList) 中的index
     */
    Integer index;

    /**
     * 同一个节点内, 答案组index, 主要是信息采集节点, 一个问题下面的多个答案是一个组的
     */
    Integer groupIndex;

    String stepName;
    String stepLabel;
    String stepId;

    String nodeName;
    String nodeId;
    String nodeLabel;

    String knowledgeId;
    String knowledgeName;
    String knowledgeLabel;

    String specialAnswerConfigName;
    String specialAnswerConfigId;
    String specialAnswerConfigLabel;
    public String getDisplayName() {
        if (Objects.isNull(answerSource)) {
            return null;
        }
        switch (answerSource) {
            case KNOWLEDGE:
                return knowledgeName;
            case STEP:
                return stepName;
            case SPECIAL_ANSWER:
                return specialAnswerConfigName;
            default:
                return null;
        }
    }
    public String getDisplayLabelName() {
        if (Objects.isNull(answerSource)) {
            return null;
        }
        switch (answerSource) {
            case KNOWLEDGE:
                return knowledgeLabel + ":" + knowledgeName;
            case STEP:
                return nodeLabel + ":" +nodeName;
            case SPECIAL_ANSWER:
                return specialAnswerConfigLabel + ":" +specialAnswerConfigName;
            default:
                return null;
        }
    }

    public String getErrorLocateInfo() {
        if (Objects.isNull(answerSource)) {
            return null;
        }
        switch (answerSource) {
            case STEP:
                return nodeName + "[" + nodeLabel +"]";
            case KNOWLEDGE:
                return knowledgeName + "[" + knowledgeLabel + "]";
            case SPECIAL_ANSWER:
                return specialAnswerConfigName + "[" + specialAnswerConfigLabel + "]";
            default:
                return null;
        }
    }

}
