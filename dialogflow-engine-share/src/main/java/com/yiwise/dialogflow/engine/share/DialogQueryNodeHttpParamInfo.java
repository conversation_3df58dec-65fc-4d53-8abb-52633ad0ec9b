package com.yiwise.dialogflow.engine.share;

import com.yiwise.dialogflow.engine.share.enums.QueryNodeHttpVarTypeEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DialogQueryNodeHttpParamInfo implements Serializable {

    /**
     * 参数名
     */
    @NotBlank(message = "参数名不能为空")
    String name;

    /**
     * 变量类型
     */
    @NotNull(message = "变量类型不能为空")
    QueryNodeHttpVarTypeEnum variableType;

    /**
     * 变量id或者常量值
     */
    @NotBlank(message = "变量值不能为空")
    String value;
}
