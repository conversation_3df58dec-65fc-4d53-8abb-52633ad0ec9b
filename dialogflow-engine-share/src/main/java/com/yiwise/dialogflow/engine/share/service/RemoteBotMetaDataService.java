package com.yiwise.dialogflow.engine.share.service;

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.engine.share.BotMetaData;
import com.yiwise.dialogflow.engine.share.MagicActivityConfig;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.engine.share.model.BotAsrConfig;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;

public interface RemoteBotMetaDataService {
    @NoLogin
    @GetMapping("getLastVersionByDialogFlowId")
    ResultObject<Integer> getLastVersionByDialogFlowId(@RequestParam("dialogFlowId") Long dialogFlowId,
                                                      @RequestParam("usageTarget") @NotNull RobotSnapshotUsageTargetEnum usageTarget);

    @NoLogin
    @GetMapping("getBotMetaData")
    ResultObject<BotMetaData> getBotMetaData(@RequestParam("dialogFlowId") Long dialogFlowId,
                                            @RequestParam("usageTarget") RobotSnapshotUsageTargetEnum usageTarget,
                                            @RequestParam("version") Integer version);

    @NoLogin
    @GetMapping("getRealtimeAsrConfig")
    ResultObject<BotAsrConfig> getRealAsrConfig(@RequestParam("botId") Long botId,
                                               @RequestParam("usageTarget") RobotSnapshotUsageTargetEnum usageTarget,
                                               @RequestParam("version") Integer version);

    @NoLogin
    @GetMapping("getMagicActivityAudioConfig")
    ResultObject<MagicActivityConfig> getMagicActivityConfig(@RequestParam("botId") Long botId,
                                                             @RequestParam("usageTarget") RobotSnapshotUsageTargetEnum usageTarget,
                                                             @RequestParam("version") Integer version,
                                                             @RequestParam("callJobId") Long callJobId);

}
