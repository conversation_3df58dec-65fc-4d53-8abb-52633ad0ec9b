package com.yiwise.dialogflow.engine.share.request;

import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

/**
 * 用户输入
 * 仅用于判断意图，不进行状态变更
 *
 * <AUTHOR>
 * @date 2022/6/7
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PROTECTED)
public class UserSayEvent extends EventParam {

    String inputText;

    /**
     * 断句补齐时, 内容为补齐前当前的输入内容, 如果非断句补齐, 则和inputText一致
     */
    String originInputText;

    Long startOffset = 0L;

    Long endOffset = 0L;

    Double playProgress;

    Boolean isMergeInput;

    /**
     * 放开打断, 不进入不可打断状态
     */
    Boolean enableInterrupt;

    /**
     * 最后正在播放的答案id
     * lastAnswerId
     */
    private String lastAnswerId;

    /**
     * 最后一个答案文本内容
     */
    private String lastAnswerPlayedContent;

    public UserSayEvent() {
        setEvent(ChatEventTypeEnum.USER_SAY);
    }
}
