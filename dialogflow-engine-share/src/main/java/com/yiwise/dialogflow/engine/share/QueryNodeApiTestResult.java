package com.yiwise.dialogflow.engine.share;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;

import java.io.Serializable;
import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class QueryNodeApiTestResult implements Serializable {

    /**
     * 接口是否超时
     */
    Boolean isTimeout;

    /**
     * 接口调用耗时
     */
    Long costTime;

    /**
     * 状态码
     */
    Integer httpStatusCode;

    /**
     * 响应体
     */
    String body;

    /**
     * <动态变量id，jsonpath读取到的值>
     */
    Map<String, String> varIdValueMap;

    private QueryNodeApiTestResult(Boolean isTimeout, Integer httpStatusCode, String body, Map<String, String> varIdValueMap) {
        this.isTimeout = isTimeout;
        this.httpStatusCode = httpStatusCode;
        this.body = body;
        this.varIdValueMap = varIdValueMap;
    }

    public static QueryNodeApiTestResult timeout(String errorVarId) {
        return new QueryNodeApiTestResult(true, null, null, buildErrorMap(errorVarId, "请求超时"));
    }

    public static QueryNodeApiTestResult success(String body, Map<String, String> varIdValueMap) {
        return new QueryNodeApiTestResult(false, HttpStatus.OK.value(), body, varIdValueMap);
    }

    public static QueryNodeApiTestResult fail(HttpStatus httpStatus, String body, String errorVarId) {
        return new QueryNodeApiTestResult(false, httpStatus.value(), body, buildErrorMap(errorVarId, "请求失败"));
    }

    public static QueryNodeApiTestResult error(String body, String errorVarId) {
        return new QueryNodeApiTestResult(false, null, body, buildErrorMap(errorVarId, "请求失败"));
    }

    private static Map<String, String> buildErrorMap(String errorVarId, String errorMsg) {
        return StringUtils.isBlank(errorVarId) ? Collections.emptyMap() : Collections.singletonMap(errorVarId, errorMsg);
    }

    public QueryNodeApiTestResult cost(Long costTime) {
        this.costTime = costTime;
        return this;
    }
}
