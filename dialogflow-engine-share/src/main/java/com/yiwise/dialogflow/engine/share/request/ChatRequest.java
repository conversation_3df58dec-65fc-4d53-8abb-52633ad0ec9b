package com.yiwise.dialogflow.engine.share.request;

import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ChatRequest {
    Long botId;

    RobotSnapshotUsageTargetEnum usageTarget;

    Integer version;

    String sessionId;

    /**
     * 日志id
     */
    String logId;

    /**
     * 请求来源
     */
    String requestHostName;

    String requestHostIp;

    Integer sequence;

    EventParam param;

    String sessionContextJson;

    String preSessionContextJson;
}
