package com.yiwise.dialogflow.engine.share.request;

import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class KeyCaptureSuccessEvent extends EventParam {

    private String result;

    public KeyCaptureSuccessEvent(String result) {
        setEvent(ChatEventTypeEnum.KEY_CAPTURE_SUCCESS);
        this.result = result;
    }
}
