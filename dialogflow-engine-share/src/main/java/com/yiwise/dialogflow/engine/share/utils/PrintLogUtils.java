package com.yiwise.dialogflow.engine.share.utils;

import javaslang.Tuple2;

import java.util.*;

public class PrintLogUtils {


    public static void stringCollectionToPrintLog(StringBuilder sb, Collection<String> collection) {
        if (collection == null) {
            sb.append("null");
            return;
        }
        if (collection.isEmpty()) {
            sb.append("[]");
            return;
        }
        sb.append("[\"");
        Iterator<String> iterator = collection.iterator();
        while (iterator.hasNext()) {
            sb.append(iterator.next());
            if (iterator.hasNext()) {
                sb.append("\", \"");
            } else {
                sb.append("\"");
            }
        }
        sb.append("]");
    }

    public static void longCollectionToPrintLog(StringBuilder sb, Collection<Long> collection) {
        if (collection == null) {
            sb.append("null");
            return;
        }
        if (collection.isEmpty()) {
            sb.append("[]");
            return;
        }
        sb.append("[");
        Iterator<Long> iterator = collection.iterator();
        while (iterator.hasNext()) {
            sb.append(iterator.next());
            if (iterator.hasNext()) {
                sb.append(", ");
            }
        }
        sb.append("]");
    }


    public static void stringNumberMapPrintLog(StringBuilder sb, Map<String, ? extends Number> map) {
        if (Objects.isNull(map)) {
            sb.append("null");
            return;
        }
        if (map.isEmpty()) {
            sb.append("{}");
            return;
        }
        sb.append("{\"");
        Iterator<? extends Map.Entry<String, ? extends Number>> iterator = map.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, ? extends Number> entry = iterator.next();
            sb.append(entry.getKey()).append("\": ").append(entry.getValue());
            if (iterator.hasNext()) {
                sb.append(", \"");
            }
        }
        sb.append("}");
    }

    public static void numberStringMapPrintLog(StringBuilder sb, Map<? extends Number, String> map) {
        if (Objects.isNull(map)) {
            sb.append("null");
            return;
        }
        if (map.isEmpty()) {
            sb.append("{}");
            return;
        }
        sb.append("{");
        Iterator<? extends Map.Entry<? extends Number, String>> iterator = map.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry< ? extends Number, String> entry = iterator.next();
            sb.append(entry.getKey()).append(": \"").append(entry.getValue());
            if (iterator.hasNext()) {
                sb.append("\", ");
            } else {
                sb.append("\"");
            }
        }
        sb.append("}");
    }

    public static void numberStringTuplePrintLog(StringBuilder sb, Tuple2<? extends Number, String> tuple) {
        if (Objects.isNull(tuple)) {
            sb.append("null");
            return;
        }
        sb.append("{\"").append(tuple._1).append("\": \"").append(tuple._2).append("\"}");
        return;
    }

    public static void stringMapToPrintLog(StringBuilder sb, Map<String, String> map) {
        if (Objects.isNull(map)) {
            sb.append("null");
            return;
        }
        if (map.isEmpty()) {
            sb.append("{}");
            return;
        }

        sb.append("{\"");
        Iterator<Map.Entry<String, String>> iterator = map.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            sb.append(entry.getKey()).append("\": \"").append(entry.getValue());
            if (iterator.hasNext()) {
                sb.append("\", \"");
            } else {
                sb.append("\"");
            }
        }
        sb.append("}");
        return;
    }

    public static void stringListMapToPrintLog(StringBuilder sb, Map<String, List<String>> map) {
        if (Objects.isNull(map)) {
            sb.append("null");
            return;
        }
        if (map.isEmpty()) {
            sb.append("{}");
            return;
        }

        sb.append("{\"");
        Iterator<Map.Entry<String, List<String>>> iterator = map.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, List<String>> entry = iterator.next();
            sb.append(entry.getKey()).append("\": ");
            stringCollectionToPrintLog(sb, entry.getValue());
            if (iterator.hasNext()) {
                sb.append(", \"");
            }
        }
        sb.append("}");
    }

}
