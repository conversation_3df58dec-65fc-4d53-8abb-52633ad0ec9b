package com.yiwise.dialogflow.engine.share.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 * @date 2022/5/30
 */
public enum QueryNodeHttpVarTypeEnum implements CodeDescEnum {

    /**
     * 自定义变量
     */
    CUSTOM(1,"自定义变量"),

    /**
     * 动态变量
     */
    DYNAMIC(2, "动态变量"),

    /**
     * 常量
     */
    CONSTANT(3,"常量"),
    ;

    private final int code;
    private final String desc;

    QueryNodeHttpVarTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    public static boolean isCustomVariable(QueryNodeHttpVarTypeEnum varType) {
        return CUSTOM.equals(varType);
    }

    public static boolean isDynamicVariable(QueryNodeHttpVarTypeEnum varType) {
        return DYNAMIC.equals(varType);
    }

    public static boolean isConstant(QueryNodeHttpVarTypeEnum varType) {
        return CONSTANT.equals(varType);
    }

    public static boolean isVariable(QueryNodeHttpVarTypeEnum varType) {
        return isCustomVariable(varType) || isDynamicVariable(varType);
    }
}
