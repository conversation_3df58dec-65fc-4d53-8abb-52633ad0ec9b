package com.yiwise.dialogflow.engine.share.action;

import com.yiwise.dialogflow.engine.share.enums.ActionScopeEnum;
import com.yiwise.dialogflow.engine.share.enums.ActionTypeEnum;

/**
 * 用来通知客户端, 当前的输入需要过滤(在断句补齐的时候过滤掉)
 */
public class IgnoreInputAction extends ChatAction {
    public IgnoreInputAction() {
        setScope(ActionScopeEnum.INTERACTION);
        setType(ActionTypeEnum.IGNORE_INPUT);
    }

    public static IgnoreInputAction of() {
        return new IgnoreInputAction();
    }
}
