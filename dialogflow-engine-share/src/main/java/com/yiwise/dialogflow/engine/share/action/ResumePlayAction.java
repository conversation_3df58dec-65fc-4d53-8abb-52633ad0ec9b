package com.yiwise.dialogflow.engine.share.action;

import com.yiwise.dialogflow.engine.share.enums.ActionScopeEnum;
import com.yiwise.dialogflow.engine.share.enums.ActionTypeEnum;
import lombok.Data;

@Data
public class ResumePlayAction extends ChatAction {

    /**
     * 需要恢复的答案 id
     */
    String answerId;

    public ResumePlayAction() {
        setScope(ActionScopeEnum.INTERACTION);
        setType(ActionTypeEnum.PAUSE_PLAY);
    }

    public ResumePlayAction(String answerId) {
        this();
        this.answerId = answerId;
    }
}
