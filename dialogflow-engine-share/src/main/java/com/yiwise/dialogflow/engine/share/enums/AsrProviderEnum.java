package com.yiwise.dialogflow.engine.share.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 * @date 2020/8/25
 */
public enum AsrProviderEnum implements CodeDescEnum {
    ALI(0, "阿里"),
    YIWISE(1, "一知"),
    TENCENT(2, "腾讯"),
    ALI_MCOS(3, "阿里云模型服务"),
    ALI_LLM(4, "阿里云大模型")
    ;
    Integer code;
    String desc;

    AsrProviderEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
