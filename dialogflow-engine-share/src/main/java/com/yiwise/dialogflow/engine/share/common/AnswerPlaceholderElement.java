package com.yiwise.dialogflow.engine.share.common;

import com.yiwise.base.common.text.TextPlaceholderElement;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnswerPlaceholderElement extends TextPlaceholderElement {

    /**
     * 前端需要的
     */
    String id;
    /**
     * 对应的录音地址
     */
    String url;

    String fullUrl;
    /**
     * 真实的渲染结果, 如果是text类型, 则和value值一样, 如果是变量, 则渲染为真实的变量值
     */
    String realValue;

    Integer volume;

    Integer duration;

    String label;
    /*
     * 是否是动态变量
     */
    boolean dynamicVariable;

    public Boolean getCompleted() {
        return StringUtils.isNoneBlank(url);
    }
}
