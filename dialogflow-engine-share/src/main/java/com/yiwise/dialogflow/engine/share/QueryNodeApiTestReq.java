package com.yiwise.dialogflow.engine.share;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yiwise.dialogflow.engine.share.serializer.HttpMethodSerializer;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.http.HttpMethod;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class QueryNodeApiTestReq implements Serializable {

    /**
     * 请求方式,GET POST
     */
    @NotNull(message = "请求方式不能为空")
    @JsonSerialize(using = HttpMethodSerializer.class)
    HttpMethod httpMethod;

    /**
     * 接口地址
     */
    @NotBlank(message = "接口地址不能为空")
    String url;

    String apiType;

    String builtInApiName;

    /**
     * query
     */
    @Valid
    List<DialogQueryNodeHttpParamInfo> queryList;

    /**
     * header
     */
    @Valid
    List<DialogQueryNodeHttpParamInfo> headerList;

    /**
     * body
     */
    String body;

    /**
     * <动态变量id,jsonPath表达式>
     */
    Map<String, String> resMap;

    /**
     * 记录失败或者超时结果的动态变量id
     */
    String errorVarId;

    /**
     * 超时时间，单位秒
     */
    @NotNull(message = "超时时间不能为空")
    Integer timeout;

    /**
     * <变量id,变量值>, 用于替换query header中的变量id
     */
    Map<String, String> varIdValueMap;

    /**
     * <变量名,变量值>，用于替换body中的变量名
     */
    Map<String, String> varNameValueMap;
}
