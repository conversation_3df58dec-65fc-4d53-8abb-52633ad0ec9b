package com.yiwise.dialogflow.engine.share.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderElement;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.engine.share.model.KeyCaptureConfig;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AnswerResult {
    String id;

    String label;

    String template;

    String realAnswer;

    /**
     * 当前答案是否不可打断
     */
    Boolean uninterrupted;
    /**
     * 当前答案打断比例
     */
    Integer customInterruptThreshold;

    /*
     * 答案按变量分割的元素列表
     */
    List<AnswerPlaceholderElement> answerElementList;

    AnswerLocateBO locate;

    AnswerSourceEnum answerSource;

    /**
     * 在不可打断时, 尝试是否可以命中允许打断的问答知识/流程, 1.4上线的需求
     */
    Boolean needTryReplyOnUninterrupted;

    /**
     * 按键采集配置
     */
    KeyCaptureConfig keyCaptureConfig;

    /**
     * 答案是否完成, 普通的对话答案是已完成了的, 大模型的引导话术和中间结果, 则是 false
     */
    Boolean isCompleted = true;
}
