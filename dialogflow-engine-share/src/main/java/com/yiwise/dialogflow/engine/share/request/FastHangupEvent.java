package com.yiwise.dialogflow.engine.share.request;

import com.yiwise.dialogflow.engine.share.enums.ChatEventTypeEnum;
import lombok.Data;

/**
 * 快速挂断, 交互层快速挂断触发时, 需要由对话层判断执行什么动作(挂机, 还是播放答案)
 * <AUTHOR>
 */
@Data
public class FastHangupEvent extends EventParam {
    public FastHangupEvent() {
        setEvent(ChatEventTypeEnum.FAST_HANGUP);
    }

    Double playProgress;

    String reason;

    String inputText;
}
