create table var_value_tts_replace (
                                       var_value_tts_replace_id bigint(11) not null auto_increment comment 'id',
                                       var_name varchar(128) not null comment '变量名',
                                       var_value varchar(128) not null comment '变量值',
                                       replacement text not null comment '替换值',
                                       tenant_id bigint(11) not null comment '租户id',
                                       tts_provider tinyint not null comment 'tts供应商',
                                       create_user_id bigint(11) not null default 0 comment '创建用户id',
                                       update_user_id bigint(11) not null default 0 comment '更新用户id',
                                       create_time timestamp not null default current_timestamp comment '创建时间',
                                       update_time timestamp not null default current_timestamp on update current_timestamp comment '更新时间',
                                       primary key (var_value_tts_replace_id),
                                       unique index uni_idx_tenantid_ttsprovider_varname_varvalue(tenant_id, tts_provider, var_name, var_value)
) engine=InnoDB default charset=utf8mb4;