CREATE TABLE IF NOT EXISTS `session_detail_intent_info`
(
    `session_detail_intent_info_id` bigint                              NOT NULL AUTO_INCREMENT COMMENT '唯一主键',
    `bot_id`                        bigint                              NOT NULL COMMENT '话术ID',
    `session_id`                    varchar(64)                         NOT NULL COMMENT '会话ID',
    `seq`                           int                                 NOT NULL COMMENT '序号',
    `user_input`                    varchar(512)                        NULL COMMENT '用户输入',
    `intent_id`                     varchar(64)                         NULL COMMENT '意图ID',
    `intent_name`                   varchar(64)                         NULL COMMENT '意图名称',
    create_time                     timestamp default CURRENT_TIMESTAMP not null,
    update_time                     timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    PRIMARY KEY (session_detail_intent_info_id) USING BTREE,
    index idx (session_id, seq) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
    COMMENT = '通话详情意图命中记录'
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

INSERT INTO `spring_batch_job_type` (`spring_batch_job_type_id`, `description`, `file_type`, `job_count_step_name`,
                                     `name`)
VALUES (501, '自定义语义分析导出', 1, 'semanticAnalysisDetailExportStep', 'SEMANTIC_ANALYSIS_DETAIL_EXPORT_STEP');