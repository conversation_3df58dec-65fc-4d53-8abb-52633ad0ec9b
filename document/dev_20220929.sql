CREATE TABLE IF NOT EXISTS `folder`
(
    `folder_id`        bigint(20)                                NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `name`             varchar(20)                               NOT NULL COMMENT '文件夹名称',
    `tenant_id`        bigint(20)                                NOT NULL COMMENT '租户ID',
    `parent_folder_id` bigint(20) COMMENT '父文件夹ID',
    `depth`            integer                                   NOT NULL COMMENT '深度',
    create_user_id     bigint unsigned default 0                 not null,
    update_user_id     bigint unsigned default 0                 not null,
    create_time        timestamp       default CURRENT_TIMESTAMP not null,
    update_time        timestamp       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    PRIMARY KEY (folder_id) USING BTREE,
    INDEX tid (tenant_id) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
    COMMENT = '话术文件夹'
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `bot_folder_rel`
(
    `bot_folder_rel_id` bigint(20)                                NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `tenant_id`         bigint(20)                                NOT NULL COMMENT '租户ID',
    `bot_id`            bigint(20)                                NOT NULL COMMENT '话术ID',
    `folder_id`         bigint(20)                                NOT NULL COMMENT '文件夹ID',
    create_user_id      bigint unsigned default 0                 not null,
    update_user_id      bigint unsigned default 0                 not null,
    create_time         timestamp       default CURRENT_TIMESTAMP not null,
    update_time         timestamp       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    PRIMARY KEY (bot_folder_rel_id) USING BTREE,
    UNIQUE INDEX uid (tenant_id, bot_id) USING BTREE,
    INDEX fid (folder_id) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
    COMMENT = '话术文件夹关联表'
  CHARACTER SET = utf8
  COLLATE = utf8_general_ci
  ROW_FORMAT = Dynamic;