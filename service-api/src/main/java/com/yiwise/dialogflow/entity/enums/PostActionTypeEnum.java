package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 */

public enum PostActionTypeEnum implements CodeDescEnum {
    HANG_UP(0, "挂机"),
    WAIT(1, "等待用户应答"),
    ORIGINAL_STEP(2, "回到原主流程"),
    SPECIFIED_STEP(3, "跳转到指定主流程"),
    HUMAN_SERVICE(4, "转人工"),
    ;

    final Integer code;
    final String desc;

    PostActionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
