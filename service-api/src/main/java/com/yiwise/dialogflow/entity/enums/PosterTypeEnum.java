package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/9/5
 * @class <code>PosterTypeEnum</code>
 * @see
 * @since JDK1.8
 */
public enum PosterTypeEnum implements CodeDescEnum {
    YIWISE(1, "一知版"),
    COMMON(2, "通用版");

    Integer code;
    String desc;

    PosterTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
