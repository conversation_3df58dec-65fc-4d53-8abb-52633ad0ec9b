package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 * @date 2018 10 30
 * @Description 审核状态枚举
 */
public enum AuditStatusEnum implements CodeDescEnum {
    ALL(0, "全选"),
    DRAFT(1, "待发布"),
    PENDING(2, "待审核"),
    PASS(3, "已审核"),
    FAIL(4, "审核不通过"),
    INVALID(5, "校验不通过")
    ;

    private Integer code;
    private String desc;

    AuditStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
