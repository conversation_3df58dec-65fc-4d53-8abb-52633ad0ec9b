package com.yiwise.dialogflow.entity.po.llm;

import com.yiwise.dialogflow.entity.enums.llm.DocumentSplitMethodEnum;
import com.yiwise.dialogflow.entity.enums.llm.DocumentSplitTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 文档的拆分配置
 */
@Data
public class DocumentSplitConfigPO implements Serializable {

    /**
     * 段落最大字数
     */
    private Integer segmentMaxWords;

    /**
     * 拆分方式
     */
    private DocumentSplitMethodEnum splitMethod;

    /**
     * 分段类型
     */
    @NotNull(message = "splitType不能为空")
    private DocumentSplitTypeEnum splitType;
}