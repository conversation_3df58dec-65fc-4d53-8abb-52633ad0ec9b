package com.yiwise.dialogflow.entity.vo.node;

import com.yiwise.dialogflow.entity.enums.NodeTypeEnum;
import com.yiwise.dialogflow.entity.po.DialogBaseNodePO;
import com.yiwise.dialogflow.entity.vo.SimpleIntentVO;
import com.yiwise.dialogflow.entity.vo.stats.NodeIntentBranchStatsVO;
import com.yiwise.dialogflow.entity.vo.stats.NodeStatsVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DialogBaseNodeVO extends DialogBaseNodePO {

    private Boolean isRoot;

    /**
     * 兼容前端, 前端需要这个字段
     */
    public NodeTypeEnum getCategory() {
        return getType();
    }

    private List<NodeIntentBranchStatsVO> intentInfoList = new ArrayList<>();

    /**
     * 当前bot所有节点引用的意图列表
     */
    private List<SimpleIntentVO> allNodeRefIntentList = new ArrayList<>();

    private NodeStatsVO statsInfo = new NodeStatsVO();

    /**
     * 节点下的所有话术是否都已录音
     */
    private Boolean audioCompleted;

    /**
     * 算法标签
     */
    private String algorithmLabel;
}
