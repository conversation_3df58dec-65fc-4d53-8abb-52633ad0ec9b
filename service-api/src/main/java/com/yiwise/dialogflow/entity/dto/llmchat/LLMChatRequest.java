package com.yiwise.dialogflow.entity.dto.llmchat;

import com.yiwise.dialogflow.api.dto.response.BotInfo;
import com.yiwise.dialogflow.engine.share.model.SimpleChatHistory;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

@Data
public class LLMChatRequest {

    /**
     * 用户输入
     */
    String query;

    /**
     * 对话历史。默认为空。
     */
    List<SimpleChatHistory> history = new ArrayList<>();

    /**
     * 话术 id
     */
    String dialogFlowId;

    /**
     * 环境名称
     */
    String envName;

    /**
     * rag 文档 id 列表
     */
    List<String> fileIdList = new ArrayList<>();

    /**
     * 系统提示词, 如果是自由配置流程, 则把提示词通过该参数传给算法
     */
    String system = "";

    /**
     * 对话任务配置, 如果是采集任务型流程, 则通过 config 传参
     */
    LLMChatConfig config = new LLMChatConfig();

    /**
     * 配置的收集信息
     */
    List<LLMChatCollectInfo> collectInfo = new ArrayList<>();

    /**
     * 工具列表
     */
    List<LLMChatTool> tools = new ArrayList<>();

    /**
     * 是否流式响应, 默认为 true
     */
    boolean stream = true;

    /**
     * 追踪信息
     */
    String trackInfo = "";

    String roleDescription;

    String background;

    boolean usePrefillResponse;

    BotInfo botInfo;

    String callLogId;

    Integer timeToLive;

    List<String> contextIds;

    /**
     * 大模型模型名称
     */
    String modelName;

    /**
     * 会话 id
     */
    String sessionId;

    public String getModelName() {
        if (StringUtils.isBlank(modelName)) {
            modelName = "基础版";
        }
        return modelName;
    }
}
