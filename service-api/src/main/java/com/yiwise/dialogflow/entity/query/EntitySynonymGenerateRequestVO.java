package com.yiwise.dialogflow.entity.query;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EntitySynonymGenerateRequestVO implements Serializable {

    /**
     * 成员名
     */
    @NotBlank(message = "memberName不能为空")
    String memberName;

    /**
     * 数量
     */
    @NotNull(message = "num不能为空")
    @Min(value = 1, message = "num不能小于1")
    Integer num;
}