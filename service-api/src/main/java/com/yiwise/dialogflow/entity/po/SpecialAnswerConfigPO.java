package com.yiwise.dialogflow.entity.po;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.po.intent.RuleActionParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Document(collection = SpecialAnswerConfigPO.COLLECTION_NAME)
@CompoundIndexes(
        @CompoundIndex(name = "idx_botId", def = "{botId: 1}")
)
public class SpecialAnswerConfigPO extends BaseTimeUserIdPO implements AnswerContainer, IntentActionSource, IntentLevelSource, Serializable, Uninterrupted {

    public static final String COLLECTION_NAME = "specialAnswerConfig";

    public static final String TEMPLATE_COLLECTION_NAME = "specialAnswerConfigTemplate";

    public static final String DEFAULT_ROLE_DESCRIPTION = "你是认真负责的电话客服。你需要表现的亲和。你应该用查询到的参考话术来辅助你回答客户的问题，你的回复需要直接回答用户的问题。";
    public static final String DEFAULT_BACKGROUND = "要求：\n" +
            "1. 你需要根据参考话术，来为你的回答提供依据。如果参考话术不能帮助回复该问题，请回复“这个问题我也不太清楚，不好意思哈”。\n" +
            "2. 回答需要考虑聊天内容。\n" +
            "3. 请用电话客服的口吻回复，并且要求回复简洁，不要超过20个字。";

    public static final String AI_REPEAT = "AI重复上句语音";
    public static final String USER_SILENCE = "客户无应答处理";
    public static final String AI_UNKNOWN = "AI无法应答处理";
    public static final String HANGUP_DELAY = "延时挂机";
    public static final String INAUDIBLE = "听不清楚AI说话";
    public static final String ASSISTANT = "语音助手";
    public static final String LLM = "大模型对话";

    public static final List<String> INTENT_TRIGGER_SPECIAL_ANSWER_CONFIG_NAME_LIST = Arrays.asList(AI_REPEAT, INAUDIBLE, ASSISTANT, LLM);

    @Id
    private String id;

    private String name;

    private String label;

    private Long botId;

    private List<KnowledgeAnswer> answerList;

    private EnabledStatusEnum enabledStatus;

    /**
     * 触发意图列表
     */
    private List<String> triggerIntentIdList;

    /**
     * 不关联意图列表, 延迟挂机由用到
     */
    private List<String> excludeIntentIdList;

    public List<String> getTriggerIntentIdList() {
        if (Objects.isNull(triggerIntentIdList)) {
            triggerIntentIdList = new ArrayList<>();
        }
        return triggerIntentIdList;
    }

    /**
     * 前端代码非常不健壮, 没做任何检查, 遇到null值就报错
     */
    public List<String> getExcludeIntentIdList() {
        if (Objects.isNull(excludeIntentIdList)) {
            excludeIntentIdList = new ArrayList<>();
        }
        return excludeIntentIdList;
    }

    private Double hangupDelaySeconds;

    /**
     * 延迟次数, 默认1次
     */
    private Integer hangupDelayCount;

    public Integer getHangupDelayCount() {
        return hangupDelayCount == null ? 1 : hangupDelayCount;
    }

    /**
     * 意向等级详情编码
     */
    private Integer intentLevelDetailCode;

    private Boolean enableIntentLevel;

    public Boolean getEnableIntentLevel() {
        if (INAUDIBLE.equals(name) || ASSISTANT.equals(name)) {
            return enableIntentLevel;
        }
        return false;
    }

    /**
     * 动作配置
     */
    private Boolean isEnableAction;

    /**
     * 触发动作
     */
    private List<RuleActionParam> actionList;

    /**
     * 是否是客户关注点
     */
    private Boolean isCustomerConcern;

    public Boolean getIsCustomerConcern() {
        return BooleanUtils.isTrue(isCustomerConcern);
    }

    /**
     * 最小输入字数(大模型特殊语境需要)
     */
    private Integer minInputLength;

    /**
     * 是否启用引导话术(大模型特殊语境需要)
     */
    private Boolean enableGuideAnswer;

    public Boolean getEnableGuideAnswer() {
        return false;
    }

    /**
     * 大模型特殊语境角色描述
     */
    private String roleDescription = DEFAULT_ROLE_DESCRIPTION;

    /**
     * 大模型特殊语境背景信息
     */
    private String background = DEFAULT_BACKGROUND;

    /**
     * 大模型模型名称
     */
    private String llmModelName;

    public String getLlmModelName() {
        if (StringUtils.isBlank(llmModelName)) {
            llmModelName = "基础版";
        }
        return llmModelName;
    }

    /**
     * 不允许打断
     */
    Boolean enableUninterrupted;

    /**
     * 允许打断比例
     */
    Integer customInterruptThreshold;

    public Integer getCustomInterruptThreshold() {
        return 100;
    }

    /**
     * 在不可打断进度下, 可以被打断的问答知识id
     */
    List<String> uninterruptedReplyKnowledgeIdList;

    /**
     * 在不可打断进度下, 可以被打断的流程id
     */
    List<String> uninterruptedReplyStepIdList;

    /**
     * 在不可打断进度下, 可以被打断的特殊语境id
     */
    List<String> uninterruptedReplySpecialAnswerIdList;

    /**
     * 是否启用人工介入
     */
    private Boolean enableHumanIntervention;

    public boolean isAiRepeat() {
        return AI_REPEAT.equals(name);
    }

    public boolean isLLMChat() {
        return LLM.equals(name);
    }

    public boolean isRequireTriggerIntent() {
        return isAiRepeat() || isInaudible();
    }

    public boolean isRequireAnswer() {
        if (isLLMChat()) {
            return false;
        }
        return !isAiRepeat();
    }

    public boolean isAiUnknown() {
        return AI_UNKNOWN.equals(name);
    }

    public boolean isUserSilence() {
        return USER_SILENCE.equals(name);
    }

    public boolean isHangupDelay() {
        return HANGUP_DELAY.equals(name);
    }

    public boolean isInaudible() {
        return INAUDIBLE.equals(name);
    }

    public boolean isAssistant() {
        return ASSISTANT.equals(name);
    }

    public Double getHangupDelaySeconds() {
        if (hangupDelaySeconds == null) {
            hangupDelaySeconds = 3.0;
        }
        return hangupDelaySeconds;
    }

    public Set<String> calDependVariableIdSet(DependentResourceBO dependentResource) {
        Set<String> dependVariableIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(getAnswerList())) {
            getAnswerList().forEach(answer -> {
                dependVariableIdSet.addAll(answer.calDependsVariableIdSet(dependentResource));
            });
        }
        return dependVariableIdSet;
    }
}
