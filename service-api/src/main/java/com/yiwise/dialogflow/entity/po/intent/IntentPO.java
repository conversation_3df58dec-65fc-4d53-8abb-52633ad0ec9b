package com.yiwise.dialogflow.entity.po.intent;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.dialogflow.common.MongoCollectionNameCenter;
import com.yiwise.dialogflow.entity.enums.CorpusTypeEnum;
import com.yiwise.dialogflow.entity.enums.IntentPropertiesEnum;
import com.yiwise.dialogflow.entity.enums.IntentTypeEnum;
import com.yiwise.dialogflow.entity.enums.TrainStatusEnum;
import com.yiwise.dialogflow.entity.po.CompositeIntentCondition;
import com.yiwise.dialogflow.entity.po.SimpleIntentInfo;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
@Document(collection = MongoCollectionNameCenter.INTENT)
public class IntentPO extends BaseTimeUserIdPO implements Serializable, SimpleIntentInfo {

    /**
     * 意图ID
     */
    @Id
    String id;

    /**
     * 意图名称
     */
    String name;

    /**
     * 话术ID
     */
    Long botId;

    /**
     * 意图类型
     */
    IntentTypeEnum intentType;

    /**
     * 语料类型
     */
    CorpusTypeEnum corpusType;

    /**
     * 意图属性
     */
    IntentPropertiesEnum intentProperties;

    /**
     * 训练状态
     */
    TrainStatusEnum trainStatus;

    /**
     * 组合条件，组内为且，组间为或
     */
    List<CompositeIntentCondition> compositeConditionList;

    /**
     * 分组id
     */
    String groupId;

    public String getNameAndTypeStr() {
        return getName() + getIntentType().getCode();
    }
}
