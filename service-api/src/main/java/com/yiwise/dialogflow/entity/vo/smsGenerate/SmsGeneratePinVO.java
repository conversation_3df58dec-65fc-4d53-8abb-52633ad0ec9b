package com.yiwise.dialogflow.entity.vo.smsGenerate;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SmsGeneratePinVO implements Serializable {

    /**
     * 话术id
     */
    @NotNull(message = "botId不能为空")
    Long botId;

    /**
     * id
     */
    @NotBlank(message = "id不能为空")
    String id;
}
