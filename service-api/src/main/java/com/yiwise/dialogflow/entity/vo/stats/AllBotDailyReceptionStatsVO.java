package com.yiwise.dialogflow.entity.vo.stats;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;

@Data
public class AllBotDailyReceptionStatsVO {

    private long epochDay;

    /**
     * 日期, 序列化为yyyy-MM-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate date;

    /**
     * 外呼总量
     */
    private long totalCallCount;

    /**
     * 接通总量
     */
    private long answeredCount;
}
