package com.yiwise.dialogflow.entity.po;

import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


@Data
@EqualsAndHashCode(callSuper = true)
public class DialogCollectNodePO extends DialogChatNodePO implements NonLeafNode {

    /**
     * 实体采集配置列表
     */
    List<CollectNodeEntityItemPO> entityCollectList;

    /**
     * 使用之前的用户输入进行提取
     * 此配置在对话阶段实现为:
     * 此节点的配置将作为全局提取, 每次用户输入时, 都会进行提取, 但是不赋值
     * 优点:
     * 1. 对 StepFlowChatManager改动较小, 不需要将接口改为异步,
     * 2. 实体提取逻辑统一, 比较方便维护
     * 缺点:
     * 1. 每一次用户输入都会进行实体提取, 可能会增加接口延迟(如果最终对话没有走到此节点了)
     */
    Boolean enableCollectWithPreInput;

    /**
     * 是否启用跳过问题条件
     */
    Boolean enableSkipCondition;

    /**
     * 跳过条件组列表
     */
    List<CollectNodeSkipCondition> skipConditionList;

    /**
     * 采集节点仅且必选选择默认意图和采集成功
     */
    @Override
    public List<String> getSelectIntentIdList() {
        return Arrays.asList(ApplicationConstant.DEFAULT_INTENT_ID, ApplicationConstant.COLLECT_SUCCESS_INTENT_ID);
    }

    @Override
    public List<NodeAnswer> getAnswerList() {
        if (CollectionUtils.isEmpty(entityCollectList)) {
            return new ArrayList<>();
        }
        return entityCollectList.stream()
                .map(CollectNodeEntityItemPO::getAnswerList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    @Override
    public void setAnswerList(List<NodeAnswer> answerList) {
        // 信息采集节点的答案列表是生成的, 不需要写入数据
        super.setAnswerList(new ArrayList<>());
    }

    @Override
    public Set<String> calDependVariableIdSet(DependentResourceBO resource) {
        Set<String> result = super.calDependVariableIdSet(resource);
        if (CollectionUtils.isNotEmpty(getEntityCollectList())) {
            result.addAll(
                    getEntityCollectList().stream()
                            .map(CollectNodeEntityItemPO::getVariableId)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList())
            );
        }
        return result;
    }
}
