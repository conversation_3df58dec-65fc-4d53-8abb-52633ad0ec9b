package com.yiwise.dialogflow.entity.bo.algorithm;

import com.yiwise.base.model.bean.po.BaseTimePO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @date 2019/7/9 5:12 PM
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TrainItemBO extends BaseTimePO {

    /**
     * 关联ID，意图ID/知识ID/实体ID/词槽ID others-废弃语料库 unknown-未知意图
     */
    String relationId;

    /**
     * 关联名称
     */
    String relationName;

    /**
     * 语料
     */
    String text;
}
