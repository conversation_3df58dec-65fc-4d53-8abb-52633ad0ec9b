package com.yiwise.dialogflow.entity.vo.analyze;

import com.yiwise.dialogflow.entity.po.analyze.AnalyzeTemplateIntentPO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AnalyzeTemplateIntentVO extends AnalyzeTemplateIntentPO implements Serializable {

    /**
     * 子意图名称集合
     */
    List<String> childNameList;

    /**
     * 语料列表
     */
    List<String> corpusList;
}
