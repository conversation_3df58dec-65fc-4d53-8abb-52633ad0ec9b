package com.yiwise.dialogflow.entity.po;

import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.enums.ConditionVarTypeEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 节点条件分支
 */
@Data
public class NodeConditionBranchPO extends BaseConditionGroup {

    private String id;
    /**
     * 分支名称
     */
    private String name;

    private Boolean isDefault;

    /**
     * 是否启用分支动作
     */
    private Boolean enableAction;

    /**
     * 分支动作列表
     */
    private List<BranchActionPO> actionList;

    public void validWithResource(DependentResourceBO resource) {
        if (StringUtils.isBlank(name)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "分支名称不能为空");
        }
        if (!BooleanUtils.isTrue(isDefault)) {
            super.conditionGroupValidateWithResource(resource);
        }
        if (BooleanUtils.isTrue(enableAction)) {
            if (CollectionUtils.isEmpty(actionList)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "分支动作列表不能为空");
            }
            actionList.forEach(action -> action.validWithResource(resource));
        }
    }

    @Override
    protected Set<String> acquireSwithConditionDependsVariableIdSet() {
        Set<String> dependsVariableIdSet = super.acquireSwithConditionDependsVariableIdSet();
        if (BooleanUtils.isTrue(enableAction) && CollectionUtils.isNotEmpty(actionList)) {
            for (BranchActionPO action : actionList) {
                dependsVariableIdSet.add(action.getVarId());
                if (ConditionVarTypeEnum.isVar(action.getVarType())) {
                    dependsVariableIdSet.add(action.getValue());
                }
            }
        }
        return dependsVariableIdSet;
    }
}
