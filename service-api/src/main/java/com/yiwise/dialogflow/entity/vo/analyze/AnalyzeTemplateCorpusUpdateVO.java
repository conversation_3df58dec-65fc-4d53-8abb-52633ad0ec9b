package com.yiwise.dialogflow.entity.vo.analyze;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AnalyzeTemplateCorpusUpdateVO implements Serializable {

    /**
     * id
     */
    @NotBlank(message = "id不能为空")
    String id;

    /**
     * 语料
     */
    @NotBlank(message = "corpus不能为空")
    String corpus;
}
