package com.yiwise.dialogflow.entity.po;

import com.alibaba.excel.util.StringUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Data
public class ConstantAssignConfigPO extends NodeAssignConfigPO implements Serializable {

    /**
     *
     */
    private List<VarAssignActionItemPO> assignActionList;

    @Override
    public List<String> getDependentEntityIdList() {
        if (CollectionUtils.isEmpty(assignActionList)) {
            return new ArrayList<>();
        }

        return assignActionList.stream()
                .map(VarAssignActionItemPO::getEntityId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getDependIntentIdList() {
        return new ArrayList<>();
    }

    @Override
    public List<String> getDependVariableIdList() {
        if (CollectionUtils.isEmpty(assignActionList)) {
            return new ArrayList<>();
        }

        return assignActionList.stream()
                .map(VarAssignActionItemPO::getVariableId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    @Override
    public void mappingIntentId(Map<String, String> oldIntentId2newIntentIdMap) {

    }

    @Override
    public void mappingVariableId(Map<String, String> oldId2newIdMap) {
        if (CollectionUtils.isEmpty(assignActionList)) {
            return;
        }
        assignActionList.forEach(item -> item.setVariableId(oldId2newIdMap.get(item.getVariableId())));
    }

    @Override
    public void mappingEntityId(Map<String, String> oldId2newIdMap) {

    }

    @Override
    public void validateConfig(StepPO step, DependentResourceBO resource, DialogBaseNodePO node) {
        super.validateConfig(step, resource, node);
        if (CollectionUtils.isEmpty(assignActionList)) {
            return;
        }
        for (VarAssignActionItemPO item : assignActionList) {
            if (StringUtils.isBlank(item.getVariableId())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "动态变量赋值配置不完整, 变量不能为空");
            }
            if (StringUtils.isBlank(item.getConstantValue())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "动态变量赋值配置不完整, 常量值不能为空");
            }
        }
    }

    @Override
    public String toDisplayString(DependentResourceBO resource) {
        if (CollectionUtils.isEmpty(assignActionList)) {
            return "";
        }

        String text = this.getAssignActionList().stream()
                .map(item -> {
                    String variableName = resource.getVariableIdNameMap().getOrDefault(item.getVariableId(), "未知");
                    return String.format("%s保存到变量:%s", item.getConstantValue(), variableName);
                }).collect(Collectors.joining("; "));
        return String.format("【常量赋值: %s】", text);
    }
}
