package com.yiwise.dialogflow.entity.vo.audio;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AudioCompleteProgressVO {
    Long botId;

    /**
     * 总共句数
     */
    Integer totalCount;

    /**
     * 已完成句数
     */
    Integer completedCount;

    /**
     * 真人已完成句数
     */
    Integer manMadeCompletedCount;

    /**
     * tts已完成句数
     */
    Integer ttsCompletedCount;

    /**
     * 未完成句数
     */
    Integer incompleteCount;

    /**
     * 去重后总句数
     */
    Integer distTotalCount;

    /**
     * 去重后已完成句数
     */
    Integer distCompletedCount;

    /**
     * 去重后真人已完成句数
     */
    Integer distManMadeCompletedCount;

    /**
     * 去重后tts已完成句数
     */
    Integer distTtsCompletedCount;

    /**
     * 去重后未完成句数
     */
    Integer distIncompleteCount;

    /**
     * 总字数
     */
    Integer totalWordCount;

    /**
     * 已完成字数
     */
    Integer completedWordCount;

    /**
     * 真人已完成字数
     */
    Integer manMadeCompletedWordCount;

    /**
     * tts已完成字数
     */
    Integer ttsCompletedWordCount;

    /**
     * 未完成字数
     */
    Integer incompleteWordCount;

    /**
     * 去重后总字数
     */
    Integer distTotalWordCount;

    /**
     * 去重后已完成字数
     */
    Integer distCompletedWordCount;

    /**
     * 去重后真人已完成字数
     */
    Integer distManMadeCompletedWordCount;

    /**
     * 去重后tts已完成字数
     */
    Integer distTtsCompletedWordCount;

    /**
     * 去重后未完成字数
     */
    Integer distIncompleteWordCount;

    Set<String> processingSet;

    /**
     * 录音完成百分比
     */
    Integer completedPercent;

    /**
     * 真人录音百分比
     */
    Integer manMadeCompletedPercent;

    /**
     * tts录音百分比
     */
    Integer ttsCompletedPercent;
}
