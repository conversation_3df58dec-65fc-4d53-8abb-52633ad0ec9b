package com.yiwise.dialogflow.entity.vo;

import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import com.yiwise.dialogflow.entity.vo.stats.SpecialAnswerConfigStatsVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SpecialAnswerConfigVO extends SpecialAnswerConfigPO {

    List<SimpleIntentVO> intentInfoList;

    List<SimpleIntentVO> excludeIntentInfoList;

    List<IdNamePair<String, String>> stepInfoList;

    SpecialAnswerConfigStatsVO statsInfo = new SpecialAnswerConfigStatsVO();

    Boolean hasVarCreated;

    /**
     * 录音是否完成
     */
    Boolean audioCompleted;

    public SpecialAnswerConfigVO hasVarCreated(Boolean hasVarCreated) {
        this.hasVarCreated = hasVarCreated;
        return this;
    }
}
