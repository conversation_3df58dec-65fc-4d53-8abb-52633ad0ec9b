package com.yiwise.dialogflow.entity.po;

import com.yiwise.dialogflow.entity.enums.AlgorithmLabelSourceEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Document(collection = AlgorithmLabelPO.COLLECTION_NAME)
@CompoundIndexes(
        @CompoundIndex(name = "idx_botId_stepId_nodeId", def = "{botId: 1, stepId: 1, nodeId: 1}")
)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlgorithmLabelPO implements Serializable {

    public static final String COLLECTION_NAME = "algorithmLabel";

    @Id
    String id;

    /**
     * 话术id
     */
    Long botId;

    /**
     * 流程id
     */
    String stepId;

    /**
     * 节点id
     */
    String nodeId;

    /**
     * 文本
     */
    String text;

    /**
     * 标签
     */
    String label;

    /**
     * 标签来源
     */
    AlgorithmLabelSourceEnum source;
}