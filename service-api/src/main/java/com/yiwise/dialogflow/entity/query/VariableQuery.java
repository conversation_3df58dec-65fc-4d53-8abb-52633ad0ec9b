package com.yiwise.dialogflow.entity.query;

import com.yiwise.base.model.bean.vo.AbstractQueryVO;
import com.yiwise.dialogflow.entity.enums.VariableTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/3/22
 * @class <code>VariableQuery</code>
 * @see
 * @since JDK1.8
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VariableQuery extends AbstractQueryVO {

    @NotNull(message = "botId不能为空")
    private Long botId;

    private List<VariableTypeEnum> typeList;

    private String search;
}
