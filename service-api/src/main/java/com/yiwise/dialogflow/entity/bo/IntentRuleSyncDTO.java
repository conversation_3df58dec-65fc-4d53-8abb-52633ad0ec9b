package com.yiwise.dialogflow.entity.bo;

import com.yiwise.base.common.utils.bean.DeepCopyUtils;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import com.yiwise.dialogflow.entity.vo.IntentRuleVO;
import com.yiwise.dialogflow.entity.vo.sync.RuleSyncVO;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/14 14:50:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class IntentRuleSyncDTO implements Serializable {
    /**
     * 目标BotId
     */
    Long targetBotId;

    /**
     * 待同步的意向配置
     */
    IntentRuleVO intentRuleVO;

    /**
     * 前端同步参数
     */
    RuleSyncVO syncVO;

    /**
     * 源bot和目标bot意向标签组是否相同
     */
    Boolean isTagSame;


    /**
     * 源bot的特殊语境映射map
     */
    Map<String, SpecialAnswerConfigPO> sourceIdToSpecialAnswerConfig;

    /**
     * 源 bot 意图 id -> 意图名称映射关系
     */
    Map<String, String> sourceIntentId2NameMapping = new HashMap<>();

    /**
     * 目标 bot, 意图名称-> 意图 id 映射关系
     */
    Map<String, String> targetIntentName2IdMapping = new HashMap<>();

    Map<String, String> srcVariableId2NameMapping = new HashMap<>();

    Map<String, String> targetVariableName2IdMapping = new HashMap<>();

    Map<String, String> srcEntityId2NameMap = new HashMap<>();

    Map<String, String> targetEntityName2IdMap = new HashMap<>();

    Map<String, String> stepIdLabelMap = new HashMap<>();

    Map<String, Map<String, String>> stepIdNodeIdLabelMap = new HashMap<>();

    Map<String, String> llmLabelIdMap = new HashMap<>();

    public IntentRuleVO getIntentRuleVO() {
        return DeepCopyUtils.copyObject(intentRuleVO);
    }

}
