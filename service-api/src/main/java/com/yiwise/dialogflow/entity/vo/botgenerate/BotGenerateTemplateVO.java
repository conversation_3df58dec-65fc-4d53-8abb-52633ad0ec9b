package com.yiwise.dialogflow.entity.vo.botgenerate;

import com.yiwise.dialogflow.entity.po.botgenerate.BotGenerateTemplatePO;
import lombok.Data;

@Data
public class BotGenerateTemplateVO extends BotGenerateTemplatePO {

    /**
     * 创建人姓名
     */
    String createUserName;

    /**
     * 更新人姓名
     */
    String updateUserName;

    /**
     * 是否忽略告警项, 如果忽略告警项, 在校验时,如果不存在错误项则会提交生成记录, 否则, 会返回告警项, 并生成失败
     */
    Boolean ignoreWarning;
}
