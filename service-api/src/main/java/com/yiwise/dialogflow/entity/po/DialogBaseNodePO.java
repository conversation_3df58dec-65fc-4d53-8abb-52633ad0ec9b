package com.yiwise.dialogflow.entity.po;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.enums.NodeTypeEnum;
import com.yiwise.dialogflow.entity.po.intent.RuleActionParam;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.*;

import static com.yiwise.dialogflow.entity.po.DialogBaseNodePO.*;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper=false)
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type", visible = true)
@JsonSubTypes({
        @JsonSubTypes.Type(value = DialogChatNodePO.class, name = CHAT_NODE),
        @JsonSubTypes.Type(value = DialogJumpNodePO.class, name = JUMP_NODE),
        @JsonSubTypes.Type(value = DialogJudgeNodePO.class, name = JUDGE_NODE),
        @JsonSubTypes.Type(value = DialogQueryNodePO.class, name = QUERY_NODE),
        @JsonSubTypes.Type(value = DialogCollectNodePO.class, name = COLLECT_NODE),
        @JsonSubTypes.Type(value = DialogKeyCaptureNodePO.class, name = KEY_CAPTURE)
})
@Document(collection = DialogBaseNodePO.COLLECTION_NAME)
@CompoundIndexes(
        @CompoundIndex(name = "idx_botId_stepId", def = "{botId: 1, stepId: 1}")
)
public class DialogBaseNodePO extends BaseTimeUserIdPO implements AnswerContainer, IntentActionSource, IntentLevelSource, Serializable, MismatchAndUninterrupted {

    public static final String COLLECTION_NAME = "stepNode";

    public static final String CHAT_NODE = "CHAT";
    public static final String JUMP_NODE = "JUMP";
    public static final String JUDGE_NODE = "JUDGE";
    public static final String QUERY_NODE = "QUERY";
    public static final String COLLECT_NODE = "COLLECT";
    public static final String KEY_CAPTURE = "KEY_CAPTURE";

    @Id
    private String id;

    private String label;

    private String name;

    private String stepId;

    private Long botId;

    private NodeTypeEnum type;

    private List<NodeAnswer> answerList;

    /**
     * 是否允许打断,
     * 开始版本为是否允许打断, 即默认是不可打断的, 仅仅为true的时候可以打断, null值为不可打断
     * 1021评审时, 改为默认允许打断, 即仅仅为false时, 不可打断, null值为可打断
     */
    @Deprecated
    private Boolean enableInterrupt;

    private Boolean enableUninterrupted;

    public Boolean getEnableUninterrupted() {
        if (Objects.isNull(enableUninterrupted)) {
            if (BooleanUtils.isTrue(enableInterrupt)) {
                enableUninterrupted = Objects.nonNull(customInterruptThreshold) && customInterruptThreshold > 0;
            } else {
                if (Objects.isNull(enableInterrupt)) {
                    enableUninterrupted = false;
                } else {
                    enableUninterrupted = true;
                    customInterruptThreshold = 100;
                }
            }
        }
        return enableUninterrupted;
    }

    /**
     * 允许打断比例, 录音播放进度大于该阈值, 才可以打断
     */
    private Integer customInterruptThreshold;

    /**
     * 在不可打断进度下, 可以被打断的问答知识id
     */
    private List<String> uninterruptedReplyKnowledgeIdList;

    /**
     * 在不可打断进度下, 可以被打断的流程id
     */
    private List<String> uninterruptedReplyStepIdList;

    /**
     * 在不可打断进度下, 可以被打断的特殊语境id
     */
    private List<String> uninterruptedReplySpecialAnswerIdList;

    /**
     * 在不可打断进度下, 可以被打断的分支意图id
     */
    private List<String> uninterruptedReplyBranchIntentIdList;

    /**
     * 意向等级编码, 最高为0
     */
    private Integer intentLevelDetailCode;

    private Boolean enableIntentLevel;

    /**
     * 动作配置
     */
    private Boolean isEnableAction;

    /**
     * 触发动作
     */
    private List<RuleActionParam> actionList;

    /**
     * 不关联问答知识、流程、特殊语境
     */
    private Boolean mismatchKnowledgeAndStep;

    /**
     * 不关联全部问答知识
     */
    private Boolean mismatchAllKnowledge;

    /**
     * 问答知识id列表
     */
    private List<String> mismatchKnowledgeIdList;

    /**
     * 不关联全部流程
     */
    private Boolean mismatchAllStep;

    /**
     * 流程id列表
     */
    private List<String> mismatchStepIdList;

    /**
     * 不关联全部特殊语境
     */
    private Boolean mismatchAllSpecialAnswerConfig;

    /**
     * 特殊语境id列表
     */
    private List<String> mismatchSpecialAnswerConfigIdList;


    /**
     * 是否开启节点动态变量赋值
     */
    private Boolean enableAssign;

    /**
     * 节点动态变量赋值配置
     */
    @Deprecated
    private VariableAssignConfigPO assignConfig;

    /**
     * 常量赋值
     */
    private ConstantAssignConfigPO constantAssign;

    /**
     * 实体赋值
     */
    private EntityAssignConfigPO entityAssign;

    /**
     * 原话赋值
     */
    private OriginInputAssignConfigPO originInputAssign;

    /**
     * 是否启用人工介入
     */
    private Boolean enableHumanIntervention;

    public boolean enableConstantAssign() {
        return constantAssign != null && CollectionUtils.isNotEmpty(constantAssign.getAssignActionList());
    }

    public boolean enableEntityAssign() {
        return entityAssign != null && CollectionUtils.isNotEmpty(entityAssign.getAssignActionList());
    }

    public boolean enableOriginInputAssign() {
        return originInputAssign != null && CollectionUtils.isNotEmpty(originInputAssign.getAssignActionList());
    }

    public boolean enableEntityOrOriginInputAssign() {
        return enableEntityAssign() || enableOriginInputAssign();
    }

    public Integer getCustomInterruptThreshold() {
        if (customInterruptThreshold == null) {
            customInterruptThreshold = 100;
        }
        return customInterruptThreshold;
    }

    public Set<String> calDependVariableIdSet(DependentResourceBO resource) {
        Set<String> result = new HashSet<>();
        if (CollectionUtils.isNotEmpty(getAnswerList())) {
            getAnswerList().forEach(answer -> {
                result.addAll(answer.calDependsVariableIdSet(resource));
            });
        }
        if (BooleanUtils.isTrue(getEnableAssign())) {
            if (enableConstantAssign()) {
                result.addAll(constantAssign.getDependVariableIdList());
            }
            if (enableEntityAssign()) {
                result.addAll(entityAssign.getDependVariableIdList());
            }
            if (enableOriginInputAssign()) {
                result.addAll(originInputAssign.getDependVariableIdList());
            }
        }
        return result;
    }

    public List<String> getMismatchKnowledgeIdList() {
        if (Objects.isNull(mismatchKnowledgeIdList)) {
            mismatchKnowledgeIdList = new ArrayList<>();
        }
        return mismatchKnowledgeIdList;
    }

    public List<String> getMismatchStepIdList() {
        if (Objects.isNull(mismatchStepIdList)) {
            mismatchStepIdList = new ArrayList<>();
        }
        return mismatchStepIdList;
    }

    public List<String> getUninterruptedReplyKnowledgeIdList() {
        if (Objects.isNull(uninterruptedReplyKnowledgeIdList)) {
            uninterruptedReplyKnowledgeIdList = new ArrayList<>();
        }
        return uninterruptedReplyKnowledgeIdList;
    }

    public List<String> getUninterruptedReplyStepIdList() {
        if (Objects.isNull(uninterruptedReplyStepIdList)) {
            uninterruptedReplyStepIdList = new ArrayList<>();
        }
        return uninterruptedReplyStepIdList;
    }

    public List<String> getUninterruptedReplySpecialAnswerIdList() {
        if (Objects.isNull(uninterruptedReplySpecialAnswerIdList)) {
            uninterruptedReplySpecialAnswerIdList = new ArrayList<>();
        }
        return uninterruptedReplySpecialAnswerIdList;
    }

    public List<String> getMismatchSpecialAnswerConfigIdList() {
        if (Objects.isNull(mismatchSpecialAnswerConfigIdList)) {
            mismatchSpecialAnswerConfigIdList = new ArrayList<>();
        }
        return mismatchSpecialAnswerConfigIdList;
    }
}
