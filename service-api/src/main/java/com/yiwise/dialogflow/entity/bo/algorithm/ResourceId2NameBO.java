package com.yiwise.dialogflow.entity.bo.algorithm;

import com.yiwise.dialogflow.entity.po.DialogFlowExtraRuleConditionNodePO;
import javaslang.Tuple2;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/6/7
 * @class <code>ResourceId2NameBO</code>
 * @see
 * @since JDK1.8
 */
@Data
public class ResourceId2NameBO {
    Map<DialogFlowExtraRuleConditionNodePO, Tuple2<String, String>> stepNode2NameMap;

    Map<String, String> specialAnswerId2NameMap;

    Map<String, String> knowledgeMap;

    Map<Integer, String> intentLevelIdNameMap;

    Map<String, String> entityId2NameMap;

    Map<String, String> varId2NameMap;

    Map<String, String> intentId2NameMap;

    Map<String, String> llmLabeId2NameMap;
}
