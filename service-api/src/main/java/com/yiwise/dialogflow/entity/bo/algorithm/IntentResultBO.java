package com.yiwise.dialogflow.entity.bo.algorithm;

import lombok.Data;

import java.io.Serializable;

/**
 * 算法返回格式：
 * {
 *     "intent": {
 *         "intent": {
 *             "id": "d7c3fdf7f73afba52c6860adc947ab98",
 *             "confidence": 0.9850233,
 *             "sim_sentence": "啊，行行好，好好，谢谢啊谢谢啊嗯",
 *             "name": "明确肯定"
 *         },
 *         "domain":"骗人",
 *         "entities": [],
 *         "intent_ranking": [
 *             {
 *                 "id": "d7c3fdf7f73afba52c6860adc947ab98",
 *                 "confidence": 0.9850233,
 *                 "sim_sentence": "啊，行行好，好好，谢谢啊谢谢啊嗯",
 *                 "name": "明确肯定"
 *             },
 *             {
 *                 "id": "621446957437cd158f36813f",
 *                 "confidence": 0.5596464276313782,
 *                 "sim_sentence": "什么东西",
 *                 "name": "干什么的"
 *             }
 *         ],
 *         "text": "嗯，好，行行，谢谢啊"
 *     },
 *     "request_id": "b2e7c00c-47d5-4e6e-a8c1-374bd027a992"
 * }
 *
 * <AUTHOR>
 * @date 2022/2/28
 */
@Data
public class IntentResultBO implements Serializable {

    private IntentResultDetailBO intent;

    private String request_id;
}
