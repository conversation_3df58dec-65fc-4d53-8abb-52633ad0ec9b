package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum DialogFlowTypeEnum implements CodeDescEnum {
    TEMPLATE(0, "模板"),
    DEMO(1, "展示"),
    NORMAL(2, "普通");

    private Integer code;
    private String desc;

    DialogFlowTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}