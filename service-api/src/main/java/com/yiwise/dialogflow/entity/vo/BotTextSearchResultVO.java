package com.yiwise.dialogflow.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BotTextSearchResultVO implements Serializable {

    /**
     * 话术id
     */
    @JsonProperty("bot_id")
    Long botId;

    /**
     * 标签
     */
    String label;

    /**
     * 话术文案
     */
    String sentence;

    /**
     * 挂机率
     */
    @JsonProperty("hangup_rate")
    Double hangupRate;

    /**
     * 拒绝率
     */
    @JsonProperty("negative_rate")
    Double negativeRate;

    /**
     * 肯定率
     */
    @JsonProperty("positive_rate")
    Double positiveRate;

    /**
     * 得分
     */
    Double score;

    /**
     * 相似度
     */
    Double similarity;
}