package com.yiwise.dialogflow.entity.enums;


import com.yiwise.base.model.enums.CodeDescEnum;


/**
 * 采集节点跳过类型
 */
public enum CollectNodeSkipTypeEnum implements CodeDescEnum {
    CURRENT_QUESTION(1, "当前问题"),
    ALL_QUESTION(2, "当前节点所有问题"),
    ;

    CollectNodeSkipTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final Integer code;
    private final String desc;

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
