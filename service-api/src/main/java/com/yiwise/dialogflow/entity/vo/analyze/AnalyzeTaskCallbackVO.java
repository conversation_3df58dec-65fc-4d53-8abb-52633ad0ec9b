package com.yiwise.dialogflow.entity.vo.analyze;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AnalyzeTaskCallbackVO implements Serializable {

    Integer code;

    Long taskId;

    String resultAddress;

    String info;

    public Boolean isSuccess() {
        return Objects.nonNull(code) && code.equals(0);
    }
}
