package com.yiwise.dialogflow.entity.vo.asrmodel;

import com.yiwise.dialogflow.entity.po.asrmodel.AsrSelfLearningDetailPO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/19 14:56:25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AsrSelfLearningDetailVO extends AsrSelfLearningDetailPO {
    /**
     * 话术名称
     */
    String botName;

    /**
     * 话术id
     */
    Long botId;

    /**
     * 开始时间
     */
    LocalDateTime startTime;

    /**
     * 结束时间
     */
    LocalDateTime endTime;

    /**
     * 通话次数
     */
    Integer callCount;

    /**
     * 话术id集合
     */
    List<Long> botIdList;

    /**
     * 是否更改语料
     */
    Boolean isUpdateCorpus;

    /**
     * 是否绑定话术
     */
    Boolean isBind;

    Long tenantId;

    Long currentUserId;

    /**
     * 更新人
     */
    String updateUserName;

    /**
     * 语料文件名
     */
    String fileName;
}