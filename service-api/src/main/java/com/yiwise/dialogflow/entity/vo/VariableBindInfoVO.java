package com.yiwise.dialogflow.entity.vo;

import com.yiwise.dialogflow.entity.po.VariableBindPO;
import com.yiwise.dialogflow.entity.po.VariablePO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class VariableBindInfoVO extends VariablePO {

    /**
     * 变量绑定信息, 如果为空, 则表示还未绑定客户属性
     */
    private VariableBindPO bindInfo;

    /**
     * 客户属性不是文本类型
     */
    private Boolean attributeTypeMismatch;
}
