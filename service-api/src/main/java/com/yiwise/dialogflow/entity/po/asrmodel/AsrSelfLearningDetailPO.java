package com.yiwise.dialogflow.entity.po.asrmodel;


import com.yiwise.base.model.bean.po.BaseTimePO;
import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import java.io.Serializable;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.GeneratedValue;
import java.math.BigDecimal;

/**
 * asr自学习模型信息表
 *
 * <AUTHOR>
 * @date 2022-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "asr_self_learning_detail")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AsrSelfLearningDetailPO extends BaseTimeUserIdPO implements Serializable {

	@Id
	@GeneratedValue(generator = "JDBC")
	Long asrSelfLearningDetailId;

	/**
	 * 模型名称
	 */
	String name;

	/**
	 * 描述
	 */
	String description;

	/**
	 * 开启状态（0-停用；1-开启）
	 */
	Integer status;

	/**
	 * 语料地址
	 */
	String url;

}
