package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum AnalyzeTaskCorpusSourceTypeEnum implements CodeDescEnum {

    /**
     * 外呼任务
     */
    CALL_JOB(0, "外呼任务"),

    /**
     * 客户
     */
    TENANT(1, "客户"),

    /**
     * 话术
     */
    DIALOG_FLOW(2, "话术"),

    /**
     * 新版外呼
     */
    NEW_CALL_JOB(3, "新版外呼"),
    ;

    private final Integer code;
    private final String desc;

    AnalyzeTaskCorpusSourceTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Boolean isCallJob(AnalyzeTaskCorpusSourceTypeEnum type) {
        return type == CALL_JOB;
    }

    public static Boolean isTenant(AnalyzeTaskCorpusSourceTypeEnum type) {
        return type == TENANT;
    }

    public static Boolean isDialogFlow(AnalyzeTaskCorpusSourceTypeEnum type) {
        return type == DIALOG_FLOW;
    }

    public static Boolean isNewCallJob(AnalyzeTaskCorpusSourceTypeEnum type) {
        return type == NEW_CALL_JOB;
    }
}
