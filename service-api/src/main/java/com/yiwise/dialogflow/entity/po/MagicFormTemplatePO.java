package com.yiwise.dialogflow.entity.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import lombok.Data;

import javax.persistence.Id;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class MagicFormTemplatePO extends BaseTimeUserIdPO {

    public static final String COLLECTION_NAME = "magicFormTemplate";

    @Id
    private String id;

    private Long botId;

    List<MagicFormFieldPO> fieldList;

    /**
     * 是否已发布
     */
    Boolean published;

    /**
     * 发布日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    LocalDateTime publishedDateTime;
}
