package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * 意图属性
 *
 * <AUTHOR>
 * @date 2022/4/2
 */
public enum IntentPropertiesEnum implements CodeDescEnum {
    DEFINITE(0, "肯定"),
    DECLINE(2, "拒绝"),
    NEUTRAL(3, "中性"),

    SYSTEM(4, "系统内置"),
    ;

    private Integer code;
    private String desc;

    IntentPropertiesEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static IntentPropertiesEnum fromNameOrDesc(String strVal) {
        try {
            return IntentPropertiesEnum.valueOf(strVal);
        } catch (Exception e) {
            for (IntentPropertiesEnum value : IntentPropertiesEnum.values()) {
                if (value.desc.equals(strVal)) {
                    return value;
                }
            }
            throw e;
        }
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}