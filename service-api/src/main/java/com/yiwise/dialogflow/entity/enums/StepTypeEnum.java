package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 */

public enum StepTypeEnum implements CodeDescEnum {
    MAIN(1, "主流程"),
    INDEPENDENT(2, "独立对话流程")
    ;
    String desc;
    Integer code;
    StepTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
