package com.yiwise.dialogflow.entity.vo.audio.request;

import com.yiwise.dialogflow.entity.enums.SyncModeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/12/21
 */
@Data
public class PublicAudioSyncRequestVO extends PublicAudioSearchRequestVO implements Serializable {

    /**
     * 目标botId
     */
    @NotNull(message = "targetBotId不能为空")
    private Long targetBotId;

    /**
     * 同步模式
     */
    @NotNull(message = "syncMode不能为空")
    private SyncModeEnum syncMode;

    private Long currentUserId;
}