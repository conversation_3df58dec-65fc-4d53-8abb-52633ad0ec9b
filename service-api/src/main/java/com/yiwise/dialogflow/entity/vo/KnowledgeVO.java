package com.yiwise.dialogflow.entity.vo;

import com.yiwise.dialogflow.entity.po.KnowledgePO;
import com.yiwise.dialogflow.entity.vo.stats.KnowledgeStatsVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KnowledgeVO extends KnowledgePO {

    List<SimpleIntentVO> intentInfoList;

    List<IdNamePair<String, String>> stepInfoList;

    KnowledgeStatsVO statsInfo = new KnowledgeStatsVO();
    /**
     * 分组路径
     */
    String groupPath;

    /**
     * 是否新创建了变量
     */
    Boolean hasVarCreated;


    /**
     * 录音是否完成
     */
    Boolean audioCompleted;

    public KnowledgeVO hasVarCreated(Boolean hasVarCreated) {
        this.hasVarCreated = hasVarCreated;
        return this;
    }
}
