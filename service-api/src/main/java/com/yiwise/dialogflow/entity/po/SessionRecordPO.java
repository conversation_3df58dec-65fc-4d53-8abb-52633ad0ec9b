package com.yiwise.dialogflow.entity.po;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import org.springframework.data.annotation.Id;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SessionRecordPO extends BaseTimeUserIdPO {

    @Id
    String id;

    Long botId;

    Long callRecordId;

    Long tenantId;

    Integer botVersion;

    String logKey;

    Long callJobId;

    String phoneNumber;

}
