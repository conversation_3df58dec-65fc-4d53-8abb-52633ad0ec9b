package com.yiwise.dialogflow.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.dialogflow.entity.enums.PosterTypeEnum;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/9/2
 * @class <code>CreateBotQrCodeVO</code>
 * @see
 * @since JDK1.8
 */
@Data
public class CreateBotQrCodeVO {

    /**
     * 机器人ID
     */
    private Long botId;

    private List<Long> botIdList;

    private Long folderId;

    /**
     * 体验名称
     */
    private String name;

    /**
     * 线路ID
     */
    private Long phoneNumberId;

    /**
     * 二维码呼频限制
     */
    private Integer callOutLimit;

    /**
     * 二维码有效时长
     */
    private Integer timeoutHour;

    /**
     * 生成的二维码ID
     */
    private String qrCodeId;

    /**
     * 话术ID
     */
    private Long dialogFlowId;

    /**
     * 话术名称
     */
    private String dialogFlowName;

    /**
     * 二维码失效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expireTime;

    /**
     * 海报类型
     */
    @Column
    private PosterTypeEnum posterType;
}
