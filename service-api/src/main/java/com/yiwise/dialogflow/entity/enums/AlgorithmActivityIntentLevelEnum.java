package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/9/26
 * @class <code>AlgorithmActivityIntentLevelEnum</code>
 * @see
 * @since JDK1.8
 */
public enum AlgorithmActivityIntentLevelEnum implements CodeDescEnum {
    A(0, "A(意向较强)"),
    B(1, "B级(意向一般)"),
    D(2, "D级(意向较弱)"),
    G(3, "G级别(客户忙)"),
    H(4, "H级别(快速挂机)"),
    J(5, "J级别(待筛选)"),
    M(6, "M级别(免打扰客户)"),
    Q(7, "Q级别(语音助手)"),
    R(8, "R级别(静音挂机)");

    private int code;
    private String desc;

    AlgorithmActivityIntentLevelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    @Override
    public String getDesc() {
        return this.desc;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

}
