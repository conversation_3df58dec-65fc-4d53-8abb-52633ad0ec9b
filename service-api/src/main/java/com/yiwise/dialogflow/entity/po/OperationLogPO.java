package com.yiwise.dialogflow.entity.po;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum;
import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2022/9/13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Document(OperationLogPO.COLLECTION_NAME)
public class OperationLogPO extends BaseTimeUserIdPO {

    public static final String COLLECTION_NAME = "operationLog";

    @Id
    private String id;

    /**
     * botId
     */
    private Long botId;

    /**
     * 操作日志类型
     */
    private OperationLogTypeEnum type;

    /**
     * 操作对象
     */
    private OperationLogResourceTypeEnum resourceType;

    /**
     * 操作日志详情
     */
    private String detail;

    /**
     * 创建人名称
     */
    protected String createUserName;

    /**
     * 记录所在的机器名
     */
    private String hostName;

    /**
     * 记录 requestId
     */
    private String requestId;
}
