package com.yiwise.dialogflow.entity.enums;

import lombok.Getter;

/**
 * 预测类型
 *
 * <AUTHOR>
 * @date 2021/2/3
 */
public enum PredictTypeEnum {
    /**
     * 算法
     */
    ALGORITHM("问法"),
    /**
     * 关键词
     */
    REGEX("关键词"),
    /**
     * 组合意图
     */
    COMPOSITE("组合意图")
    ;

    PredictTypeEnum(String desc) {
        this.desc = desc;
    }

    @Getter
    final String desc;

    public boolean isRegexMatch() {
        return this.equals(REGEX);
    }
}
