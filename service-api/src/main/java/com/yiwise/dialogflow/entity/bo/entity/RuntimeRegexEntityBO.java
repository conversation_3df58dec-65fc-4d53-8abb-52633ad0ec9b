package com.yiwise.dialogflow.entity.bo.entity;

import com.yiwise.dialogflow.entity.po.RegexEntityPO;
import com.yiwise.dialogflow.pattern.PatternEnhance;
import lombok.Data;

import java.util.List;

@Data
public class RuntimeRegexEntityBO extends RegexEntityPO implements RuntimeEntityBO {

    /**
     * 反例正则, 正则表达式中没有引用当前实体的反列列表组成的大的正则表达式
     */
    List<PatternEnhance> preSkipPatternList;

    /**
     * 包含了当前实体引用的反例正则
     */
    List<PatternEnhance> postSkipPatternList;

    List<PatternEnhance> patternList;
}
