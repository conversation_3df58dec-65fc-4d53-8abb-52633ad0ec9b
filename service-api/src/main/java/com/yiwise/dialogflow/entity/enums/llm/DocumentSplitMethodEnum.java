package com.yiwise.dialogflow.entity.enums.llm;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum DocumentSplitMethodEnum implements CodeDescEnum {

    // 按行拆分
    LINE(0, "按行拆分"),
    // 按标点拆分
    PUNCTUATION(1, "按标点拆分"),

    ;

    private final Integer code;
    private final String desc;

    DocumentSplitMethodEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
