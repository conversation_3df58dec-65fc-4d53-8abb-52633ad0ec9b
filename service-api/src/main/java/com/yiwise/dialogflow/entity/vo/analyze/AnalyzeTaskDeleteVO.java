package com.yiwise.dialogflow.entity.vo.analyze;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AnalyzeTaskDeleteVO implements Serializable {

    /**
     * id
     */
    @NotNull(message = "id不能为空")
    Long id;
}
