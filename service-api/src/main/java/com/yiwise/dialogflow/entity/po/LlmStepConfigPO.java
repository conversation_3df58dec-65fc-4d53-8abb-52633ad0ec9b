package com.yiwise.dialogflow.entity.po;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderSplitter;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@Document(collection = LlmStepConfigPO.COLLECTION_NAME)
@CompoundIndexes(
        @CompoundIndex(name = "idx_botId_stepId", def = "{botId: 1, stepId: 1}", unique = true)
)
public class LlmStepConfigPO extends BaseTimeUserIdPO implements MismatchAndUninterrupted, LlmStepVariableAssign, Serializable {

    public static final String COLLECTION_NAME = "llmStepConfig";

    @Id
    String id;

    /**
     * 话术id
     */
    Long botId;

    /**
     * 流程id
     */
    String stepId;

    /**
     * 采集任务型-角色描述
     */
    String roleDesc;

    /**
     * 任务配置型-背景信息
     */
    String background;

    /**
     * 采集任务型-任务配置
     */
    List<LlmStepCollectTaskConfigPO> collectTaskConfigList;

    /**
     * 自由配置型-提示词
     */
    String prompt;

    /**
     * 不允许打断
     */
    Boolean enableUninterrupted;

    /**
     * 允许打断比例, 如果enableInterrupt为true, 且录音播放进度大于该阈值, 才可以打断
     */
    Integer customInterruptThreshold;

    /**
     * 在不可打断进度下, 可以被打断的问答知识id
     */
    List<String> uninterruptedReplyKnowledgeIdList;

    /**
     * 在不可打断进度下, 可以被打断的流程id
     */
    List<String> uninterruptedReplyStepIdList;

    /**
     * 在不可打断进度下, 可以被打断的特殊语境id
     */
    List<String> uninterruptedReplySpecialAnswerIdList;

    /**
     * 不关联问答知识、流程、特殊语境
     */
    Boolean mismatchKnowledgeAndStep;

    /**
     * 不关联全部问答知识
     */
    Boolean mismatchAllKnowledge;

    /**
     * 问答知识id列表
     */
    List<String> mismatchKnowledgeIdList;

    /**
     * 不关联全部流程
     */
    Boolean mismatchAllStep;

    /**
     * 流程id列表
     */
    List<String> mismatchStepIdList;

    /**
     * 不关联全部特殊语境
     */
    Boolean mismatchAllSpecialAnswerConfig;

    /**
     * 特殊语境id列表
     */
    List<String> mismatchSpecialAnswerConfigIdList;

    /**
     * 是否开启动态变量赋值
     */
    Boolean enableAssign;

    /**
     * 变量赋值配置列表
     */
    List<LlmStepVariableAssignConfigPO> variableAssignConfigList;

    /**
     * 是否开启用户无应答
     */
    Boolean enableUserSilence;

    /**
     * 针对当前这个答案设置的用户无应答时长
     */
    private Double customUserSilenceSecond;

    public Set<String> calDependVariableIdSet(DependentResourceBO resource) {
        Set<String> dependVariableIdSet = new HashSet<>();
        if (StringUtils.isNotBlank(getRoleDesc())) {
            dependVariableIdSet.addAll(obtainUsedVarIdSet(getRoleDesc(), resource));
        }
        if (StringUtils.isNotBlank(getBackground())) {
            dependVariableIdSet.addAll(obtainUsedVarIdSet(getBackground(), resource));
        }
        if (CollectionUtils.isNotEmpty(getCollectTaskConfigList())) {
            for (LlmStepCollectTaskConfigPO config : getCollectTaskConfigList()) {
                if (StringUtils.isNotBlank(config.getDesc())) {
                    dependVariableIdSet.addAll(obtainUsedVarIdSet(config.getDesc(), resource));
                }
                if (StringUtils.isNotBlank(config.getGuideAnswer())) {
                    dependVariableIdSet.addAll(obtainUsedVarIdSet(config.getGuideAnswer(), resource));
                }
                if (BooleanUtils.isTrue(config.getEnableAssign()) && CollectionUtils.isNotEmpty(config.getVariableAssignConfigList())) {
                    dependVariableIdSet.addAll(config.getVariableAssignConfigList().stream().map(LlmStepVariableAssignConfigPO::getVariableId).collect(Collectors.toSet()));
                }
            }
        }
        if (StringUtils.isNotBlank(getPrompt())) {
            dependVariableIdSet.addAll(obtainUsedVarIdSet(getPrompt(), resource));
        }
        if (BooleanUtils.isTrue(getEnableAssign()) && CollectionUtils.isNotEmpty(getVariableAssignConfigList())) {
            dependVariableIdSet.addAll(getVariableAssignConfigList().stream().map(LlmStepVariableAssignConfigPO::getVariableId).collect(Collectors.toSet()));
        }
        return dependVariableIdSet;
    }

    private Set<String> obtainUsedVarIdSet(String text, DependentResourceBO resource) {
        return new AnswerPlaceholderSplitter(text, false).getVariableSet()
                .stream().map(resource.getVariableNameIdMap()::get)
                .filter(Objects::nonNull).collect(Collectors.toSet());
    }

    @Override
    public void mapLlmStepVariableAssign(Map<String, String> variableIdMapping) {
        LlmStepVariableAssign.super.mapLlmStepVariableAssign(variableIdMapping);
        if (CollectionUtils.isNotEmpty(getCollectTaskConfigList())) {
            for (LlmStepCollectTaskConfigPO config : getCollectTaskConfigList()) {
                config.mapLlmStepVariableAssign(variableIdMapping);
            }
        }
    }
}