package com.yiwise.dialogflow.entity.po;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CollectNodeEntityItemPO implements Serializable {

    /**
     * 实体 id
     */
    String entityId;

    /**
     * 变量 id
     */
    String variableId;

    /**
     * 重复反问次数
     */
    Integer repeatCount;

    /**
     * 答案列表
     */
    List<NodeAnswer> answerList;
}
