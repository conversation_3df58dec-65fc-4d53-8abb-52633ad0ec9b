package com.yiwise.dialogflow.entity.vo.analyze;

import com.yiwise.dialogflow.entity.enums.AnalyzeTemplateIntentCategoryEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnalyzeTemplateIntentMergeVO extends AnalyzeTemplateIntentQueryVO implements Serializable {

    /**
     * 合并后的意图名称
     */
    @NotBlank(message = "targetName不能为空")
    private String targetName;

    /**
     * 合并后的意图类别
     */
    @NotNull(message = "targetCategory不能为空")
    private AnalyzeTemplateIntentCategoryEnum targetCategory;
}
