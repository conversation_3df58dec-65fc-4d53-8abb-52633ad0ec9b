package com.yiwise.dialogflow.entity.enums;


import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 * @date 2019/5/29 10:34 AM
 **/
public enum SnapshotTypeEnum implements CodeDescEnum {

    TEST(1, "对话流测试"),
    PUBLISHED(2, "对话流发布"),
    ;

    Integer code;
    String desc;
    SnapshotTypeEnum(Integer code, String desc) {
        this.code =code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
