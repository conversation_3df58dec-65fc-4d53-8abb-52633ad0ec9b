package com.yiwise.dialogflow.entity.po;

import com.yiwise.dialogflow.entity.enums.JumpTypeEnum;
import com.yiwise.dialogflow.entity.po.DialogBaseNodePO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 跳转节点
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DialogJumpNodePO extends DialogBaseNodePO {

    private JumpTypeEnum jumpType;

    private String jumpStepId;

}
