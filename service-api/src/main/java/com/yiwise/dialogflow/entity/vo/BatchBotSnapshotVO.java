package com.yiwise.dialogflow.entity.vo;

import com.yiwise.dialogflow.entity.po.BotPO;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/21 10:34:25
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Builder
public class BatchBotSnapshotVO {
    /**
     * 发布成功bot个数
     */
    Integer successNum;

    /**
     * 发布失败bot个数
     */
    Integer failNum;

    /**
     * 发布失败bot信息汇总
     */
    List<BotPO> failBotVOList;
}
