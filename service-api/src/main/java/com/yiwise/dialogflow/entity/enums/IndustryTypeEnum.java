package com.yiwise.dialogflow.entity.enums;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

//@JsonSerialize(using = IndustryTypeEnum.IndustryTypeEnumSerializer.class)
public enum IndustryTypeEnum {
    //使用中
    OTHER("其他行业", true),
    FINANCE("金融行业", true),
    REALTY("房产行业", true),
    COMPANY("企业服务", true),
    INSURANCE("保险行业", true),
    HEALTH("健康保健", true),
    RETAIL("零售行业",true),
    EDUCATION("教育行业", true),
    AUTO("汽车行业", true),
    GOVERNMENT("政府机构", true),
    E_COMMERCE("电商行业", true),
    BANK("银行", true),
    COMMUNICATION("信息通讯", true),
    GAME("游戏行业", true),

    //已刪除
    CONFERENCE("会务营销", false),
    ALLIANCE("招商加盟", false),
    CALLBACK("通知回访", false),
    INTERNET("互联网行业", false),
    CULTURAL("文化行业", false),
    CLOTHING("服装行业", false),
    MARKETING("市场调研", false),
    HUMANRESOURCE("人力资源", false),
    TRANSPORT("交通出行", false),
    WEDDING("婚庆", false),
    SUPPLY_CHAIN("运输行业", false)
    ;

    private String desc;
    private List<IndustrySubTypeEnum> subTypes = new ArrayList<>();
    private boolean display;

    IndustryTypeEnum(String desc, boolean display) {
        this.desc = desc;
        this.display = display;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public boolean isDisplay() {
        return display;
    }

    public List<IndustrySubTypeEnum> getSubTypes() {
        return subTypes;
    }

    public void addSubType(IndustrySubTypeEnum subType) {
        this.subTypes.add(subType);
    }

    public static class IndustryTypeEnumSerializer extends JsonSerializer<IndustryTypeEnum> {
        @Override
        public void serialize(IndustryTypeEnum value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            gen.writeStartObject();
            gen.writeObjectField("name", value.name());
            gen.writeObjectField("desc", value.getDesc());
            gen.writeEndObject();
        }
    }

    public static boolean isOldCallbackType(IndustryTypeEnum industryType, IndustrySubTypeEnum subIndustryType) {
        if (IndustryTypeEnum.CALLBACK.equals(industryType)) {
            return true;
        }
        if (IndustryTypeEnum.COMMUNICATION.equals(industryType) && IndustrySubTypeEnum.CALLBACK.equals(subIndustryType) ) {
            return true;
        }
        if (IndustryTypeEnum.GOVERNMENT.equals(industryType) && IndustrySubTypeEnum.CUSTOMER_INTERVIEW.equals(subIndustryType) ) {
            return true;
        }
        if (IndustryTypeEnum.HEALTH.equals(industryType) && IndustrySubTypeEnum.HEALTH_NOTIFY.equals(subIndustryType) ) {
            return true;
        }
        if (IndustryTypeEnum.INSURANCE.equals(industryType) && IndustrySubTypeEnum.INSURANCE_NOTIFY.equals(subIndustryType)) {
            return true;
        }
        if (IndustryTypeEnum.RETAIL.equals(industryType) && IndustrySubTypeEnum.RETAIL_NOTIFY.equals(subIndustryType)) {
            return true;
        }
        if (IndustryTypeEnum.EDUCATION.equals(industryType) && IndustrySubTypeEnum.EDUCATION_NOTIFY.equals(subIndustryType)) {
            return true;
        }
        if (IndustryTypeEnum.AUTO.equals(industryType) && IndustrySubTypeEnum.AUTO_NOTIFY.equals(subIndustryType)) {
            return true;
        }
        if (IndustryTypeEnum.BANK.equals(industryType) && IndustrySubTypeEnum.BANK_NOTIFY.equals(subIndustryType)) {
            return true;
        }

        return false;
    }
}
