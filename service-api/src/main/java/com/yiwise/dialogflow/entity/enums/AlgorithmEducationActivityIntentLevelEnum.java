package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * 教育行业活动通知算法意向等级
 * <AUTHOR>
 */
public enum AlgorithmEducationActivityIntentLevelEnum implements CodeDescEnum {
    A1(0,"A1(意向最强)"),
    A(1, "A(意向较强)"),
    B2(2,"B2(意向一般)"),
    B(3, "B(意向一般)"),
    D(4, "D(意向较弱)"),
    G(5, "G(客户忙)"),
    H(6, "H(快速挂机)"),
    J(7, "J(待筛选)"),
    M(8, "M(免打扰客户)"),
    Q(9, "Q(语音助手)"),
    R(10, "R(静音挂机)");

    private final int code;
    private final String desc;

    AlgorithmEducationActivityIntentLevelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

}
