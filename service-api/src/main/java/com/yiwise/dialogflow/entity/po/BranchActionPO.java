package com.yiwise.dialogflow.entity.po;

import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.enums.BranchActionCategoryEnum;
import com.yiwise.dialogflow.entity.enums.ConditionVarTypeEnum;
import com.yiwise.dialogflow.entity.enums.VariableTypeEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BranchActionPO implements Serializable {

    /**
     * 动态变量id
     */
    String varId;

    /**
     * 动作类型
     */
    BranchActionCategoryEnum category;

    /**
     * 变量类型
     */
    ConditionVarTypeEnum varType;

    /**
     * 常量值或者变量id
     */
    String value;

    public void validWithResource(DependentResourceBO resource) {
        if (StringUtils.isBlank(varId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "分支动作中动态变量不能为空");
        }
        if (VariableTypeEnum.isNotDynamicVariable(resource.getVarIdTypeMap().get(varId))) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "分支动作中动态变量类型异常");
        }
        if (Objects.isNull(category)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "分支动作中动作类型不能为空");
        }
        if (BranchActionCategoryEnum.isAssign(category)) {
            if (Objects.isNull(varType)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "分支动作赋值变量类型不能为空");
            }
            if (ConditionVarTypeEnum.isVar(varType)) {
                if (StringUtils.isBlank(value)) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "分支动作赋值变量不能为空");
                }
                if (ConditionVarTypeEnum.CUSTOM.equals(varType) &&
                        (VariableTypeEnum.isNotCustomVariable(resource.getVarIdTypeMap().get(value)) && VariableTypeEnum.isNotSystemVariable(resource.getVarIdTypeMap().get(value)))) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "分支动作赋值变量类型异常");
                }
                if (ConditionVarTypeEnum.DYNAMIC.equals(varType) && VariableTypeEnum.isNotDynamicVariable(resource.getVarIdTypeMap().get(value))) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "分支动作赋值变量类型异常");
                }
            } else if (ConditionVarTypeEnum.CONSTANT.equals(varType)) {
                if (StringUtils.isBlank(value)) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "分支动作赋值常量不能为空");
                }
            } else {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "分支动作赋值变量类型异常");
            }
        }
    }
}
