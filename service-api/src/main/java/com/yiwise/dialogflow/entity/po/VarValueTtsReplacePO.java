package com.yiwise.dialogflow.entity.po;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.dialogflow.entity.enums.handler.TtsProviderEnumHandler;
import com.yiwise.middleware.mysql.handler.EnabledStatusEnumHandler;
import com.yiwise.middleware.tts.enums.TtsProviderEnum;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 变量读音值替换
 * 进行 tts 时, 执行替换
 */
@Data
@Table(name = "var_value_tts_replace")
public class VarValueTtsReplacePO extends BaseTimeUserIdPO {

    /**
     * 自增主键
     */
    @Id
    private Long varValueTtsReplaceId;

    /**
     * 租户 id
     */
    private Long tenantId;

    private String varName;

    private String varValue;

    private String replacement;

    @ColumnType(typeHandler = TtsProviderEnumHandler.class)
    private TtsProviderEnum ttsProvider;
}
