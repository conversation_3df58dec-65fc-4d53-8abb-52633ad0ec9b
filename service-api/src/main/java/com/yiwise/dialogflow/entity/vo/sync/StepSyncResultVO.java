package com.yiwise.dialogflow.entity.vo.sync;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StepSyncResultVO implements Serializable {

    /**
     * 发布成功bot个数
     */
    Integer successNum;

    /**
     * 发布失败bot个数
     */
    Integer failNum;

    /**
     * <话术名称,同步失败的流程列表>
     */
    Map<Long, List<String>> failBotMap;
}