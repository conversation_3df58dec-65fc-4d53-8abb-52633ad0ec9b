package com.yiwise.dialogflow.entity.vo.audio;

import com.yiwise.base.common.text.TextPlaceholderTypeEnum;
import com.yiwise.dialogflow.engine.share.common.AnswerPlaceholderElement;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import com.yiwise.dialogflow.entity.enums.RecordingResultsEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/3/6
 */
@Data
public class AnswerPlaceholderElementVO extends AnswerPlaceholderElement implements Serializable {

    private AudioTypeEnum audioType;

    /**
     * 录音时间
     */
    private LocalDateTime recordingTime;

    /**
     * 录音情况
     */
    public RecordingResultsEnum getRecordingResults() {
        if (!TextPlaceholderTypeEnum.TEXT.equals(this.type)) {
            return RecordingResultsEnum.RECORDED;
        }
        if (StringUtils.isBlank(this.getUrl())) {
            return RecordingResultsEnum.NOT_RECORDED;
        }
        if (AudioTypeEnum.COMPOSE.equals(audioType)) {
            return RecordingResultsEnum.COMPOSED;
        }
        return RecordingResultsEnum.RECORDED;
    }
}