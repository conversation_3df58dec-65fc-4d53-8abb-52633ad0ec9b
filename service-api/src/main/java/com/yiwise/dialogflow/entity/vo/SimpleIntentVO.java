package com.yiwise.dialogflow.entity.vo;

import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.entity.enums.CorpusTypeEnum;
import com.yiwise.dialogflow.entity.enums.IntentPropertiesEnum;
import com.yiwise.dialogflow.entity.enums.IntentTypeEnum;
import com.yiwise.dialogflow.entity.po.SimpleIntentInfo;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SimpleIntentVO implements Serializable, SimpleIntentInfo {

    /**
     * 意图id
     */
    String id;

    /**
     * 意图名称
     */
    String name;

    /**
     * 意图类型, SINGLE: 单个意图, COMPOSITE: 组合意图
     */
    IntentTypeEnum intentType;

    /**
     * 意图属性, DEFINITE(0, "肯定"),  DECLINE(2, "拒绝"), NEUTRAL(3, "中性"),
     */
    IntentPropertiesEnum intentProperties;

    public String getIntentTypeName() {
        return intentType == null ? "" : intentType.getDesc();
    }

    public String getIntentPropertiesName() {
        return intentProperties == null ? "" : intentProperties.getDesc();
    }

    /**
     * 是否内置
     */
    Boolean isBuiltIn;

    CorpusTypeEnum corpusType;

    public static SimpleIntentVO createFrom(IntentPO intent) {
        if (Objects.isNull(intent)) {
            return null;
        }
        SimpleIntentVO result = MyBeanUtils.copy(intent, SimpleIntentVO.class);
        result.setIsBuiltIn(false);
        return result;
    }

    public static SimpleIntentVO createFromMapOrBuildIn(Map<String, IntentPO> intentMap, String intentId) {
        if (ApplicationConstant.DEFAULT_INTENT_ID.equals(intentId)) {
            return defaultIntent();
        }
        if (ApplicationConstant.USER_SILENCE_INTENT_ID.equals(intentId)) {
            return userSilence();
        }
        if (ApplicationConstant.COLLECT_SUCCESS_INTENT_ID.equals(intentId)) {
            return collectSuccess();
        }
        if (ApplicationConstant.COLLECT_FAILED_INTENT_ID.equals(intentId)) {
            return collectFailed();
        }
        IntentPO intent = intentMap.get(intentId);
        if (Objects.isNull(intent)) {
            return null;
        }
        SimpleIntentVO result = MyBeanUtils.copy(intent, SimpleIntentVO.class);
        result.setIsBuiltIn(false);
        result.setCorpusType(intent.getCorpusType());
        return result;
    }

    public static SimpleIntentVO userSilence() {
        SimpleIntentVO result = new SimpleIntentVO();
        result.setId(ApplicationConstant.USER_SILENCE_INTENT_ID);
        result.setName(ApplicationConstant.USER_SILENCE_INTENT_NAME);
        result.setIsBuiltIn(true);
        result.setIntentType(IntentTypeEnum.SINGLE);
        result.setIntentProperties(IntentPropertiesEnum.SYSTEM);
        result.setCorpusType(CorpusTypeEnum.BUILD_IN);
        return result;
    }

    public static SimpleIntentVO defaultIntent() {
        SimpleIntentVO result = new SimpleIntentVO();
        result.setId(ApplicationConstant.DEFAULT_INTENT_ID);
        result.setName(ApplicationConstant.DEFAULT_INTENT_NAME);
        result.setIsBuiltIn(true);
        result.setIntentType(IntentTypeEnum.SINGLE);
        result.setIntentProperties(IntentPropertiesEnum.SYSTEM);
        result.setCorpusType(CorpusTypeEnum.BUILD_IN);
        return result;
    }

    public static SimpleIntentVO collectSuccess() {
        SimpleIntentVO result = new SimpleIntentVO();
        result.setId(ApplicationConstant.COLLECT_SUCCESS_INTENT_ID);
        result.setName(ApplicationConstant.COLLECT_SUCCESS_INTENT_NAME);
        result.setIsBuiltIn(true);
        result.setIntentType(IntentTypeEnum.SINGLE);
        result.setIntentProperties(IntentPropertiesEnum.SYSTEM);
        result.setCorpusType(CorpusTypeEnum.BUILD_IN);
        return result;
    }

    public static SimpleIntentVO collectFailed() {
        SimpleIntentVO result = new SimpleIntentVO();
        result.setId(ApplicationConstant.COLLECT_FAILED_INTENT_ID);
        result.setName(ApplicationConstant.COLLECT_FAILED_INTENT_NAME);
        result.setIsBuiltIn(true);
        result.setIntentType(IntentTypeEnum.SINGLE);
        result.setIntentProperties(IntentPropertiesEnum.SYSTEM);
        result.setCorpusType(CorpusTypeEnum.BUILD_IN);
        return result;
    }

    @Override
    public CorpusTypeEnum getCorpusType() {
        return corpusType;
    }
}
