package com.yiwise.dialogflow.entity.vo.llm;

import com.yiwise.dialogflow.entity.enums.LlmLabelTypeEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class LlmLabelQueryVO implements Serializable {

    /**
     * 话术id
     */
    Long botId;

    /**
     * 搜索
     */
    String search;

    /**
     * 类型
     */
    LlmLabelTypeEnum type;
}