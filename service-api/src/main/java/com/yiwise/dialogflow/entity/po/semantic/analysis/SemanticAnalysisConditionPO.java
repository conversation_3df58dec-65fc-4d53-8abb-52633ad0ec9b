package com.yiwise.dialogflow.entity.po.semantic.analysis;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.base.model.bean.po.BaseTimePO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/28
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SemanticAnalysisConditionPO extends BaseTimePO implements Serializable {

    /**
     * 外呼任务ID列表
     */
    List<Long> newRobotCallJobIdList;

    /**
     * 外呼任务ID列表
     */
    List<Long> robotCallJobIdList;

    /**
     * 客户ID列表
     */
    List<Long> tenantIdList;

    /**
     * 话术ID列表
     */
    List<Long> dialogFlowIdList;

    /**
     * 外呼日期-开始
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    LocalDate startDate;

    /**
     * 外呼日期-结束
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd",
            timezone = "GMT+8"
    )
    LocalDate endDate;

    /**
     * 查询到的通话详情ID列表
     */
    List<Long> resultIdList;

    Long callDetailIdGt;
}
