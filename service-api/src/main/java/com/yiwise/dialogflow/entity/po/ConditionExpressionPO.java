package com.yiwise.dialogflow.entity.po;

import com.yiwise.dialogflow.entity.enums.ConditionEnum;
import com.yiwise.dialogflow.entity.enums.ConditionVarTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/30
 */
@Data
public class ConditionExpressionPO implements Serializable {

    /**
     * 前置变量类型
     */
    private ConditionVarTypeEnum preVarType;

    /**
     * 前置变量id
     */
    private String preVarId;

    /**
     * 意图id列表
     */
    private List<String> intentIdList;

    /**
     * 条件
     */
    private ConditionEnum condition;

    /**
     * 后置变量类型
     */
    private ConditionVarTypeEnum postVarType;

    /**
     * 后置变量id
     */
    private String postVarId;

    /**
     * 常量字符串
     */
    private String constantStr;
}
