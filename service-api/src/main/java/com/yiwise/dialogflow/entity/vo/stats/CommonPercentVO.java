package com.yiwise.dialogflow.entity.vo.stats;

import lombok.Data;

import java.io.Serializable;

@Data
public class CommonPercentVO implements Serializable {
    /**
     * 次数
     */
    int count;
    /**
     * 百分比
     */
    double percent;
    /**
     * 用于前端显示的百分比, 进行过四舍五入处理, 99.5
     */
    String displayPercent;

    public CommonPercentVO() {
        this(0, 0);
    }

    public CommonPercentVO(int count, double percent) {
        this.count = count;
        this.percent = percent * 100;
        this.displayPercent = String.format("%.2f", this.percent) + "%";
    }

}
