package com.yiwise.dialogflow.entity.po.intent;

import com.yiwise.dialogflow.entity.enums.ActionCategoryEnum;
import com.yiwise.dialogflow.entity.enums.SmsTemplateSourceEnum;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.yiwise.dialogflow.common.ApplicationConstant.SMS_TEMPLATE_PLACEHOLDER_ID;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/5/23
 * @class <code>RuleActionParam</code>
 * @see
 * @since JDK1.8
 */
@Data
public class RuleActionParam implements Serializable {

    private ActionCategoryEnum actionType;

    /**
     * 是否立即执行?
     * 默认是 false, 即通话结束后再执行, 如果为 true, 则在对话中, 命中次节点/问答知识等, 立即执行
     * 另:
     * 1. 在答案播放前即执行
     * 2. 多次触发节点, 也生成多次执行的指令, 由具体的调用方做业务上的去重处理
     * 3. 断句补齐的清空, 也会执行, 不会进行回退
     * 4. 对于立即执行的指令, 在对话结束后不会再次执行
     */
    private Boolean immediateExecute;

    /**
     * 短信模板来源, 默认为 bot 这边的配置, 可选为使用外呼任务配置的短信模板
     */
    private SmsTemplateSourceEnum smsTemplateSource;

    public SmsTemplateSourceEnum getSmsTemplateSource() {
        if (smsTemplateSource == null) {
            return SmsTemplateSourceEnum.BOT;
        }
        return smsTemplateSource;
    }

    /**
     * 动作依赖的资源, key: 短信模板 id, 白名单分组 id等, value: 名称
     */
    private List<IdNamePair<Long, String>> sourceIdList;

    public List<IdNamePair<Long, String>> getSourceIdList() {
        if (ActionCategoryEnum.SEND_SMS.equals(actionType)) {
            if (SmsTemplateSourceEnum.CALL_JOB.equals(getSmsTemplateSource())) {
                List<IdNamePair<Long, String>> list  = new ArrayList<>();
                list.add(new IdNamePair<>(SMS_TEMPLATE_PLACEHOLDER_ID, "随外呼任务配置"));
                sourceIdList = list;
            } else if (SmsTemplateSourceEnum.BOT.equals(getSmsTemplateSource())) {
                if (CollectionUtils.isNotEmpty(sourceIdList)) {
                    List<IdNamePair<Long, String>> list  = new ArrayList<>();
                    for (IdNamePair<Long, String> pair : sourceIdList) {
                        if (!Objects.equals(SMS_TEMPLATE_PLACEHOLDER_ID, pair.getId())) {
                            list.add(pair);
                        }
                    }
                    sourceIdList = list;
                }
            }
        }
        return sourceIdList;
    }
}
