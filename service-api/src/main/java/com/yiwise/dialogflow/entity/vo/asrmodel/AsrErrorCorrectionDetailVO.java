package com.yiwise.dialogflow.entity.vo.asrmodel;

import com.yiwise.dialogflow.entity.po.asrmodel.AsrErrorCorrectionDetailPO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/18 14:48:41
 */
@Data
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AsrErrorCorrectionDetailVO extends AsrErrorCorrectionDetailPO {
    /**
     * 更新人
     */
    String updateUserName;

    /**
     * 是否已绑定
     */
    Boolean isBound;

    /**
     * botId
     */
    Long botId;

    /**
     * botId
     */
    List<Long> botIdList;

    /**
     * 是否需要训练
     */
    Boolean isNeedTrain;
}