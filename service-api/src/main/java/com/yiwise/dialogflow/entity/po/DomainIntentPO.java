package com.yiwise.dialogflow.entity.po;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.dialogflow.common.MongoCollectionNameCenter;
import com.yiwise.dialogflow.entity.enums.IntentPropertiesEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
@Document(collection = MongoCollectionNameCenter.DOMAIN_INTENT)
public class DomainIntentPO extends BaseTimeUserIdPO {

    /**
     * 意图ID
     */
    @Id
    String id;

    /**
     * 意图名称
     */
    String name;

    /**
     * 领域名称
     */
    String domainName;

    /**
     * 意图属性
     */
    IntentPropertiesEnum intentProperties;

    /**
     * 对外展示的问法列表
     */
    List<String> corpusList;

    /**
     * 内置的问法列表
     */
    List<String> builtInCorpusList;

    /**
     * 对外展示的正则列表
     */
    List<String> regexList;

    /**
     * 内置的正则列表
     */
    List<String> builtInRegexList;
}
