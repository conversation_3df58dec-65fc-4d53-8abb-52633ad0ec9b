package com.yiwise.dialogflow.entity.vo.sync;

import com.yiwise.dialogflow.entity.enums.SyncModeEnum;
import com.yiwise.dialogflow.entity.vo.KnowledgeQueryVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/9/1
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KnowledgeSyncVO extends BasicSyncVO {

    /**
     * 知识相同处理
     */
    SyncModeEnum sameKnowledge;

    /**
     * 触发意图相同处理
     */
    SyncModeEnum sameTriggerIntent;

    /**
     * 知识查询条件
     */
    KnowledgeQueryVO knowledgeQuery;
}
