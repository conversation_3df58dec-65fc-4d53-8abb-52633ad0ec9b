package com.yiwise.dialogflow.entity.vo.analyze;

import com.yiwise.base.model.bean.vo.AbstractQueryVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnalyzeTemplateCorpusQueryVO extends AbstractQueryVO implements Serializable {

    /**
     * 意图id
     */
    @NotBlank(message = "intentId不能为空")
    private String intentId;

    /**
     * 语料模糊查询
     */
    private String search;

    /**
     * id列表
     */
    private List<String> idList;
}
