package com.yiwise.dialogflow.entity.po.asrmodel;


import com.yiwise.base.model.bean.po.BaseTimePO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import java.io.Serializable;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.GeneratedValue;

/**
 * asr语言类型
 *
 * <AUTHOR>
 * @date 2022-11-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "asr_language")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AsrLanguagePO extends BaseTimePO implements Serializable {

	@Id
	@GeneratedValue(generator = "JDBC")
	Long asrLanguageId;

	/**
	 * 语言类型
	 */
	String languageName;

	/**
	 * 默认供应商ID
	 */
	Long defaultProviderId;

}
