package com.yiwise.dialogflow.entity.vo.llm;

import com.yiwise.dialogflow.entity.query.RagDocumentQueryVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FieldDefaults(level = lombok.AccessLevel.PRIVATE)
public class RagDocumentTestRequestVO extends RagDocumentQueryVO implements Serializable {

    /**
     * 测试文案
     */
    @NotBlank(message = "测试文案不能为空")
    String testContent;
}