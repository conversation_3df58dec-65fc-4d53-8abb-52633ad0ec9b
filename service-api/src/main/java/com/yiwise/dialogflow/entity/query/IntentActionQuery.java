package com.yiwise.dialogflow.entity.query;

import com.yiwise.dialogflow.entity.enums.ActionCategoryEnum;
import lombok.Data;

import java.util.List;

@Data
public class IntentActionQuery extends BaseStatsQuery {
    List<ActionCategoryEnum> actionTypeList;

    List<String> intentRuleActionIdList;


    /**
     * 在流程节点删除后, 规则中依赖的节点是不会删除的(对意向等级判断无影响), 但是前端会出现渲染失败的问题
     * 所以需要后端在返回给前端时, 过滤掉无效的节点信息
     * 是否过滤掉无效的流程节点
     */
    Boolean filterInvalidStepNode;

    Boolean filterInvalidKnowledge;
}
