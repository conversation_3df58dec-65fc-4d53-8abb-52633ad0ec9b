package com.yiwise.dialogflow.entity.vo;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/13 17:44:57
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BotAutoImportVO implements Serializable {

    String botName;
    Long botId;
    /**
     * bot生成记录id
     */
    String job_id;

    String complete_rate;

    List<NodeVO> nodeList;
    List<KnowledgeVO> knowledgeList;
    List<SpecialVO> specialList;

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class NodeVO {
        String label;
        List<AnswerVO> answer;
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class AnswerVO {
        String text;
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class KnowledgeVO {
        String name;
        List<AnswerVO> answer;
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class SpecialVO {
        String name;
        List<AnswerVO> answer;
    }
}