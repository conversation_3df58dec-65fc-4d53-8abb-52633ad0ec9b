package com.yiwise.dialogflow.entity.vo;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BotRecommendRephraseRequestVO implements Serializable {

    /**
     * 用户句子
     */
    @NotBlank(message = "userSentence不能为空")
    String userSentence;

    /**
     * 每个例句生成的数量
     */
    @NotNull(message = "count不能为空")
    @Min(value = 0, message = "count不能小于0")
    Integer count;

    /**
     * <引用例句，历史记录列表>
     */
    @NotEmpty(message = "historyMap不能为空")
    Map<String, List<String>> historyMap;
}