package com.yiwise.dialogflow.entity.po.magic;

import javax.persistence.Id;
import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class MagicTemplateVarGenerateRecordDetailPO extends BaseTimeUserIdPO {

    public static final String COLLECTION_NAME = "magicTemplateVarGenerateRecordDetail";

    @Id
    private String id;

    private Long botId;

    /**
     * 记录ID
     */
    private String recordId;

    /**
     * 模板变量ID
     */
    private String variableId;

    /**
     * 模板变量名称
     */
    private String variableName;

    /**
     * 模板变量值
     */
    private String variableValue;

}
