package com.yiwise.dialogflow.entity.vo.node;

import com.yiwise.dialogflow.entity.po.CollectNodeEntityItemPO;
import com.yiwise.dialogflow.entity.po.CollectNodeSkipCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DialogCollectNodeVO extends DialogChatNodeVO implements Serializable {

    /**
     * 实体采集配置列表
     */
    List<CollectNodeEntityItemPO> entityCollectList;

    /**
     * 使用之前的用户输入进行提取
     * 此配置在对话阶段实现为:
     * 此节点的配置将作为全局提取, 每次用户输入时, 都会进行提取, 但是不赋值
     * 优点:
     * 1. 对 StepFlowChatManager改动较小, 不需要将接口改为异步,
     * 2. 实体提取逻辑统一, 比较方便维护
     * 缺点:
     * 1. 每一次用户输入都会进行实体提取, 可能会增加接口延迟(如果最终对话没有走到此节点了)
     */
    Boolean enableCollectWithPreInput;

    /**
     * 是否启用跳过问题条件
     */
    Boolean enableSkipCondition;

    /**
     * 跳过条件组列表
     */
    List<CollectNodeSkipCondition> skipConditionList;
}
