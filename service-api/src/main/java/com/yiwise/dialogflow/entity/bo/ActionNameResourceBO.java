package com.yiwise.dialogflow.entity.bo;

import lombok.Data;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static com.yiwise.dialogflow.common.ApplicationConstant.SMS_TEMPLATE_PLACEHOLDER_ID;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/6/2
 * @class <code>ActionNameResourceBO</code>
 * @see
 * @since JDK1.8
 */
@Data
public class ActionNameResourceBO {

    /**
     * 短信模板名称map
     */
    private Map<Long, String> smsTempId2NameMap = Collections.emptyMap();

    /**
     * 黑名单分组名称map
     */
    private Map<Long, String> groupId2NameMap = Collections.emptyMap();

    /**
     * 标签名称map
     */
    private Map<Long, String> tagId2NameMap = Collections.emptyMap();

    /**
     * <黑名单id,是否同步>
     */
    private Map<Long, Boolean> groupIdSharedCustomersMap = Collections.emptyMap();

    public Map<Long, String> getSmsTempId2NameMap() {
        if (null == smsTempId2NameMap) {
            smsTempId2NameMap = new HashMap<>();
        }

        smsTempId2NameMap = new HashMap<>(smsTempId2NameMap);

        // todo 常量
        if (!smsTempId2NameMap.containsKey(SMS_TEMPLATE_PLACEHOLDER_ID)) {
            smsTempId2NameMap.put(SMS_TEMPLATE_PLACEHOLDER_ID, "随外呼任务配置");
        }

        return smsTempId2NameMap;
    }
}
