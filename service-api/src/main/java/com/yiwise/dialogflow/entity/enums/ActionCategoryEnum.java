package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/5/24
 * @class <code>ActionTypeEnum</code>
 * @see
 * @since JDK1.8
 */
public enum ActionCategoryEnum implements CodeDescEnum {
    WHITE_LIST(1, "加黑名单"),
    SEND_SMS(2, "发短信"),
    ADD_TAG(3, "打标签"),
    ADD_WECHAT(4, "外呼中主动加微"),
    ;

    Integer code;
    String desc;

    ActionCategoryEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

}
