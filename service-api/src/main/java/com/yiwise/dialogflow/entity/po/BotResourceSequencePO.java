package com.yiwise.dialogflow.entity.po;

import com.yiwise.dialogflow.common.MongoCollectionNameCenter;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Document(collection = MongoCollectionNameCenter.BOT_RESOURCE_SEQUENCE)
@CompoundIndexes(
        @CompoundIndex(name = "idx_botId_resourceType", def = "{botId: 1, resourceType: 1}")
)
public class BotResourceSequencePO {

    @Id
    private String id;

    private String resourceType;

    private Long botId;

    private Long sequence;
}
