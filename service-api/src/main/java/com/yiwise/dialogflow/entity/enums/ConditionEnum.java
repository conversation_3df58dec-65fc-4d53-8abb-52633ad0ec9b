package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 * @date 2022/5/30
 */
public enum ConditionEnum implements CodeDescEnum {

    /**
     * 为空
     */
    BLANK(1,"为空"),

    /**
     * 不为空
     */
    NOT_BLANK(2,"不为空"),

    /**
     * 等于
     */
    EQ(3,"等于"),

    /**
     * 不等于
     */
    NEQ(4,"不等于"),

    /**
     * 大于等于
     */
    GE(5,"大于等于"),

    /**
     * 小于等于
     */
    LE(6,"小于等于"),

    /**
     * 大于
     */
    GT(7,"大于"),

    /**
     * 小于
     */
    LT(8,"小于"),

    /**
     * 包含
     */
    CT(9,"包含"),

    /**
     * 不包含
     */
    NCT(10,"不包含"),

    /**
     * 全部命中
     */
    ALL_HITS(11, "全部命中"),

    /**
     * 全部不命中
     */
    ALL_MISS(12, "全部不命中"),

    /**
     * 任意命中
     */
    ANY_HIT(17, "任一命中"),

    /**
     * 任意未命中
     */
    ANY_MISS(18, "任一未命中")
    ;

    private final int code;
    private final String desc;

    ConditionEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    public static boolean onlySupportIntent(ConditionEnum condition) {
        return ALL_MISS.equals(condition) || ALL_HITS.equals(condition);
    }

    public static boolean notSupportIntent(ConditionEnum condition) {
        return !onlySupportIntent(condition);
    }

    public static boolean onlySupportPreValue(ConditionEnum condition) {
        return BLANK.equals(condition) || NOT_BLANK.equals(condition) || onlySupportIntent(condition);
    }
}
