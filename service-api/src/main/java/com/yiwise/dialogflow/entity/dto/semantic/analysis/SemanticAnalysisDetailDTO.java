package com.yiwise.dialogflow.entity.dto.semantic.analysis;

import com.google.common.collect.Maps;
import com.yiwise.batch.common.ExcelRowModel;
import com.yiwise.dialogflow.entity.po.callout.CallDetailPO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.Map;

import static com.yiwise.dialogflow.common.BatchConstant.Header.*;

/**
 * 自定义语义分析详情导出
 *
 * <AUTHOR>
 * @date 2022/11/29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SemanticAnalysisDetailDTO extends CallDetailPO implements ExcelRowModel, Serializable {

    /**
     * 客户名称
     */
    String tenantName;

    /**
     * 话术名称
     */
    String dialogFlowName;

    /**
     * 任务名称
     */
    String robotCallJobName;

    /**
     * 历史命中的意图
     */
    String realIntent;

    /**
     * 当前测试命中的意图
     */
    String testIntent;

    @Override
    public Map<String, Object> getRowModelMap() {
        Map<String, Object> map = Maps.newHashMap();
        map.put(通话ID, getCallRecordId());
        map.put(客户名称, getTenantName());
        map.put(话术名称, getDialogFlowName());
        map.put(任务名称, getRobotCallJobName());
        map.put(语料内容, getText());
        map.put(历史命中意图, getRealIntent());
        map.put(当前命中的意图, getTestIntent());
        map.put(时间, getStartTime());
        return map;
    }
}
