package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum VariableTypeEnum implements CodeDescEnum {
    SYSTEM(0, "内置自定义变量"),
    CUSTOM(1, "自定义变量"),
    DYNAMIC(2, "动态变量"),
    TEMPLATE(3, "模板变量"),
    BUILT_IN_SYSTEM(4, "内置系统变量")
    ;

    VariableTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    String desc;
    Integer code;

    public static boolean isNotBuiltInSystem(VariableTypeEnum type) {
        return type != BUILT_IN_SYSTEM;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public static boolean isNotDynamicVariable(VariableTypeEnum variableType) {
        return variableType != DYNAMIC;
    }

    public static boolean isDynamicVariable(VariableTypeEnum variableType) {
        return variableType == DYNAMIC;
    }

    public static boolean isSystemVariable(VariableTypeEnum variableType) {
        return variableType == SYSTEM;
    }

    public static boolean isCustomVariable(VariableTypeEnum variableType) {
        return variableType == CUSTOM;
    }

    public static boolean isNotCustomVariable(VariableTypeEnum variableType) {
        return variableType != CUSTOM;
    }

    public static boolean isNotSystemVariable(VariableTypeEnum variableType) {
        return variableType != SYSTEM;
    }

    public static boolean isTemplateVariable(VariableTypeEnum variableType) {
        return variableType == TEMPLATE;
    }

    public static boolean isNotTemplateVariable(VariableTypeEnum variableType) {
        return variableType != TEMPLATE;
    }

    public static boolean isCustomOrSystemVariable(VariableTypeEnum variableType) {
        return CUSTOM == variableType || SYSTEM == variableType;
    }
}
