package com.yiwise.dialogflow.entity.po;

import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BotAudioConfigPO {

    Long botId;

    AudioTypeEnum audioType;

    /**
     * 录音师id
     */
    Long recordUserId;

    /**
     * 是否启用变量录音, 默认是允许
     */
    Boolean enableVariableAudio;

    /**
     * 合成音色使用配置
     */
    TtsVoiceConfigPO ttsConfig;

    /**
     * 真人录音时使用的配置
     */
    TtsVoiceConfigPO spliceTtsConfig;

    private Boolean enableBackground;

    private List<BackgroundAudioConfigPO> backgroundList;
}
