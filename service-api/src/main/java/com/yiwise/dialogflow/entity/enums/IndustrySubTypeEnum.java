package com.yiwise.dialogflow.entity.enums;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.IOException;

//@JsonSerialize(using = IndustrySubTypeEnum.IndustrySubTypeEnumSerializer.class)
public enum IndustrySubTypeEnum {
    OTHER(IndustryTypeEnum.OTHER, "其他", true),

    LOAN(IndustryTypeEnum.FINANCE, "金融贷款", true),
    STOCK(IndustryTypeEnum.FINANCE, "投资理财", true),
    DEBT_COLLECTION(IndustryTypeEnum.FINANCE, "催收", true),
    FINICING(IndustryTypeEnum.FINANCE, "投资理财", false),
    ACCOUNT_INVESTMENT(IndustryTypeEnum.FINANCE, "开户邀请", true),
    FINANCE_OTHER(IndustryTypeEnum.FINANCE, "其他", true), //新

    CREDIT_CARD(IndustryTypeEnum.FINANCE, "信用卡"), //删除
    EXCHANGE(IndustryTypeEnum.FINANCE, "外汇"), // 删除
    STOCK_INVESTMENT(IndustryTypeEnum.FINANCE, "股权投资"),// 删
    OPTION(IndustryTypeEnum.FINANCE, "个股期权"),// 删
    AUTO_LOAN(IndustryTypeEnum.FINANCE, "车贷"),// 删
    INSTALLMENT(IndustryTypeEnum.FINANCE, "分期"),// 删
    FINICE(IndustryTypeEnum.FINANCE, "融资"),// 删
    DEPOSIT(IndustryTypeEnum.FINANCE, "提现"),// 删
    BLOCK_CHAIN(IndustryTypeEnum.FINANCE, "区块链"),// 删
    WAKE_UP(IndustryTypeEnum.FINANCE, "客户唤醒"),// 删
    ALLOCATION(IndustryTypeEnum.FINANCE, "配资"),// 删
    STOCK_APP(IndustryTypeEnum.FINANCE, "炒股APP"), // 删


    MANSION(IndustryTypeEnum.REALTY, "豪宅"),// 删
    HOUSE(IndustryTypeEnum.REALTY, "住宅"),// 删
    DECORATION(IndustryTypeEnum.REALTY, "房屋装修", true),
    INTERMEDIARY(IndustryTypeEnum.REALTY, "中介"),// 删
    SHOP(IndustryTypeEnum.REALTY, "商铺"),// 删
    FOUREIGN_REALTY(IndustryTypeEnum.REALTY, "海外房产"),// 删
    RENT_OR_SALE(IndustryTypeEnum.REALTY, "出租出售"),// 删
    OFFICE_BUILDING(IndustryTypeEnum.REALTY, "写字楼"),// 删
    FURNITURE(IndustryTypeEnum.REALTY, "家具"),// 删
    COMPLEX(IndustryTypeEnum.REALTY, "商业综合体"),// 删
    DIVERSION(IndustryTypeEnum.REALTY, "案场导流", false), // 删

    BUILDING_RECOMMEND(IndustryTypeEnum.REALTY, "楼盘推介", true), //新
    HOUSE_LEASE(IndustryTypeEnum.REALTY, "房屋租赁", true),//新
    REALTY_OTHER(IndustryTypeEnum.REALTY, "其他", true), // 新

    COMPANY_OTHER(IndustryTypeEnum.COMPANY, "其他", true), // 新
    LEGAL_SERVICE(IndustryTypeEnum.COMPANY, "法律服务", true),
    FINANCE_SERVICE(IndustryTypeEnum.COMPANY, "财税服务", true),// 新
    SOFTWARE_SERVICE(IndustryTypeEnum.COMPANY, "软件服务", true), // new
    PROPERTY_RIGHT(IndustryTypeEnum.COMPANY, "产权/资质", true), //new
    MARKETING_PLAN(IndustryTypeEnum.COMPANY, "营销策划", true),//new
    HUMAN_RESOURCE(IndustryTypeEnum.COMPANY, "人力资源", true),//new

    ACCOUNTING(IndustryTypeEnum.COMPANY, "代理记账"), // 删
    TRANSFER(IndustryTypeEnum.COMPANY, "公司转让"),// 删
    SHOP_MANAGING(IndustryTypeEnum.COMPANY, "店铺代运营"),// 删
    FOUREIGN_MERCHANT(IndustryTypeEnum.COMPANY, "外贸"),// 删
    ADVERTISING(IndustryTypeEnum.COMPANY, "广告推广"),// 删
    TRADEMARK(IndustryTypeEnum.COMPANY, "产权商标"),// 删
    INSPECTION(IndustryTypeEnum.COMPANY, "检验认证"),// 删
    ENTERPRISE_INVESTMENT(IndustryTypeEnum.COMPANY, "企业招商"),// 删
    CHECK(IndustryTypeEnum.COMPANY, "承诺汇票"),// 删
    ACCOUNT_APP(IndustryTypeEnum.COMPANY, "财务软件"),// 删
    VERIFICATION(IndustryTypeEnum.COMPANY, "认证"),// 删
    WIN_WIN(IndustryTypeEnum.COMPANY, "双创"),// 删

    E_RECEIPT(IndustryTypeEnum.COMPANY, "电子发票"),// 删
    CONSULTATION(IndustryTypeEnum.COMPANY, "企业管理咨询"),// 删
    FACILITY_RENT(IndustryTypeEnum.COMPANY, "设备租凭"),// 删
    PUBLICIST(IndustryTypeEnum.COMPANY, "公关"),// 删
    GIFT_CARD(IndustryTypeEnum.COMPANY, "礼品卡"),// 删

    INSURANCE_OTHER(IndustryTypeEnum.INSURANCE, "其他", true), // new
    INSURANCE_RECOMMEND(IndustryTypeEnum.INSURANCE, "保险推荐", true), //new
    INSURANCE_NOTIFY(IndustryTypeEnum.INSURANCE, "通知回访", true), //new

    AUTO_INSURANCE(IndustryTypeEnum.INSURANCE, "车险"), //del
    LIFE_INSURANCE(IndustryTypeEnum.INSURANCE, "人寿险"), //del
    MEDICAL_INSURANCE(IndustryTypeEnum.INSURANCE, "医疗保险"), //del
    INSURANCE_SALE(IndustryTypeEnum.INSURANCE, "保险销售"), //del
    ENDOWMENT_INSURANCE(IndustryTypeEnum.INSURANCE, "养老保险"),//del
    HEALTH_INSURANCE(IndustryTypeEnum.INSURANCE, "健康险"),// del
    INJURY_INSURANCE(IndustryTypeEnum.INSURANCE, "工伤"),//del
    INSURANCE_AGENT(IndustryTypeEnum.INSURANCE, "保险代理"),//del

    HEALTH_NOTIFY(IndustryTypeEnum.HEALTH, "通知回访", true), //new
    HEALTH_PRODUCT_RECOMMEND(IndustryTypeEnum.HEALTH, "产品推介", true), //new
    HEALTH_ALLIANCE(IndustryTypeEnum.HEALTH, "招商加盟", true),//new
    HEALTH_OTHER(IndustryTypeEnum.HEALTH, "其他", true),


    BEAUTY(IndustryTypeEnum.HEALTH, "美容"), // del
    HEALTH_PRODUCT(IndustryTypeEnum.HEALTH, "保健品"),// del
    HEAKTH_FACILITY(IndustryTypeEnum.HEALTH, "保健器械"),// del
    DIET(IndustryTypeEnum.HEALTH, "减肥"),// del
    HEALTH_CHECK(IndustryTypeEnum.HEALTH, "体检"),// del
    HOSPITAL(IndustryTypeEnum.HEALTH, "医院"),// del
    FIT(IndustryTypeEnum.HEALTH, "健身"),// del
    DENTIST(IndustryTypeEnum.HEALTH, "牙医"),// del

    MEETING(IndustryTypeEnum.CONFERENCE, "会议邀约"), //del
    EXHIBITION(IndustryTypeEnum.CONFERENCE, "展会邀约"), //del
    EXHIBITION_ENTERPRISE(IndustryTypeEnum.CONFERENCE, "会议邀约(企业)"), //del


    // 零售行业
    RETAIL_ALLIANCE(IndustryTypeEnum.RETAIL, "招商加盟", true), //new
    PRODUCT_SALE(IndustryTypeEnum.RETAIL, "产品销售", true), //new
    RETAIL_NOTIFY(IndustryTypeEnum.RETAIL, "客户回访", true), //new
    RETAIL_OTHER(IndustryTypeEnum.RETAIL, "其他", true), //new

    FAST_FOOD(IndustryTypeEnum.ALLIANCE, "快餐店"), //del
    SNACK(IndustryTypeEnum.ALLIANCE, "小吃店"),//del
    ESTORE(IndustryTypeEnum.ALLIANCE, "网店"),//del
    ALCOHOL(IndustryTypeEnum.ALLIANCE, "酒类"),//del
    APP(IndustryTypeEnum.ALLIANCE, "APP招商"),//del
    INVESTMENT(IndustryTypeEnum.ALLIANCE, "投资邀约"),//del
    BEALTY_INVESTMENT(IndustryTypeEnum.ALLIANCE, "美容"),//del
    RESTARUNT(IndustryTypeEnum.ALLIANCE, "饭店"),//del
    BABY(IndustryTypeEnum.ALLIANCE, "母婴"),//del
    FOOD(IndustryTypeEnum.ALLIANCE, "食品"),//del

    EDUCATION_ONLINE(IndustryTypeEnum.EDUCATION, "线上公海", true), //new
    EDUCATION_OFFLINE(IndustryTypeEnum.EDUCATION, "线下招生", true), //new
    EDUCATION_REMIND(IndustryTypeEnum.EDUCATION, "催到课", true),//new
    OFFLINE_BLUE_WATER(IndustryTypeEnum.EDUCATION, "线下公海", true),//new
    EDUCATION_NOTIFY(IndustryTypeEnum.EDUCATION, "信息流回访", true),//new
    EDUCATION_STRANGER_VISIT(IndustryTypeEnum.EDUCATION, "陌客拜访", true),//new
    EDUCATION_OTHER(IndustryTypeEnum.EDUCATION, "其他", true),//new

    ADULT_EDUCATION(IndustryTypeEnum.EDUCATION, "成人培训"), //del
    SKILL_EDUCATION(IndustryTypeEnum.EDUCATION, "技能培训"), //del
    SPEECH(IndustryTypeEnum.EDUCATION, "课程演讲"),//del
    START_NOTIFICATION(IndustryTypeEnum.EDUCATION, "开课通知"), //del
    ADMISSION(IndustryTypeEnum.EDUCATION, "招生"), //del
    ONLINE_EDUCATION(IndustryTypeEnum.EDUCATION, "线上培训"), //del
    DAILY_EDUCATION(IndustryTypeEnum.EDUCATION, "日托"), //del

    // 信息通讯
    CALLBACK(IndustryTypeEnum.COMMUNICATION, "通知回访", true), //new
    NOTIFY_NO_SAY(IndustryTypeEnum.COMMUNICATION, "通知（无对话）", true),//new
    BUSINESS_RECOMMEND(IndustryTypeEnum.COMMUNICATION, "业务推介", true),//new
    COMMUNICATION_OTHER(IndustryTypeEnum.COMMUNICATION, "其他", true),//new

    // 市政
    GOVERNMENT_NOTIFY(IndustryTypeEnum.GOVERNMENT, "信息通知", true),//new
    CUSTOMER_INTERVIEW(IndustryTypeEnum.GOVERNMENT, "客户回访", true),//new
    CUSTOMER_DEBT_COLLECTION(IndustryTypeEnum.GOVERNMENT, "催收", true),//new
    CUSTOMER_OTHER(IndustryTypeEnum.GOVERNMENT, "其他", true),//new


    STATISFICATION(IndustryTypeEnum.CALLBACK, "满意度调查"), //del
    GOV_NOTIFICATION(IndustryTypeEnum.CALLBACK, "政府通知"), //del
    INVESTIGATION(IndustryTypeEnum.CALLBACK, "使用情况调查"),//del
    ADDRESS_VERIFICATION(IndustryTypeEnum.CALLBACK, "确认地址"),//del
    URGE(IndustryTypeEnum.CALLBACK, "催退收货"), //del
    NOTIFICATION(IndustryTypeEnum.CALLBACK, "通知"), //del


    AUTO_SALES(IndustryTypeEnum.AUTO, "汽车置换", true),//new
    AUTO_MAINTAIN(IndustryTypeEnum.AUTO, "汽车保养", true),//new
    AUTO_INVITE(IndustryTypeEnum.AUTO, "邀请试驾", true),//new
    AUTO_INSURANCE_SALE(IndustryTypeEnum.AUTO, "车险销售", true),//new
    AUTO_NOTIFY(IndustryTypeEnum.AUTO, "通知回访", true),//new
    AUTO_LOANS(IndustryTypeEnum.AUTO, "汽车贷款", true),//new
    AUTO_OTHER(IndustryTypeEnum.AUTO, "其他", true),//new

    USED_CAR(IndustryTypeEnum.AUTO, "二手车"), //del
    ACCESSORIES(IndustryTypeEnum.AUTO, "配件"),//del
    NEW_CAR(IndustryTypeEnum.AUTO, "新车"),//del
    GAS(IndustryTypeEnum.AUTO, "加油"),//del

    WECHAT_APP(IndustryTypeEnum.INTERNET, "小程序"),//del
    SEO(IndustryTypeEnum.INTERNET, "SEO"),//del
    TAOBAO_COMMENT(IndustryTypeEnum.INTERNET, "淘宝评论"),//del
    SOFTWARE(IndustryTypeEnum.INTERNET, "软件服务"),//del
    BIG_DATA(IndustryTypeEnum.INTERNET, "大数据营销"),//del
    WEB_BUILD(IndustryTypeEnum.INTERNET, "网站制作"),//del
    PHOTOGRAPH(IndustryTypeEnum.INTERNET, "淘宝拍摄"),//del
    INTERNET_SERVICE(IndustryTypeEnum.INTERNET, "电商服务"),//del

    COLLECTION(IndustryTypeEnum.CULTURAL, "藏品收藏"), //del
    PUBLISHING(IndustryTypeEnum.CULTURAL, "出书"), //del
    PUBLISHING_RIGHT(IndustryTypeEnum.CULTURAL, "知识产权"), //del
    MAGAZINE(IndustryTypeEnum.CULTURAL, "杂志销售"), //del
    PAINTING(IndustryTypeEnum.CULTURAL, "书画"), //del

    PERSONAL(IndustryTypeEnum.CLOTHING, "私人定制"), //del
    FOUREIGN_CLOTHING(IndustryTypeEnum.CLOTHING, "外贸"), //deld

    MOVIE(IndustryTypeEnum.MARKETING, "影视"), //del

    HIRE(IndustryTypeEnum.HUMANRESOURCE, "招聘"), //del

    AIRLINE_TICKET(IndustryTypeEnum.TRANSPORT, "飞机票"), //del
    TRAVEL_NOTIFICATION(IndustryTypeEnum.TRANSPORT, "登机登车提醒"), //del

    GOV_SERVICE(IndustryTypeEnum.GOVERNMENT, "市政服务"), //del
    HOUSEHOLD(IndustryTypeEnum.GOVERNMENT, "入户"), //del

    WED_WEB(IndustryTypeEnum.WEDDING, "婚庆网站"),//del
    WED_SERVICE(IndustryTypeEnum.WEDDING, "婚庆服务"),//del
    WED_INVESTMENT(IndustryTypeEnum.WEDDING, "活动邀请"), //del

    LOGISTICS(IndustryTypeEnum.SUPPLY_CHAIN, "物流"), //del

    PLATFORM_PROMOTION(IndustryTypeEnum.GAME, "游戏平台推广", false),
    SINGLE_PLAYER_GAME(IndustryTypeEnum.GAME, "单机游戏", false),
    ONLINE_GAME(IndustryTypeEnum.GAME, "网游", false),
    GAME_CHANNEL(IndustryTypeEnum.GAME, "游戏渠道", false),
    GAME_PROXY(IndustryTypeEnum.GAME, "游戏代理", false),
    GAME_NEW(IndustryTypeEnum.GAME, "拉新", true),
    GAME_RECALL(IndustryTypeEnum.GAME, "老客户召回", true),
    GAME_OTHER(IndustryTypeEnum.GAME, "其他", true),

    BROADBAND(IndustryTypeEnum.COMMUNICATION, "宽带"), //del
    PLAN_UPGRADE(IndustryTypeEnum.COMMUNICATION, "套餐升级"), //del
    CONTRACT_PHONE(IndustryTypeEnum.COMMUNICATION, "合约机"), //del

    E_COMMERCE_DIVERSION(IndustryTypeEnum.E_COMMERCE, "私域导流", true),
    PROMOTION_NOTIFY(IndustryTypeEnum.E_COMMERCE, "活动通知", true),
    INVITE_ZAN(IndustryTypeEnum.E_COMMERCE, "邀请好评", true),
    STOCK_OUT(IndustryTypeEnum.E_COMMERCE, "缺货提醒", true),
    E_COMMERCE_SURVEY(IndustryTypeEnum.E_COMMERCE, "回访调查", true),
    REMIND_PAY(IndustryTypeEnum.E_COMMERCE, "催付", true),
    E_COMMERCE_OTHER(IndustryTypeEnum.E_COMMERCE, "其他", true),


    BANK_LOAN(IndustryTypeEnum.BANK, "贷款", true),
    BANK_DEBT_COLLECTION(IndustryTypeEnum.BANK, "催收", true),
    BANK_SPREAD(IndustryTypeEnum.BANK, "产品推介", true),
    BANK_NOTIFY(IndustryTypeEnum.BANK, "通知回访", true),
    BANK_OTHER(IndustryTypeEnum.BANK, "其他", true),

    ;

    private String desc;
    private boolean display;

    IndustrySubTypeEnum(IndustryTypeEnum IndustryTypeEnum, String desc, boolean display) {
        IndustryTypeEnum.addSubType(this);
        this.desc = desc;
        this.display = display;
    }
    IndustrySubTypeEnum(IndustryTypeEnum IndustryTypeEnum, String desc) {
        this(IndustryTypeEnum, desc, false);
    }

    public boolean isDisplay() {
        return display;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static class IndustrySubTypeEnumSerializer extends JsonSerializer<IndustrySubTypeEnum> {
        @Override
        public void serialize(IndustrySubTypeEnum value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            gen.writeStartObject();
            gen.writeObjectField("name", value.name());
            gen.writeObjectField("desc", value.getDesc());
            gen.writeEndObject();
        }
    }
}
