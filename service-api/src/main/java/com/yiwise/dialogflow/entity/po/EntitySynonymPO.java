package com.yiwise.dialogflow.entity.po;

import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * 实体同义词
 */
@Data
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class EntitySynonymPO implements Serializable {

    /**
     * 实体成员名称, 提取后归一为name值
     */
    String name;

    /**
     * 同义词列表
     */
    List<String> synonymList;

    /**
     * 正则同义词列表
     */
    List<String> regexSynonymList;
}
