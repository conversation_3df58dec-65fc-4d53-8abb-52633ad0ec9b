package com.yiwise.dialogflow.entity.po;


import com.yiwise.base.model.bean.po.BaseTimePO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import java.io.Serializable;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.GeneratedValue;

/**
 * 通话详情意图命中记录
 *
 * <AUTHOR>
 * @date 2022-12-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "session_detail_intent_info")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SessionDetailIntentInfoPO extends BaseTimePO implements Serializable {

	/**
	 * 唯一主键
	 */
	@Id
	@GeneratedValue(generator = "JDBC")
	Long sessionDetailIntentInfoId;

	/**
	 * 话术ID
	 */
	Long botId;

	/**
	 * 会话ID
	 */
	String sessionId;

	/**
	 * 序号
	 */
	Integer seq;

	/**
	 * 用户输入
	 */
	String userInput;

	/**
	 * 意图ID
	 */
	String intentId;

	/**
	 * 意图名称
	 */
	String intentName;

}
