package com.yiwise.dialogflow.entity.po;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.dialogflow.entity.enums.AssignInfoSaveModeEnum;
import com.yiwise.dialogflow.entity.enums.TemplateVariableConfigTypeEnum;
import com.yiwise.dialogflow.entity.enums.VariableTypeEnum;
import com.yiwise.middleware.tts.enums.VarInterpretTypeEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
@Document(collection = VariablePO.COLLECTION_NAME)
public class VariablePO extends BaseTimeUserIdPO {

    public static final String COLLECTION_NAME = "variable";

    @Id
    String id;

    /**
     * botId
     */
    Long botId;

    /**
     * 变量名称
     */
    String name;

    /**
     * 变量类型
     */
    VariableTypeEnum type;

    /**
     * 变量描述信息
     */
    String desc;

    /**
     * 发音类型
     */
    VarInterpretTypeEnum interpretType;

    public VarInterpretTypeEnum getInterpretType() {
        if (Objects.isNull(interpretType)) {
            return VarInterpretTypeEnum.DEFAULT;
        }
        return interpretType;
    }

    /**
     * 是否启用采集信息保存到客户属性字段
     */
    Boolean enableSave;

    /**
     * 对话中多次采集保存模式
     */
    AssignInfoSaveModeEnum saveMode;

    /**
     * 可选的, 自定义变量新增可选字段, 如果是可选的, 需要在轻量化侧选择该变量, 如果不选择, 则在导入客户时, 不提供该字段的导入
     */
    Boolean isOptional;

    public Boolean getIsOptional() {
        if (isOptional == null) {
            isOptional = false;
        }
        return isOptional;
    }

    public AssignInfoSaveModeEnum getSaveMode() {
        return Objects.isNull(saveMode) ? AssignInfoSaveModeEnum.NEWEST : saveMode;
    }

    /**
     * 是否启用相同实体值去重
     */
    Boolean enableDeduplicate;

    public Boolean getEnableDeduplicate() {
        return Objects.isNull(enableDeduplicate) || enableDeduplicate;
    }

    /**
     * 客户属性id
     */
    Long customerAttributeId;

    /**
     * 模板变量的默认值
     */
    String defaultValue;

    /**
     * 是否启用自动对齐
     */
    Boolean enableAutoAlign;

    /**
     * 模板变量配置方式
     */
    TemplateVariableConfigTypeEnum templateVariableConfigType;

    public TemplateVariableConfigTypeEnum getTemplateVariableConfigType() {
        if (templateVariableConfigType == null) {
            templateVariableConfigType = TemplateVariableConfigTypeEnum.DEFAULT_VALUE;
        }
        return templateVariableConfigType;
    }

    /**
     * 提示词
     */
    String prompt;

    /**
     * 模板句
     */
    String templateSentence;
}
