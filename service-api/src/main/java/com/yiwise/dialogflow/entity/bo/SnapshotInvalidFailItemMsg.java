package com.yiwise.dialogflow.entity.bo;

import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.entity.enums.BotResourceTypeEnum;
import lombok.Builder;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Builder
@Data
public class SnapshotInvalidFailItemMsg {
    BotResourceTypeEnum resourceType;
    String resourceId;
    String resourceName;
    String resourceLabel;
    String nodeId;
    String nodeName;
    String nodeLabel;
    String failMsg;
    // 是否是告警, 如果只是告警, 不影响快照的发布
    Boolean isWarning = false;

    public static SnapshotInvalidFailItemMsg fromAnswerLocate(AnswerLocateBO locate, String errorMsg) {
        if (Objects.isNull(locate)) {
            return null;
        }
        SnapshotInvalidFailItemMsg result = null;
        switch (locate.getAnswerSource()) {
            case STEP:
                result = SnapshotInvalidFailItemMsg
                        .builder()
                        .resourceType(BotResourceTypeEnum.NODE)
                        .resourceId(locate.getStepId())
                        .failMsg(errorMsg)
                        .resourceName(locate.getStepName())
                        .resourceLabel(locate.getStepLabel()).nodeId(locate.getNodeId())
                        .nodeName(locate.getNodeName())
                        .nodeLabel(locate.getNodeLabel())
                        .build();
                break;
            case KNOWLEDGE:
                result = SnapshotInvalidFailItemMsg
                        .builder()
                        .resourceType(BotResourceTypeEnum.KNOWLEDGE)
                        .resourceId(locate.getKnowledgeId())
                        .failMsg(errorMsg)
                        .resourceName(locate.getKnowledgeName())
                        .resourceLabel(locate.getKnowledgeLabel())
                        .build();
                break;
            case SPECIAL_ANSWER:
                result = SnapshotInvalidFailItemMsg
                        .builder()
                        .resourceType(BotResourceTypeEnum.SPECIAL_ANSWER_CONFIG)
                        .resourceId(locate.getSpecialAnswerConfigId())
                        .failMsg(errorMsg)
                        .resourceName(locate.getSpecialAnswerConfigName())
                        .resourceLabel(locate.getSpecialAnswerConfigLabel())
                        .build();
                break;
            default:
                break;
        }
        return result;
    }
}
