package com.yiwise.dialogflow.entity.po.botgenerate;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.base.model.enums.EnabledStatusEnum;
import com.yiwise.dialogflow.entity.enums.BotGenerateTemplateStatusEnum;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

/**
 * bot生成模板
 */
@Data
@Document(collection = BotGenerateTemplatePO.COLLECTION_NAME)
public class BotGenerateTemplatePO extends BaseTimeUserIdPO {

    public static final String COLLECTION_NAME = "botGenerateTemplate";

    private String id;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 对应配置id
     */
    private String configId;

    /**
     * 表单模板内容, 前端传入的json, 会原样存储
     */
    private Map<String, Object> payload;

    /**
     * 最后生成完成度
     */
    private String lastCompletePercent;

    /**
     * 逻辑删除
     */
    private EnabledStatusEnum enabledStatus;

    /**
     * 当前状态
     */
    private BotGenerateTemplateStatusEnum status;
}
