package com.yiwise.dialogflow.entity.vo.smsGenerate;

import com.yiwise.dialogflow.entity.enums.SmsGenerateLinkTypeEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SmsGenerateCreateVO implements Serializable {

    /**
     * 话术id
     */
    @NotNull(message = "botId不能为空")
    Long botId;

    /**
     * 是否插入引流拦截
     */
    @NotNull(message = "insertLink不能为空")
    Boolean insertLink;

    /**
     * 链接内容类型
     */
    SmsGenerateLinkTypeEnum linkType;

    /**
     * 自定义短信链接
     */
    String link;
}
