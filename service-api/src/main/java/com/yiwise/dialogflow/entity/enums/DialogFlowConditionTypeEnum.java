package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum DialogFlowConditionTypeEnum implements CodeDescEnum {
    MAIN_STEP_FINISH_PERCENTAGE(1, "主流程完成度"),
    DIALOG_ROUND_COUNT(2, "对话轮次"),
    DIALOG_STATUS(3, "通话状态"),
    DIALOG_DURATION(4, "通话时长"),
    CUSTOMER_SAY_WORD_COUNT(5, "客户说话字数"),
    BUSINESS_KNOWLEDGE_TRIGGER_COUNT(6, "触发业务知识/流程次数"),
    DECLINE_TRIGGER_COUNT(7, "触发拒绝次数"),
    DEFINITIVE_TRIGGER_COUNT(8, "触发肯定次数"),
    TRIGGER_PROCESS_NODE(9, "触发流程节点"),
    CUSTOMER_HANGUP_ON_SPECIAL_NODE(10, "当前节点客户主动挂断"),
    TRIGGER_ROBOT_KNOWLEDGE(11, "触发问答知识"),
    TRIGGER_DEPENDENCE_DIALOGFLOW(12, "触发独立对话流"),
    CUSTOMER_FINALLY_REJECT(13, "用户最后拒绝"),
    CUSTOMER_FINALLY_DEFINITIVE(14, "用户最后肯定"),
    AI_UNKNOWN_PERCENT(15, "ai无法应答占比"),
    TRIGGER_SPECIAL_CONTEXT(16, "触发特殊语境"),
    FAST_HANGUP(17, "快速挂断"),
    CUSTOMER_HANGUP(18, "用户主动挂断"),
    DIALOG_CONTENT(19, "通话内容"),
    LAST_CUSTOMER_CONTENT(20, "客户最后通话内容"),
    STEP_NODE_FINISH_PERCENTAGE(21, "流程节点完成度"),
    KNOWLEDGE_FINISH_PERCENTAGE(22, "问答知识完成度"),
    ALGORITHM_PRIVATE_DOMAIN_INTENT_LEVEL(23, "算法判断意向等级(电商主动加微)"),
    ALGORITHM_ACTIVITY_INTENT_LEVEL(24, "算法判断意向等级(电商活动通知)"),
    INTENT_TAG(25, "意向标签"),
    ALGORITHM_PRIVATE_DOMAIN_INTENT_LEVEL_PASSIVE(26, "算法判断意向等级(电商被动加微)"),
    ALGORITHM_EDUCATION_ACTIVITY_INTENT_LEVEL(27, "算法判断意向等级(教育活动通知)"),
    ENTITY_COLLECT(28, "实体采集"),
    HIT_INTENT(29, "意图名称"),
    DYNAMIC_VARIABLE(30, "动态变量"),
    LLM_LABEL(31, "大模型通用分类"),
    LLM_BUILT_IN_TAG(32, "大模型通话标签"),
    LLM_CUSTOM_TAG(33, "大模型自定义通话标签"),
    EFFECTIVE_CHAT_ROUNDS(34, "有效对话轮次"),
    USER_LAST_SAY(35, "用户最后说话"),
    LAST_AI_CONTENT(36, "ai最后通话内容"),
    ACTUAL_DIALOG_DURATION(37, "实际通话时长"),
    ;

    private Integer code;
    private String desc;

    DialogFlowConditionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
