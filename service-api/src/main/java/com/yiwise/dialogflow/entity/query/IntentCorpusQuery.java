package com.yiwise.dialogflow.entity.query;

import com.yiwise.base.model.bean.vo.AbstractQueryVO;
import com.yiwise.dialogflow.entity.enums.IntentSearchScopeEnum;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/15
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Builder
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class IntentCorpusQuery extends AbstractQueryVO {

    Long botId;

    Collection<String> intentIdList;

    String keyword;

    /**
     * 搜索的scope集合
     */
    List<IntentSearchScopeEnum> intentSearchScopeList;
}
