package com.yiwise.dialogflow.entity.po.botgenerate;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.dialogflow.entity.enums.BotGenerateConfigTypeEnum;
import com.yiwise.middleware.objectstorage.annotation.AddOssPrefix;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * bot生成配置
 * 主要是维护表单元数据的信息
 */
@Data
@Document(collection = BotGenerateConfigPO.COLLECTION_NAME)
public class BotGenerateConfigPO extends BaseTimeUserIdPO {

    public static final String COLLECTION_NAME = "botGenerateConfig";

    BotGenerateConfigTypeEnum type;

    /**
     * 配置id
     */
    String id;

    /**
     * 配置文件的oss key(算法侧直接上传的schema/template文件, 会被重命名)
     */
    @AddOssPrefix
    String ossKey;

}
