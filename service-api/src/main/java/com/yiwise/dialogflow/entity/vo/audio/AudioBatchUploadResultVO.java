package com.yiwise.dialogflow.entity.vo.audio;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AudioBatchUploadResultVO {
    Integer totalCount;
    Integer successCount;
    Integer failCount;
    List<Detail> detailList;
    String failDetailFileUrl;
    @Data
    public static class Detail {
        String fileName;
        String message;
        public Detail(String fileName, String errorMsg) {
            this.fileName = fileName;
            this.message = errorMsg;
        }

        public Detail() {

        }
    }
}
