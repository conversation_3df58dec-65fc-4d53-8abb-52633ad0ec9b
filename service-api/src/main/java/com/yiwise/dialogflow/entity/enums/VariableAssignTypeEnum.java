package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum VariableAssignTypeEnum implements CodeDescEnum {
    CONSTANT(0, "常量赋值"),
    ENTITY_COLLECT(1, "实体赋值"),
    ORIGIN_INPUT(2, "原话采集"),
    ;

    private final String desc;

    private final Integer code;

    VariableAssignTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public static boolean isConstant(VariableAssignTypeEnum type) {
        return CONSTANT == type;
    }

    public static boolean isNotConstant(VariableAssignTypeEnum type) {
        return !isConstant(type);
    }

    public static boolean isCollect(VariableAssignTypeEnum type) {
        return ENTITY_COLLECT == type || ORIGIN_INPUT == type;
    }

    public static boolean isEntityCollect(VariableAssignTypeEnum type) {
        return ENTITY_COLLECT.equals(type);
    }

    public static boolean isOriginInput(VariableAssignTypeEnum type) {
        return ORIGIN_INPUT.equals(type);
    }
}
