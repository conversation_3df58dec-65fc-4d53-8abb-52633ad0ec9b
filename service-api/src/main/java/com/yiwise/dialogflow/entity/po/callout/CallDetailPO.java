package com.yiwise.dialogflow.entity.po.callout;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yiwise.cloud.aicc.enums.callrecord.CharacterEnum;
import com.yiwise.cloud.aicc.enums.callrecord.CorrectionFeedbackStatusEnum;
import com.yiwise.cloud.aicc.enums.callrecord.CorrectionStatusEnum;
import com.yiwise.cloud.aicc.enums.callrecord.CustomerEmotionEnum;
import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.time.LocalDateTime;


@Data
@Table(name = "call_detail")
public class CallDetailPO implements Serializable {
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long callDetailId;
    private Long callRecordId;
    private Long tenantId;
    private String text;
    private Integer startOffset;
    private Integer endOffset;
    private CharacterEnum type;
    private String debugLog;
    private String robotKnowledgeOid;
    private String dialogFlowStepOid;
    private String intentBranchOid;
    private String judgeBranchOid;

    private CustomerEmotionEnum emotion;
    /**
     * 语音识别纠错状态 0 未纠错 1 已纠错 2 无错误
     */
    private CorrectionStatusEnum correctionStatus;

    /**
     * 反馈状态 0 无反馈 1 有效 2 无效
     */
    private CorrectionFeedbackStatusEnum feedbackStatus;

    @JsonIgnore
    private String correctionText;

    /**
     * 任务id
     */
    private Long robotCallJobId;
    /**
     * 话术id
     */
    private Long dialogFlowId;

    /**
     * 通话开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime startTime;

    @Transient
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss SSS", timezone = "GMT+8")
    private LocalDateTime asrTime;

    private Float branchConfidence;

    /**
     * 记录重构话术中的seq，用于自定义语义分析
     */
    private String sessionId;

    /**
     * 记录重构话术中的seq，用于自定义语义分析
     */
    private Integer seq;
}
