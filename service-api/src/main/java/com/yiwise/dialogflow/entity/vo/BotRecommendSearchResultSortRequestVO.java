package com.yiwise.dialogflow.entity.vo;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BotRecommendSearchResultSortRequestVO implements Serializable {

    List<BotTextSearchResultVO> resultList;

    /**
     * ascending descending
     */
    String direction;

    /**
     * 挂机率 -- hangupRate
     * 拒绝率 -- negativeRate
     * 肯定率 -- positiveRate
     * 得分 -- score
     * 相似度 -- similarity
     */
    String orderBy;
}