package com.yiwise.dialogflow.entity.po;

import com.yiwise.base.model.bean.po.BaseTimePO;
import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 答案文本和录音的映射存储
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper=false)
@Document(collection = AnswerAudioMappingPO.COLLECTION_NAME)
@CompoundIndexes(
        @CompoundIndex(name = "idx_botId_audioType_recordUserId", def = "{botId: 1, audioType: 1, recordUserId: 1}")
)
public class AnswerAudioMappingPO extends BaseTimePO {

    public static final String COLLECTION_NAME = "answerAudioMapping";

    @Id
    String id;

    Long botId;

    Long recordUserId;

    String text;

    String url;

    AudioTypeEnum audioType;

    Boolean isByCopy;

    Integer volume;

    Boolean headBlank;

    Integer duration;

}
