package com.yiwise.dialogflow.entity.enums.llm;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum DocumentTypeEnum implements CodeDescEnum {
    DOC(0, "文档"),
    WEB_SITE(1, "网站"),
    ;

    DocumentTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    final String desc;
    final Integer code;

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public static boolean isDoc(DocumentTypeEnum type) {
        return DOC.equals(type);
    }
}
