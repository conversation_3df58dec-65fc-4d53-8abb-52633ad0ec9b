package com.yiwise.dialogflow.entity.po.llm;

import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = RagTextUploadRecordPO.COLLECTION_NAME)
@CompoundIndexes(
        @CompoundIndex(name = "idx_botId", def = "{botId: 1}")
)
public class RagTextUploadRecordPO extends BaseTimeUserIdPO {

    public static final String COLLECTION_NAME = "ragTextUploadRecord";

    @Id
    String id;

    Long botId;

    String textHashValue;

    Boolean success;

    String requestId;

    String reason;
}
