package com.yiwise.dialogflow.entity.vo.stats;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AllBotHourlyReceptionStatsVO {


    private long epochHour;

    /**
     * 日期, 序列化为yyyy-MM-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime dateTime;

    /**
     * 外呼总量
     */
    private long totalCallCount;

    /**
     * 接通总量
     */
    private long answeredCount;
}
