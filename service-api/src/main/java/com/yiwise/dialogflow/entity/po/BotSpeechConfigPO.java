package com.yiwise.dialogflow.entity.po;

import com.yiwise.dialogflow.entity.enums.llm.LlmGuideAnswerConfigEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BotSpeechConfigPO {

    Long botId;

    /**
     * 全局用户无应答时长, 单位秒
     */
    Double userSilenceThreshold;

    // 是否开启语气词打断
    private Boolean enableToneInterrupt;
    // 语气词打断百分比
    private Integer toneInterruptPercent;
    // 语气词列表
    private List<String> toneWordList;

    /**
     * 是否启用断句补齐
     */
    private Boolean enableInputMerge;

    public Boolean getEnableInputMerge() {
        return enableInputMerge == null || enableInputMerge;
    }

    /**
     * 承接语设置
     */
    public LlmGuideAnswerConfigEnum llmGuideAnswerConfig;

    public LlmGuideAnswerConfigEnum getLlmGuideAnswerConfig() {
        if (llmGuideAnswerConfig == null) {
            llmGuideAnswerConfig = LlmGuideAnswerConfigEnum.COMPOSE_OPTIMIZATION;
        }
        return llmGuideAnswerConfig;
    }

    /**
     * 关闭提示音检测
     */
    private Boolean disablePromptAudioCheck;
}
