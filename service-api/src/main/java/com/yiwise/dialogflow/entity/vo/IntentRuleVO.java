package com.yiwise.dialogflow.entity.vo;

import com.yiwise.dialogflow.entity.po.intent.IntentRulePO;
import com.yiwise.dialogflow.entity.vo.stats.IntentRuleStatsVO;
import lombok.Data;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/6/8
 * @class <code>IntentRuleVO</code>
 * @see
 * @since JDK1.8
 */
@Data
public class IntentRuleVO extends IntentRulePO {
    /**
     * 规则描述
     */
    private String ruleContent;

    /**
     * 统计信息, 如果开启查询统计信息, 则返回, 否则返回null
     */
    private IntentRuleStatsVO statsInfo = new IntentRuleStatsVO();
}
