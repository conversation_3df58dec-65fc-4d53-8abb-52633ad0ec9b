package com.yiwise.dialogflow.entity.vo.botgenerate;

import com.yiwise.dialogflow.entity.enums.RewriteTaskSourceEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Data
public class BotRewriteRequestVO {

    private Long templateBotId;

    /**
     * botId
     */
    @NotNull(message = "botId不能为空")
    private Long botId;

    /**
     * 话术offer
     */
    @NotEmpty(message = "offer信息不能为空")
    private Map<String, List<String>> offerMap;

    /**
     * 描述信息
     */
    private String description;

    private RewriteTaskSourceEnum source;

}
