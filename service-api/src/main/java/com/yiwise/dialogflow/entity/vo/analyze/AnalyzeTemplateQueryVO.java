package com.yiwise.dialogflow.entity.vo.analyze;

import com.yiwise.base.model.bean.vo.AbstractQueryVO;
import com.yiwise.dialogflow.entity.enums.AnalyzeTemplateModelTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnalyzeTemplateQueryVO extends AbstractQueryVO implements Serializable {

    /**
     * 模板名称模糊查询
     */
    private String search;

    /**
     * 创建人id
     */
    private Long createUserId;

    /**
     * 模板类型
     */
    private AnalyzeTemplateModelTypeEnum modelType;
}
