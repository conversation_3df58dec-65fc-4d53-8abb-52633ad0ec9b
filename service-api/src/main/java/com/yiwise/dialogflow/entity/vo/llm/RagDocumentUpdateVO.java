package com.yiwise.dialogflow.entity.vo.llm;

import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = lombok.AccessLevel.PRIVATE)
public class RagDocumentUpdateVO implements Serializable {

    @NotNull(message = "botId不能为空")
    Long botId;

    @NotBlank(message = "ragDocumentId不能为空")
    String ragDocumentId;

    @NotBlank(message = "docName不能为空")
    String docName;
}
