package com.yiwise.dialogflow.entity.po;

import com.yiwise.dialogflow.entity.enums.VariableAssignTypeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class KnowledgeAssignConfigPO implements Serializable {

    /**
     * 赋值类型
     */
    private VariableAssignTypeEnum assignType;

    /**
     * 动态变量id
     */
    private String variableId;

    /**
     * 实体id(系统实体也是有实体id的)
     */
    private String entityId;

    /**
     * 常量赋值时需要填的值
     */
    private String constantValue;

}
