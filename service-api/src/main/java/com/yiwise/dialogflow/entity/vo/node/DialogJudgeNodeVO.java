package com.yiwise.dialogflow.entity.vo.node;

import com.yiwise.dialogflow.entity.po.NodeConditionBranchPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
public class DialogJudgeNodeVO extends DialogBaseNodeVO {


    /**
     * 分支列表
     */
    List<NodeConditionBranchPO> branchList;

    /**
     * 这里需要和前端讨论下这个结构, 向下一个节点的引用是维护在分支里还是在外面维护
     */
    private Map<String, String> branchRelatedNodeMap;

}
