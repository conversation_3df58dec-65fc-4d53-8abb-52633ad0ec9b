package com.yiwise.dialogflow.entity.po;


import com.yiwise.base.model.bean.po.BaseTimeUserIdPO;
import com.yiwise.base.model.enums.SystemEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import java.io.Serializable;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.GeneratedValue;

/**
 * 话术文件夹关联表
 *
 * <AUTHOR>
 * @date 2022-09-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "bot_folder_rel")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BotFolderRelPO extends BaseTimeUserIdPO implements Serializable {

	/**
	 * 自增主键
	 */
	@Id
	@GeneratedValue(generator = "JDBC")
	Long botFolderRelId;

	/**
	 * 系统
	 */
	SystemEnum systemType;

	/**
	 * 租户ID
	 */
	Long tenantId;

	/**
	 * 话术ID
	 */
	Long botId;

	/**
	 * 文件夹ID
	 */
	Long folderId;

}
