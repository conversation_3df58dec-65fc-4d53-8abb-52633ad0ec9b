package com.yiwise.dialogflow.entity.vo.analyze;

import com.yiwise.dialogflow.entity.enums.AnalyzeTemplateModelTypeEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AnalyzeTemplateCreateVO implements Serializable {

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    String name;

    /**
     * 模板类型
     */
    @NotNull(message = "模板类型不能为空")
    AnalyzeTemplateModelTypeEnum modelType;

    /**
     * 来源模板ID
     */
    String srcTemplateId;

    /**
     * 描述
     */
    String desc;
}