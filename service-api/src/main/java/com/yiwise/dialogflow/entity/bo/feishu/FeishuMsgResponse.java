package com.yiwise.dialogflow.entity.bo.feishu;

import com.alibaba.fastjson.JSONObject;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/10/11
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FeishuMsgResponse implements Serializable {

    Integer code;

    String msg;

    JSONObject data;
    
    public boolean isSuccess() {
        return code == 0;
    }
}
