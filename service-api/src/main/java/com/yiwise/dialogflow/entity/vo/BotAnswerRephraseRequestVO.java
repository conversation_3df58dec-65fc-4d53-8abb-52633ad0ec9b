package com.yiwise.dialogflow.entity.vo;

import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BotAnswerRephraseRequestVO extends BotRecommendRephraseRequestVO implements Serializable {

    /**
     * 话术id
     */
    @NotNull(message = "botId不能为空")
    Long botId;

    /**
     * 答案来源
     */
    @NotNull(message = "answerSource不能为空")
    AnswerSourceEnum answerSource;

    /**
     * 流程id
     */
    String stepId;

    /**
     * 节点id
     */
    String nodeId;

    /**
     * 知识id
     */
    String knowledgeId;
}