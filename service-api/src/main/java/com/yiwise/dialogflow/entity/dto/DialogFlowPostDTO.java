package com.yiwise.dialogflow.entity.dto;

import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.middleware.tts.enums.TtsVoiceEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DialogFlowPostDTO implements Serializable {

    private Long id;

    private String name;

    private String description;

    private IndustryTypeEnum industry;

    private IndustrySubTypeEnum subIndustry;

    private Long intentLevelTagId;

    private Long userId;

    private Long tenantId;

    private Long distributorId;

    private Long recordUserId;

    private DialogFlowTypeEnum type = DialogFlowTypeEnum.NORMAL;

    /**
     * 灵敏度等级参数, 这里是差值,减去基数1500以后的差值
     */
//    @NotNull(message = "打断灵敏度不能为空", groups = {DialogFlowOpenApiCopyValidate.class})
//    @Range(min = 1,  max = 9, message = "打断灵敏度取值范围为0-9", groups = {DialogFlowOpenApiCopyValidate.class})
    private Integer vadGateMute = 950;

    /**
     * 反应灵敏度
     */
//    @NotNull(message = "反应灵敏度不能为空", groups = {DialogFlowOpenApiCopyValidate.class})
//    @Range(min = 1,  max = 9, message = "反应灵敏度取值范围为0-9", groups = {DialogFlowOpenApiCopyValidate.class})
    private Integer maxSentenceSilence = 500;

    /**
     * tts 语速参数
     */
    private Float ttsSpeech = 5f;

    /**
     * tts 发音人
     */
    private TtsVoiceEnum ttsVoice;

    /**
     * tts 音量
     */
    private Float ttsVolume = 5f;

    /**
     * 开启自定义tts配置
     */
    private Boolean enableCustomTtsConfig;

    /**
     * 默认分支优先级
     */
    private List<String> branchLevel;

    private SystemEnum system;

    /**
     * 是否开启问法
     */
    private Boolean enableAskService;
    /**
     * 问法介入字数设置
     */
    private Integer askNumber;

    /**
     * 问法阈值上限
     */
    private Double knowledgeUpperThreshold;

    /**
     * 问法阈值下限
     */
    private Double knowledgeLowerThreshold;

    /**
     * nlp机器人id
     */
    private Long nlpRobotId;

    /**
     * 话术(机器人) 版本类别, 分为普通版和专业版
     */
    private BotTypeEnum botType;

    /**
     * 问法模型
     */
    private String modelType;

    /**
     * 算法补丁
     */
    private String patch;

    /**
     * 是否开启自定义/领域混合模型
     */
    private Boolean mixedModel;

    /**
     * 赛道类型
     */
    private Integer customerTrackType;

    /**
     * 二级赛道类型
     */
    private Integer customerTrackSubType;

    /**
     * 场景id
     */
    private Integer customerSceneId;
}
