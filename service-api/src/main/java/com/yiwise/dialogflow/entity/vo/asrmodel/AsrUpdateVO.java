package com.yiwise.dialogflow.entity.vo.asrmodel;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/2
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AsrUpdateVO implements Serializable {

    Long botId;

    Long asrLanguageId;

    Long asrProviderId;

    Long asrVocabDetailId;

    Long asrSelfLearningDetailId;

    String asrErrorCorrectionDetailId;

    Long currentUserId;

    Long tenantId;

    /**
     * 反应灵敏度等级
     */
    Integer maxSentenceSilence;

    private Boolean enableAsrDelayStart;

    private Double asrDelayStartSeconds;
}
