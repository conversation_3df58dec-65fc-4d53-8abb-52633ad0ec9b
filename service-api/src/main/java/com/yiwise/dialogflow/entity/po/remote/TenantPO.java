package com.yiwise.dialogflow.entity.po.remote;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.Set;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TenantPO implements Serializable {

    Long tenantId;

    Long distributorId;

    Long userId;

    String name;

    String companyName;

    /**
     * 是否开启ip登录限制
     */
    Boolean enableIpLimit;

    /**
     * 可以登录的ip
     */
    Set<String> ipRange;
}
