package com.yiwise.dialogflow.entity.vo.audio.request;

import com.yiwise.dialogflow.entity.enums.SyncModeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/14
 */
@Data
public class SyncAudioRequestVO implements Serializable {

    /**
     * botId
     */
    @NotNull(message = "botId不能为空")
    private Long botId;

    /**
     * 文案列表,传参与音频重置接口相同
     */
    @NotEmpty(message = "answerIdList不能为空")
    private List<String> answerIdList;

    /**
     * 同步模式
     */
    @NotNull(message = "syncMode不能为空")
    private SyncModeEnum syncMode;

    /**
     * 公共音频库的分组id
     */
    @NotBlank(message = "groupId不能为空")
    private String groupId;
}