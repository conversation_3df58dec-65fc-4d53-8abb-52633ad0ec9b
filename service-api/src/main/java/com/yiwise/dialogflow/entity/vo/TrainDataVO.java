package com.yiwise.dialogflow.entity.vo;

import com.yiwise.dialogflow.entity.bo.algorithm.ModelTrainKey;
import com.yiwise.dialogflow.entity.bo.algorithm.TrainItemBO;
import com.yiwise.dialogflow.entity.enums.AlgorithmTrainTypeEnum;
import com.yiwise.dialogflow.entity.enums.SnapshotTypeEnum;
import com.yiwise.dialogflow.entity.po.TrainResultPO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-12-26
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TrainDataVO implements Serializable {

    private static final long serialVersionUID = -2522869423394067252L;

    /**
     * 环境
     */
    String environment;

    /**
     * 租户ID
     */
    Long tenantId;

    /**
     * 机器人ID
     */
    String robotId;

    /**
     * 训练类型
     */
    AlgorithmTrainTypeEnum trainType;

    /**
     * 版本类型
     */
    SnapshotTypeEnum snapshotType;

    /**
     * 数据内容
     */
    List<TrainItemBO> content;

    /**
     * 关键词列表
     */
    List<TrainItemBO> keywords;

    /**
     * 模型ID
     */
    String modelId;

    ModelTrainKey modelTrainKey;

    String domainName;

    public static TrainDataVO of(TrainResultPO trainResult) {
        ModelTrainKey modelTrainKey = trainResult.getModelTrainKey();
        TrainDataVO trainDataVO = new TrainDataVO();
        trainDataVO.setTenantId(modelTrainKey.getTenantId());
        trainDataVO.setRobotId(modelTrainKey.getModelId());
        trainDataVO.setTrainType(modelTrainKey.getTrainType());
        trainDataVO.setSnapshotType(SnapshotTypeEnum.PUBLISHED);
        trainDataVO.setDomainName(trainResult.getDomainName());
        return trainDataVO;
    }
}
