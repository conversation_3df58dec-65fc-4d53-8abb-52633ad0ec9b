package com.yiwise.dialogflow.entity.enums;


import com.yiwise.base.model.enums.CodeDescEnum;

public enum RuleActionSourceEnum  implements CodeDescEnum {
    RULE(0, "规则"),
    SPECIAL_ANSWER(1,"特殊语境"),
    STEP(2,"流程"),
    KNOWLEDGE(3,"知识库"),

    ;

    private final Integer code;
    private final String desc;

    RuleActionSourceEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}