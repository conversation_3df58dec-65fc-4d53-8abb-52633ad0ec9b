package com.yiwise.dialogflow.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/9/13
 */
@Data
public class OperationLogVO implements Serializable {

    /**
     * id
     */
    private String id;

    /**
     * botId
     */
    private Long botId;

    /**
     * 操作日志类型
     */
    private OperationLogTypeEnum type;

    /**
     * 日志类型名称
     */
    private String logTypeName;

    /**
     * 操作对象
     */
    private OperationLogResourceTypeEnum resourceType;

    /**
     * 操作对象名称
     */
    private String resourceTypeName;

    /**
     * 操作日志详情
     */
    private String detail;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operateTime;

    /**
     * 请求 id
     */
    private String requestId;

    /**
     * 请求所在主机名
     */
    private String hostName;
}
