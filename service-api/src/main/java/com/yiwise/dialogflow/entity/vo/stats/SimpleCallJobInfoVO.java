package com.yiwise.dialogflow.entity.vo.stats;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;

@Data
public class SimpleCallJobInfoVO {
    /**
     * 任务id
     */
    Long callJobId;

    /**
     * 任务名称
     */
    String callJobName;

    /**
     * 任务第一次外呼日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    LocalDate firstCallDate;

    /**
     * 任务最后一次外呼日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    LocalDate lastCallDate;
}
