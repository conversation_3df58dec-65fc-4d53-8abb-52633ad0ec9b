package com.yiwise.dialogflow.entity.bo.feishu;

import com.yiwise.base.common.utils.bean.JsonUtils;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class FeishuMsgBody {
    private String msg_type;
    private Content content;

    public FeishuMsgBody(String title, String content, List<String> ids){
        this.msg_type = "post";
        this.content = new Content();
        Zhcn zhcn = new Zhcn();
        zhcn.setTitle(title);
        List<List<Map<String,String>>> contentBlock = new ArrayList<>();
        List<Map<String,String>> tagBlockList = new ArrayList();
        Map<String,String> tagBlock = new HashMap();
        tagBlock.put("tag","text");
        tagBlock.put("text",content);
        tagBlockList.add(tagBlock);

        ids.forEach(id -> {
            HashMap<String,String> atTag = new HashMap();
            atTag.put("tag","at");
            atTag.put("user_id",id);
            tagBlockList.add(atTag);
        });
        contentBlock.add(tagBlockList);
        zhcn.setContent(contentBlock);

        Post post = new Post();
        post.setZh_cn(zhcn);
        this.content.setPost(post);
    }

    public String toJson(){
        return JsonUtils.object2PrettyString(this);
    }
    @Data
    class Content{
        Post post;
    }

    @Data
    class Post{
        Zhcn zh_cn;
    }

    @Data
    class Zhcn{
        private String title;
        private List<List<Map<String,String>>> content;
    }


}
