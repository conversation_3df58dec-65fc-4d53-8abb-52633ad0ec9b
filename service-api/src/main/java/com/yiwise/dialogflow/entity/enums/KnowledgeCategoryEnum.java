package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 */

public enum KnowledgeCategoryEnum implements CodeDescEnum {
    NORMAL(0, "一般知识"),
    BUSINESS(1, "业务知识");


    Integer code;
    String desc;
    KnowledgeCategoryEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
