package com.yiwise.dialogflow.entity.po;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Deprecated
public class DataReportStepDataPO implements Serializable {

    /**
     * 序号
     */
    Integer seq;

    /**
     * 流程到达率-最高
     */
    String maxArriveRate;

    /**
     * 流程到达率-最低
     */
    String minArriveRate;

    /**
     * 流程到达数-总数
     */
    Integer totalArriveCount = 0;

    /**
     * 接通总数
     */
    Integer totalCount = 0;

    /**
     * 流程到达率-平均
     */
    String avgArriveRate;

    /**
     * 流程下节点数据统计
     */
    List<DataReportNodeDataPO> dataReportNodeDataPOList;

    public void addArriveCount(Integer totalArriveCount) {
        this.totalArriveCount += totalArriveCount;
    }

    public void addTotalCount(Integer totalCount) {
        this.totalCount += totalCount;
    }

    public void setArriveRate(String arriveRate) {
        if (StringUtils.isEmpty(this.maxArriveRate)) {
            this.maxArriveRate = arriveRate;
        }
        if (StringUtils.isEmpty(this.minArriveRate)) {
            this.minArriveRate = arriveRate;
        }
        this.maxArriveRate = String.valueOf(Math.max(Double.parseDouble(this.maxArriveRate), Double.parseDouble(arriveRate)));
        this.minArriveRate = String.valueOf(Math.min(Double.parseDouble(this.minArriveRate), Double.parseDouble(arriveRate)));
    }
}
