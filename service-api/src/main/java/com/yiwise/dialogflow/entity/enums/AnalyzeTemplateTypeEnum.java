package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum AnalyzeTemplateTypeEnum implements CodeDescEnum {

    /**
     * 内置模板
     */
    BUILTIN(0, "内置模板"),

    /**
     * 自定义模板
     */
    CUSTOMIZED(1, "自定义模板"),

    /**
     * 任务模板
     */
    TASK(2, "任务模板"),
    ;

    private final Integer code;
    private final String desc;

    AnalyzeTemplateTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Boolean isBuiltin(AnalyzeTemplateTypeEnum type) {
        return type == BUILTIN;
    }

    public static Boolean isTask(AnalyzeTemplateTypeEnum type) {
        return type == TASK;
    }
}
