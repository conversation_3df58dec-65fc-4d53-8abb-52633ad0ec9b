package com.yiwise.dialogflow.entity.vo.sync;

import com.yiwise.dialogflow.entity.enums.SyncModeEnum;
import com.yiwise.dialogflow.entity.query.IntentActionQuery;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/29 15:16:06
 */
@Data
public class RuleActionSyncVO extends BasicSyncVO implements Serializable {
    /**
     * 同步方式
     */
    SyncModeEnum syncType;

    /**
     * 查询条件
     */
    IntentActionQuery intentActionQuery;

    Long tenantId;
}