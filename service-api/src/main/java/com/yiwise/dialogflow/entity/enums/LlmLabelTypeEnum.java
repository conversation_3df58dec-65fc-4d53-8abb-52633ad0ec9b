package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * 大模型分类类型
 */
public enum LlmLabelTypeEnum implements CodeDescEnum {

    BUILTIN(0, "内置"),

    CUSTOMIZED(1, "自定义"),
    ;

    Integer code;
    String desc;

    LlmLabelTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public static boolean isBuiltin(LlmLabelTypeEnum type) {
        return BUILTIN.equals(type);
    }

    public static boolean isCustomized(LlmLabelTypeEnum type) {
        return CUSTOMIZED.equals(type);
    }
}