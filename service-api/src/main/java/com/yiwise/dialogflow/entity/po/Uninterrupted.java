package com.yiwise.dialogflow.entity.po;

import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public interface Uninterrupted {

    Boolean getEnableUninterrupted();

    Integer getCustomInterruptThreshold();

    List<String> getUninterruptedReplyKnowledgeIdList();

    List<String> getUninterruptedReplyStepIdList();

    List<String> getUninterruptedReplySpecialAnswerIdList();

    default List<String> getUninterruptedReplyBranchIntentIdList() {
        return Collections.emptyList();
    }

    void setEnableUninterrupted(final Boolean enableUninterrupted);

    void setCustomInterruptThreshold(final Integer customInterruptThreshold);

    void setUninterruptedReplyKnowledgeIdList(final List<String> uninterruptedReplyKnowledgeIdList);

    void setUninterruptedReplyStepIdList(final List<String> uninterruptedReplyStepIdList);

    void setUninterruptedReplySpecialAnswerIdList(final List<String> uninterruptedReplySpecialAnswerIdList);

    default void setUninterruptedReplyBranchIntentIdList(final List<String> uninterruptedReplyBranchIntentIdList) {
    }

    default void mapUninterrupted(Map<String, String> stepIdMapping,
                                  Map<String, String> knowledgeIdMapping,
                                  Map<String, String> specialAnswerConfigIdMapping,
                                  Map<String, String> intentIdMapping) {
        // 不可打断期间, 允许响应的流程和问答知识
        if (CollectionUtils.isNotEmpty(getUninterruptedReplyKnowledgeIdList())) {
            List<String> collect = getUninterruptedReplyKnowledgeIdList()
                    .stream()
                    .map(knowledgeIdMapping::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            setUninterruptedReplyKnowledgeIdList(collect);
        }
        if (CollectionUtils.isNotEmpty(getUninterruptedReplyStepIdList())) {
            List<String> collect = getUninterruptedReplyStepIdList()
                    .stream()
                    .map(stepIdMapping::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            setUninterruptedReplyStepIdList(collect);
        }

        if (CollectionUtils.isNotEmpty(getUninterruptedReplySpecialAnswerIdList())) {
            List<String> collect = getUninterruptedReplySpecialAnswerIdList()
                    .stream()
                    .map(specialAnswerConfigIdMapping::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            setUninterruptedReplySpecialAnswerIdList(collect);
        }
        if (CollectionUtils.isNotEmpty(getUninterruptedReplyBranchIntentIdList())) {
            List<String> collect = getUninterruptedReplyBranchIntentIdList()
                    .stream()
                    .map(intentIdMapping::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            setUninterruptedReplyBranchIntentIdList(collect);
        }
    }
}