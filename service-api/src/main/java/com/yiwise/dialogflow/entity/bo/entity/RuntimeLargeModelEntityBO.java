package com.yiwise.dialogflow.entity.bo.entity;

import com.yiwise.dialogflow.entity.po.LargeModelEntityPO;
import com.yiwise.dialogflow.pattern.PatternEnhance;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collections;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class RuntimeLargeModelEntityBO extends LargeModelEntityPO implements RuntimeEntityBO {

    @Override
    public List<PatternEnhance> getPreSkipPatternList() {
        return Collections.emptyList();
    }

    @Override
    public List<PatternEnhance> getPostSkipPatternList() {
        return Collections.emptyList();
    }

    @Override
    public void setPreSkipPatternList(List<PatternEnhance> preSkipPatternList) {

    }

    @Override
    public void setPostSkipPatternList(List<PatternEnhance> postSkipPatternList) {

    }
}
