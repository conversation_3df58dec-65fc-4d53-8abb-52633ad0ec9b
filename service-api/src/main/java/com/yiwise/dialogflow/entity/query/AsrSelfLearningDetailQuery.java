package com.yiwise.dialogflow.entity.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.base.model.bean.vo.AbstractQueryVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/27 09:46:23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AsrSelfLearningDetailQuery extends AbstractQueryVO {

    /**
     * 模型名称
     */
    String name;

    /**
     * 开启状态（0-停用；1-开启）
     */
    Integer status;

    /**
     * 话术名称或id
     */
    String botInfo;

    /**
     * 自学习模型id集合
     */
    List<Long> asrSelfLearningDetailIdList;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    LocalDate startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    LocalDate endTime;

}