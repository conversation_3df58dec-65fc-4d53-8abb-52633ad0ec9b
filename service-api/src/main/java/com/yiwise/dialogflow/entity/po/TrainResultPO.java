package com.yiwise.dialogflow.entity.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.entity.bo.algorithm.ModelTrainKey;
import com.yiwise.dialogflow.entity.enums.AlgorithmTrainTypeEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/12/2
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Document(collection = TrainResultPO.COLLECTION_NAME)
@CompoundIndexes(
        @CompoundIndex(name = "idx_tid_rid_trainType", def = "{tenantId: 1, robotId: 1, trainType: 1}")
)
public class TrainResultPO implements Serializable {

    public static final String COLLECTION_NAME = "trainResult";

    private static final long serialVersionUID = 6468655317043344700L;

    @Id
    String id;

    Long tenantId;

    Long botId;

    @Indexed
    String modelId;

    String modelName;

    Integer version;

    String desc;

    SystemEnum systemType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    LocalDateTime finishTime;

    Boolean success;

    AlgorithmTrainTypeEnum trainType;

    ModelTrainKey modelTrainKey;

    /**
     * 意图的领域模型, 需要传给算法
     */
    String domainName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    LocalDateTime createTime;

    Long createUserId;
}
