package com.yiwise.dialogflow.entity.po.intent;

import com.yiwise.dialogflow.entity.vo.IntentRuleVO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/5/23
 * @class <code>IntentRuleActionPO</code>
 * @see
 * @since JDK1.8
 */
@Data
public class IntentRuleActionPO extends IntentRuleVO {

    public static final String COLLECTION_NAME = "botIntentRuleAction";
    public static final String SORT_ATTRIBUTE = "matchOrder";
    /**
     * 触发动作
     */
    private List<RuleActionParam> actionList;

    /**
     * 这个字段前端未检查null,所以这里处理一下
     */
    public List<RuleActionParam> getActionList() {
        if (actionList == null) {
            actionList = new ArrayList<>();
        }
        return actionList;
    }
}
