package com.yiwise.dialogflow.entity.bo.feishu;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/10/12
 */
@Data
public class FeishuCardBO implements Serializable {

    public static String CONTENT = "{\n" +
                                           "  \"config\": {\n" +
                                           "    \"wide_screen_mode\": true\n" +
                                           "  },\n" +
                                           "  \"header\": {\n" +
                                           "    \"template\": \"red\",\n" +
                                           "    \"title\": {\n" +
                                           "      \"tag\": \"plain_text\",\n" +
                                           "      \"content\": \"全链路监控告警-%s\"\n" +
                                           "    }\n" +
                                           "  },\n" +
                                           "  \"elements\": [\n" +
                                           "    {\n" +
                                           "      \"tag\": \"div\",\n" +
                                           "      \"fields\": [\n" +
                                           "        {\n" +
                                           "          \"is_short\": true,\n" +
                                           "          \"text\": {\n" +
                                           "            \"tag\": \"lark_md\",\n" +
                                           "            \"content\": \"**\uD83D\uDCC5 时间：**\\n%s\"\n" +
                                           "          }\n" +
                                           "        },\n" +
                                           "        {\n" +
                                           "          \"is_short\": true,\n" +
                                           "          \"text\": {\n" +
                                           "            \"tag\": \"lark_md\",\n" +
                                           "            \"content\": \"**\uD83D\uDD22 告警名称：**\\n%s\"\n" +
                                           "          }\n" +
                                           "        },\n" +
                                           "        {\n" +
                                           "          \"is_short\": true,\n" +
                                           "          \"text\": {\n" +
                                           "            \"tag\": \"lark_md\",\n" +
                                           "            \"content\": \"**\uD83D\uDCCB 主机名：**\\n%s\"\n" +
                                           "          }\n" +
                                           "        },\n" +
                                           "        {\n" +
                                           "          \"is_short\": true,\n" +
                                           "          \"text\": {\n" +
                                           "            \"tag\": \"lark_md\",\n" +
                                           "            \"content\": \"**\uD83D\uDCCB 项目：**\\n%s\"\n" +
                                           "          }\n" +
                                           "        },\n" +
                                           "        {\n" +
                                           "          \"is_short\": false,\n" +
                                           "          \"text\": {\n" +
                                           "            \"tag\": \"lark_md\",\n" +
                                           "            \"content\": \"**\uD83D\uDC64 对接人：**\\n%s\"\n" +
                                           "          }\n" +
                                           "        },\n" +
                                           "        {\n" +
                                           "          \"is_short\": false,\n" +
                                           "          \"text\": {\n" +
                                           "            \"tag\": \"lark_md\",\n" +
                                           "            \"content\": \"**\uD83D\uDCDA MDC_LOG_ID：**\\n%s\"\n" +
                                           "          }\n" +
                                           "        }\n" +
                                           "      ]\n" +
                                           "    },\n" +
                                           "    {\n" +
                                           "      \"tag\": \"action\",\n" +
                                           "      \"actions\": [\n" +
                                           "        {\n" +
                                           "          \"tag\": \"button\",\n" +
                                           "          \"text\": {\n" +
                                           "            \"tag\": \"plain_text\",\n" +
                                           "            \"content\": \"开始跟进\"\n" +
                                           "          },\n" +
                                           "          \"type\": \"primary\",\n" +
                                           "          \"value\": {\n" +
                                           "            \"key1\": \"value1\"\n" +
                                           "          }\n" +
                                           "        },\n" +
                                           "        {\n" +
                                           "          \"tag\": \"button\",\n" +
                                           "          \"text\": {\n" +
                                           "            \"tag\": \"plain_text\",\n" +
                                           "            \"content\": \"无需处理\"\n" +
                                           "          },\n" +
                                           "          \"type\": \"default\",\n" +
                                           "          \"value\": {\n" +
                                           "            \"key1\": \"value1\"\n" +
                                           "          }\n" +
                                           "        },\n" +
                                           "        {\n" +
                                           "          \"tag\": \"button\",\n" +
                                           "          \"text\": {\n" +
                                           "            \"tag\": \"plain_text\",\n" +
                                           "            \"content\": \"告警静音\"\n" +
                                           "          },\n" +
                                           "          \"type\": \"default\",\n" +
                                           "          \"value\": {\n" +
                                           "            \"key1\": \"value1\"\n" +
                                           "          }\n" +
                                           "        }\n" +
                                           "      ]\n" +
                                           "    }\n" +
                                           "  ]\n" +
                                           "}";

}
