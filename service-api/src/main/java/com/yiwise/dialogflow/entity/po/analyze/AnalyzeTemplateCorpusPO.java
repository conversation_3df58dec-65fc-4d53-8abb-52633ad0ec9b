package com.yiwise.dialogflow.entity.po.analyze;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 */
@Data
@Document(collection = AnalyzeTemplateCorpusPO.COLLECTION_NAME)
@CompoundIndexes(
        {
                @CompoundIndex(name = "idx_intentId", def = "{intentId: 1}"),
                @CompoundIndex(name = "idx_templateId", def = "{templateId: 1}"),
        }
)
public class AnalyzeTemplateCorpusPO {

    public static final String COLLECTION_NAME = "analyzeTemplateCorpus";

    @Id
    private String id;

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 意图id
     */
    private String intentId;

    /**
     * 语料
     */
    private String corpus;

    /**
     * 通话记录id
     */
    private Long callRecordId;
}