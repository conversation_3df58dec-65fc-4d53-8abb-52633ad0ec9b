package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum AlgorithmLabelSourceEnum implements CodeDescEnum {

    /**
     * 人工
     */
    MAN_MADE(1,"人工"),

    /**
     * 算法
     */
    ALGORITHM(2, "算法"),
    ;

    private final Integer code;
    private final String desc;

    public static boolean isManMade(AlgorithmLabelSourceEnum source) {
        return MAN_MADE.equals(source);
    }
}