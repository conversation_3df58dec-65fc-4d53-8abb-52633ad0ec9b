package com.yiwise.dialogflow.entity.po;

import com.yiwise.dialogflow.entity.enums.CollectNodeSkipTypeEnum;
import com.yiwise.dialogflow.entity.enums.ConditionVarTypeEnum;
import lombok.Data;

@Data
public class CollectNodeSkipCondition extends ConditionExpressionPO {
    /**
     * 跳过类型, CURRENT_QUESTION: 跳过当前问题, ALL_QUESTION: 跳过当前节点所有问题
     */
    private CollectNodeSkipTypeEnum skipType;

    public ConditionVarTypeEnum getPreVarType() {
        return ConditionVarTypeEnum.INTENT;
    }
}
