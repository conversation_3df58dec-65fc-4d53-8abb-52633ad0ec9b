package com.yiwise.dialogflow.entity.enums;

/**
 * <AUTHOR>
 * @date 2023/4/19
 */
public enum IntentPriorityLevelEnum {

    /**
     * 组合意图
     */
    COMPOSITE_INTENT(10),

    /**
     * 高于置信度上限的算法意图
     */
    HIGH_PRIORITY_ALGORITHM_INTENT(20),

    /**
     * nlu
     */
    NLU_INTENT(30),

    /**
     * 置信度阈值范围内，与正则有交集的算法意图
     */
    ALGORITHM_INTENT_INTERSECT_WITH_REGEX(40),

    /**
     * 正则意图
     */
    REGEX_INTENT(50),

    /**
     * 低于置信度下限的算法意图
     */
    LOW_PRIORITY_ALGORITHM_INTENT(60),

    DEFAULT(70),
    ;

    private final Integer code;

    IntentPriorityLevelEnum(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }
}
