package com.yiwise.dialogflow.entity.vo;

import lombok.Data;

@Data
public class LLMStepTestResponseVO {

    /**
     * botId
     */
    Long botId;

    /**
     * 流程 id
     */
    String stepId;

    /**
     * 用户输入
     */
    String userInput;

    /**
     * 答案文本
     */
    String answer;

    /**
     * 响应时长, 单位毫秒, 时间为接收到该请求开始到响应该部分消息为止
     */
    Integer durationMs;

    String trackInfo;

    /**
     * 响应是否完成
     */
    Boolean isComplete;

    String action;
}
