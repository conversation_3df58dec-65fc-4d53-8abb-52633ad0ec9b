package com.yiwise.dialogflow.entity.bo;

import com.yiwise.dialogflow.entity.enums.GroupTypeEnum;
import com.yiwise.dialogflow.entity.enums.VariableTypeEnum;
import com.yiwise.dialogflow.entity.po.StepPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 话术所有可能被其他依赖的资源的集合, 用于校验的时候避免对资源的重复加载
 * <AUTHOR>
 */
@Data
public class DependentResourceBO {
    /**
     * 意图
     */
    Map<String, String> intentIdNameMap = new HashMap<>();

    /**
     * 问答知识
     */
    Map<String, String> knowledgeIdNameMap = new HashMap<>();

    Map<String, String> knowledgeNameToIdMap = new HashMap<>();

    /**
     * 流程
     */
    Map<String, String> stepIdNameMap = new HashMap<>();

    Map<String, String> stepNameToIdMap = new HashMap<>();

    Map<String, StepPO> stepMap = new HashMap<>();

    /**
     * 节点
     */
    Map<String, String> nodeIdNameMap = new HashMap<>();
    /**
     * 节点
     */
    Map<String, String> nodeId2LabelNameMap = new HashMap<>();

    /**
     * 变量
     */
    Map<String, String> variableNameIdMap = new HashMap<>();


    /**
     * 变量
     */
    Map<String, String> variableIdNameMap = new HashMap<>();

    /**
     * <变量id,变量类型>
     */
    Map<String, VariableTypeEnum> varIdTypeMap = new HashMap<>();

    /**
     * 特殊语境
     */
    Map<String, String> specialAnswerIdNameMap = new HashMap<>();

    Map<String, String> specialAnswerNameToIdMap = new HashMap<>();

    /**
     * 分组
     */
    Map<String, GroupTypeEnum> groupIdTypeMap = new HashMap<>();

    /**
     * 意向标签
     */
    Map<Integer, String> intentLevelIdNameMap = new HashMap<>();

    Map<String, String> entityId2NameMap = new HashMap<>();

    Map<String, String> llmLabelId2NameMap = new HashMap<>();

    @Getter
    @EqualsAndHashCode
    public static class Condition {

        Long botId;
        Boolean intent;
        Boolean knowledge;
        Boolean variable;
        Boolean node;
        Boolean step;
        Boolean group;
        Boolean specialAnswerConfig;
        Boolean intentLevel;
        Boolean entity;

        public Condition(Long botId) {
            this.botId = botId;
        }

        public Condition intent() {
            this.intent = true;
            return this;
        }

        public Condition step() {
            this.step = true;
            return this;
        }

        public Condition knowledge() {
            this.knowledge = true;
            return this;
        }
        public Condition specialAnswerConfig() {
            this.specialAnswerConfig = true;
            return this;
        }

        public Condition variable() {
            this.variable = true;
            return this;
        }

        public Condition node() {
            this.node = true;
            return this;
        }

        public Condition group() {
            this.group = true;
            return this;
        }

        public Condition intentLevel() {
            this.intentLevel = true;
            return this;
        }

        public Condition entity() {
            this.entity = true;
            return this;
        }
    }

}
