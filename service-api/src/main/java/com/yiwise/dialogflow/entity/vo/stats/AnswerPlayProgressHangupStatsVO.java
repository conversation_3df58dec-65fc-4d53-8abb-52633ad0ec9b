package com.yiwise.dialogflow.entity.vo.stats;

import com.yiwise.dialogflow.entity.po.stats.AnswerPlayProgressHangupStatsPO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Data
public class AnswerPlayProgressHangupStatsVO {
    String answerTemplate;

    public String getAnswerTemplate() {
        return StringUtils.trimToEmpty(answerTemplate);
    }
    final int[] progressHangupCountArray = new int[101];

    public static AnswerPlayProgressHangupStatsVO from(AnswerPlayProgressHangupStatsPO po) {
        if (Objects.isNull(po)) {
            return null;
        }

        AnswerPlayProgressHangupStatsVO vo = new AnswerPlayProgressHangupStatsVO();
        vo.setAnswerTemplate(po.getAnswerTemplate());
        vo.progressHangupCountArray[0] = po.get_0();
        vo.progressHangupCountArray[1] = po.get_1();
        vo.progressHangupCountArray[2] = po.get_2();
        vo.progressHangupCountArray[3] = po.get_3();
        vo.progressHangupCountArray[4] = po.get_4();
        vo.progressHangupCountArray[5] = po.get_5();
        vo.progressHangupCountArray[6] = po.get_6();
        vo.progressHangupCountArray[7] = po.get_7();
        vo.progressHangupCountArray[8] = po.get_8();
        vo.progressHangupCountArray[9] = po.get_9();
        vo.progressHangupCountArray[10] = po.get_10();
        vo.progressHangupCountArray[11] = po.get_11();
        vo.progressHangupCountArray[12] = po.get_12();
        vo.progressHangupCountArray[13] = po.get_13();
        vo.progressHangupCountArray[14] = po.get_14();
        vo.progressHangupCountArray[15] = po.get_15();
        vo.progressHangupCountArray[16] = po.get_16();
        vo.progressHangupCountArray[17] = po.get_17();
        vo.progressHangupCountArray[18] = po.get_18();
        vo.progressHangupCountArray[19] = po.get_19();
        vo.progressHangupCountArray[20] = po.get_20();
        vo.progressHangupCountArray[21] = po.get_21();
        vo.progressHangupCountArray[22] = po.get_22();
        vo.progressHangupCountArray[23] = po.get_23();
        vo.progressHangupCountArray[24] = po.get_24();
        vo.progressHangupCountArray[25] = po.get_25();
        vo.progressHangupCountArray[26] = po.get_26();
        vo.progressHangupCountArray[27] = po.get_27();
        vo.progressHangupCountArray[28] = po.get_28();
        vo.progressHangupCountArray[29] = po.get_29();
        vo.progressHangupCountArray[30] = po.get_30();
        vo.progressHangupCountArray[31] = po.get_31();
        vo.progressHangupCountArray[32] = po.get_32();
        vo.progressHangupCountArray[33] = po.get_33();
        vo.progressHangupCountArray[34] = po.get_34();
        vo.progressHangupCountArray[35] = po.get_35();
        vo.progressHangupCountArray[36] = po.get_36();
        vo.progressHangupCountArray[37] = po.get_37();
        vo.progressHangupCountArray[38] = po.get_38();
        vo.progressHangupCountArray[39] = po.get_39();
        vo.progressHangupCountArray[40] = po.get_40();
        vo.progressHangupCountArray[41] = po.get_41();
        vo.progressHangupCountArray[42] = po.get_42();
        vo.progressHangupCountArray[43] = po.get_43();
        vo.progressHangupCountArray[44] = po.get_44();
        vo.progressHangupCountArray[45] = po.get_45();
        vo.progressHangupCountArray[46] = po.get_46();
        vo.progressHangupCountArray[47] = po.get_47();
        vo.progressHangupCountArray[48] = po.get_48();
        vo.progressHangupCountArray[49] = po.get_49();
        vo.progressHangupCountArray[50] = po.get_50();
        vo.progressHangupCountArray[51] = po.get_51();
        vo.progressHangupCountArray[52] = po.get_52();
        vo.progressHangupCountArray[53] = po.get_53();
        vo.progressHangupCountArray[54] = po.get_54();
        vo.progressHangupCountArray[55] = po.get_55();
        vo.progressHangupCountArray[56] = po.get_56();
        vo.progressHangupCountArray[57] = po.get_57();
        vo.progressHangupCountArray[58] = po.get_58();
        vo.progressHangupCountArray[59] = po.get_59();
        vo.progressHangupCountArray[60] = po.get_60();
        vo.progressHangupCountArray[61] = po.get_61();
        vo.progressHangupCountArray[62] = po.get_62();
        vo.progressHangupCountArray[63] = po.get_63();
        vo.progressHangupCountArray[64] = po.get_64();
        vo.progressHangupCountArray[65] = po.get_65();
        vo.progressHangupCountArray[66] = po.get_66();
        vo.progressHangupCountArray[67] = po.get_67();
        vo.progressHangupCountArray[68] = po.get_68();
        vo.progressHangupCountArray[69] = po.get_69();
        vo.progressHangupCountArray[70] = po.get_70();
        vo.progressHangupCountArray[71] = po.get_71();
        vo.progressHangupCountArray[72] = po.get_72();
        vo.progressHangupCountArray[73] = po.get_73();
        vo.progressHangupCountArray[74] = po.get_74();
        vo.progressHangupCountArray[75] = po.get_75();
        vo.progressHangupCountArray[76] = po.get_76();
        vo.progressHangupCountArray[77] = po.get_77();
        vo.progressHangupCountArray[78] = po.get_78();
        vo.progressHangupCountArray[79] = po.get_79();
        vo.progressHangupCountArray[80] = po.get_80();
        vo.progressHangupCountArray[81] = po.get_81();
        vo.progressHangupCountArray[82] = po.get_82();
        vo.progressHangupCountArray[83] = po.get_83();
        vo.progressHangupCountArray[84] = po.get_84();
        vo.progressHangupCountArray[85] = po.get_85();
        vo.progressHangupCountArray[86] = po.get_86();
        vo.progressHangupCountArray[87] = po.get_87();
        vo.progressHangupCountArray[88] = po.get_88();
        vo.progressHangupCountArray[89] = po.get_89();
        vo.progressHangupCountArray[90] = po.get_90();
        vo.progressHangupCountArray[91] = po.get_91();
        vo.progressHangupCountArray[92] = po.get_92();
        vo.progressHangupCountArray[93] = po.get_93();
        vo.progressHangupCountArray[94] = po.get_94();
        vo.progressHangupCountArray[95] = po.get_95();
        vo.progressHangupCountArray[96] = po.get_96();
        vo.progressHangupCountArray[97] = po.get_97();
        vo.progressHangupCountArray[98] = po.get_98();
        vo.progressHangupCountArray[99] = po.get_99();
        vo.progressHangupCountArray[100] = po.get_100();
        return vo;
    }

    public int getTotalHangupCount() {
        int count = 0;
        for (int i = 0; i < progressHangupCountArray.length; i++) {
            count += progressHangupCountArray[i];
        }
        return count;
    }

}
