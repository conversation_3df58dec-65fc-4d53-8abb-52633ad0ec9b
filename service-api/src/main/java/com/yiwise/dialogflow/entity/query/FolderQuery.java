package com.yiwise.dialogflow.entity.query;

import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.entity.enums.AuditStatusEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/9/21
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FolderQuery implements Serializable {

    Long targetFolderId;

    List<Long> botIdList;

    List<Long> folderIdList;

    Set<AuditStatusEnum> auditStatusSet;

    SystemEnum systemType;
}
