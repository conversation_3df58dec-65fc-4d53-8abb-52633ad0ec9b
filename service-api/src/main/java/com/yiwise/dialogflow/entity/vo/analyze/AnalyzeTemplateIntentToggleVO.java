package com.yiwise.dialogflow.entity.vo.analyze;

import com.yiwise.dialogflow.entity.enums.AnalyzeTemplateIntentCategoryEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AnalyzeTemplateIntentToggleVO extends AnalyzeTemplateIntentQueryVO implements Serializable {

    /**
     * 目标分类
     */
    @NotNull(message = "targetCategory不能为空")
    AnalyzeTemplateIntentCategoryEnum targetCategory;
}
