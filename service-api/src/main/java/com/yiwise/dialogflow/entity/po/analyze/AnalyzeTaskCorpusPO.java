package com.yiwise.dialogflow.entity.po.analyze;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.dialogflow.entity.enums.AnalyzeTaskCorpusSourceTypeEnum;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AnalyzeTaskCorpusPO implements Serializable {

    /**
     * 语料来源
     */
    @NotNull(message = "source不能为空")
    AnalyzeTaskCorpusSourceTypeEnum source;

    /**
     * 新版外呼任务列表
     */
    List<IdNamePair<Long, String>> newRobotCallJobList;

    /**
     * 外呼任务列表
     */
    List<IdNamePair<Long, String>> robotCallJobList;

    /**
     * 客户列表
     */
    List<IdNamePair<Long, String>> tenantList;

    /**
     * 话术列表
     */
    List<IdNamePair<Long, String>> dialogFlowList;

    /**
     * 外呼日期-开始
     */
    @NotNull(message = "startDate不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    LocalDate startDate;

    /**
     * 外呼日期-结束
     */
    @NotNull(message = "endDate不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    LocalDate endDate;

    /**
     * 语料数量
     */
    @NotNull(message = "corpusSize不能为空")
    Long corpusSize;
}
