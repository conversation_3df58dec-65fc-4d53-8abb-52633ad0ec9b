package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * 对话配置类型
 */
public enum BotTypeEnum implements CodeDescEnum {
    STANDARD("标准版", 0),
    PROFESSIONAL("专业版", 1),
    IVR("IVR语音导航", 2),
    V3("话术重构", 3),
    ALL("所有", 99),
    ;

    String desc;
    Integer code;

    BotTypeEnum(String desc, Integer code) {
        this.code = code;
        this.desc = desc;
    }
    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}