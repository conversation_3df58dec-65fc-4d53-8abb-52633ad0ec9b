package com.yiwise.dialogflow.entity.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 */
public enum StepCategoryEnum implements CodeDescEnum {
    NORMAL(0, "一般流程"),
    BUSINESS(1, "业务流程");


    Integer code;
    String desc;
    StepCategoryEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
