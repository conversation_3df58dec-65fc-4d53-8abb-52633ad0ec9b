package com.yiwise.dialogflow.entity.vo.analyze;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AnalyzeTemplateIntentUpdateVO implements Serializable {

    /**
     * id
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 意图名称
     */
    @NotBlank(message = "意图名称不能为空")
    private String name;

    /**
     * 语料列表
     */
    private List<String> corpusList;
}
