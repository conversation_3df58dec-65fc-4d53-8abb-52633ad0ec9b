package com.yiwise.dialogflow.entity.po;

public interface AsrConfigPO {

    Long getBotId();

    Long getAsrProviderId();

    Long getAsrVocabId();

    Long getAsrSelfLearningDetailId();

    String getAsrErrorCorrectionDetailId();

    Integer getMaxSentenceSilence();

    Long getAsrLanguageId();

    Boolean getEnableAsrDelayStart();

    Double getAsrDelayStartSeconds();

    Boolean getEnableAsrOptimization();

    void setAsrProviderId(Long asrProviderId);

    void setAsrVocabId(Long asrVocabId);

    void setAsrSelfLearningDetailId(Long asrSelfLearningDetailId);

    void setAsrErrorCorrectionDetailId(String asrErrorCorrectionDetailId);

    void setMaxSentenceSilence(Integer maxSentenceSilence);

    void setAsrLanguageId(Long asrLanguageId);

    void setEnableAsrDelayStart(Boolean enableAsrDelayStart);

    void setAsrDelayStartSeconds(Double asrDelayStartSeconds);

    void setEnableAsrOptimization(Boolean enableAsrOptimization);
}
