package com.yiwise.dialogflow.entity.po;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public interface Mismatch {

    Boolean getMismatchKnowledgeAndStep();

    Boolean getMismatchAllKnowledge();

    List<String> getMismatchKnowledgeIdList();

    Boolean getMismatchAllStep();

    List<String> getMismatchStepIdList();

    Boolean getMismatchAllSpecialAnswerConfig();

    List<String> getMismatchSpecialAnswerConfigIdList();

    void setMismatchKnowledgeAndStep(final Boolean mismatchKnowledgeAndStep);

    void setMismatchAllKnowledge(final Boolean mismatchAllKnowledge);

    void setMismatchKnowledgeIdList(final List<String> mismatchKnowledgeIdList);

    void setMismatchAllStep(final Boolean mismatchAllStep);

    void setMismatchStepIdList(final List<String> mismatchStepIdList);

    void setMismatchAllSpecialAnswerConfig(final Boolean mismatchAllSpecialAnswerConfig);

    void setMismatchSpecialAnswerConfigIdList(final List<String> mismatchSpecialAnswerConfigIdList);

    default void mapMismatch(Map<String, String> stepIdMapping, Map<String, String> knowledgeIdMapping, Map<String, String> specialAnswerConfigIdMapping) {
        // 不关联问答知识/流程
        if (BooleanUtils.isTrue(getMismatchKnowledgeAndStep())) {
            // 全选不用做特殊处理
            if (CollectionUtils.isNotEmpty(getMismatchStepIdList())) {
                List<String> collect = getMismatchStepIdList()
                        .stream()
                        .map(stepIdMapping::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                setMismatchStepIdList(collect);
            }
            if (CollectionUtils.isNotEmpty(getMismatchKnowledgeIdList())) {
                List<String> collect = getMismatchKnowledgeIdList()
                        .stream()
                        .map(knowledgeIdMapping::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                setMismatchKnowledgeIdList(collect);
            }
            if (CollectionUtils.isNotEmpty(getMismatchSpecialAnswerConfigIdList())) {
                List<String> collect = getMismatchSpecialAnswerConfigIdList()
                        .stream()
                        .map(specialAnswerConfigIdMapping::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                setMismatchSpecialAnswerConfigIdList(collect);
            }
        }
    }
}