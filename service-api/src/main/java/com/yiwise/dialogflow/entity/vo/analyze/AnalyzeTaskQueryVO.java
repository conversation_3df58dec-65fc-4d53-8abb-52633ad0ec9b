package com.yiwise.dialogflow.entity.vo.analyze;

import com.yiwise.base.model.bean.vo.AbstractQueryVO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AnalyzeTaskQueryVO extends AbstractQueryVO implements Serializable {

    /**
     * 名称、id模糊查询
     */
    String search;

    /**
     * 创建人id列表
     */
    List<Long> createUserIdList;
}