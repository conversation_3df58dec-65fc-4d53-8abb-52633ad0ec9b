package com.yiwise.dialogflow.entity.bo.analyze;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AnalyzeTemplateBlackData implements Serializable {

    List<String> keywords;

    List<String> builtin;

    List<String> customized;
}
