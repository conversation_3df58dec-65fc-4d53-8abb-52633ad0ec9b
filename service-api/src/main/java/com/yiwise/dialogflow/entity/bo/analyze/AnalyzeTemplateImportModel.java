package com.yiwise.dialogflow.entity.bo.analyze;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AnalyzeTemplateImportModel implements Serializable {

    @JsonProperty("white_list")
    Map<String, AnalyzeTemplateWhiteData> whiteMap;

    @JsonProperty("black_list")
    AnalyzeTemplateBlackData blackInfo;

    String domain;
}
