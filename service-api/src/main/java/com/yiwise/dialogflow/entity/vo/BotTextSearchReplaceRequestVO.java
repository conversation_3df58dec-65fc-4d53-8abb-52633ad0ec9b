package com.yiwise.dialogflow.entity.vo;

import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.entity.enums.BotAnswerSearchSourceEnum;
import com.yiwise.dialogflow.entity.enums.BotTextSearchTypeEnum;
import com.yiwise.dialogflow.entity.enums.StepTypeEnum;
import com.yiwise.dialogflow.entity.po.intent.RuleActionParam;
import lombok.Data;

import java.util.List;

@Data
public class BotTextSearchReplaceRequestVO {
    /**
     * 话术id集合
     */
    private List<Long> botIdList;
    /**
     * BOT主键
     */
    private Long botId;

    /**
     * 搜索内容
     */
    private String searchText;

    /**
     * 搜索的短信id
     */
    private Long searchSmsId;

    /**
     * 查找类型
     */
    private BotTextSearchTypeEnum searchType;

    /**
     * 需要替换的内容集合
     */
    private List<BotTextSearchReplaceResultVO> botTextSearchReplaceResultVOList;

    /**
     * 替换内容
     */
    private String replaceText;

    /**
     * 替换的短信id
     */
    private Long replaceSmsId;

    /**
     * 答案来源列表
     */
    private List<BotAnswerSearchSourceEnum> answerSourceList;

    private RuleActionParam replaceRuleActionParam;
}
