package com.yiwise.dialogflow.entity.vo;

import com.yiwise.dialogflow.entity.po.DialogFlowExtraRuleConditionNodePO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/8
 */
@Data
public class BotExportPartExcelVO implements Serializable {

    /**
     * botId
     */
    @NotNull(message = "botId不能为空")
    private Long botId;

    /**
     * 流程节点可多选
     */
    @NotEmpty(message = "nodeList不能为空")
    private List<DialogFlowExtraRuleConditionNodePO> nodeList;
}
