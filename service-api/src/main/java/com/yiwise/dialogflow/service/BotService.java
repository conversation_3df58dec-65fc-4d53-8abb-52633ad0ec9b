package com.yiwise.dialogflow.service;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.api.dto.request.*;
import com.yiwise.dialogflow.entity.enums.EasyCallVersionEnum;
import com.yiwise.middleware.mysql.service.BasicService;
import com.yiwise.dialogflow.api.dto.response.*;
import com.yiwise.dialogflow.entity.bo.BotBO;
import com.yiwise.dialogflow.entity.dto.BotNameCountDTO;
import com.yiwise.dialogflow.entity.enums.AuditStatusEnum;
import com.yiwise.dialogflow.entity.enums.BotGenerateStatusEnum;
import com.yiwise.dialogflow.api.enums.V3BotTypeEnum;
import com.yiwise.dialogflow.entity.po.BotConfigPO;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.RobotSnapshotPO;
import com.yiwise.dialogflow.entity.query.BotQuery;
import com.yiwise.dialogflow.entity.vo.*;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface BotService extends BasicService<BotPO> {

    BotPO create(BotCreateRequestVO botVO);

    Long createFromSnapshot(RobotSnapshotPO snapshot);

    @Transactional(rollbackFor = Exception.class)
    void update(BotPO botPO);

    @Transactional(rollbackFor = Exception.class)
    void delete(Long botId, Long userId);

    PageResultObject<BotVO> list(BotQuery botQuery);

    List<IdNamePair<Long, String>> listAllCreateUser(Long tenantId);

    PageResultObject<BotVO> queryListWithoutWrapVO(BotQuery botQuery);

    BotVO detail(Long botId);

    BotPO getById(Long botId);

    List<BotPO> getByIdList(List<Long> botIdList);

    List<BotVO> queryListWithoutPage(BotQuery botQuery);

    void onUpdateBotResource(Long botId);

    void updateAuditStatus(Long botId, AuditStatusEnum auditStatus);

    void updateDomainName(Long botId, String domainName);

    void updateAsrProvider(Long botId, Long asrProviderId);

    @Transactional(rollbackFor = Exception.class)
    BotSnapshotVO publish(BotPublishVO publishParam, Long userId);

    @Transactional(rollbackFor = Exception.class)
    void manualPublish(BotBO botQuery, Long userId);

    /**
     * 根据录音师id查询关联的bot列表
     *
     * @param query
     * @return
     */
    PageResultObject<IdNamePair<Long, String>> queryBotListByRecordUserId(BotQuery query);

    List<BotConfigPO> bind(List<Long> dialogFlowIds, Long tenantId, Long userId);

    void unbind(List<Long> dialogFlowId, Long userId);

    int unBindAsrErrorCorrection(Long botId, String asrErrorCorrectionDetailId, Long userId);

    Optional<String> getNameByDialogFlowId(Long dialogFlowId);

    String createQrCode(CreateBotQrCodeVO vo);

    BatchBotSnapshotVO batchPublishBot(BotQuery botQuery, Long userId);

    void batchManualPublish(BotBO botQuery, Long userId);

    int batchBindAsrVocab(List<Long> botIdList, Long asrVocabId);

    int batchBindAsrSelfLearning(List<Long> botIdList, Long asrSelfLearningDetailId);

    int bindAsrErrorCorrection(List<Long> botIdList, String asrErrorCorrectionDetailId, Long userId);

    int unBindAsrVocab(Long botId, Long asrVocabId);

    int unBindAsrSelfLearning(Long botId, Long asrSelfLearningDetailId);

    PageResultObject<BotVO> getPage(BotQuery botQuery);

    void batchUpdate(List<BotVO> updateList);

    List<SimpleBotInfo> querySimpleBotInfoList(BotListRequest request);

    Set<String> getConcernNameSetByDialogFlowId(Long dialogFlowId);

    boolean checkEnableHumanInterventionByDialogFlowId(Long dialogFlowId);

    boolean checkEnableHumanInterventionByBotId(Long botId);

    void updateGenerateStatus(Long botId, BotGenerateStatusEnum generateStatus);

    List<BotNameCountDTO> countByNameList(List<String> botNameList);

    void updateDescription(Long botId, String description);

    /**
     * 判断目标bot是否可以改写/生成
     * @param botId
     */
    boolean checkCanRewrite(Long botId);

    SimpleBotApprovalResult publishAndApproval(BotApprovalRequest request);

    /**
     * 复制bot并提交意图训练
     */
    Long copyBotAndSubmitIntentTrain(CopyBotRequest request);

    void checkBotExistAndThrow(Long botId);

    void checkBotExistAndThrowByDialogFlowId(Long dialogFlowId);

    List<SimpleBotInfo> queryPublishedBotList(Long tenantId, String searchWord);

    BotInfo getByDialogFlowId(Long tenantId, Long dialogFlowId);

    BotExportInfo exportBotInfo(Long tenantId, Long dialogFlowId);

    /**
     * 根据租户查询所有的 bot 列表, 目前天润那边使用
     *
     * @param tenantId  租户 id
     * @param v3BotType
     * @return bot 列表
     */
    List<BotInfo> getAllListByTenantId(Long tenantId, V3BotTypeEnum v3BotType);

    List<BotInfo> getDetailListByIdList(BotListRequest request);

    List<MagicBotTemplateInfo> getMagicBotTemplateByIdList(BotListRequest request);

    /**
     * 返回所有的轻量 bot 模板列表
     * 前端要求返回所有数据
     * @return 轻量 bot 模板列表
     */
    List<MagicBotTemplateInfo> getMagicBotTemplateByEasyCallVersion(@Nullable EasyCallVersionEnum easyCallVersion);

    /**
     * 获取当前话术可以复制的类型
     *
     * @param botId 话术id
     * @return [0,1]
     */
    List<V3BotTypeEnum> availableCopyType(Long botId);

    /**
     * 查询当前话术关联的场景名称列表
     *
     * @param botId 话术id
     * @return T/F
     */
    List<String> getDependentSceneNameList(Long botId);

    /**
     * 获取话术类型
     *
     * @param botId 话术id
     * @return V3BotTypeEnum
     */
    V3BotTypeEnum getBotType(Long botId);

    MagicBotInfo createMagicBot(MagicBotCreateRequest request);

    Boolean preCreateMagicBot(MagicBotCreateRequest request);

    BotAttributeDTO getBotAttributeByDialogFlowId(Long dialogFlowId);

    PageResultObject<SimpleBotInfo> searchBot(BotSearchRequest request);

    /**
     * 批量更新话术可见状态
     *
     * @param request form
     * @param userId 操作人id
     */
    void batchUpdateVisibleStatus(BatchUpdateVisibleStatusRequestVO request, Long userId);

    Boolean preCreateMagicActivityConfig(MagicActivityConfigCreateRequest request);

    String createMagicActivityConfig(MagicActivityConfigCreateRequest request);

    Boolean checkEnableLLMChat(Long botId);
}
