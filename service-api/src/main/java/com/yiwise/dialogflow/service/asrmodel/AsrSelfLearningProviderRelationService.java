package com.yiwise.dialogflow.service.asrmodel;

import com.yiwise.dialogflow.engine.share.enums.AsrProviderEnum;
import com.yiwise.dialogflow.entity.enums.asr.AsrSlefLearningTrainStatusEnum;
import com.yiwise.dialogflow.entity.po.asrmodel.AsrSelfLearningProviderRelationPO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/2 14:32:31
 */
public interface AsrSelfLearningProviderRelationService {
    List<AsrSelfLearningProviderRelationPO> getByAsrSelfLearningId(Long asrSelfLearningId);

    AsrSelfLearningProviderRelationPO getByAsrSelfLearningIdAndProvider(Long asrSelfLearningId, AsrProviderEnum provider);

    void add(Long asrSelfLearningId, AsrProviderEnum provider);

    void updateByCondition(Long asrSelfLearningId, String providerModelId, String dataId, AsrProviderEnum provider);

    void updateTrainStatusById(Long asrSelfLearningProviderRelationId, AsrSlefLearningTrainStatusEnum status);

    void delete(AsrSelfLearningProviderRelationPO asrSelfLearningProviderRelationPO);
}