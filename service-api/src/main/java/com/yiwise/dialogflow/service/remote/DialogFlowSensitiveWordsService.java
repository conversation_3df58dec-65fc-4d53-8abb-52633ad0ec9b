package com.yiwise.dialogflow.service.remote;

import com.yiwise.dialogflow.service.openfeign.RemoteServiceAdapter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/4/7
 * @class <code>DialogFlowSensitiveWordsService</code>
 * @see
 * @since JDK1.8
 */
@Service
public class DialogFlowSensitiveWordsService {

    @Resource
    RemoteServiceAdapter remoteServiceAdapter;

    public List<List<String>> getSensitiveWordsList() {
        return remoteServiceAdapter.invoke("dialogFlowSensitiveWordsServiceImpl", "getSensitiveWordsListRemote", List.class);
    }
}
