package com.yiwise.dialogflow.service;

import com.yiwise.dialogflow.entity.po.*;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface LabelGenerateService {

    void answerLabel(Long botId, List<? extends BaseAnswerContent> answerList);

    void nodeLabel(Long botId, String parentLabel, List<? extends DialogBaseNodePO> nodeList);

    void updateNodeLabelSeqIfNeed(Long botId, Integer newSeq, String parentLabel);

    void renameNodeLabelPrefix(Long botId, String oldStepLabel, String newStepLabel);

    void mainStepLabel(Long botId, List<StepPO> mainStepList);

    void updateMainStepSeqIfNeed(Long botId, Integer newSeq);

    void independentStepLabel(Long botId, List<StepPO> independentStepList);

    void updateIndependentStepSeqIfNeed(Long botId, Integer newSeq);

    void knowledgeLabel(Long botId, List<? extends KnowledgePO> knowledgeList);

    void specialAnswerLabel(Long botId, List<SpecialAnswerConfigPO> specialAnswerList);
}
