package com.yiwise.dialogflow.service.magic;

import java.util.List;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.po.magic.MagicTemplateVarGenerateRecordDetailPO;
import com.yiwise.dialogflow.entity.vo.magic.MagicTemplateVarGenerateRecordQueryVO;

/**
 * 轻量化模板变量测试生成记录详情
 */
public interface MagicTemplateVarGenerateRecordDetailService {

    /**
     * 创建记录详情
     */
    void create(MagicTemplateVarGenerateRecordDetailPO detail);
    
    /**
     * 批量创建记录详情
     */
    void batchCreate(List<MagicTemplateVarGenerateRecordDetailPO> details);
    
    /**
     * 根据详情ID查询
     */
    MagicTemplateVarGenerateRecordDetailPO findById(Long botId, String recordId,String detailId);
    
    /**
     * 根据记录ID查询详情列表
     */
    List<MagicTemplateVarGenerateRecordDetailPO> findByRecordId(Long botId, String recordId);

    /**
     * 根据条件查询
     */
    PageResultObject<MagicTemplateVarGenerateRecordDetailPO> queryLastListByCondition(MagicTemplateVarGenerateRecordQueryVO query);

} 