package com.yiwise.dialogflow.service.entitycollect;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.bo.EntityValueBO;
import com.yiwise.dialogflow.entity.enums.SyncModeEnum;
import com.yiwise.dialogflow.entity.po.BaseEntityPO;
import com.yiwise.dialogflow.entity.po.SystemEntityPO;
import com.yiwise.dialogflow.entity.query.EntityQueryVO;
import com.yiwise.dialogflow.entity.query.EntitySyncVO;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.SimpleEntityVO;
import com.yiwise.dialogflow.entity.vo.entitycollect.EntityDeleteRequestVO;
import com.yiwise.dialogflow.entity.vo.sync.BotSyncResultVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface EntityService {

    void initSystemEntity(Long botId);

    SystemEntityPO getOriginInputSystemEntity();

    /**
     * 创建实体
     */
    BaseEntityPO create(BaseEntityPO entity, Long userId);

    Map<String, String> getAllEntityIdNameMapByBotId(Long botId);

    /**
     * 查询实体列表
     */
    PageResultObject<BaseEntityPO> queryByCondition(EntityQueryVO condition);

    /**
     * 更新实体
     */
    BaseEntityPO update(BaseEntityPO entity, Long userId);

    /**
     * 批量删除实体
     */
    void batchDelete(EntityDeleteRequestVO request, Long userId);

    List<BaseEntityPO> getAllByBotId(Long botId);

    List<SimpleEntityVO> getIdNamePairListByBotId(Long botId);

    void fixData();

    void fixDataByBotId(Long botId);

    BaseEntityPO getById(Long botId, String id);

    /**
     * 实体同步
     *
     * @param request form
     */
    BotSyncResultVO sync(EntitySyncVO request);

    /**
     * 根据id列表查询实体
     *
     * @param idList id列表
     * @return 实体列表
     */
    List<BaseEntityPO> getByIdList(Collection<String> idList);

    BaseEntityPO singleSync(Long targetBotId, BaseEntityPO srcEntity, SyncModeEnum syncMode);
}