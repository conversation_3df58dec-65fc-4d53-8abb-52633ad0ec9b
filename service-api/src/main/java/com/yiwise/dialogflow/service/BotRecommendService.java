package com.yiwise.dialogflow.service;

import com.yiwise.dialogflow.entity.po.BotRecommendRephraseRecordPO;
import com.yiwise.dialogflow.entity.vo.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface BotRecommendService {

    /**
     * 话术文本解析
     *
     * @param botId 话术id
     * @return 是否成功
     */
    boolean textParsing(Long botId);

    /**
     * 解析符合条件的话术列表
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param minCallCount 最小呼叫次数
     */
    void textParsingByRangeTime(LocalDateTime startTime, LocalDateTime endTime, Long minCallCount);

    /**
     * 搜索相似话术
     *
     * @param request form
     * @return 相似话术列表
     */
    List<BotTextSearchResultVO> search(BotRecommendSearchRequestVO request);

    /**
     * 排序
     *
     * @param request form
     * @return 排序结果
     */
    List<BotTextSearchResultVO> sort(BotRecommendSearchResultSortRequestVO request);

    /**
     * 改写
     *
     * @param request form
     * @return 改写结果
     */
    Map<String, List<String>> rephrase(BotRecommendRephraseRequestVO request);

    /**
     * 话术中答案改写
     *
     * @param request form
     * @param userId  操作人id
     * @return 改写结果
     */
    Map<String, List<BotRecommendRephraseRecordPO>> botAnswerRephrase(BotAnswerRephraseRequestVO request, Long userId);

    /**
     * 查询答案改写记录
     *
     * @param request form
     * @return 改写记录
     */
    List<BotRecommendRephraseRecordPO> queryRephraseRecordList(BotAnswerRephraseRequestVO request);

    /**
     * 更新改写结果
     *
     * @param id                id
     * @param rephrasedSentence 改写结果
     * @param userId            操作人id
     */
    void updateRephrasedSentence(String id, String rephrasedSentence, Long userId);

    /**
     * 收藏
     *
     * @param id id
     * @param userId 操作人id
     */
    void starred(String id, Long userId);

    /**
     * 删除改写记录
     *
     * @param idList id列表
     */
    void deleteRephrasedRecord(List<String> idList);

    /**
     * 查询指定时间范围内使用了话术推荐的botId列表
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return botId列表
     */
    List<Long> queryBotIdListByRangeTime(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询话术中答案和被收藏的改写记录
     *
     * @param botId botId
     * @return 改写记录
     */
    List<BotRecommendRephraseRecordVO> queryAnswerStarredRephraseRecordList(Long botId);
}