package com.yiwise.dialogflow.service.train;


import com.yiwise.dialogflow.entity.enums.AlgorithmTrainTypeEnum;
import com.yiwise.dialogflow.entity.po.ModelVersionPO;

import javax.annotation.Nullable;

/**
 * <AUTHOR>
 * @date 2020/2/21
 */
public interface ModelVersionService {

    ModelVersionPO get(String modelId);

    String create(Long tenantId, Long robotId, AlgorithmTrainTypeEnum type);

    ModelVersionPO createByRefId(Long tenantId, String refId, AlgorithmTrainTypeEnum type);

    @Nullable
    ModelVersionPO getLast(Long tenantId, Long robotId, AlgorithmTrainTypeEnum type);

    ModelVersionPO getByRefId(Long tenantId, String refId, AlgorithmTrainTypeEnum type);
}
