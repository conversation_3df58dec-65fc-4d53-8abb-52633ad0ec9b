package com.yiwise.dialogflow.service.remote.customer;

import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.customer.data.platform.rpc.api.service.dto.CustomerAttributeMetaDataDTO;
import com.yiwise.customer.data.platform.rpc.api.service.request.CustomerAttributeMetaDataRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class CustomerAttributeService {

    @Resource
    private CustomerAttributeMetaDataClient customerAttributeMetaDataClient;

    public List<CustomerAttributeMetaDataDTO> getCustomerAttributeByIdList(Long tenantId, List<Long> attributeIdList) {
        if (CollectionUtils.isEmpty(attributeIdList) || Objects.isNull(tenantId)) {
            return Collections.emptyList();
        }
        CustomerAttributeMetaDataRequest request = new CustomerAttributeMetaDataRequest();
        request.setTenantId(tenantId);
        request.setAttributeIdList(attributeIdList);
        log.info("请求ma接口, getCustomerAttributeByIdList, request: {}", JsonUtils.object2String(request));
        List<CustomerAttributeMetaDataDTO> result = customerAttributeMetaDataClient.getCustomerAttributeByIdList(request);
        log.info("请求ma接口, getCustomerAttributeByIdList, response: {}", JsonUtils.object2String(result));
        return result;
    }

}
