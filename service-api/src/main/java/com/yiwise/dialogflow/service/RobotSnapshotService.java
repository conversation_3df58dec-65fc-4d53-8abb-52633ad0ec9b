package com.yiwise.dialogflow.service;

import com.alibaba.fastjson.JSONObject;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.dialogflow.engine.share.BotMetaData;
import com.yiwise.dialogflow.engine.share.MagicActivityConfig;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.engine.share.model.BotAsrConfig;
import com.yiwise.dialogflow.entity.po.RobotSnapshotPO;
import com.yiwise.dialogflow.entity.query.RobotSnapshotCreateRequest;
import com.yiwise.dialogflow.entity.query.RobotSnapshotQuery;
import com.yiwise.dialogflow.entity.vo.RobotSnapshotCreateResult;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface RobotSnapshotService {

    RobotSnapshotCreateResult createAndValid(RobotSnapshotCreateRequest request);

    /**
     * 为导出创建快照, 快照数据不入库
     * @param botId botId
     * @return 快照数据
     */
    RobotSnapshotPO createSnapshotForExport(Long botId, Long userId);

    RobotSnapshotCreateResult create(Long botId, Long userId, Boolean ignoreWarning);

    RobotSnapshotCreateResult createTrainTestSnapshot(Long botId, RobotSnapshotUsageTargetEnum usageTarget, SystemEnum systemEnum);

    Integer createTextTestSnapshot(Long dialogFlowId);

    Integer createTextSnapshotByBotId(Long botId);

    Integer createSpeechTestSnapshot(Long dialogFlowId);


    Optional<RobotSnapshotPO> queryLastSnapshotByCondition(RobotSnapshotQuery condition);

    Optional<Integer> queryLastVersionByCondition(RobotSnapshotQuery condition);

    RobotSnapshotPO getByVersion(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version);

    Mono<RobotSnapshotPO> asyncGetByVersion(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version);

    Integer getLastVersionNumber(Long botId, RobotSnapshotUsageTargetEnum usageTarget);

    Mono<Integer> asyncGetLastVersionNumber(Long botId, RobotSnapshotUsageTargetEnum usageTarget);
    JSONObject getAsrMetaData(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version);

    JSONObject getAsrMetaDataByDialogFlowId(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget, Integer version);

    /**
     * 获取最近已发布快照
     *
     * @param botId
     * @return
     */
    RobotSnapshotPO getLastPublishRobotSnapshot(Long botId);

    /**
     * 修改快照状态为已发布
     *
     * @param botId
     * @return
     */
    void updateRobotSnapshotPublishStatus(Long botId, Integer version);

    void updateRobotSnapshotPublishStatus(Long botId);

    /**
     * 外呼任务那边获取的机器人的数据, 主要是在任务开始前对录音的下载和提前拼接等
     *
     * @param botId
     * @param version
     * @return
     */
    BotMetaData getBotMetaData(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version);

    Mono<BotMetaData> asyncGetBotMetaDataByDialogFlowId(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version);

    Integer getLastVersionByDialogFlowId(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget);

    Mono<Integer> asyncGetLastVersionByDialogFlowId(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget);

    BotMetaData getBotMetaDataByDialogFlowId(Long dialogFlowId, RobotSnapshotUsageTargetEnum usageTarget, Integer version);

    BotAsrConfig getRealtimeAsrConfig(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version);

    Mono<BotAsrConfig> asyncGetRealtimeAsrConfig(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version);
    /**
     * 查询实时资源快照, 不进行任何校验, 快照也不会持久化
     * @param botId  botId
     * @return
     */
    RobotSnapshotPO getRealtimeResourceSnapshot(Long botId);

    MagicActivityConfig getMagicActivityConfig(Long botId, RobotSnapshotUsageTargetEnum usageTarget, Integer version, Long callJobId);

    Map<Long, Object> getLastPublishedSnapshotByBotIdList(List<Long> idList);

}
