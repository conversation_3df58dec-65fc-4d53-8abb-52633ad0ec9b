package com.yiwise.dialogflow.service.stats;

import com.yiwise.dialogflow.entity.bo.stats.BotStatsAnalysisResult;
import com.yiwise.dialogflow.entity.enums.DialStatusEnum;
import com.yiwise.dialogflow.entity.po.stats.BotReceptionStatsPO;
import com.yiwise.dialogflow.entity.po.stats.JobCallDateRangePO;
import com.yiwise.dialogflow.entity.query.BaseStatsQuery;
import com.yiwise.dialogflow.entity.vo.stats.AllBotDailyReceptionStatsVO;
import com.yiwise.dialogflow.entity.vo.stats.AllBotHourlyReceptionStatsVO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;


/**
 * bot接待统计
 */
public interface BotReceptionStatsService {

    int queryBotReceptionCount(Long botId, BaseStatsQuery condition);

    List<Long> queryAllCallJobId(Long botId, BaseStatsQuery condition);

    List<JobCallDateRangePO> queryJobReceptionInfo(Long botId, BaseStatsQuery condition);

    List<BotReceptionStatsPO> queryBotDailyReceptionInfo(Long botId, BaseStatsQuery condition);

    void saveBotReceptionStats(BotStatsAnalysisResult analysisResult, int dialStatus);

    List<AllBotDailyReceptionStatsVO> queryAllBotDailyReceptionInfo(LocalDate beginDate, LocalDate endDate);

    List<AllBotHourlyReceptionStatsVO> queryAllBotHourlyReceptionInfo(LocalDateTime beginDateTime, LocalDateTime endDateTime);
}