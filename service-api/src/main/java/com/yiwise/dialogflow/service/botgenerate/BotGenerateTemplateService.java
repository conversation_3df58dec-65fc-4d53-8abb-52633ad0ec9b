package com.yiwise.dialogflow.service.botgenerate;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.enums.BotGenerateTemplateStatusEnum;
import com.yiwise.dialogflow.entity.po.botgenerate.BotGenerateTemplatePO;
import com.yiwise.dialogflow.entity.query.botgenerate.BotGenerateTemplateQueryVO;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.botgenerate.BotGenerateResultVO;
import com.yiwise.dialogflow.entity.vo.botgenerate.BotGenerateTemplateVO;

import java.util.List;
import java.util.Optional;

public interface BotGenerateTemplateService {

    PageResultObject<BotGenerateTemplateVO> queryByCondition(BotGenerateTemplateQueryVO condition);

    List<IdNamePair<Long, String>> getAllCreateUserInfoList();

    BotGenerateTemplateVO create(BotGenerateTemplatePO template, Long createUserId);

    BotGenerateTemplateVO update(BotGenerateTemplatePO template, Long updateUserId);

    void deleteById(String templateId, Long deleteUserId);

    BotGenerateTemplatePO copy(String sourceTemplateId, Long userId);

    BotGenerateResultVO generate(String templateId, Long userId, Boolean ignoreWarning);

    Optional<BotGenerateTemplatePO> getById(String templateId);

    Optional<BotGenerateTemplateVO> getVOById(String templateId);

    BotGenerateResultVO updateAndGenerate(BotGenerateTemplateVO template, Long userId);

    void updateLastCompletePercentAndStatus(String templateId, String lastCompletePercent, BotGenerateTemplateStatusEnum status);
}
