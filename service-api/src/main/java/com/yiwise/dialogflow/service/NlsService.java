package com.yiwise.dialogflow.service;

import com.yiwise.dialogflow.entity.po.NlsFlashRecognizerResultPO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface NlsService {

    /**
     * 录音文件识别极速版
     *
     * @param audioAddress 音频文件完整地址
     * @return 识别的文本
     */
    String flashRecognizer(String audioAddress);

    /**
     * 异步录音文件识别
     *
     * @param botId  话术id
     * @param ossKey ossKey
     */
    void asyncFlashRecognizer(Long botId, String ossKey);

    /**
     * 查询全部的音频识别结果
     *
     * @param botId 话术id
     * @return 音频识别结果列表
     */
    List<NlsFlashRecognizerResultPO> findAll(Long botId);
}
