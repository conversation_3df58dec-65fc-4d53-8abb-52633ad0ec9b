package com.yiwise.dialogflow.service;

import com.yiwise.dialogflow.entity.po.BotAudioConfigPO;
import com.yiwise.dialogflow.entity.po.TtsJobPO;
import com.yiwise.dialogflow.entity.vo.audio.TtsComposeProgressVO;
import com.yiwise.dialogflow.entity.vo.audio.TtsComposeResultVO;

import java.util.Optional;
import java.util.Set;

/**
 * tts 处理任务, 合成完直接通过 AnswerAudioMappingService直接入库, 不更新原始数据引用
 * <AUTHOR>
 */
public interface TtsJobService {

    TtsJobPO create(BotAudioConfigPO audioConfig, int distTotalCount, Set<String> answerList, Long userId, int distManMadeCompletedCount);

    /**
     * 创建合成任务, 如果录音模式不是合成音,则不会创建任务
     * @param botId botId
     * @param originalAnswerSet 原始待合成文本集合, 即对于变量部分未拆分
     * @param userId 操作日
     * @return 合成任务
     */
    Optional<TtsJobPO> createIfComposeAudioType(Long botId, Set<String> originalAnswerSet, Long userId);

    TtsJobPO getLastJob(Long botId);

    boolean lastJobIsRunning(Long botId);

    void updateProgress(Long botId, String jobId, Set<String> completedSet, Set<String> failSet, boolean finish);

    void updateFinishStatus(Long botId, String jobId, boolean completed, String failMsg);

    TtsComposeResultVO composeTextByBotConfig(Long botId, String text);

    TtsComposeProgressVO getLastComposeProgress(Long botId);
}
