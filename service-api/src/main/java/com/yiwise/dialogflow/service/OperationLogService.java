package com.yiwise.dialogflow.service;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.dto.OperationLogDTO;
import com.yiwise.dialogflow.entity.enums.OperationLogResourceTypeEnum;
import com.yiwise.dialogflow.entity.enums.OperationLogTypeEnum;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.OperationLogQueryVO;
import com.yiwise.dialogflow.entity.vo.OperationLogVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/13
 */
public interface OperationLogService {

    ThreadLocal<Boolean> IGNORE = new ThreadLocal<>();

    static void setIgnoreSign() {
        IGNORE.set(true);
    }

    static void clearIgnoreSign() {
        IGNORE.remove();
    }

    /**
     * 操作对象枚举列表
     *
     * @return 操作对象枚举列表
     */
    List<IdNamePair<String, String>> resourceTypeList();

    /**
     * 操作日志类型枚举列表
     *
     * @return 操作日志类型枚举列表
     */
    List<IdNamePair<String, String>> logTypeList();

    /**
     * 查询bot操作日志的操作人列表
     *
     * @param botId botId
     * @return 操作人列表
     */
    List<IdNamePair<Long, String>> operatorList(Long botId);

    /**
     * 保存操作日志
     *
     * @param botId        botId
     * @param type         操作日志类型
     * @param resourceType 操作对象
     * @param detail       操作日志详情
     * @param operatorId   操作人id
     */
    void save(Long botId, OperationLogTypeEnum type, OperationLogResourceTypeEnum resourceType, String detail, Long operatorId);

    /**
     * 保存操作日志
     *
     * @param botId        botId
     * @param type         操作日志类型
     * @param resourceType 操作对象
     * @param detailList   操作日志详情列表
     * @param operatorId   操作人id
     */
    void save(Long botId, OperationLogTypeEnum type, OperationLogResourceTypeEnum resourceType, List<String> detailList, Long operatorId);

    /**
     * 批量保存操作日志
     *
     * @param list DTO list
     */
    void batchSave(List<OperationLogDTO> list);

    /**
     * 查询操作日志列表
     *
     * @param queryVO 查询表单
     * @return 操作日志列表
     */
    PageResultObject<OperationLogVO> list(OperationLogQueryVO queryVO);

    /**
     * 操作日志导出
     *
     * @param queryVO 查询表单
     * @return Excel文件链接
     */
    String export(OperationLogQueryVO queryVO);

    /**
     * 查询时间范围内有操作日志的日期列表
     *
     * @param botId     botId
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 日期列表[2022-01-01,2022-01-02]
     */
    List<String> operationDateList(Long botId, LocalDateTime startTime, LocalDateTime endTime);
}
