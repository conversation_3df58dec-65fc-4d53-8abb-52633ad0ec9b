package com.yiwise.dialogflow.service.intent;

import com.yiwise.dialogflow.entity.po.intent.IntentCorpusPO;
import com.yiwise.dialogflow.entity.query.IntentCorpusQuery;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/15
 */
public interface IntentCorpusService {

    void save(IntentCorpusPO intentCorpusPO);

    void saveAll(Collection<IntentCorpusPO> intentCorpusPOList);

    void delete(String intentCorpusId);

    void deleteByIntentIdList(Collection<String> intentIdList);

    IntentCorpusPO findByIntentId(String intentId);

    List<IntentCorpusPO> findByIntentIdIn(Collection<String> intentIdList);

    List<IntentCorpusPO> findByBotId(Long botId);

    List<IntentCorpusPO> scroll(IntentCorpusQuery intentCorpusQuery);

    /**
     * 修复意图内置语料, 问题来源于bot前移时, 未处理同名意图语料冲突
     * @param botId
     */
    void fixIntentBuildInCorpus(Long botId);

    List<IntentCorpusPO> findByBotIdAndKeywordList(Long botId, List<String> keywordList);

    List<IntentCorpusPO> findByBotIdAndDescList(Long botId, List<String> descList);

    void updateIntentOnVariableRename(Long botId, String variableId, String oldVariableName, String newVariableName, Long userId);
}
