package com.yiwise.dialogflow.service.magic;

import com.yiwise.dialogflow.api.dto.response.activity.MagicFormTemplateDTO;
import com.yiwise.dialogflow.entity.po.MagicFormTemplatePO;

public interface MagicFormTemplateService {

    /**
     * 保存
     */
    MagicFormTemplatePO create(MagicFormTemplatePO template, Long userId);

    /**
     * 保存并发布
     */
    MagicFormTemplatePO createAndPublish(MagicFormTemplatePO template, Long userId);

    /**
     * 查询最后保存的模板
     */
    MagicFormTemplatePO getLastMagicFormTemplate(Long botId);

    /**
     * 查询最后发布的模板
     */
    MagicFormTemplatePO getLastPublishedMagicFormTemplate(Long botId);

    MagicFormTemplateDTO getLastPublishedMagicFormTemplateDTO(Long botId);
}
