package com.yiwise.dialogflow.service.stats;

import com.yiwise.dialogflow.engine.share.CallDataInfo;
import com.yiwise.dialogflow.engine.share.enums.AnswerSourceEnum;
import com.yiwise.dialogflow.entity.bo.stats.BotStatsAnalysisResult;
import com.yiwise.dialogflow.entity.query.BaseStatsQuery;
import com.yiwise.dialogflow.entity.query.SpecialAnswerConfigQuery;
import com.yiwise.dialogflow.entity.query.StepNodeQuery;
import com.yiwise.dialogflow.entity.query.StepQueryVO;
import com.yiwise.dialogflow.entity.vo.*;
import com.yiwise.dialogflow.entity.vo.node.DialogBaseNodeVO;
import com.yiwise.dialogflow.entity.vo.stats.*;

import java.util.List;

/**
 * 数据统计对外提供入口
 */
public interface BotStatsFacadeService {


    void saveAnalysisResult(BotStatsAnalysisResult analysisResult, CallDataInfo callDataInfo);

    /**
     * 查询意图触发统计, 就是查询特殊语境, 问答知识,
     * @param botId
     * @param condition
     * @return
     */
    List<IntentTriggerStatsVO> queryIntentTriggerStats(Long botId, BaseStatsQuery condition);

    List<AnswerPlayProgressHangupStatsVO> queryAnswerPlayProgressHangupStats(Long botId,
                                                                             AnswerSourceEnum answerSource,
                                                                             String stepId,
                                                                             String nodeId,
                                                                             String knowledgeId,
                                                                             String specialAnswerConfigId,
                                                                             BaseStatsQuery condition);

    void wrapNodeStatsInfo(List<DialogBaseNodeVO> nodeList, StepNodeQuery condition);

    void wrapKnowledgeStatsInfo(List<KnowledgeVO> knowledgeList, KnowledgeQueryVO condition);

    void wrapSpecialAnswerStatsInfo(List<SpecialAnswerConfigVO> specialAnswerConfigList, SpecialAnswerConfigQuery condition);

    void wrapIntentLevelRuleStatsInfo(List<IntentRuleVO> ruleList, BaseStatsQuery condition);

    void wrapIntentActionRuleStatsInfo(List<IntentRuleActionVO> ruleList, BaseStatsQuery condition);

    void wrapStepStatsInfo(List<StepVO> result, StepQueryVO condition);

    List<SimpleCallJobInfoVO> queryCallJobList(BaseStatsQuery condition);

    List<SimpleMaFlowInfoVO> queryMaFlowList(BaseStatsQuery condition);

    List<SimpleNewCallJobInfoVO> queryNewCallJobList(BaseStatsQuery condition);

    JobCallDateVO queryCallDateInfo(BaseStatsQuery condition);

    List<SimpleNodeStatsInfoVO> queryAllNodeStats(BaseStatsQuery condition);

    List<SimpleIntentLevelStatsVO> queryIntentLevelStatsList(BaseStatsQuery condition);

    List<SimpleIntentActionStatsVO> queryIntentActionStatsList(BaseStatsQuery condition);

    List<NodeJumpStatsVO> queryNodeJumpOutStats(Long botId, String stepId, String nodeId, BaseStatsQuery condition);

}
