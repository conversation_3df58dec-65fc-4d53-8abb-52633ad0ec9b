package com.yiwise.dialogflow.service.remote.account;

import com.yiwise.cloud.aicc.service.remote.ServiceFeignConfig;
import com.yiwise.common.cloud.feign.helper.decode.FeignResultDecoder;
import feign.codec.Decoder;
import org.springframework.context.annotation.Bean;

public class AccountFeignConfig extends ServiceFeignConfig {

    @Bean
    public Decoder feignDecoder() {
        return new FeignResultDecoder();
    }
}
