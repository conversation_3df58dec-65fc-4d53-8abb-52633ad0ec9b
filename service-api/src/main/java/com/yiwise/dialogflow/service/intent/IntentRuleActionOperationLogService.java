package com.yiwise.dialogflow.service.intent;

import com.yiwise.dialogflow.entity.bo.ActionNameResourceBO;
import com.yiwise.dialogflow.entity.bo.algorithm.ResourceId2NameBO;
import com.yiwise.dialogflow.entity.po.intent.IntentRuleActionPO;
import com.yiwise.dialogflow.entity.po.intent.IntentRulePO;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/9/22
 * @class <code>IntentRuleActionOperationLogService</code>
 * @see
 * @since JDK1.8
 */
public interface IntentRuleActionOperationLogService {
    void addOperationLog(IntentRuleActionPO oldIntentRule, IntentRuleActionPO newIntentRule,
                         ResourceId2NameBO resourceMap, ActionNameResourceBO actionResourceMap, Long botId, Long userId);
}
