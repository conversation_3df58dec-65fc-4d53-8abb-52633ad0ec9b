package com.yiwise.dialogflow.service;

import com.yiwise.dialogflow.entity.po.BaseEntityPO;
import com.yiwise.dialogflow.entity.po.DialogBaseNodePO;
import com.yiwise.dialogflow.entity.po.SpecialAnswerConfigPO;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.po.intent.IntentRuleActionPO;
import com.yiwise.dialogflow.entity.po.intent.IntentRulePO;
import com.yiwise.dialogflow.entity.query.EntitySyncVO;
import com.yiwise.dialogflow.entity.vo.StepVO;
import com.yiwise.dialogflow.entity.vo.audio.request.PublicAudioSyncRequestVO;
import com.yiwise.dialogflow.entity.vo.sync.*;

import java.util.List;

public interface BotSyncOperationLogService {

    void stepSync(StepVO step,
                  StepSyncVO sync,
                  List<Long> successBotIdList,
                  List<Long> failBotIdList);

    void nodeSync(DialogBaseNodePO node,
                  NodeSyncVO sync,
                  List<Long> successBotIdList,
                  List<Long> failBotIdList);

    void knowledgeSync(KnowledgeSyncVO sync,
                       List<String> srcKnowledgeIdList,
                       List<Long> successBotIdList,
                       List<Long> failBotIdList);

    void intentSync(IntentSyncVO sync,
                    List<? extends IntentPO> syncIntentList,
                    List<Long> successBotIdList,
                    List<Long> failBotIdList);

    void audioSync(PublicAudioSyncRequestVO syncRequest,
                   List<String> successAnswerList);

    void specialAnswerConfigSync(SpecialAnswerSyncVO syncConfig,
                                 List<SpecialAnswerConfigPO> specialAnswerConfigList,
                                 List<Long> successBotIdList,
                                 List<Long> failBotIdList);

    void intentLevelRuleSync(RuleSyncVO syncConfig,
                             List<? extends IntentRulePO> list,
                             List<Long> successBotIdList,
                             List<Long> failBotIdList);

    void intentActionRuleSync(RuleActionSyncVO syncConfig,
                              List<? extends IntentRuleActionPO> list,
                              List<Long> successBotIdList,
                              List<Long> failBotIdList);

    void speechConfigSync(BasicSyncVO syncConfig,
                          List<Long> successBotIdList,
                          List<Long> failBotIdList);

    void entitySync(EntitySyncVO syncConfig,
                    List<BaseEntityPO> entityList,
                    List<Long> successBotIdList,
                    List<Long> failBotIdList);
}
