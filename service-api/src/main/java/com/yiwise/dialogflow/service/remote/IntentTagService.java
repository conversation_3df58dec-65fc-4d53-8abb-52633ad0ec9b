package com.yiwise.dialogflow.service.remote;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.yiwise.dialogflow.service.openfeign.RemoteServiceAdapter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/6/1
 * @class <code>CustomerTagService</code>
 * @see
 * @since JDK1.8
 */
@Service
public class IntentTagService {
    @Resource
    RemoteServiceAdapter remoteServiceAdapter;

    private static final Cache<Long, List<Integer>> CACHE = CacheBuilder
            .newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(10, java.util.concurrent.TimeUnit.MINUTES)
            .build();

    public List<Integer> getIntentTagDetailCodeList(Long intentLevelTagId) {
        TypeReference<List<Integer>> typeRef = new TypeReference<List<Integer>>() {
        };
        List<Integer> intentTagList = CACHE.getIfPresent(intentLevelTagId);
        if (CollectionUtils.isEmpty(intentTagList)) {
            intentTagList = remoteServiceAdapter.invoke("intentLevelTagDetailServiceImpl", "getIntentTagDetailCodeByTagId", typeRef, intentLevelTagId);
            if (CollectionUtils.isNotEmpty(intentTagList)) {
                CACHE.put(intentLevelTagId, intentTagList);
            }
        }
        return intentTagList;
    }
}
