package com.yiwise.dialogflow.service.remote;

import com.yiwise.account.api.dto.TenantDetailDTO;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.entity.po.remote.TenantPO;
import com.yiwise.dialogflow.service.remote.account.FeignTenantApi;
import com.yiwise.dialogflow.utils.IpMaskUtils;
import com.yiwise.middleware.redis.service.RedisOpsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/2/11
 */
@Slf4j
@Service
public class TenantService {

    @Resource
    RedisOpsService redisOpsService;

    @Resource
    private FeignTenantApi feignTenantApi;

    public TenantPO selectByKey(Long key) {
        String redisKey = RedisKeyCenter.getTenantPrimaryRedisKey(key);
        if (redisOpsService.isKeyExist(redisKey)) {
            return redisOpsService.get(redisKey, TenantPO.class);
        }
        TenantDetailDTO tenantDetail = feignTenantApi.selectByKey(key);
        return convert(tenantDetail);
    }


    private TenantPO convert(TenantDetailDTO tenantDetail) {
        if (Objects.isNull(tenantDetail)) {
            return null;
        }
        TenantPO tenantPO = MyBeanUtils.copy(tenantDetail, TenantPO.class);
        tenantPO.setName(tenantDetail.getCompanyName());
        return tenantPO;
    }

    public boolean checkTenantIp(Long tenantId, String ip) {
        TenantPO tenantPO = selectByKey(tenantId);
        if (tenantPO != null && tenantPO.getEnableIpLimit() != null && Objects.equals(Boolean.TRUE, tenantPO.getEnableIpLimit())) {
            Set<String> ipRange = tenantPO.getIpRange();
            if (!CollectionUtils.isEmpty(ipRange)) {
                for (String objRange : ipRange)
                    try {
                        if (IpMaskUtils.matchIpMask(ip, objRange)) {
                            return true;
                        }
                    } catch (Exception e) {
                        log.error("判断ip错误");
                    }
            }
            return false;
        }
        return true;
    }
}
