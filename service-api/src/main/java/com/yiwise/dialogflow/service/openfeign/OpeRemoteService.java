package com.yiwise.dialogflow.service.openfeign;

import com.yiwise.cloud.aicc.model.bo.invoke.InvokeBO;
import com.yiwise.cloud.aicc.service.remote.ServiceFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/9/2
 * @class <code>OpeDialogFlowService</code>
 * @see
 * @since JDK1.8
 */
@FeignClient(name = "ai-call-ope-web", configuration = ServiceFeignConfig.class)
public interface OpeRemoteService {

    /**
     * 方法统一调用接口
     *
     * @param method 方法名
     * @param params 参数
     * @return 返回结果
     */
    @PostMapping("/invoke/{service}/{method}")
    String invoke(@PathVariable("service") String service,
                  @PathVariable("method") String method,
                  @RequestBody String params);

    @PostMapping("/invoke/invokeByStr")
    String invoke(@RequestBody InvokeBO invokeBO);
}
