package com.yiwise.dialogflow.service;

import com.yiwise.dialogflow.api.enums.AudioTypeEnum;
import com.yiwise.dialogflow.entity.enums.SyncModeEnum;
import com.yiwise.dialogflow.entity.po.AnswerAudioMappingPO;
import com.yiwise.dialogflow.entity.vo.audio.AnswerAudioMappingVO;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 主要是录音答案和音频存储方案
 * <AUTHOR>
 */
public interface AnswerAudioMappingService {

    List<AnswerAudioMappingPO> getAllByBotId(Long botId, Long recordUserId, AudioTypeEnum audioType);

    List<AnswerAudioMappingPO> listAllAvailableAudio(Long botId, Long recordUserId, AudioTypeEnum audioType, List<String> answerTextList);

    List<AnswerAudioMappingVO> listAllAvailableAudioVO(Long botId, Long recordUserId, AudioTypeEnum audioType);

    AnswerAudioMappingPO upsertAudioMapping(Long botId, Long userId, AudioTypeEnum type, String answerText, String url, Integer volume);

    AnswerAudioMappingPO upsertAudioMapping(Long botId, Long userId, AudioTypeEnum type, String answerText, String url, Integer volume, Integer duration);

    List<AnswerAudioMappingPO> copyAudioMapping(Long botId, AudioTypeEnum type, Long fromUserId, Long toUserId, Set<String> includeAnswerSet);

    Integer copyAllOnCopyBot(Long formBotId, Long fromUserId, Long toBotId, Long toUserId, Set<String> includeAnswerSet, Long operationUserId, SyncModeEnum syncMode);

    /**
     * 删除机器人下所有的合成音频
     * @param botId 机器人id
     */
    void deleteAllComposeAudio(Long botId);

    /**
     * 批量更新数据库中该 url 下的volume值
     * @param botId 机器人id
     * @param url 音频url, oss file key
     * @param volume 音量值
     */
    void adjustVolume(Long botId, String url, Integer volume);

    List<AnswerAudioMappingPO> queryByUrl(Long botId, String url);

    Optional<AnswerAudioMappingPO> getLastMapping(Long botId, AudioTypeEnum audioType);

    /**
     * 删除机器人指定录音师的所有录音
     */
    void deleteAllByRecordUserId(Long botId, Long recordUserId);

    void deleteByAnswerList(Long botId, List<String> answerTextList);

    List<AnswerAudioMappingVO> getVOListByTextList(Long botId, Long recordUserId, AudioTypeEnum audioType, List<String> answerTextList);

    // 维护答案文本, 前后的标点符号
    void maintainText();
}
