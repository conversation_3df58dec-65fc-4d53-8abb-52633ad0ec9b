package com.yiwise.dialogflow.service;

import com.yiwise.dialogflow.engine.share.response.AnswerLocateBO;
import com.yiwise.dialogflow.entity.enums.BotTextSearchTypeEnum;
import com.yiwise.dialogflow.entity.po.BaseAnswerContent;
import com.yiwise.dialogflow.entity.po.DialogBaseNodePO;
import com.yiwise.dialogflow.entity.po.RobotSnapshotPO;
import com.yiwise.dialogflow.entity.query.AnswerQuery;
import com.yiwise.dialogflow.entity.vo.BotAnswerReplaceRequestItem;
import com.yiwise.dialogflow.entity.vo.audio.BaseAnswerContentVO;

import java.util.List;


/**
 * 答案管理, 和AnswerAudioManager区别是不设计的录音的处理
 * 会把一部分之前的 AnswerAudioManager 的逻辑抽离过来
 * <AUTHOR>
 */
public interface AnswerManagerService {

    /**
     * 根据调解搜索答案列表
     * @param condition 查询条件, 如果里面的字段不填, 为不进行该项过滤
     * @return 答案列表
     */
    List<BaseAnswerContentVO> queryByCondition(AnswerQuery condition);

    void updateAnswerOnVariableRename(Long botId, String variableId, String oldVariableName, String newVariableName, Long userId);

    List<BaseAnswerContentVO> getAnswerFromSnapshot(RobotSnapshotPO snapshot);

    BaseAnswerContentVO getAnswerContextByLocation(Long botId, AnswerLocateBO answerLocate);

    void replaceAnswerContent(Long botId, List<AnswerLocateBO> targetAnswerLocateList, String oldValue, String newValue, Long userId);

    void updateAnswer(Long botId, List<? extends BotAnswerReplaceRequestItem> answerList, Long userId);

    void updateAnswerAndBotStatus(Long botId, List<? extends BotAnswerReplaceRequestItem> answerList, Long userId);

    void replaceAnswerVariable(Long botId, List<AnswerLocateBO> targetAnswerLocateList, String oldVariable, String newVariable, Long userI, BotTextSearchTypeEnum searchType);

    BaseAnswerContentVO convertToVO(BaseAnswerContent answerContent, AnswerLocateBO locate);

    BaseAnswerContentVO convertToVO(BaseAnswerContent answerContent, AnswerLocateBO locate, DialogBaseNodePO node);
}
