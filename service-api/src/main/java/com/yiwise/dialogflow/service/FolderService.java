package com.yiwise.dialogflow.service;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.middleware.mysql.service.BasicService;
import com.yiwise.dialogflow.entity.enums.AuditStatusEnum;
import com.yiwise.dialogflow.entity.po.FolderPO;
import com.yiwise.dialogflow.entity.vo.folder.FolderTreeVO;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/9/20
 */
public interface FolderService extends BasicService<FolderPO> {
    Long create(FolderPO folderPO);

    void update(FolderPO folderPO);

    void deleteById(Long folderId);

    FolderTreeVO treeList(Long tenantId, String name, Long parentFolderId, List<Long> createUserIdList, Integer botType);

    List<Long> getBotIdListByFolderId(Long folderId);

    List<Long> getBotIdListByFolderIdList(Collection<Long> folderIdList);

    List<Long> getBotIdListByFolderIdList(Collection<Long> folderIdList, boolean includeSubFolder);

    List<Long> getSubBotIdListByFolderId(Long tenantId, Long folderId, Boolean sameDepthOnly);

    List<Long> subFolderIdList(Long parentFolderId);

    @Transactional
    void move(Long tenantId, Long currentUserId, List<Long> botIdList, List<Long> folderIdList, Long targetFolderId);

    Map<Long, List<IdNamePair<Long, String>>> pathMap(Long tenantId, List<Long> folderIdList);

    Map<AuditStatusEnum, List<IdNamePair<Long, String>>> auditStatusCount(Long tenantId, List<Long> botIdList, List<Long> folderIdList, Set<AuditStatusEnum> auditStatusSet);

    @Transactional
    FolderPO getRootByTenantId(Long tenantId);

    void dataRevise();

    PageResultObject<FolderPO> flatList(Long tenantId, String name, Integer pageNum, Integer pageSize);

    /**
     * 获取所有创建者列表
     * @param tenantId 租户id
     * @return
     */
    List<IdNamePair<Long, String>> getAllCreatorList(Long tenantId);

    /**
     * 查询所有文件夹和机器人创建者列表
     * @param tenantId 租户
     * @return 文件夹和机器人创建者列表
     */
    List<IdNamePair<Long, String>> getAllFolderAndBotCreatorList(Long tenantId);

}
