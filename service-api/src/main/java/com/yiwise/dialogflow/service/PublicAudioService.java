package com.yiwise.dialogflow.service;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.po.BotPO;
import com.yiwise.dialogflow.entity.po.PublicAudioPO;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.audio.AnswerAudioWrapVO;
import com.yiwise.dialogflow.entity.vo.audio.PublicAudioVO;
import com.yiwise.dialogflow.entity.vo.audio.request.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/14
 */
public interface PublicAudioService {

    /**
     * 查询同一分组、同一录音师、“相同文案”的录音
     * 相同文案定义：句首的标点忽略，句尾的标点为中英文逗号/句号时都判断为相同。
     */
    PublicAudioPO selectOne(String groupId, Long recordUserId, String regex);

    /**
     * 分页查询公共音频
     */
    PageResultObject<PublicAudioVO> list(PublicAudioSearchRequestVO request);

    /**
     * 公共音频库中的录音师列表
     */
    List<IdNamePair<Long, String>> recordUserList();

    /**
     * 来源bot列表
     */
    List<IdNamePair<Long, String>> sourceBotList(String search, Integer pageSize);

    /**
     * 更新人列表
     */
    List<IdNamePair<Long, String>> updateUserList();

    /**
     * bot音频同步到公共音频库
     */
    void sync(Long recordUserId, List<AnswerAudioWrapVO> answerAudioWrapVOList, BotPO bot, SyncAudioRequestVO request, Long userId);

    /**
     * 删除公共音频
     */
    void delete(PublicAudioSearchRequestVO request);

    /**
     * 移动分组
     */
    void changeGroup(PublicAudioChangeGroupRequestVO request, Long userId);

    /**
     * 检验选中的音频中是否存在重复
     */
    Boolean duplicateCheck(PublicAudioSearchRequestVO request);

    /**
     * 更新音频
     */
    void update(PublicAudioUpdateRequestVO request, Long userId);

    /**
     * 同步到bot
     */
    void sync(PublicAudioSyncRequestVO request);

    /**
     * 下载音频
     */
    void download(String id, HttpServletResponse response);
}
