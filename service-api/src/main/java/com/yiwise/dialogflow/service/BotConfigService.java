package com.yiwise.dialogflow.service;


import com.yiwise.dialogflow.entity.bo.AsrMetaData;
import com.yiwise.dialogflow.entity.po.BotAudioConfigPO;
import com.yiwise.dialogflow.entity.po.BotConfigPO;
import com.yiwise.dialogflow.entity.po.BotSpeechConfigPO;
import com.yiwise.dialogflow.entity.vo.asrmodel.AsrUpdateVO;
import com.yiwise.dialogflow.entity.vo.audio.BotAudioConfigVO;
import com.yiwise.dialogflow.entity.vo.sync.BasicSyncVO;
import com.yiwise.dialogflow.entity.vo.sync.BotSyncResultVO;

import java.util.List;

public interface BotConfigService {

    void initOnCreateBot(Long botId);

    BotConfigPO getByBotId(Long botId);

    BotAudioConfigPO getAudioConfig(Long botId);

    BotAudioConfigVO getAudioConfigVO(Long botId);

    BotAudioConfigPO saveAudioConfig(Long botId, BotAudioConfigPO botAudioConfig, Long userId);

    BotSpeechConfigPO getSpeechConfig(Long botId);

    void saveSpeechConfig(Long botId, BotSpeechConfigPO botAudioConfig, Long currentUserId);

    AsrMetaData getAsrMetaData(Long botId);

    void updateAsrMetaData(AsrUpdateVO asrUpdateVO);

    List<Long> queryBotIdListByRecordUserId(Long callRecordId);

    List<BotConfigPO> getBotConfigListByBotId(List<Long> botIdList);

    BotSyncResultVO sync(BasicSyncVO syncVO);

    List<BotConfigPO> queryByBotIdList(List<Long> botIdList);

    void batchUpdateRecordUserId();
}
