package com.yiwise.dialogflow.service.botgenerate;

import com.yiwise.dialogflow.entity.po.BotAnswerRewriteTaskPO;
import com.yiwise.dialogflow.entity.po.RobotSnapshotPO;
import com.yiwise.dialogflow.entity.vo.botgenerate.BotRewriteRequestVO;
import org.springframework.transaction.annotation.Transactional;

import javax.swing.text.html.Option;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * bot答案改写服务
 * 调用算法的rewrite接口, 对答案内容进行改写
 */
public interface BotAnswerRewriteTaskService {

    @Transactional(rollbackFor = Exception.class)
    BotAnswerRewriteTaskPO submitRewriteTask(BotRewriteRequestVO request, Long userId);

    /**
     * 查询改写任务信息
     * @param taskId
     * @return
     */
    BotAnswerRewriteTaskPO getById(String taskId);

    /**
     * 取消改写任务
     * @param botId
     * @param userId
     * @return
     */
    List<BotAnswerRewriteTaskPO> cancelRewriteTask(Long botId, Long userId);

    Object requestAnalyzeContext(BotAnswerRewriteTaskPO rewriteTask, RobotSnapshotPO botSnapshot);

    List<String> requestRewrite(Map<String, Object> contextAnalysis, String label, List<String> answerTextList);

    Optional<BotAnswerRewriteTaskPO> getLastTaskByBotId(Long botId);

    /**
     * 获取最后一次改写成功的任务
     * @param botId botId
     * @return 改写任务
     */
    Optional<BotAnswerRewriteTaskPO> getLastApplySuccessTaskByBotId(Long botId);

    Map<String, List<String>> analyzePrompt(Long botId);

    List<BotAnswerRewriteTaskPO.AnswerRewriteDetailGroup> getRealtimeAnswerGroup(Long botId);

    /**
     * 返回有改写成功记录的 botId
     * @param botIdList 待查询的 botId列表
     * @return 有改写成功记录的 botId列表
     */
    Set<Long> getExistSuccessTaskBotIdSet(List<Long> botIdList);

}
