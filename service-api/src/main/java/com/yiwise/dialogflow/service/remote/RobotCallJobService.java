package com.yiwise.dialogflow.service.remote;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.service.openfeign.RemoteServiceAdapter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

@Service
public class RobotCallJobService {

    @Resource
    RemoteServiceAdapter remoteServiceAdapter;


    public List<IdNamePair<Long, String>> selectJobIdNamePairByIdList(Collection<Long> jobIds) {
        TypeReference<List<IdNamePair<Long, String>>> typeRef = new TypeReference<List<IdNamePair<Long, String>>>() {};
        return remoteServiceAdapter.invoke("robotCallJobServiceImpl", "selectJobIdNamePairByIdList", typeRef, jobIds);
    }
}
