package com.yiwise.dialogflow.service;

/**
 * bot 资源修改锁, 通过websocket来实现, ait打开某个bot的标签页时, 前端会自动连上此websocket
 * 1. 第一版, 先向前端推送当前哪些人占有了bot的某个资源的锁
 */
public interface BotResourceModifyLockService {

    /**
     * websocket连接
     */
    void onActive(String sessionId, Long userId);

    /**
     * websocket断开连接
     */
    void onInactive(String sessionId);

    /**
     * websocket心跳
     */
    void onHeartbeat(String sessionId);

    /**
     * 会话订阅bot
     */
    void onSubscribe(String sessionId, Long botId);

}
