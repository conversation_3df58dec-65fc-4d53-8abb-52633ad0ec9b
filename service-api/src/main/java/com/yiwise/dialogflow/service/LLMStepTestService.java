package com.yiwise.dialogflow.service;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.entity.vo.LLMStepTestRequestVO;
import com.yiwise.dialogflow.entity.vo.LLMStepTestResponseVO;
import reactor.core.publisher.Flux;

/**
 * 大模型对话流程测试服务
 * 在大模型编辑处, 可以对大模型的提示词进行测试
 */
public interface LLMStepTestService {

    Flux<ResultObject<LLMStepTestResponseVO>> test(LLMStepTestRequestVO request);

}
