package com.yiwise.dialogflow.service.intent;

import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.bo.algorithm.ResourceId2NameBO;
import com.yiwise.dialogflow.entity.context.RobotResourceContext;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.po.intent.IntentPO;
import com.yiwise.dialogflow.entity.po.intent.IntentRuleConditionPO;
import com.yiwise.dialogflow.entity.po.intent.IntentRulePO;
import com.yiwise.dialogflow.entity.query.IntentRuleQueryVO;
import com.yiwise.dialogflow.entity.vo.IntentRuleVO;
import com.yiwise.dialogflow.entity.vo.sync.BotSyncResultVO;
import com.yiwise.dialogflow.entity.vo.sync.RuleSyncVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2022/3/30
 * @class <code>IntentRuleService</code>
 * @see
 * @since JDK1.8
 */
public interface IntentRuleService {
    /**
     * 添加意向规则
     *
     * @param intentRulePO 添加属性值
     * @return
     */
    IntentRulePO addIntentRule(IntentRulePO intentRulePO, boolean isCheck);

    /**
     * 保存意向规则
     *
     * @param botId
     * @param intentRuleList
     * @param isCopy
     * @return
     */
    List<IntentRulePO> saveIntentRule(Long botId, List<IntentRulePO> intentRuleList, boolean isCopy, boolean isCheck);

    /**
     * 更新意向规则
     *
     * @param intentRulePO 更新值
     * @return
     */
    IntentRulePO updateIntentRule(IntentRulePO intentRulePO);

    /**
     * 获取意向规则列表
     *
     * @param botId 话术id
     * @return
     */
    List<IntentRulePO> getIntentRuleList(Long botId);

    /**
     * 获取意向规则列表(不包含内置规则)
     *
     * @param botId
     * @return
     */
    List<IntentRulePO> getIntentRuleListWithOutInit(Long botId);

    /**
     * 通过id获取意向规则
     *
     * @param id 规则id
     * @return
     */
    IntentRulePO getDialogFlowIntentRule(String id);

    /**
     * 批量删除规则
     *
     * @param botId  话术id
     * @param idList 删除id列表
     */
    void deleteIntentRuleByIdList(Long botId, List<String> idList, Long userId);

    /**
     * 重排序
     *
     * @param idList 排序后id列表
     * @param botId  话术id
     */
    void rearrangeMatchOrder(List<String> idList, Long botId, Long userId);

    /**
     * 复制意向规则
     *
     * @param context
     */
    void copyDialogFlowIntentRuleWithKnowledgeAndStep(RobotResourceContext context);

    /**
     * 初始化意向规则
     *
     * @param botId 话术id
     */
    List<IntentRulePO> initDialogFlowIntentRule(Long botId, List<Integer> intentTagDetailCodeList, boolean isCopy);

    List<IntentRulePO> getByIdList(Long botId, List<String> idList);

    IntentRuleVO getDialogFlowIntentRuleDetail(String id);

    List<IntentRuleVO> getIntentRuleListDetail(IntentRuleQueryVO condition);

    List<IntentRuleVO> getIntentRuleListDetailWithOutInit(IntentRuleQueryVO condition);

    void handleConditionList(List<IntentRuleConditionPO> conditionList, RobotResourceContext context);

    void checkIntentRuleParam(IntentRulePO intentRulePO);

    void resetBuildInRuleConditionList(IntentRulePO intentRulePO);

    void addKnowledgeName(IntentRulePO intentRulePO, Map<String, String> knowledgeMap);

    ResourceId2NameBO getResourceNameMap(Long botId);

    ResourceId2NameBO getResourceNameMap(List<StepPO> stepList,
                                         List<DialogBaseNodePO> stepNodeList,
                                         List<SpecialAnswerConfigPO> specialAnswerConfigList,
                                         List<KnowledgePO> knowledgeList,
                                         Map<Integer, String> intentLevelDetailCode2NameMap,
                                         List<BaseEntityPO> entityList,
                                         List<IntentPO> intentList,
                                         List<VariablePO> variableList,
                                         List<LlmLabelPO> llmLabelList);

    void fixData();

    void clearInvalidNodeId(Set<String> oldNodeIdSet, Long botId);

    BotSyncResultVO sync(RuleSyncVO syncVO);

    void compareKnowledgeAndNode(IntentRuleVO sourceIntentRuleVO, Long targetBotId, Map<String, String> sourceStepIdLabelMap, Map<String, Map<String, String>> sourceStepIdNodeIdLabelMap);

    void filterConditionInvalidResource(List< ? extends IntentRulePO> ruleList, DependentResourceBO dependResource);

    List<IntentRulePO> findLlmLabelRule(Long botId, String llmLabelId);
}
