package com.yiwise.dialogflow.service;

import com.yiwise.dialogflow.entity.bo.AudioPropertiesBO;
import com.yiwise.dialogflow.entity.po.AudioPropertiesPO;

import java.io.File;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface AudioPropertiesService {

    AudioPropertiesPO getByUrl(String url);

    void upsert(String url, Integer volume, Integer duration);

    AudioPropertiesBO calculateAudioProperties(File waveFile);

    List<Short> generateWaveform(File waveFile);

    int calculateVolume(File file);
}
