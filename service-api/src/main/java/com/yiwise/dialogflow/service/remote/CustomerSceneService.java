package com.yiwise.dialogflow.service.remote;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.entity.po.remote.CustomerScenePO;
import com.yiwise.dialogflow.service.openfeign.RemoteServiceAdapter;
import com.yiwise.middleware.redis.service.RedisOpsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2023/4/3
 * @class <code>CustomerSceneService</code>
 * @see
 * @since JDK1.8
 */
@Service
public class CustomerSceneService {
    @Resource
    RemoteServiceAdapter remoteServiceAdapter;

    @Resource
    RedisOpsService redisOpsService;

    public String getCustomerSceneName(Integer customerSceneId) {
        if (Objects.isNull(customerSceneId)) {
            return "";
        }
        String redisKey = RedisKeyCenter.getCustomerSceneNameRedisKey(customerSceneId);
        String customerSceneName = redisOpsService.get(redisKey);
        if (StringUtils.isEmpty(customerSceneName)) {
            customerSceneName = remoteServiceAdapter.invoke("customerSceneServiceImpl", "getCustomerSceneName", String.class, customerSceneId);
            if (StringUtils.isNotEmpty(customerSceneName)) {
                redisOpsService.set(redisKey, customerSceneId, ApplicationConstant.POJO_CACHE_TIMEOUT_OF_SECOND, TimeUnit.SECONDS);
            }
        }
        return customerSceneName;
    }

    public List<CustomerScenePO> getCustomerSceneList(Integer customerTrackType, Boolean isAll) {
        TypeReference<List<CustomerScenePO>> typeRef = new TypeReference<List<CustomerScenePO>>() {
        };
        return remoteServiceAdapter.invoke("customerSceneServiceImpl", "getSceneList", typeRef, customerTrackType, isAll);
    }
}
