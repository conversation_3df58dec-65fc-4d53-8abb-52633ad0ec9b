package com.yiwise.dialogflow.service;

import com.yiwise.dialogflow.api.dto.response.step.SimpleStep;
import com.yiwise.dialogflow.entity.bo.DependentResourceBO;
import com.yiwise.dialogflow.entity.enums.StepTypeEnum;
import com.yiwise.dialogflow.entity.po.StepPO;
import com.yiwise.dialogflow.entity.query.StepQueryVO;
import com.yiwise.dialogflow.entity.vo.IdNamePair;
import com.yiwise.dialogflow.entity.vo.StepVO;
import com.yiwise.dialogflow.entity.vo.stats.SimpleStepStatsInfoVO;
import com.yiwise.dialogflow.entity.vo.step.SimpleStepInfoVO;
import com.yiwise.dialogflow.entity.vo.sync.StepSyncResultVO;
import com.yiwise.dialogflow.entity.vo.sync.StepSyncVO;

import java.util.*;

/**
 * <AUTHOR>
 */
public interface StepService {

    void initOnCreateBot(Long botId);

    /**
     * 创建流程
     * @param form 表单
     * @return 创建后数据
     */
    StepVO create(StepPO form, Long userId);

    /**
     * 更新流程
     * @param form
     * @return
     */
    StepVO update(StepPO form, Long userId);

    void validAndThrow(StepPO step);

    void validAndThrow(StepPO step, DependentResourceBO dependentResource);

    void validAndThrow(StepPO step, DependentResourceBO dependentResource, boolean isSync);

    /**
     * 根据id删除流程
     * @param botId
     * @param id
     * @return
     */
    StepPO deleteById(Long botId, String id, Long userId);

    StepVO getById(Long botId, String id);

    StepPO getPOById(Long botId, String stepId);

    /**
     *
     * @param botId
     * @return
     */
    List<StepPO> getAllListByBotId(Long botId);

    List<StepVO> queryByCondition(StepQueryVO condition);

    List<StepPO> getMainListByBotId(Long botId);

    List<StepPO> getIndependentListByBotId(Long botId);

    /**
     */
    List<StepVO> mainStepSort(Long botId, List<String> stepIdList);

    StepPO copyStepInBot(Long botId, String sourceStepId, String targetName, StepTypeEnum targetStepType, Long userId);

    Optional<StepPO> getRootStep(List<StepPO> stepList);

    Optional<StepPO> getLastMainStep(List<StepPO> stepList);

    Map<String, String> getNameByIdList(Collection<String> stepIdList);

    String getNameById(String stepId);

    List<IdNamePair<String, String>> getIdNamePairByBotId(Long botId);

    void updateStepLabel(Long botId, String id, String label);

    List<SimpleStepInfoVO> getSimpleStepInfo(Long botId, StepTypeEnum type);

    /**
     * 获取所有step
     *
     * @return
     */
    List<StepPO> getAllStepList();

    List<SimpleStepStatsInfoVO> getAllStepStatsInfo(StepQueryVO condition);

    StepSyncResultVO sync(StepSyncVO syncVO);

    void resetAllStepLabel(Long newBotId);

    void resetResourceReferenceInfo(Long newBotId);

    List<SimpleStep> getSimpleStepListByDialogFlowId(Long dialogFlowId);

    /**
     * 根据流程 id 批量删除流程, 会出现跨 bot 情况, 所以待删除的流程botId 一定在 botIdList 范围中
     * 删除时按 bot 分组分批删除, 删除过程中出现异常不会回滚, 并不会继续删除后续的 bot
     * @param botIdList botId 列表
     * @param stepIdList stepId 列表
     * @param userId 操作人
     * @return 删除的流程列表
     */
    List<StepPO> deleteByBotIdsAndStepIds(List<Long> botIdList, List<String> stepIdList, Long userId);
}
