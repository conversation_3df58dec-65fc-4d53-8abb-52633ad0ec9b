package com.yiwise.dialogflow.service.stats;

import com.yiwise.dialogflow.entity.bo.stats.BotStatsAnalysisResult;
import com.yiwise.dialogflow.entity.po.stats.StepNodeJumpStatsPO;
import com.yiwise.dialogflow.entity.query.BaseStatsQuery;
import com.yiwise.dialogflow.entity.vo.stats.IntentTriggerStatsVO;
import com.yiwise.dialogflow.entity.vo.stats.NodeJumpStatsVO;

import java.util.List;

/**
 * 流程节点统计
 */
public interface StepNodeStatsService {
    List<StepNodeJumpStatsPO> queryNodesAllJumpStats(Long botId, String stepId, List<String> nodeIdList, BaseStatsQuery condition);

    List<StepNodeJumpStatsPO> queryNodeJumpStats(Long botId, String stepId, String nodeId, BaseStatsQuery condition);
    void saveNodeJumpStats(BotStatsAnalysisResult analysisResult);
}
