package com.yiwise.dialogflow.service.analyze;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.po.analyze.AnalyzeTemplatePO;
import com.yiwise.dialogflow.entity.vo.analyze.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AnalyzeTemplateService {

    /**
     * 创建任务模板
     *
     * @param templateId 模板id
     * @param taskId 任务id
     * @return 模板实体
     */
    AnalyzeTemplatePO createTaskTemplate(String templateId, Long taskId);

    /**
     * 创建模板
     *
     * @param request form
     * @param userId 操作人id
     */
    void create(AnalyzeTemplateCreateVO request, Long userId);

    /**
     * 编辑模板
     *
     * @param request form
     * @param userId  操作人id
     */
    void update(AnalyzeTemplateUpdateVO request, Long userId);

    /**
     * 根据id查询模板
     *
     * @param id id
     * @return 模板实体
     */
    AnalyzeTemplatePO getById(String id);

    /**
     * 根据id列表查询模板
     *
     * @param idList id列表
     * @return 模板列表
     */
    List<AnalyzeTemplatePO> listByIds(List<String> idList);

    /**
     * 列表查询
     *
     * @param request form
     * @return 模板列表
     */
    PageResultObject<AnalyzeTemplateVO> list(AnalyzeTemplateQueryVO request);

    /**
     * 删除模板
     *
     * @param request form
     */
    void delete(AnalyzeTemplateDeleteVO request);

    /**
     * 根据id直接删除模板
     *
     * @param templateId 模板id
     */
    void deleteById(String templateId);

    /**
     * 导入模板
     *
     * @param name 模板名称
     * @param json 模板文件
     */
    void importTemplate(String name, MultipartFile json);

    /**
     * 预览分析模板json
     *
     * @param templateId 模板id
     * @return json
     */
    String preview(String templateId);

    /**
     * 模板复制
     *
     * @param request form
     * @param userId  操作人id
     */
    void copy(AnalyzeTemplateCopyVO request, Long userId);

    /**
     * 导出语料
     *
     * @param templateId 模板id
     * @return oss地址
     */
    String exportCorpus(String templateId);

    /**
     * 生成模板
     *
     * @param request form
     * @param userId  操作人id
     */
    void generateTemplate(AnalyzeTemplateCopyVO request, Long userId);

    /**
     * 修复历史数据
     */
    void fixData();
}
