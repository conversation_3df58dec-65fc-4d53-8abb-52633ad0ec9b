package com.yiwise.dialogflow.service.train;

import com.yiwise.dialogflow.entity.enums.AlgorithmTrainTypeEnum;
import com.yiwise.dialogflow.entity.vo.NameDescVO;
import com.yiwise.dialogflow.entity.vo.TrainDataVO;

import java.util.List;

/**
 * 问答知识和意图训练数据同步
 * <AUTHOR>
 * @date 2019/6/19 11:39 AM
 **/
public interface TrainSyncService {

    List<TrainDataVO> redisKeyList();

    TrainDataVO queryTrainData(Long tenantId, String refId, AlgorithmTrainTypeEnum trainType);

    void postTrainSignal(TrainDataVO trainSignalVO);

    List<NameDescVO> domainList();

}
