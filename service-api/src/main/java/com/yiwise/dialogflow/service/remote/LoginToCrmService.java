package com.yiwise.dialogflow.service.remote;

import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.dialogflow.common.RedisKeyCenter;
import com.yiwise.dialogflow.entity.po.remote.UserPO;
import com.yiwise.middleware.redis.service.RedisOpsService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/1/17
 */
@Service
public class LoginToCrmService {

    @Resource
    RedisOpsService redisOpsService;

    public UserPO getAiccUserForOpeLogin(Long opeUserId) {
        String key = RedisKeyCenter.getOpeUserLoginAiccRedisKey(opeUserId);
        RedisTemplate redisTemplate = redisOpsService.getRedisTemplate();
        Object value = redisTemplate.boundValueOps(key).get();
        UserPO aiccUser = null;
        if (Objects.nonNull(value)) {
            aiccUser = JsonUtils.string2Object(value.toString(), UserPO.class);
        }
        return aiccUser;
    }

}
