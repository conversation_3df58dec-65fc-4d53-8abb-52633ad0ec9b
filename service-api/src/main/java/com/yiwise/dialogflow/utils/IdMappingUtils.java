package com.yiwise.dialogflow.utils;

import com.yiwise.dialogflow.engine.share.DialogQueryNodeHttpParamInfo;
import com.yiwise.dialogflow.engine.share.enums.QueryNodeHttpVarTypeEnum;
import com.yiwise.dialogflow.entity.po.DialogQueryNodePO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
public class IdMappingUtils {

    public static void mappingQueryNodeHttpInfo(DialogQueryNodePO queryNode, Map<String, String> varIdMapping) {
        List<DialogQueryNodeHttpParamInfo> paramInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(queryNode.getQueryList())) {
            paramInfoList.addAll(queryNode.getQueryList());
        }
        if (CollectionUtils.isNotEmpty(queryNode.getHeaderList())) {
            paramInfoList.addAll(queryNode.getHeaderList());
        }

        // 替换query header中的变量id
        for (DialogQueryNodeHttpParamInfo paramInfo : paramInfoList) {
            if (QueryNodeHttpVarTypeEnum.isVariable(paramInfo.getVariableType())) {
                paramInfo.setValue(varIdMapping.get(paramInfo.getValue()));
            }
        }

        // 替换查询结果中的变量id
        if (MapUtils.isNotEmpty(queryNode.getResMap())) {
            Map<String, String> newResMap = new HashMap<>();
            for (Map.Entry<String, String> entry : queryNode.getResMap().entrySet()) {
                newResMap.put(varIdMapping.get(entry.getKey()), entry.getValue());
            }
            queryNode.setResMap(newResMap);
        }

        // 替换记录失败和超时结果的变量id
        if (StringUtils.isNotBlank(queryNode.getErrorVarId())) {
            queryNode.setErrorVarId(varIdMapping.get(queryNode.getErrorVarId()));
        }
    }
}
