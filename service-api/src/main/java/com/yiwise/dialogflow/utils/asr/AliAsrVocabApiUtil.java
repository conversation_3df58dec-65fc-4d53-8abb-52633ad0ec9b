package com.yiwise.dialogflow.utils.asr;

/**
 * <AUTHOR>
 * @date 2022/11/1 16:14:57
 */

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.yiwise.base.common.utils.PropertyLoaderUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AliAsrVocabApiUtil {

    private static final Logger logger = LoggerFactory.getLogger(AliAsrVocabApiUtil.class);

    /**
     * 地域信息
     * 常量内容，固定值。
     */
    private static final String REGION_ID = "cn-shanghai";
    private static final String DOMAIN = "nls-slp.cn-shanghai.aliyuncs.com";
    private static final ProtocolType PROTOCOL_TYPE = ProtocolType.HTTPS;
    /**
     * 密钥信息
     */
    private static final String akId = PropertyLoaderUtils.getProperty("aliyun.nls.accessKeyId");
    private static final String akSecret = PropertyLoaderUtils.getProperty("aliyun.nls.accessKeySecret");
    /**
     * POP API信息
     * 常量内容，固定值。
     */
    private static final String API_VERSION = "2018-11-20";
    private static final String ACTION_CREATE_ASR = "CreateAsrVocab";
    private static final String ACTION_GET_ASR_VOCAB = "GetAsrVocab";
    private static final String ACTION_LIST_ASR_VOCAB = "ListAsrVocab";
    private static final String ACTION_UPDATE_ASR_VOCAB = "UpdateAsrVocab";
    private static final String ACTION_DELETE_ASR_VOCAB = "DeleteAsrVocab";
    /**
     * 参数设置key
     * 常量内容，固定值。
     */
    private static final String KEY_VOCAB_ID = "VocabId";
    private static final String KEY_ID = "Id";
    private static final String KEY_NAME = "Name";
    private static final String KEY_DESCRIPTION = "Description";
    private static final String KEY_WORD_WEIGHTS = "WordWeights";
    private static final String KEY_VOCAB = "Vocab";
    private static final String KEY_PAGE = "Page";
    private static final String KEY_PAGE_NUMBER = "PageNumber";
    private static final String KEY_PAGE_SIZE = "PageSize";
    // 阿里云鉴权client
    private static IAcsClient client;

    static {
        DefaultProfile profile = DefaultProfile.getProfile(REGION_ID, akId, akSecret);
        client = new DefaultAcsClient(profile);
    }

    static class Vocab {
        public String Id;
        public String Name;
        public String Description;
        public int Size;
        public String Md5;
        public String CreateTime;
        public String UpdateTime;
        public Map<String, Integer> WordWeights = new HashMap<String, Integer>();
    }

    static class Page {
        class VocabContent {
            public String Id;
            public String Name;
            public String Description;
            public int Size;
            public String Md5;
            public String CreateTime;
            public String UpdateTime;
        }

        public int PageNumber;
        public int PageSize;
        public int TotalItems;
        public int TotalPages;
        public List<VocabContent> Content = new ArrayList<VocabContent>();
    }

    private static CommonRequest newRequest(String action) {
        CommonRequest request = new CommonRequest();
        request.setDomain(DOMAIN);
        request.setProtocol(PROTOCOL_TYPE);
        request.setVersion(API_VERSION);
        request.setMethod(MethodType.POST);
        request.setAction(action);
        return request;
    }

    /**
     * 创建词表
     *
     * @param name        词表名称，必填。
     * @param description 词表描述信息，可选。
     * @return String 创建的词表Id。
     */
    public static String createAsrVocab(String name, String description, List<String> content) {
        CommonRequest request = newRequest(ACTION_CREATE_ASR);
        request.putBodyParameter(KEY_NAME, name);
        request.putBodyParameter(KEY_DESCRIPTION, description);
        request.putBodyParameter(KEY_WORD_WEIGHTS, listToWordWeightStr(content));
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            logger.error("【阿里】创建词表:{}报错：{}", name, e);
        }
        if (response.getHttpStatus() != 200) {
            logger.error("【阿里】创建词表:{}失败,返回结果：{}", name, response.getData());
            return null;
        }
        JSONObject result = JSONObject.parseObject(response.getData());
        String vocabId = result.getString(KEY_VOCAB_ID);
        logger.debug("【阿里】创建词表{}成功,阿里侧词表id：{}", name, vocabId);
        return vocabId;
    }

    private static String listToWordWeightStr(List<String> content) {
        Map<String, Integer> param = new HashMap<>();
        content.forEach(str -> {
            String[] split = str.split("：");
            param.put(split[0], Integer.valueOf(split[1]));
        });
        return JsonUtils.object2String(param);
    }

    /**
     * 获取词表
     *
     * @param vocabId 词表Id。
     * @return Vocab 获取的词表对象。
     */
    public static Vocab getAsrVocab(String vocabId) {
        CommonRequest request = newRequest(ACTION_GET_ASR_VOCAB);
        request.putBodyParameter(KEY_ID, vocabId);
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            e.printStackTrace();
        }
        if (response.getHttpStatus() != 200) {
            logger.error("【阿里】获取词表失败,返回信息：{}", response.getData());
            return null;
        }
        JSONObject result = JSONObject.parseObject(response.getData());
        String vocabJson = result.getString(KEY_VOCAB);
        Vocab vocab = JSONObject.parseObject(vocabJson, Vocab.class);
        logger.debug("【阿里】获取词表成功,vocabId:{}", vocabId);
        return vocab;
    }

    /**
     * 更新词表
     *
     * @param vocabId     待更新的词表Id。
     * @param name        更新后的词表名称。
     * @param description 更新后的词表描述。
     * @return boolean 更新词表是否成功。
     */
    public static boolean updateAsrVocab(String vocabId, String name, String description, List<String> content) {
        CommonRequest request = newRequest(ACTION_UPDATE_ASR_VOCAB);
        request.putBodyParameter(KEY_ID, vocabId);
        request.putBodyParameter(KEY_NAME, name);
        request.putBodyParameter(KEY_DESCRIPTION, description);
        request.putBodyParameter(KEY_WORD_WEIGHTS, listToWordWeightStr(content));
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            logger.error("【阿里】更新词表:{}出错：{}", name, e);
        }
        if (response.getHttpStatus() != 200) {
            logger.error("【阿里】更新词表:{}失败,返回信息：{}", name, response.getData());
            return false;
        }
        logger.debug("【阿里】更新词表：{}成功", name);
        return true;
    }

    /**
     * 删除词表
     *
     * @param vocabId 词表Id。
     * @return boolean 删除词表是否成功。
     */
    public static boolean deleteAsrVocab(String vocabId) {
        CommonRequest request = newRequest(ACTION_DELETE_ASR_VOCAB);
        request.putBodyParameter(KEY_ID, vocabId);
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            logger.error("【阿里】删除词表出错：{}", e);
        }
        if (response.getHttpStatus() != 200) {
            logger.error("【阿里】删除词表失败,返回消息：{}", response.getData());
            return false;
        }
        logger.debug("【阿里】删除词表成功，vocabId:{}", vocabId);
        return true;
    }

    /**
     * 列举词表
     * 如果不指定获取的页号，默认获取第1页。
     * 如果不指定每页的词表数量，默认每页10个词表。
     *
     * @return Page 所有词表信息。
     */
    public static Page listAsrVocab() {
        CommonRequest request = newRequest(ACTION_LIST_ASR_VOCAB);
        request.putBodyParameter(KEY_PAGE_NUMBER, 1);
        request.putBodyParameter(KEY_PAGE_SIZE, 10);
        CommonResponse response = null;
        try {
            response = client.getCommonResponse(request);
        } catch (ClientException e) {
            logger.error("【阿里】列举词表出错：{}", e);
            e.printStackTrace();
        }
        if (response.getHttpStatus() != 200) {
            logger.error("【阿里】列举词表失败,返回消息：{}", response.getData());
            return null;
        }
        JSONObject result = JSONObject.parseObject(response.getData());
        String pageJson = result.getString(KEY_PAGE);
        Page page = JSONObject.parseObject(pageJson, Page.class);
        return page;
    }
}