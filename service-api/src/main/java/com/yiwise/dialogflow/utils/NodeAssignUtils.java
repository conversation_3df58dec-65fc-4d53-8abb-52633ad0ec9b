package com.yiwise.dialogflow.utils;

import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.dialogflow.entity.enums.VariableAssignTypeEnum;
import com.yiwise.dialogflow.entity.po.*;
import com.yiwise.dialogflow.entity.query.BotQuery;
import com.yiwise.dialogflow.entity.vo.BotVO;
import com.yiwise.dialogflow.service.BotService;
import com.yiwise.dialogflow.service.RobotSnapshotService;
import com.yiwise.dialogflow.service.StepNodeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
public class NodeAssignUtils {


    private static BotService botService = AppContextUtils.getBean(BotService.class);

    private static StepNodeService stepNodeService = AppContextUtils.getBean(StepNodeService.class);

    private static RobotSnapshotService snapshotService = AppContextUtils.getBean(RobotSnapshotService.class);

    private static MongoTemplate mongoTemplate = AppContextUtils.getBean(MongoTemplate.class);


    public static void processAllBot() {
        BotQuery botQuery = new BotQuery();
        botQuery.setPageNum(1);
        botQuery.setPageSize(20);
        botQuery.setWithPage(true);
        PageResultObject<BotVO> botList = botService.queryListWithoutWrapVO(botQuery);
        while (CollectionUtils.isNotEmpty(botList.getContent())) {
            log.debug("pageNum:{}, pageSize:{}, pages:{}", botList.getNumber(), botList.getPageSize(), botList.getPages());
            for (BotVO bot : botList.getContent()) {
                try {
                    processByBotId(bot.getBotId());
                } catch (Exception e) {
                    log.warn("[LogHub_Warn] 处理失败, botId:{}", bot.getBotId(), e);
                }
            }
            botQuery.setPageNum(botQuery.getPageNum() + 1);
            botList = botService.queryListWithoutWrapVO(botQuery);
        }
    }

    public static void processByBotId(Long botId) {
        // 处理节点中的数据
        // 处理快照中的数据
        log.debug("开始处理botId:{}", botId);
        List<DialogBaseNodePO> nodeList = stepNodeService.getAllListByBotId(botId);
        if (CollectionUtils.isEmpty(nodeList)) {
            return;
        }

        List<DialogBaseNodePO> updateList = new ArrayList<>();
        for (DialogBaseNodePO node : nodeList) {
            boolean updated = processNode(node);
            if (updated) {
                updateList.add(node);
            }
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            for (DialogBaseNodePO node : updateList) {
                mongoTemplate.save(node, DialogBaseNodePO.COLLECTION_NAME);
            }
        }

        // 加载快照
        processSnapshot(botId, RobotSnapshotPO.TEXT_TRAIN_COLLECTION_NAME);
        processSnapshot(botId, RobotSnapshotPO.SPEECH_TRAIN_COLLECTION_NAME);
        processSnapshot(botId, RobotSnapshotPO.COLLECTION_NAME);
    }

    private static void processSnapshot(Long botId, String collectionName) {
        Query query = new Query();
        query.addCriteria(Criteria.where("botId").is(botId));
        query.with(Sort.by(Sort.Order.desc("_id")));
        query.limit(5);

        List<RobotSnapshotPO> snapshotList = mongoTemplate.find(query, RobotSnapshotPO.class, collectionName);
        if (CollectionUtils.isNotEmpty(snapshotList)) {
            for (RobotSnapshotPO snapshot : snapshotList) {
                boolean updated = false;
                if (CollectionUtils.isNotEmpty(snapshot.getNodeList())) {
                    for (DialogBaseNodePO node : snapshot.getNodeList()) {
                        if (processNode(node)) {
                            updated = true;
                        }
                    }
                }
                if (updated) {
                    mongoTemplate.save(snapshot, collectionName);
                }
            }
        }
    }

    private static boolean processNode(DialogBaseNodePO node) {
        boolean updated = false;
        if (Objects.nonNull(node.getAssignConfig())) {
            VariableAssignConfigPO assignConfig = node.getAssignConfig();
            if (VariableAssignTypeEnum.CONSTANT.equals(assignConfig.getAssignType())) {
                ConstantAssignConfigPO constantAssignConfig = new ConstantAssignConfigPO();
                constantAssignConfig.setAssignActionList(assignConfig.getAssignActionList());
                node.setConstantAssign(constantAssignConfig);
                updated = true;
            } else if (VariableAssignTypeEnum.ENTITY_COLLECT.equals(assignConfig.getAssignType())) {
                EntityAssignConfigPO entityAssign = new EntityAssignConfigPO();
                entityAssign.setEnableCollectOnPullback(assignConfig.getEnableCollectOnPullback());
                entityAssign.setAssignActionList(assignConfig.getAssignActionList());
                node.setEntityAssign(entityAssign);
                updated = true;
            } else if (VariableAssignTypeEnum.ORIGIN_INPUT.equals(assignConfig.getAssignType())) {
                OriginInputAssignConfigPO originInputAssignConfig = new OriginInputAssignConfigPO();
                OriginInputAssignConfigItem item = new OriginInputAssignConfigItem();
                item.setVariableId(assignConfig.getVariableId());
                item.setOriginInputCollectType(assignConfig.getOriginInputCollectType());
                item.setFilteredIntentIdList(assignConfig.getFilteredIntentIdList());
                item.setFilteredRegexList(assignConfig.getFilteredRegexList());
                originInputAssignConfig.setAssignActionList(Collections.singletonList(item));
                node.setOriginInputAssign(originInputAssignConfig);
                updated = true;
            }
        }
        return updated;
    }


}
