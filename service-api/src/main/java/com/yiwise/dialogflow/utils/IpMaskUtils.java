package com.yiwise.dialogflow.utils;

import org.apache.commons.lang3.StringUtils;
import sun.net.util.IPAddressUtil;

/**
 * Created by 昌夜 on 2021-08-09.
 */
public class IpMaskUtils {
    public static boolean matchIpMask(String ip, String maskIp) {
        byte[] ipBytes = IPAddressUtil.textToNumericFormatV4(ip);
        int bitNumber = 32;
        String extractedMaskIp = maskIp;
        if (StringUtils.contains(maskIp, "/")) {
            int idx = StringUtils.indexOf(maskIp, "/");
            bitNumber = Integer.parseInt(StringUtils.substring(maskIp, idx + 1));
            extractedMaskIp = StringUtils.substring(maskIp,0, idx);
        }
        byte[] extractedMaskIpBytes = IPAddressUtil.textToNumericFormatV4(extractedMaskIp);

        for (int i=0; i<bitNumber; ++i) {
            int x1 = getBitValue(ipBytes, i);
            int x2 = getBitValue(extractedMaskIpBytes, i);
            if (x1 != x2) {
                return false;
            }
        }
        return true;
    }

    public static int getBitValue(byte[] arr, int bitNumber) {
        int idx1 = bitNumber / 8;
        int idx2 = bitNumber % 8;
        int x1 = arr[idx1] & (1 << (7 - idx2));
        return x1 >> (7 - idx2);
    }
}
