package com.yiwise.dialogflow.utils.asr;

/**
 * <AUTHOR>
 * @date 2022/11/2 11:14:55
 */

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import com.yiwise.base.common.utils.PropertyLoaderUtils;
import com.yiwise.dialogflow.entity.bo.asr.AsrLmData;
import com.yiwise.dialogflow.entity.bo.asr.AsrLmModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AliSelfLearningApiUtil {
    private static final Logger logger = LoggerFactory.getLogger(AliSelfLearningApiUtil.class);

    public static final String BASE_ID = "customer_service_8k";
    private static String REGION = "cn-shanghai";
    private static final String STATUS_FETCHING = "Fetching";
    private static final String STATUS_FETCHINGFAILED = "FetchingFailed";
    private static final String STATUS_READY = "Ready";
    private static final String STATUS_EMPTY = "Empty";
    private static final String STATUS_TRAINING = "Training";
    private static final String STATUS_TRAININGFAILED = "TrainingFailed";
    private static final String STATUS_DEPLOYING = "Deploying";
    private static final String STATUS_DEPLOYED = "Deployed";
    /**
     * 密钥信息
     */
    private static final String akId = PropertyLoaderUtils.getProperty("aliyun.nls.accessKeyId");
    private static final String akSecret = PropertyLoaderUtils.getProperty("aliyun.nls.accessKeySecret");

    private static IAcsClient client;
    private static AsrLmData asrLmData;
    private static AsrLmModel asrLmModel;

    static {
        DefaultProfile profile = DefaultProfile.getProfile(REGION, akId, akSecret);
        client = new DefaultAcsClient(profile);
        asrLmData = new AsrLmData(client);
        asrLmModel = new AsrLmModel(client);
    }

    /******************************* 数据集管理 *******************************/
    // 创建数据集
    public static String createAsrLmData(String name, String fileUrl, String description) {
        String dataId = asrLmData.createAsrLmData(name, fileUrl, description);
        if (null == dataId) {
            return dataId;
        }
        // 轮询数据集，检查状态是否为Ready，即数据集导入成功。
        while (true) {
            AsrLmData.LmData data = asrLmData.getAsrLmData(dataId);
            if (null == data) {
                dataId = null;
                break;
            }
            if (data.Status.equals(STATUS_FETCHING)) {
                logger.debug("【阿里】正在将数据集导入到自学习服务中，dataId:{}", dataId);
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            } else if (data.Status.equals(STATUS_FETCHINGFAILED)) {
                logger.error("【阿里】复制数据集出现错误,dataId：{}", dataId);
                asrLmData.deleteAsrLmData(dataId);
                dataId = null;
                break;
            } else if (data.Status.equals(STATUS_READY)) {
                logger.debug("【阿里】数据集导入成功，dataId: {}", dataId);
                break;
            }
        }
        return dataId;
    }

    // 查询数据集
    public static AsrLmData.LmData getAsrLmData(String dataId) {
        return asrLmData.getAsrLmData(dataId);
    }

    // 删除数据集
    public static boolean deleteAsrLmData(String dataId) {
        AsrLmData.LmData data = asrLmData.getAsrLmData(dataId);
        if (null == data) {
            return false;
        }
        if (!data.Status.equals(STATUS_READY)) {
            logger.error("【阿里】数据集状态不允许进行删除操作，status: {},dataId: {}", data.Status, dataId);
            return false;
        }
        return asrLmData.deleteAsrLmData(dataId);
    }

    // 列举数据集
    public static AsrLmData.LmDataPage listAsrLmData(Integer pageNumber, Integer pageSize) {
        return asrLmData.listAsrLmData(pageNumber, pageSize);
    }

    /******************************* 自学习模型管理 *******************************/
    // 创建自学习模型
    public static String createAsrLmModel(String name, String baseId, String description) {
        String modelId = asrLmModel.createAsrLmModel(name, baseId, description);
        if (null == modelId) {
            logger.error("【阿里】自学习模型：{}创建失败，modelId为null", name);
            return modelId;
        }
        // 轮询自学习模型，检查模型状态是否是Empty，即新创建的自学习模型。
        while (true) {
            AsrLmModel.LmModel model = asrLmModel.getAsrLmModel(modelId);
            if (null == model) {
                modelId = null;
                break;
            }
            if (model.Status.equals(STATUS_EMPTY)) {
                break;
            } else {
                logger.error("【阿里】创建自学习模型:{}失败，modelId:：{}", name, modelId);
                asrLmModel.deleteAsrLmModel(modelId);
                modelId = null;
                break;
            }
        }
        logger.debug("【阿里】创建自学习模型成功，name:{},modelId:{}", name, modelId);
        return modelId;
    }

    // 获取自学习模型
    public static AsrLmModel.LmModel getAsrLmModel(String modelId) {
        return asrLmModel.getAsrLmModel(modelId);
    }

    // 删除自学习模型
    public static boolean deleteAsrLmModel(String modelId) {
        AsrLmModel.LmModel model = asrLmModel.getAsrLmModel(modelId);
        if (null == model) {
            return false;
        }
        if (model.Status.equals(STATUS_TRAINING) || model.Status.equals(STATUS_DEPLOYING)) {
            logger.error("【阿里】自学习模型状态不允许进行删除操作，status:{},modelId:{}", model.Status, modelId);
            return false;
        }
        return asrLmModel.deleteAsrLmModel(modelId);
    }

    // 列举自学习模型
    public static AsrLmModel.LmModelPage listAsrLmModel(Integer pageNumber, Integer pageSize) {
        return asrLmModel.listAsrLmModel(pageNumber, pageSize);
    }

    /**************************** 自学习模型的训练与发布 ***************************/
    // 添加数据集到自学习模型
    public static boolean addDataToAsrLmModel(String dataId, String modelId) {
        AsrLmData.LmData data = asrLmData.getAsrLmData(dataId);
        if (null == data) {
            return false;
        }
        if (!data.Status.equals(STATUS_READY)) {
            logger.error("【阿里】数据集状态不允许进行添加到自学习模型操作，status:{},dataId:{}", data.Status, dataId);
            return false;
        }
        AsrLmModel.LmModel model = asrLmModel.getAsrLmModel(modelId);
        if (null == model) {
            return false;
        }
        if (model.Status.equals(STATUS_TRAINING) || model.Status.equals(STATUS_DEPLOYING)) {
            logger.debug("【阿里】自学习模型状态不允许进行添加数据集操作，status:{},modelId:{}", model.Status, modelId);
            return false;
        }
        return asrLmModel.addDataToAsrLmModel(dataId, modelId);
    }

    // 从自学习模型中删除数据集
    public static boolean removeDataFromAsrLmModel(String dataId, String modelId) {
        // 列举指定模型使用到的数据集，判断待删除的数据集是否已经添加到该模型。
        boolean isAdded = false;
        AsrLmData.LmDataPage page = asrLmData.listAsrLmData(1, 10, modelId);
        if (page != null && page.Content.size() > 0) {
            for (int i = 0; i < page.Content.size(); i++) {
                if (dataId.equals(page.Content.get(i).Id)) {
                    isAdded = true;
                    break;
                }
            }
        }
        if (!isAdded) {
            logger.error("【阿里】待删除的数据集没有添加到指定模型，不能进行删除操作！dataId:{}", dataId);
            return false;
        }
        // 检查模型状态是否允许删除数据集操作
        AsrLmModel.LmModel model = asrLmModel.getAsrLmModel(modelId);
        if (null == model) {
            return false;
        }
        if (model.Status.equals(STATUS_TRAINING)) {
            logger.error("【阿里】自学习模型状态不允许进行删除数据集操作，status:{},modelId:{}", model.Status, modelId);
            return false;
        }
        return asrLmModel.removeDataFromAsrLmModel(dataId, modelId);
    }

    // 训练自学习模型
    public static boolean trainAsrLmModel(String modelId) {
        AsrLmModel.LmModel model = asrLmModel.getAsrLmModel(modelId);
        if (null == model) {
            return false;
        }
        if (model.Status.equals(STATUS_DEPLOYING)) {
            logger.error("【阿里】自学习模型状态不允许进行训练操作，status:{},modelId:{}", model.Status, modelId);
            return false;
        }
        boolean isTrain = asrLmModel.trainAsrLmModel(modelId);
        if (!isTrain) {
            return isTrain;
        }
        // 轮询自学习模型，直到状态为Deployed，即自学习模型训练成功且并已上线。
        while (true) {
            model = asrLmModel.getAsrLmModel(modelId);
            if (null == model) {
                isTrain = false;
                break;
            }
            if (model.Status.equals(STATUS_TRAINING) || model.Status.equals(STATUS_DEPLOYING)) {
                if (model.Status.equals(STATUS_TRAINING)) {
                    logger.debug("【阿里】自学习模型正在训练中，modelId：{}", modelId);
                } else {
                    logger.debug("【阿里】自学习模型正在上线，modelId：{}", modelId);
                }
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            } else if (model.Status.equals(STATUS_TRAININGFAILED)) {
                logger.error("【阿里】自学习模型训练失败, modelId：{}", modelId);
                isTrain = false;
                break;
            } else if (model.Status.equals(STATUS_DEPLOYED)) {
                logger.debug("【阿里】自学习模型训练成功并已上线，modeId：{}", modelId);
                isTrain = true;
                break;
            } else {
                logger.error("【阿里】自学习模型状态不允许进行训练操作，Status：{},modeId：{}", model.Status, modelId);
                isTrain = false;
                break;
            }
        }
        return isTrain;
    }

    // 上线自学习模型
    public static boolean deployAsrLmModel(String modelId) {
        AsrLmModel.LmModel model = asrLmModel.getAsrLmModel(modelId);
        if (null == model) {
            return false;
        }
        if (!model.Status.equals(STATUS_READY)) {
            logger.error("【阿里】自学习模型状态不允许进行上线操作，status:{},modelId:{}", model.Status, modelId);
            return false;
        }
        boolean isDeployed = asrLmModel.deployAsrLmModel(modelId);
        if (!isDeployed) {
            return isDeployed;
        }
        // 轮询自学习模型，检查状态是否是Deployed，即自学习模型已上线。
        while (true) {
            model = asrLmModel.getAsrLmModel(modelId);
            if (null == model) {
                isDeployed = false;
                break;
            }
            if (model.Status.equals(STATUS_DEPLOYING)) {
                logger.debug("【阿里】自学习模型正在上线，modelId：{}", modelId);
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            } else if (model.Status.equals(STATUS_DEPLOYED)) {
                logger.debug("【阿里】自学习模型已经上线，modelId：{}", modelId);
                isDeployed = true;
                break;
            } else {
                logger.error("【阿里】自学习模型的状态不允许上线操作，Status：{},modelId:{}", model.Status, modelId);
                isDeployed = false;
                break;
            }
        }
        return isDeployed;
    }

    // 下线自学习模型
    public static boolean undeployAsrLmModel(String modelId) {
        AsrLmModel.LmModel model = asrLmModel.getAsrLmModel(modelId);
        if (null == model) {
            return false;
        }
        if (!model.Status.equals(STATUS_DEPLOYED)) {
            logger.error("【阿里】自学习模型的状态不允许进行下线操作，status:{},modelId:{}", model.Status, modelId);
            return false;
        }
        boolean isUnDeployed = asrLmModel.undeployAsrLmModel(modelId);
        if (!isUnDeployed) {
            return isUnDeployed;
        }
        // 轮询自学习模型，检查状态是否是Ready，即自学习模型下线完成。
        while (true) {
            model = asrLmModel.getAsrLmModel(modelId);
            if (null == model) {
                isUnDeployed = false;
                break;
            }
            if (model.Status.equals(STATUS_DEPLOYING)) {
                logger.debug("【阿里】自学习模型正在下线, modelId：{}", modelId);
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            } else if (model.Status.equals(STATUS_READY)) {
                logger.debug("【阿里】自学习模型下线完成，modelId：{}", modelId);
                isUnDeployed = true;
                break;
            } else {
                logger.error("【阿里】自学习模型的状态不允许进行下线操作，Status:{},modelId:{}", model.Status, modelId);
                isUnDeployed = false;
                break;
            }
        }
        return isUnDeployed;
    }
}