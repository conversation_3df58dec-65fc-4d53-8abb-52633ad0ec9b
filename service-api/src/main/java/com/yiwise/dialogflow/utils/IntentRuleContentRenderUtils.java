package com.yiwise.dialogflow.utils;

import com.yiwise.base.model.enums.CodeDescEnum;
import com.yiwise.dialogflow.entity.bo.algorithm.ResourceId2NameBO;
import com.yiwise.dialogflow.entity.enums.*;
import com.yiwise.dialogflow.entity.po.DialogFlowExtraRuleConditionNodePO;
import com.yiwise.dialogflow.entity.po.intent.IntentRuleConditionPO;
import javaslang.Tuple2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 话术中条件组转成字符内容工具类
 */
public class IntentRuleContentRenderUtils {


    public static String renderIntentRuleConditionContent(List<IntentRuleConditionPO> conditionList,
                                                          ResourceId2NameBO resource) {
        return renderIntentRuleConditionContent(conditionList,
                resource.getStepNode2NameMap(),
                resource.getSpecialAnswerId2NameMap(),
                resource.getKnowledgeMap(),
                resource.getIntentLevelIdNameMap(),
                resource.getEntityId2NameMap(),
                resource.getIntentId2NameMap(),
                resource.getVarId2NameMap(),
                resource.getLlmLabeId2NameMap());
    }

    public static String renderIntentRuleConditionContent(List<IntentRuleConditionPO> conditionList,
                                                          Map<DialogFlowExtraRuleConditionNodePO, Tuple2<String, String>> stepNode2NameMap,
                                                          Map<String, String> specialAnswerId2NameMap,
                                                          Map<String, String> knowledgeId2NameMap,
                                                          Map<Integer, String> intentLevelIdNameMap,
                                                          Map<String, String> entityId2NameMap,
                                                          Map<String, String> intentId2NameMap,
                                                          Map<String, String> varId2NameMap,
                                                          Map<String, String> llmLabeIdNameMap) {
        if (CollectionUtils.isEmpty(conditionList)) {
            return "";
        }

        List<String> itemStrList = conditionList.stream()
                .map(item -> renderIntentRuleCondition(item, stepNode2NameMap, specialAnswerId2NameMap, knowledgeId2NameMap, intentLevelIdNameMap, entityId2NameMap, intentId2NameMap, varId2NameMap, llmLabeIdNameMap))
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                .collect(Collectors.toList());

        return String.join("  \n", itemStrList);
    }

    private static String renderIntentRuleCondition(IntentRuleConditionPO condition,
                                                    Map<DialogFlowExtraRuleConditionNodePO, Tuple2<String, String>> stepNode2NameMap,
                                                    Map<String, String> specialAnswerId2NameMap,
                                                    Map<String, String> knowledgeId2NameMap,
                                                    Map<Integer, String> intentLevelIdNameMap,
                                                    Map<String, String> entityId2NameMap,
                                                    Map<String, String> intentId2NameMap,
                                                    Map<String, String> varId2NameMap,
                                                    Map<String, String> llmLabeIdNameMap) {
        if (Objects.isNull(condition)) {
            return "";
        }

        switch (condition.getType()) {
            case MAIN_STEP_FINISH_PERCENTAGE:
                return String.format("【主流程】%s %s %s ", condition.getOperation().getDesc(), condition.getNumber(), "%");
            case DIALOG_ROUND_COUNT:
                return String.format("【对话轮次】%s %s轮", condition.getOperation().getDesc(), condition.getNumber());
            case DIALOG_STATUS:
                List<String> dialStatus = condition.getDialStatusList().stream().map(DialStatusEnum::getDesc).collect(Collectors.toList());
                return String.format("【通话状态】%s %s", condition.getOperation().getDesc(), String.join("、", dialStatus));
            case DIALOG_DURATION:
                return String.format("【通话时长】%s %s秒", condition.getOperation().getDesc(), condition.getNumber());
            case ACTUAL_DIALOG_DURATION:
                return String.format("【实际通话时长】%s %s秒", condition.getOperation().getDesc(), condition.getNumber());
            case CUSTOMER_SAY_WORD_COUNT:
                return String.format("【客户说话字数】%s %s字", condition.getOperation().getDesc(), condition.getNumber());
            case BUSINESS_KNOWLEDGE_TRIGGER_COUNT:
                return String.format("【触发业务知识/流程次数】%s %s次", condition.getOperation().getDesc(), condition.getNumber());
            case DECLINE_TRIGGER_COUNT:
                return String.format("触发【拒绝次数】%s %s次", condition.getOperation().getDesc(), condition.getNumber());
            case DEFINITIVE_TRIGGER_COUNT:
                return String.format("触发【肯定次数】%s %s次", condition.getOperation().getDesc(), condition.getNumber());
            case TRIGGER_PROCESS_NODE:
                List<String> nodeNameList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(condition.getNodeList())) {
                    condition.getNodeList().forEach(nodeInfo -> {
                        Tuple2<String, String> tuple = stepNode2NameMap.get(nodeInfo);
                        if (Objects.nonNull(tuple)) {
                            String stepName = tuple._1;
                            String nodeName = tuple._2;
                            nodeNameList.add(String.format("%s流程的%s节点", stepName, nodeName));
                        }
                    });
                }
                String desc = String.join(" 或 ", nodeNameList);
                return String.format("【触发流程节点】触发主流程 %s", desc);
            case CUSTOMER_HANGUP_ON_SPECIAL_NODE:
                return String.format("【当前节点客户主动挂断】%s", condition.getOperation().getDesc());
            case TRIGGER_ROBOT_KNOWLEDGE:
                if (CollectionUtils.isNotEmpty(condition.getRobotKnowledgeIdList())) {
                    List<String> knowledgeList = new ArrayList<>();
                    condition.getRobotKnowledgeIdList().forEach(item -> {
                        knowledgeList.add(String.format("%s %s %s次", knowledgeId2NameMap.getOrDefault(item, null), condition.getOperation().getDesc(), condition.getNumber()));
                    });
                    String content = String.join("或", knowledgeList);
                    return String.format("【触发问答知识】%s", content);
                }
            case TRIGGER_DEPENDENCE_DIALOGFLOW:
                if (Objects.isNull(condition.getOperation())) {
                    if (CollectionUtils.isEmpty(condition.getNodeList())) {
                        return "";
                    }
                    List<String> nodeList = new ArrayList<>();
                    condition.getNodeList().forEach(nodeInfo -> {
                        Tuple2<String, String> tuple = stepNode2NameMap.get(nodeInfo);
                        if (Objects.nonNull(tuple)) {
                            String stepName = tuple._1;
                            String nodeName = tuple._2;
                            nodeList.add(String.format("%s流程的%s节点", stepName, nodeName));
                        }
                    });
                    String msg = String.join(" 或 ", nodeList);
                    return String.format("【触发独立对话流】%s", msg);
                } else {
                    return String.format("【触发独立对话流】%s %s次", condition.getOperation().getDesc(), condition.getNumber());
                }
            case CUSTOMER_FINALLY_REJECT:
                return String.format("【用户最后拒绝】%s", condition.getOperation().getDesc());
            case CUSTOMER_FINALLY_DEFINITIVE:
                return String.format("【用户最后肯定】%s", condition.getOperation().getDesc());
            case AI_UNKNOWN_PERCENT:
                return String.format("【ai无法应答占比】%s %s %s ", condition.getOperation().getDesc(), condition.getNumber(), "%");
            case TRIGGER_SPECIAL_CONTEXT:
                return String.format("【触发特殊语境】%s %s %s次", specialAnswerId2NameMap.get(condition.getSpecialAnswerId()), condition.getOperation() == null ? null : condition.getOperation().getDesc(), condition.getNumber());
            case FAST_HANGUP:
                return String.format("【快速挂断】%s", condition.getOperation().getDesc());
            case CUSTOMER_HANGUP:
                return String.format("【用户主动挂断】%s", condition.getOperation().getDesc());
            case DIALOG_CONTENT:
                return String.format("【通话内容】%s %s", condition.getOperation().getDesc(), String.join("、", condition.getKeywords()));
            case LAST_CUSTOMER_CONTENT:
                return String.format("【用户最后通话内容】%s %s", condition.getOperation().getDesc(), String.join("、", condition.getKeywords()));
            case LAST_AI_CONTENT:
                return String.format("【ai最后通话内容】%s %s", condition.getOperation().getDesc(), String.join("、", condition.getKeywords()));
            case STEP_NODE_FINISH_PERCENTAGE:
                String msg = Optional.ofNullable(condition.getNodeList()).orElse(new ArrayList<>()).stream().map(stepNode2NameMap::get).filter(Objects::nonNull)
                        .map(t -> String.format("%s流程中的%s节点", t._1, t._2)).collect(Collectors.joining(" 或 "));
                return String.format("【流程节点完成度】 触发流程 %s %s %s", msg, condition.getOperation().getDesc(), condition.getNumber() + "%");
            case KNOWLEDGE_FINISH_PERCENTAGE:
                return String.format("【知识问答完成度】 %s %s %s", condition.getRobotKnowledgeIdList().stream().map(knowledgeId2NameMap::get).filter(Objects::nonNull).collect(Collectors.joining(" 或 "))
                        , condition.getOperation().getDesc(), condition.getNumber() + "%");
            case ALGORITHM_ACTIVITY_INTENT_LEVEL:
                AlgorithmActivityIntentLevelEnum activityIntentLevelEnum = CodeDescEnum.getFromCodeOrNull(AlgorithmActivityIntentLevelEnum.class, condition.getNumber());
                return String.format("【算法标签(电商活动通知)】为%s", Objects.isNull(activityIntentLevelEnum) ? null : activityIntentLevelEnum.getDesc());
            case ALGORITHM_EDUCATION_ACTIVITY_INTENT_LEVEL:
                AlgorithmEducationActivityIntentLevelEnum educationActivityIntentLevelEnum = CodeDescEnum.getFromCodeOrNull(AlgorithmEducationActivityIntentLevelEnum.class, condition.getNumber());
                return String.format("【算法标签(教育活动通知)】为%s", Objects.isNull(educationActivityIntentLevelEnum) ? null : educationActivityIntentLevelEnum.getDesc());
            case ALGORITHM_PRIVATE_DOMAIN_INTENT_LEVEL:
                AlgorithmPrivateIntentLevelEnum intentLevelEnum = CodeDescEnum.getFromCodeOrNull(AlgorithmPrivateIntentLevelEnum.class, condition.getNumber());
                return String.format("【算法标签(电商主动加微)】为%s", Objects.isNull(intentLevelEnum) ? null : intentLevelEnum.getDesc());
            case ALGORITHM_PRIVATE_DOMAIN_INTENT_LEVEL_PASSIVE:
                AlgorithmPrivateIntentLevelEnum privateIntentLevelEnum = CodeDescEnum.getFromCodeOrNull(AlgorithmPrivateIntentLevelEnum.class, condition.getNumber());
                return String.format("【算法标签(电商被动加微)】为%s", Objects.isNull(privateIntentLevelEnum) ? null : privateIntentLevelEnum.getDesc());
            case INTENT_TAG:
                if (CollectionUtils.isEmpty(condition.getIntentLevelIdList())) {
                    return String.format("【意向标签】为%s", "");
                }
                List<String> intentLevelList = new ArrayList<>();
                condition.getIntentLevelIdList().forEach(item -> {
                    intentLevelList.add(String.format("%s", intentLevelIdNameMap.getOrDefault(item, null)));
                });
                String content = String.join("或", intentLevelList);
                return String.format("【意向标签】为%s", content);
            case ENTITY_COLLECT:
                List<String> entityNameList = new ArrayList<>();
                condition.getEntityIdList().forEach(entityId -> {
                    String entityName = entityId2NameMap.getOrDefault(entityId, null);
                    if (StringUtils.isNotBlank(entityName)) {
                        entityNameList.add(entityName);
                    }
                });
                String entityName = String.join(", ", entityNameList);
                return String.format("【实体采集】%s %s", entityName, condition.getOperation().getDesc());
            case HIT_INTENT:
                List<String> intentNameList = new ArrayList<>();
                condition.getIntentIdList().forEach(intentId -> {
                    String intentName = intentId2NameMap.getOrDefault(intentId, null);
                    if (StringUtils.isNotBlank(intentName)) {
                        intentNameList.add(intentName);
                    }
                });
                String intentName = String.join(", ", intentNameList);
                if (DialogFlowConditionOperationTypeEnum.ANY_HIT.equals(condition.getOperation())) {
                    return String.format("【意图名称】%s %s %s %s次", intentName, condition.getOperation().getDesc(), condition.getSubOperation().getDesc(), condition.getNumber());
                } else {
                    return String.format("【意图名称】%s %s", intentName, condition.getOperation().getDesc());
                }
            case DYNAMIC_VARIABLE:
                List<String> variableNameList = new ArrayList<>();
                condition.getVariableIdList().forEach(variableId -> {
                    String varName = varId2NameMap.getOrDefault(variableId, null);
                    if (StringUtils.isNotBlank(varName)) {
                        variableNameList.add(varName);
                    }
                });
                String varName = String.join(", ", variableNameList);
                return String.format("【动态变量】%s %s", varName, condition.getOperation().getDesc());
            case LLM_LABEL:
                return String.format("【大模型通用分类】为%s", llmLabeIdNameMap.getOrDefault(condition.getLlmLabelId(), ""));
            case LLM_BUILT_IN_TAG:
                return String.format("【大模型通话标签】匹配意图描述 %s", String.join(",", condition.getDescList()));
            case LLM_CUSTOM_TAG:
                return String.format("【大模型自定义通话标签】匹配意图描述 %s", String.join(",", condition.getDescList()));
            case EFFECTIVE_CHAT_ROUNDS:
                return String.format("【有效对话轮次】%s %s", condition.getOperation().getDesc(), condition.getNumber());
            case USER_LAST_SAY:
                return String.format("【用户最后说话】 %s", condition.getOperation().getDesc());
            default:
                return "";
        }
    }
}
