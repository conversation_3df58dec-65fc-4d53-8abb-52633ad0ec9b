package com.yiwise.dialogflow.chatcontroller;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.engine.share.SystemMetaData;
import com.yiwise.dialogflow.service.SystemMetaDataService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("apiBot/v3/system/")
public class SystemMetaDataController {

    @Resource
    private SystemMetaDataService systemMetaDataService;

    @GetMapping("getMetaData")
    public ResultObject<SystemMetaData> getMetaData() {
        return ResultObject.success(systemMetaDataService.getSystemMetaData());
    }
}
