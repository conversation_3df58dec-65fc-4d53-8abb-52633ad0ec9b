package com.yiwise.dialogflow;

import com.yiwise.base.common.application.StartFailedEvent;
import com.yiwise.dialogflow.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoReactiveAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@Slf4j
@EnableFeignClients(basePackages = "com.yiwise")
@EnableDiscoveryClient
@SpringBootApplication(exclude = {
        MongoReactiveAutoConfiguration.class
})
public class ChatWebApplication {

    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplicationBuilder(ChatWebApplication.class)
                .build();
        springApplication.addListeners(new StartFailedEvent());
        springApplication.run(args);
        for (int i = 0; i < 100; i++) {
            log.info("=============================spring boot start successful !=============================");
        }
        if (!WebApplicationType.REACTIVE.equals(springApplication.getWebApplicationType())) {
            log.error("当前应用不是REACTIVE应用, 请检查配置");
            System.exit(1);
        }
        LogUtils.setLoggerImmediateFlush(false, "asyncRollingFileAppender", "rollingFileAppender");
    }

}
