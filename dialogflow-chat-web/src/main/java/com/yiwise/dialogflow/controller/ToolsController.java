package com.yiwise.dialogflow.controller;

import com.yiwise.base.model.annotation.auth.InnerOnly;
import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.dialogflow.engine.resource.PatternCache;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("apiBot/v3/tools")
public class ToolsController {

    @NoLogin
    @InnerOnly
    @GetMapping("getPatternCacheUseInfo")
    public ResultObject<Map<String, Long>> getPatternCacheUseInfo() {
        return ResultObject.success(PatternCache.getCacheInfo());
    }
}
