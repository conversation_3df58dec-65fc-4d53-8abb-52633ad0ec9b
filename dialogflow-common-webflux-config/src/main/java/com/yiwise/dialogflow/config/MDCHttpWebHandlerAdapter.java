package com.yiwise.dialogflow.config;

import com.yiwise.base.common.utils.string.MyRandomStringUtils;
import com.yiwise.dialogflow.common.ApplicationConstant;
import com.yiwise.dialogflow.common.MdcLogIdLifterTransformer;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.server.WebHandler;
import org.springframework.web.server.adapter.HttpWebHandlerAdapter;
import reactor.core.publisher.Mono;

@Slf4j
public class MDCHttpWebHandlerAdapter extends HttpWebHandlerAdapter {
    public MDCHttpWebHandlerAdapter(WebHandler delegate) {
        super(delegate);
    }

    @Override
    public Mono<Void> handle(ServerHttpRequest request, ServerHttpResponse response) {
        final String mdcLogId = MyRandomStringUtils.getRandomEasyStringByLength(6);
        MDC.put(ApplicationConstant.MDC_LOG_ID, mdcLogId);
        return super.handle(request, response)
                .transformDeferred(MdcLogIdLifterTransformer.lift())
                .doOnSuccess(aVoid -> MDC.remove(ApplicationConstant.MDC_LOG_ID))
                .doOnError(throwable -> MDC.remove(ApplicationConstant.MDC_LOG_ID))
                .subscriberContext(context -> context.put(ApplicationConstant.MDC_LOG_ID, mdcLogId));
    }
}
